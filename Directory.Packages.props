<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <NoWarn>$(NoWarn);NU1507</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="AutoMapper" Version="12.0.1" />
    <PackageVersion Include="Azure.AI.Agents.Persistent" Version="1.1.0-beta.2" />
    <PackageVersion Include="Azure.Search.Documents" Version="11.6.1" />
    <PackageVersion Include="coverlet.collector" Version="6.0.0" />
    <PackageVersion Include="DC.Api.Base" Version="1.2.1229175" />
    <PackageVersion Include="DC.BusinessLogic.Base" Version="1.2.1229175" />
    <PackageVersion Include="DC.Customers.Client" Version="3.0.20250523.4" />
    <PackageVersion Include="DC.Domain.Exceptions" Version="1.2.1229175" />
    <PackageVersion Include="DC.Financials.Client" Version="3.0.********.1" />
    <PackageVersion Include="DC.OpenTelemetry.Dynatrace" Version="1.2.1237813" />
    <PackageVersion Include="DC.Products.Client" Version="3.0.********.4" />
    <PackageVersion Include="DC.Repositories.Base" Version="1.2.1229175" />
    <PackageVersion Include="DC.SAPI" Version="1.2.1229175" />
    <PackageVersion Include="DC.Security.Encryption" Version="1.2.1229175" />
    <PackageVersion Include="DC.Storage.Client" Version="3.0.********.2" />
    <PackageVersion Include="DC.Telemetry" Version="1.2.1229175" />
    <PackageVersion Include="DC.Test.Integration" Version="1.2.1229175" />
    <PackageVersion Include="DC.Usages.Client" Version="3.0.********.3" />
    <PackageVersion Include="DC.UserAccounts.Client" Version="3.0.********.1" />
    <PackageVersion Include="DC.Utilities" Version="1.2.1229175" />
    <PackageVersion Include="FluentAssertions" Version="[7.0.0, 8.0.0)" />
    <PackageVersion Include="JWT" Version="10.1.1" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Azure.KeyVault.Core" Version="3.0.5" />
    <PackageVersion Include="Microsoft.Bcl.TimeProvider" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Bot.Builder" Version="4.22.0" />
    <PackageVersion Include="Microsoft.Bot.Builder.Azure" Version="4.22.0" />
    <PackageVersion Include="Microsoft.Bot.Builder.Azure.Blobs" Version="4.22.0" />
    <PackageVersion Include="Microsoft.Bot.Builder.Dialogs" Version="4.22.0" />
    <PackageVersion Include="Microsoft.Bot.Builder.Integration.AspNet.Core" Version="4.22.0" />
    <PackageVersion Include="Microsoft.Bot.Builder.Testing" Version="4.22.0" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.TimeProvider.Testing" Version="9.4.0" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageVersion Include="Microsoft.Rest.ClientRuntime" Version="2.3.24" />
    <PackageVersion Include="Microsoft.SemanticKernel" Version="1.58.0" />
    <PackageVersion Include="Microsoft.SemanticKernel.Agents.AzureAI" Version="1.58.0-preview" />
    <PackageVersion Include="Microsoft.SemanticKernel.Agents.Core" Version="1.58.0" />
    <PackageVersion Include="Microsoft.TestPlatform.TestHost" Version="17.8.0" />
    <PackageVersion Include="MimeTypeMapOfficial" Version="1.0.17" />
    <PackageVersion Include="ModelContextProtocol" Version="0.3.0-preview.1" />
    <PackageVersion Include="ModelContextProtocol.AspNetCore" Version="0.3.0-preview.1" />
    <PackageVersion Include="Moq" Version="4.20.70" />
    <PackageVersion Include="xunit" Version="2.6.6" />
    <PackageVersion Include="xunit.extensibility.core" Version="2.6.6" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.5.6" />
  </ItemGroup>
</Project>