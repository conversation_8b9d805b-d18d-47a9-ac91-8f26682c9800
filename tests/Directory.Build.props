<Project>
  <PropertyGroup>
    <!--
      This Directory.Build.props file is included in the DC.Api.Base nuget, which is then redistributed to all API solutions.
      Ensuring that all solutions have this value set to true will make sure that .pdbs are included in the bin folder when an API is built. 
      This can improve the debugging experience. https://learn.microsoft.com/en-us/dotnet/core/project-sdk/msbuild-props#copydebugsymbolfilesfrompackages
    -->
    <CopyDebugSymbolFilesFromPackages>true</CopyDebugSymbolFilesFromPackages>
	<SolutionDir Condition="'$(SolutionDir)' == ''">$(MSBuildThisFileDirectory)</SolutionDir>
    <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
    <DisableImplicitNuGetFallbackFolder>true</DisableImplicitNuGetFallbackFolder>
	<MsbuildTaskMicrosoftCodeAnalysisCSharpVersion>$(MicrosoftCodeAnalysisCSharpVersion)</MsbuildTaskMicrosoftCodeAnalysisCSharpVersion>
  </PropertyGroup>
	<!-- Disable Warnings like the XML Comments on public class, methods and attribute on Projects with Generate Documenation file on but the models are not exposed to the Frontend.
	Because of the Property GenerateDocumentationFile is set on true SonarCloud and MSBuild gives build warnings that XML comments are needed with the nowarn the specific XML comment code warnings this is getting disabled in the MSbuild and SonarCloud.
	This only effect projects with a specific property where you want to disable these warnings -->
	<PropertyGroup Label="Disabled XML Comments Warnings" Condition="'$(DisableXmlWarnings)' == 'true'">
		<NoWarn>$(NoWarn);1701;1702;CA2254;CS1591;CS1572;CS1573;CS1570</NoWarn>
	</PropertyGroup>
</Project>