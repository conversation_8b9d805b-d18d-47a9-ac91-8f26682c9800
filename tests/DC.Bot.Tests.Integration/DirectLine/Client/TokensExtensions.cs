// <auto-generated>
// Code generated by Microsoft (R) AutoRest Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.
// </auto-generated>

namespace DC.Bot.Tests.Integration.Directline
{
    using Models;
    using System.Threading;
    using System.Threading.Tasks;

    /// <summary>
    /// Extension methods for Tokens.
    /// </summary>
    public static partial class TokensExtensions
    {
            /// <summary>
            /// Refresh a token
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            public static Conversation RefreshToken(this ITokens operations)
            {
                return operations.RefreshTokenAsync().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Refresh a token
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<Conversation> RefreshTokenAsync(this ITokens operations, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RefreshTokenWithHttpMessagesAsync(null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Generate a token for a new conversation
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='tokenParameters'>
            /// </param>
            public static Conversation GenerateTokenForNewConversation(this ITokens operations, TokenParameters tokenParameters = default(TokenParameters))
            {
                return operations.GenerateTokenForNewConversationAsync(tokenParameters).GetAwaiter().GetResult();
            }

            /// <summary>
            /// Generate a token for a new conversation
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='tokenParameters'>
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<Conversation> GenerateTokenForNewConversationAsync(this ITokens operations, TokenParameters tokenParameters = default(TokenParameters), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.GenerateTokenForNewConversationWithHttpMessagesAsync(tokenParameters, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

    }
}
