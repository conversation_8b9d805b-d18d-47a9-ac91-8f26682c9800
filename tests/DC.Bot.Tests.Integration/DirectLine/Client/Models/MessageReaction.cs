// <auto-generated>
// Code generated by Microsoft (R) AutoRest Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.
// </auto-generated>

namespace DC.Bot.Tests.Integration.Directline.Models
{
    using Newtonsoft.Json;
    using System.Linq;

    /// <summary>
    /// Message reaction object
    /// </summary>
    public partial class MessageReaction
    {
        /// <summary>
        /// Initializes a new instance of the MessageReaction class.
        /// </summary>
        public MessageReaction()
        {
            CustomInit();
        }

        /// <summary>
        /// Initializes a new instance of the MessageReaction class.
        /// </summary>
        /// <param name="type">Message reaction type</param>
        public MessageReaction(string type = default(string))
        {
            Type = type;
            CustomInit();
        }

        /// <summary>
        /// An initialization method that performs custom operations like setting defaults
        /// </summary>
        partial void CustomInit();

        /// <summary>
        /// Gets or sets message reaction type
        /// </summary>
        [JsonProperty(PropertyName = "type")]
        public string Type { get; set; }

    }
}
