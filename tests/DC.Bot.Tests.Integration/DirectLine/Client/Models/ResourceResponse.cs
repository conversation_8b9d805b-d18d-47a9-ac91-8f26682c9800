// <auto-generated>
// Code generated by Microsoft (R) AutoRest Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.
// </auto-generated>

namespace DC.Bot.Tests.Integration.Directline.Models
{
    using Newtonsoft.Json;
    using System.Linq;

    /// <summary>
    /// A response containing a resource ID
    /// </summary>
    public partial class ResourceResponse
    {
        /// <summary>
        /// Initializes a new instance of the ResourceResponse class.
        /// </summary>
        public ResourceResponse()
        {
            CustomInit();
        }

        /// <summary>
        /// Initializes a new instance of the ResourceResponse class.
        /// </summary>
        /// <param name="id">Id of the resource</param>
        public ResourceResponse(string id = default(string))
        {
            Id = id;
            CustomInit();
        }

        /// <summary>
        /// An initialization method that performs custom operations like setting defaults
        /// </summary>
        partial void CustomInit();

        /// <summary>
        /// Gets or sets id of the resource
        /// </summary>
        [JsonProperty(PropertyName = "id")]
        public string Id { get; set; }

    }
}
