// <auto-generated>
// Code generated by Microsoft (R) AutoRest Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.
// </auto-generated>

namespace DC.Bot.Tests.Integration.Directline.Models
{
    using Newtonsoft.Json;
    using System.Collections;
    using System.Collections.Generic;
    using System.Linq;

    /// <summary>
    /// Represents a reference to a programmatic action
    /// </summary>
    public partial class SemanticAction
    {
        /// <summary>
        /// Initializes a new instance of the SemanticAction class.
        /// </summary>
        public SemanticAction()
        {
            CustomInit();
        }

        /// <summary>
        /// Initializes a new instance of the SemanticAction class.
        /// </summary>
        /// <param name="state">State of this action. Allowed values: `start`,
        /// `continue`, `done`</param>
        /// <param name="id">ID of this action</param>
        /// <param name="entities">Entities associated with this action</param>
        public SemanticAction(string state = default(string), string id = default(string), IDictionary<string, Entity> entities = default(IDictionary<string, Entity>))
        {
            State = state;
            Id = id;
            Entities = entities;
            CustomInit();
        }

        /// <summary>
        /// An initialization method that performs custom operations like setting defaults
        /// </summary>
        partial void CustomInit();

        /// <summary>
        /// Gets or sets state of this action. Allowed values: `start`,
        /// `continue`, `done`
        /// </summary>
        [JsonProperty(PropertyName = "state")]
        public string State { get; set; }

        /// <summary>
        /// Gets or sets ID of this action
        /// </summary>
        [JsonProperty(PropertyName = "id")]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets entities associated with this action
        /// </summary>
        [JsonProperty(PropertyName = "entities")]
        public IDictionary<string, Entity> Entities { get; set; }

    }
}
