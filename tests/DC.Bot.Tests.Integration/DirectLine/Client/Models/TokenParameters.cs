// <auto-generated>
// Code generated by Microsoft (R) AutoRest Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.
// </auto-generated>

namespace DC.Bot.Tests.Integration.Directline.Models
{
    using Newtonsoft.Json;
    using System.Collections;
    using System.Collections.Generic;
    using System.Linq;

    /// <summary>
    /// Parameters for creating a token
    /// </summary>
    public partial class TokenParameters
    {
        /// <summary>
        /// Initializes a new instance of the TokenParameters class.
        /// </summary>
        public TokenParameters()
        {
            CustomInit();
        }

        /// <summary>
        /// Initializes a new instance of the TokenParameters class.
        /// </summary>
        /// <param name="user">User account to embed within the token</param>
        /// <param name="trustedOrigins">Trusted origins to embed within the
        /// token</param>
        public TokenParameters(ChannelAccount user = default(ChannelAccount), IList<string> trustedOrigins = default(IList<string>), string eTag = default(string))
        {
            User = user;
            TrustedOrigins = trustedOrigins;
            ETag = eTag;
            CustomInit();
        }

        /// <summary>
        /// An initialization method that performs custom operations like setting defaults
        /// </summary>
        partial void CustomInit();

        /// <summary>
        /// Gets or sets user account to embed within the token
        /// </summary>
        [JsonProperty(PropertyName = "user")]
        public ChannelAccount User { get; set; }

        /// <summary>
        /// Gets or sets trusted origins to embed within the token
        /// </summary>
        [JsonProperty(PropertyName = "trustedOrigins")]
        public IList<string> TrustedOrigins { get; set; }

        /// <summary>
        /// </summary>
        [JsonProperty(PropertyName = "eTag")]
        public string ETag { get; set; }

    }
}
