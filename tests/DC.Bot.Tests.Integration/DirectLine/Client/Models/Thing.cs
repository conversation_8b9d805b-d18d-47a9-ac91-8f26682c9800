// <auto-generated>
// Code generated by Microsoft (R) AutoRest Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.
// </auto-generated>

namespace DC.Bot.Tests.Integration.Directline.Models
{
    using Newtonsoft.Json;
    using System.Linq;

    /// <summary>
    /// Thing (entity type: "https://schema.org/Thing")
    /// </summary>
    public partial class Thing
    {
        /// <summary>
        /// Initializes a new instance of the Thing class.
        /// </summary>
        public Thing()
        {
            CustomInit();
        }

        /// <summary>
        /// Initializes a new instance of the Thing class.
        /// </summary>
        /// <param name="type">The type of the thing</param>
        /// <param name="name">The name of the thing</param>
        public Thing(string type = default(string), string name = default(string))
        {
            Type = type;
            Name = name;
            CustomInit();
        }

        /// <summary>
        /// An initialization method that performs custom operations like setting defaults
        /// </summary>
        partial void CustomInit();

        /// <summary>
        /// Gets or sets the type of the thing
        /// </summary>
        [JsonProperty(PropertyName = "type")]
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the name of the thing
        /// </summary>
        [JsonProperty(PropertyName = "name")]
        public string Name { get; set; }

    }
}
