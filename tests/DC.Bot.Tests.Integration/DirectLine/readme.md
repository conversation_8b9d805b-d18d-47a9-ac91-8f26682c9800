﻿# DC Customers API
> see https://aka.ms/autorest

## Getting Started
Just a small sidenote for autorest

1. You need a file with a swagger json in it (like MVS-swagger.json)
2. Check your swagger here https://editor.swagger.io/
3. install autorest using powershell:      
    
        npm install -g autorest
4. new version: 
        
        npm install -g autorest@latest
5. Set the input-file field below to the Swagger.json from step 1 and set the namespace to a fitting name for the API.
6. Regenerate by starting powershell in current folder and type in:
		
		autorest
---

## Configuration
The following are the settings for this using this API with AutoRest.

``` yaml

input-file: directline-swagger.json
csharp:
  namespace: DC.Bot.Tests.Integration.Directline
  output-folder: Client

# Set (de)serialization settings objects to static
directive:
  - reason: Altering the usage of SerializationSettings/DeserializationSettings to use a static instance instead.
    from: source-file-csharp
    where: $
    transform: >
      if( /public JsonSerializerSettings/g.exec( $ ) ) {
        $ = $.replace( /public (JsonSerializerSettings) (\w*) (.*)/g, "private static $1 _$2;\r\n        public $1 $2 => _$2;" );
        $ = $.replace( /(\s*)(\w*erializationSettings) = (new JsonSerializerSettings)/g, "$1_$2 = _$2 ?? $3" );
      }

```
