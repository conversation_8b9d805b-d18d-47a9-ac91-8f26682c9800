﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ProduceReferenceAssembly>false</ProduceReferenceAssembly>
    <IsPackable>false</IsPackable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="DC.Test.Integration" />
    <PackageReference Include="Microsoft.Bot.Builder.Testing" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="xunit.extensibility.core" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\code\DC.Bot.Api\DC.Bot.Api.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="integration.Acceptance.runsettings">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="integration.runsettings">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="integration.Test.runsettings">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>