﻿<?xml version="1.0" encoding="utf-8"?>
<RunSettings>

  <MSTest>
    <Parallelize>
      <Workers>4</Workers>
      <Scope>ClassLevel</Scope>
    </Parallelize>
  </MSTest>

  <RunConfiguration>
    <TestSessionTimeout>300000</TestSessionTimeout>
  </RunConfiguration>

  <TestRunParameters>

    <Parameter name="baseUrlApi" value="https://digitalcore-xapi-bot-v1-p-staging.azurewebsites.net/" />
    <Parameter name="integrationtestUrl" value="https://digitalcore-xapi-bot-v1-p-staging.azurewebsites.net/api/messages/test" />
    <Parameter name="environment" value="DevelopmentLocal" />

    <Parameter name="clientCertBlob" value="set this value via override testruntime parameters in DevOps release pipe (preferably from keyvault)."/>
    <Parameter name="clientCertPassword" value="set this value via override testruntime parameters in DevOps release pipe (preferably from keyvault)."/>

  </TestRunParameters>
</RunSettings>
