﻿using DC.Bot.BusinessLogic.Models;
using DC.Financials.Client.Models;
using FluentAssertions;
using Xunit;
namespace DC.Bot.Tests.Models;

public class AdvancePaymentTests
{
    [Fact]
    public void GetDescriptionByMonth_Test()
    {
        var advancePayment = new AdvancePayment
        {
            Preferences = new FinancialPreferences
            {
                PaymentMethodIsDirectDebit = true,
                PaymentDayOfMonth = 5
            }
        };
        advancePayment.IsRecurringPaymentWithPreferedPaymentDay.Should().BeTrue();
        advancePayment.IsRecurringPaymentWithoutPreferedPaymentDay.Should().BeFalse();
        advancePayment.PaymentWithGiroCard.Should().BeFalse();

        advancePayment.Preferences.PaymentMethodIsDirectDebit = false;
        advancePayment.PaymentWithGiroCard.Should().BeTrue();

        advancePayment.Preferences.PaymentMethodIsDirectDebit = true;
        advancePayment.Preferences.PaymentDayOfMonth = null;
        advancePayment.PaymentWithGiroCard.Should().BeFalse();
        advancePayment.IsRecurringPaymentWithoutPreferedPaymentDay.Should().BeTrue();

        advancePayment.Preferences = null;
        advancePayment.PaymentWithGiroCard.Should().BeTrue();
    }
}