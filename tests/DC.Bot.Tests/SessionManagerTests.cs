﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Customers.Client.Models;
using DC.Domain.Exceptions;
using DC.Domain.Models.Customers;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using FluentAssertions;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.Bot.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using AddressModel = DC.Customers.Client.Models.AddressModel;

namespace DC.Bot.Tests;

[Collection("Sequential")]
public class SessionManagerTests : TestBase
{
    private static ConfigurationManager<OpenIdConnectConfiguration> openIdConfigurationManager { get; set; }

    [Fact]
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Blocker Code Smell", "S2699:Tests should include assertions", Justification = "<Pending>")]
    public async Task SendCustomerInfoEvent_WorksExpected()
    {
        var session = Setup();
        var dialog = SetupMainDialog();
        await dialog.SendActivityAsync(DialogCommands._VERIFICATION).ConfigureAwait(true);
        await session.SendCustomerInfoEvent(dialog.DialogContext.Context, new PersonalisationInfo { }, null);
    }

    [Fact]
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Blocker Code Smell", "S2699:Tests should include assertions", Justification = "<Pending>")]
    public async Task SendCustomMessageWithAttachment_WorksExpected()
    {
        var session = Setup();
        var dialog = SetupMainDialog();
        await dialog.SendActivityAsync(DialogCommands._VERIFICATION).ConfigureAwait(true);
        await session.SendCustomMessageWithAttachment(dialog.DialogContext.Context, new PersonalisationInfo { });
    }

    [Fact]
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Blocker Code Smell", "S2699:Tests should include assertions", Justification = "<Pending>")]
    public async Task SendMessageWithExpectedAttachmentSend_WorksExpected()
    {
        var session = Setup();
        var dialog = SetupMainDialog();
        await dialog.SendActivityAsync(DialogCommands._VERIFICATION).ConfigureAwait(true);
        await session.SendMessageWithExpectedAttachmentSend(dialog.DialogContext.Context, "message");
    }

    [Fact]
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Blocker Code Smell", "S2699:Tests should include assertions", Justification = "<Pending>")]
    public async Task SetDialogData_WorksExpected()
    {
        var session = Setup();
        var dialog = SetupMainDialog();
        await dialog.SendActivityAsync(DialogCommands._VERIFICATION).ConfigureAwait(true);
        await session.SetCurrentDialogAction(dialog.DialogContext.Context, DialogAction.AnonymousAddress);
    }

    [Fact]
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Blocker Code Smell", "S2699:Tests should include assertions", Justification = "<Pending>")]
    public async Task ResetConversationData_WorksExpected()
    {
        var session = Setup();
        var dialog = SetupMainDialog();
        await dialog.SendActivityAsync(DialogCommands._VERIFICATION).ConfigureAwait(true);
        await session.ResetConversationData(dialog.DialogContext.Context);
    }

    [Theory]
    [InlineData(TransactionStatus.Success, false)]
    [InlineData(TransactionStatus.Success, true)]
    [InlineData(TransactionStatus.PermanentFailure, false)]
    [InlineData(TransactionStatus.PermanentFailure, true)]
    public async Task SendEndOfTransactionActivity_WorksExpected(TransactionStatus transactionStatus, bool isNba)
    {
        var session = Setup();
        var dialog = SetupMainDialog();

        await dialog.SendActivityAsync(DialogCommands._VERIFICATION).ConfigureAwait(true);

        var dialogData = await session.GetDialogData(dialog.DialogContext.Context);
        dialogData.NextBestAction.CurrentTransactionIsNba = isNba;
        dialogData.NextBestAction.Data = new NextBestAction();

        await session.SendEndOfTransactionActivity(dialog.DialogContext.Context, null, null, transactionStatus);

        // CurrentTransactionIsNba should always be false after the end of transaction trigger.
        dialogData.NextBestAction.CurrentTransactionIsNba.Should().BeFalse();

        // if the CurrentTransactionIsNba is true and ended with success, the NextBestAction.Data should be resetted to null
        if (transactionStatus == TransactionStatus.Success && isNba)
            dialogData.NextBestAction.Data.Should().BeNull();
        else
            dialogData.NextBestAction.Data.Should().NotBeNull();
    }

    [Fact]
    public async Task GetSeamlyCustomerInfo_WorksExpected()
    {
        var session = Setup();

        var result = await session.GetSeamlyCustomerInfo(new DialogData
        {
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Channel = Telemetry.Models.BotChannel.Web
        });

        result.customer.Should().NotBeNull();
        result.personalisation.Should().NotBeNull();
        result.nba.Should().NotBeNull();
    }

    private static SessionManager Setup(int customerId = 1, bool throwsException = false, Label label = Label.Eneco)
    {
        var contextMock = new Mock<IStatePropertyAccessor<ConversationData>>();
        var cRepoMock = new Mock<ICustomersService>();
        var nbaMock = new Mock<INextBestActionService>();
        var cmMock = new Mock<IConfigurationManager<OpenIdConnectConfiguration>>();
        var mC = new Mock<IConfiguration>();
        var loggerMock = new Mock<ILoggerFactory>();
        ILogger logger = new Mock<ILogger>().Object;
        loggerMock.Setup(f => f.CreateLogger(It.IsAny<string>())).Returns(logger);

        contextMock.Setup(x => x.GetAsync(It.IsAny<ITurnContext>(), It.IsAny<Func<ConversationData>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ConversationData
            {
                DialogData = new DialogData(),
                CurrentDialogAction = DialogAction.None
            });
        if (!throwsException)
        {
            cRepoMock.Setup(x => x.GetCustomer(It.IsAny<DialogData>()))
                .ReturnsAsync(new CustomerModel
                {
                    Id = customerId,
                    Person = new PersonModel
                    {
                        Initials = "Paul",
                        Surname = "Testman",
                        Gender = Gender.Male
                    },
                    CustomerType = CustomerType.Person,
                    Accounts = new List<CustomerAccountModel>
                    {
                        new CustomerAccountModel
                        {
                            CustomerId = customerId,
                            Address = new AddressModel {
                                PostalCode = "3025CB",
                                HouseNumber = 9,
                                HouseNumberSuffix = ""
                            },
                            Active = true,
                            Id = 2
                        }
                    }
                });
        }
        else
        {
            cRepoMock.Setup(x => x.GetCustomer(It.IsAny<DialogData>()))
                .Throws(new DataNotFoundException(Guid.NewGuid(), "error"));
        }

        cRepoMock.Setup(x => x.GetPersonalisationInfo(It.IsAny<DialogData>(), It.IsAny<CustomerModel>()))
            .ReturnsAsync(new PersonalisationInfo());
        nbaMock.Setup(x => x.GetNextBestActions(It.IsAny<DialogData>(), It.IsAny<bool>()))
            .ReturnsAsync(new NextBestAction());
        openIdConfigurationManager = new ConfigurationManager<OpenIdConnectConfiguration>(
            $"https://inloggen.acc.{label.ToString().ToLower()}.nl/oauth2/default/.well-known/oauth-authorization-server",
            new OpenIdConnectConfigurationRetriever(),
            new HttpDocumentRetriever());
        cmMock.Setup(c => c.GetConfigurationAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(new OpenIdConnectConfiguration { Issuer = $"https://inloggen.{label.ToString().ToLower()}.nl/oauth2/aus28y2phrdW58yIZ0i7" }));

        mC.Setup(m => m[$"Authorization:{label}:OktaIssuer"]).Returns($"https://inloggen.acc.{label.ToString().ToLower()}.nl/oauth2/default");
        mC.Setup(m => m[$"AesEncryption:Key"]).Returns("DmzBob4y2/NBYlsPuwv3yQ==");

        return new SessionManager(contextMock.Object,
            new Microsoft.ApplicationInsights.TelemetryClient(TelemetryConfiguration.CreateDefault()),
            mC.Object,
            loggerMock.Object,
            openIdConfigurationManager,
            cRepoMock.Object,
            nbaMock.Object);
    }
}