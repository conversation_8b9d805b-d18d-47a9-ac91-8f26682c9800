﻿using AutoMapper;
using DC.Bot.BusinessLogic.Configuration;
using DC.Domain.Exceptions;
using DC.Domain.Exceptions.ResponseModels;
using Microsoft.Rest;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net;
using Xunit;

namespace DC.Bot.Tests.Base;

public class BaseServiceUnitTest
{
    protected IMapper _mapper;
    private readonly MapperConfiguration _mappingConfig;

    public BaseServiceUnitTest()
    {
        _mappingConfig = DcAutoMapper.GetMapperConfiguration();
        _mapper = _mappingConfig.CreateMapper();
    }

    [Fact]
    public void ProfileIsValid()
    {
        _mappingConfig.AssertConfigurationIsValid();
    }

    protected static HttpOperationResponse<T> GetResponse<T>(T body, HttpStatusCode statuscode)
    {
        return new HttpOperationResponse<T>
        {
            Response = new System.Net.Http.HttpResponseMessage
            {
                StatusCode = statuscode
            },
            Body = body
        };
    }

    protected static HttpOperationResponse<object> GetResponse(object body, HttpStatusCode statuscode)
    {
        return new HttpOperationResponse<object>
        {
            Response = new System.Net.Http.HttpResponseMessage
            {
                StatusCode = statuscode
            },
            Body = body
        };
    }

    protected static HttpOperationException GetHttpOperationException(HttpStatusCode statuscode)
    {
        return new HttpOperationException
        {
            Response = new HttpResponseMessageWrapper(new System.Net.Http.HttpResponseMessage(statuscode), ""),
        };
    }

    protected static HttpOperationException GetHttpOperationExceptionWithDcException(HttpStatusCode statusCode)
    {
        var errorResponse = new ErrorResponse
        {
            Errors = new List<ErrorModel>
            {
                new ErrorModel
                {
                    Details = "Technical error in PAPI",
                    Code = "dcp|f73a2970-06f5-46d9-9e7d-6328c4d73cf2",
                    Type = $"{nameof(TechnicalException)}",
                    Reference = Guid.NewGuid()
                }
            }
        };
        return new HttpOperationException
        {
            Response = new HttpResponseMessageWrapper(new System.Net.Http.HttpResponseMessage(statusCode), JsonConvert.SerializeObject(errorResponse)),
        };
    }
}