﻿using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DC.Bot.Tests;

public static class DialogTestClientExtensions
{
    /// <summary>
    /// Get the next reply from the stack. If the reply is a prompt it will then put the message in the "Text" property.
    /// </summary>
    /// <param name="dialogTestClient"></param>
    public static IMessageActivity GetNextReply(this DialogTestClient dialogTestClient)
    {
        var reply = dialogTestClient.GetNextReply<IMessageActivity>();
        if (reply == null)
            return null;

        reply.Text = ParseReply(reply);
        return reply;
    }

    /// <summary>
    /// Iterates through all replies until it sees the "expectedReply" message. It then returns the next reply.
    /// The "expectedMessage" is partially matched against the actual reply with a string.Contains().
    /// Max iterations is set to 10.
    /// </summary>
    /// <param name="dialogTestClient"></param>
    /// <param name="expectedReply"></param>
    public static IMessageActivity GetNextReplyAfter(this DialogTestClient dialogTestClient, string expectedReply)
    {
        for (var i = 0; i < 10; i++)
        {
            var reply = dialogTestClient.GetNextReply<IMessageActivity>();
            if (reply == null)
                return null;

            reply.Text = ParseReply(reply);

            if (i >= 9 || reply.Text.Contains(expectedReply, StringComparison.OrdinalIgnoreCase))
            {
                break;
            }
        }

        var nextReply = dialogTestClient.GetNextReply<IMessageActivity>();
        nextReply.Text = ParseReply(nextReply);

        return nextReply;
    }

    /// <summary>
    /// Iterates through all replies until it sees the "expectedReply" message. It then returns the next reply.
    /// The "expectedMessage" is partially matched against the actual reply with a string.Contains().
    /// Max iterations is set to 10.
    /// </summary>
    /// <param name="dialogTestClient"></param>
    /// <param name="expectedReply"></param>
    public static bool ConversationContainsReply(this DialogTestClient dialogTestClient, string expectedReply)
    {
        var reply = dialogTestClient.GetNextReply<IMessageActivity>();
        while (reply != null)
        {
            reply.Text = ParseReply(reply);

            if (reply.Text.Contains(expectedReply, StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }

            reply = dialogTestClient.GetNextReply<IMessageActivity>();
        }
        return false;
    }

    /// <summary>
    /// Send a reply to the bot. This will return with a reply from the bot. If the reply is a prompt it will then put the message in the "Text" property.
    /// </summary>
    /// <param name="dialogTestClient"></param>
    /// <param name="message"></param>
    public static async Task<IMessageActivity> SendActivityAsync(this DialogTestClient dialogTestClient, string message)
    {
        var reply = await dialogTestClient.SendActivityAsync<IMessageActivity>(message).ConfigureAwait(true);

        if (reply == null)
            return null;

        reply.Text = ParseReply(reply);
        return reply;
    }

    /// <summary>
    /// Send a reply to the bot. This will return with a reply from the bot. If the reply is a prompt it will then put the message in the "Text" property.
    /// </summary>
    /// <param name="dialogTestClient"></param>
    /// <param name="message"></param>
    public static async Task<IMessageActivity> SendAttachmentActivityAsync(this DialogTestClient dialogTestClient, bool noContent = false)
    {
        var messageActivity = Activity.CreateMessageActivity();
        messageActivity.Attachments = noContent ? new List<Attachment>() : new List<Attachment>() { new Attachment() { Content = "Testcontent", ContentType = "docx", ContentUrl = "/", Name = "Test" } };
        messageActivity.Text = "";
        var reply = await dialogTestClient.SendActivityAsync<IMessageActivity>(messageActivity as Activity).ConfigureAwait(true);

        if (reply == null)
            return null;

        reply.Text = ParseReply(reply);
        return reply;
    }

    private static string ParseReply(IMessageActivity reply)
    {
        if (reply?.InputHint == "acceptingInput" && reply.Attachments != null && reply.Attachments.Any())
        {
            reply.Text = ((HeroCard)reply.Attachments.FirstOrDefault()?.Content)?.Text;
        }

        return reply?.Text;
    }
}