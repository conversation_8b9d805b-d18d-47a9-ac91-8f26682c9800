﻿using DC.Bot.BusinessLogic;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Base;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Domain.Exceptions;
using DC.Domain.Exceptions.ResponseModels;
using DC.Domain.Models.Products;
using DC.Domain.Models.Products.ProductRates;
using DC.Domain.Models.Products.ServiceAgreements;
using DC.Domain.Models.Products.ServiceOrders;
using DC.Domain.Models.Usages;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;
using ServiceOrderType = DC.Products.Client.Models.ServiceOrderType;

namespace DC.Bot.Tests.Services;

public class ProductsServiceTests : BaseServiceUnitTest
{
    private ProductsService _productsService;

    [Fact]
    public async Task GetProductRates_WorksExpected()
    {
        Setup("dcp|ab");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Channel = BotChannel.Web,
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _productsService.GetProductRates(dialogData, 1).ConfigureAwait(true);
        response.Should().NotBeNull();
    }

    [Fact]
    public async Task GetProductRates_Oxxio_WorksExpected()
    {
        Setup("dcp|ab");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Oxxio
            },
            Channel = BotChannel.Web,
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _productsService.GetProductRates(dialogData, 1).ConfigureAwait(true);
        response.Should().NotBeNull();
    }

    [Fact]
    public async Task GetProductRates_ThrowsException()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Channel = BotChannel.Web,
            Verification = new VerificationData
            {
                CustomerId = 2
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        await Assert.ThrowsAnyAsync<TechnicalException>(() => _productsService.GetProductRates(dialogData, 1))
            .ConfigureAwait(true);
    }

    [Fact]
    public async Task CancelProductContract_WorksExpected()
    {
        Setup("dcp|ab", customerId: 1);

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1,
                PostalCode = "3025CB",
                HouseNumber = 9
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            },
            DiscontinueContractData = new DiscontinueContractDialogData
            {
                AgreementId = 1
            }
        };

        var response = await _productsService
            .CancelProductContract(dialogData, ProductType.KetelComfort, ProductDiscontinueReason.AAK)
            .ConfigureAwait(true);
        response.Should().BeTrue();
    }

    [Fact]
    public async Task CancelProductContract_ThrowsException()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93", customerId: 3);

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 3,
                PostalCode = "3025CB",
                HouseNumber = 9
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            },
            DiscontinueContractData = new DiscontinueContractDialogData
            {
                AgreementId = 1
            }
        };

        await Assert.ThrowsAnyAsync<TechnicalException>(() =>
            _productsService.CancelProductContract(dialogData, ProductType.KetelComfort,
                ProductDiscontinueReason.AAK)).ConfigureAwait(true);
    }

    [Fact]
    public async Task CancelProductContract_WithoutDiscontinueContractData_ThrowsException()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 2
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        await Assert.ThrowsAnyAsync<TechnicalException>(() =>
            _productsService.CancelProductContract(dialogData, ProductType.KetelComfort,
                ProductDiscontinueReason.AAK)).ConfigureAwait(true);
    }

    [Fact]
    public async Task GetDiscontinueIntake_WorksExpected()
    {
        Setup("dcp|ab");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _productsService
            .GetDiscontinueIntake(dialogData, Domain.Models.Products.DiscontinueProductType.ServiceContract)
            .ConfigureAwait(true);
        response.Should().NotBeNull();
    }

    [Fact]
    public async Task GetDiscontinueIntake_ThrowsException()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 2
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        await Assert.ThrowsAnyAsync<TechnicalException>(() =>
            _productsService.GetDiscontinueIntake(dialogData,
                Domain.Models.Products.DiscontinueProductType.ServiceContract)).ConfigureAwait(true);
    }

    [Fact]
    public async Task GetDiscontinueIntake_ThrowsDataQualityException()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 0
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        await Assert.ThrowsAnyAsync<DataQualityException>(() =>
            _productsService.GetDiscontinueIntake(dialogData,
                Domain.Models.Products.DiscontinueProductType.ServiceContract)).ConfigureAwait(true);
    }

    [Fact]
    public async Task GetKetelComfortProductDetails_WorksExpected()
    {
        Setup("dcp|ab");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _productsService.GetKetelComfortProductDetails(dialogData).ConfigureAwait(true);
        response.Should().NotBeNull();
    }

    [Fact]
    public async Task GetKetelComfortProductDetails_ThrowsException()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 2
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        await Assert
            .ThrowsAnyAsync<TechnicalException>(() => _productsService.GetKetelComfortProductDetails(dialogData))
            .ConfigureAwait(true);
    }

    [Theory]
    [InlineData(1, 1)]
    [InlineData(2, 0)]
    [InlineData(3, 2)]
    public async Task GetServiceAgreementsForCustomerAccount_WorksExpected(int agreementId, int numberOfAgreements)
    {
        Setup("dcp|ab");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = agreementId
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _productsService.GetServiceAgreementsForAgreementId(dialogData, agreementId)
            .ConfigureAwait(true);
        response.Should().NotBeNull();
        response.Count.Should().Be(numberOfAgreements);
    }

    [Theory]
    [InlineData(1, "ASCON00333692", 1)]
    [InlineData(2, "SCON012345", 0)]
    public async Task GetServiceOrdersForAgreement(long customerId, string serviceAgreementId, int numberOfOrders)
    {
        Setup("dcp|ab");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = customerId
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _productsService
            .GetServiceOrdersForAgreement(dialogData, serviceAgreementId, ServiceOrderStatus.Open)
            .ConfigureAwait(true);
        response.Should().NotBeNull();
        response.Count.Should().Be(numberOfOrders);
    }

    [Fact]
    public async Task CreateServiceOrder_WorksExpected()
    {
        Setup("dcp|ab");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            },
            ServiceOrder = new ServiceOrderData
            {
                Type = ServiceOrderType.SolveMalfunction,
                ResourceNumber = "resourceNumber",
                MalfunctionCode = "MlfunctionCode",
                Description = "Description"
            }
        };

        var response = await _productsService.CreateServiceOrder(dialogData).ConfigureAwait(true);
        response.Should().NotBeNull();
    }

    [Fact]
    public async Task CreateServiceOrder_ThrowsException()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 2
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        await Assert.ThrowsAnyAsync<TechnicalException>(() => _productsService.CreateServiceOrder(dialogData))
            .ConfigureAwait(true);
    }

    [Fact]
    public async Task GetServiceOrderPlanLink_WorksExpected()
    {
        Setup("dcp|ab");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            },
            ServiceOrder = new ServiceOrderData
            {
                Type = ServiceOrderType.SolveMalfunction,
                ResourceNumber = "resourceNumber",
                MalfunctionCode = "MlfunctionCode",
                Description = "Description"
            }
        };

        var response = await _productsService
            .GetServiceOrderPlanLink(dialogData, "ASOR001619087", PlanLinkType.Agent)
            .ConfigureAwait(true);
        response.Should().NotBeNull();
    }

    [Fact]
    public async Task GetServiceOrderPlanLink_ThrowsException()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 2
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        await Assert.ThrowsAnyAsync<TechnicalException>(() =>
            _productsService.GetServiceOrderPlanLink(dialogData, "ASOR001619088",
                PlanLinkType.Agent)).ConfigureAwait(true);
    }

    [Fact]
    public async Task GetHardwareProductSpecifications_WorksExpected()
    {
        Setup("dcp|ab");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            },
            ServiceOrder = new ServiceOrderData
            {
                ResourceNumber = "ONBEKEND"
            }
        };

        var response = await _productsService.GetHardwareProductSpecifications(dialogData).ConfigureAwait(true);
        response.Should().NotBeNull();
    }

    [Fact]
    public async Task GetHardwareProductSpecifications_ThrowsException()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 2
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            },
            ServiceOrder = new ServiceOrderData
            {
                ResourceNumber = "abc"
            }
        };

        await Assert
            .ThrowsAnyAsync<TechnicalException>(() => _productsService.GetHardwareProductSpecifications(dialogData))
            .ConfigureAwait(true);
    }

    [Fact]
    public async Task GetCustomerProductsByAccount_WorksExpected()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93", GetProducts(), HttpStatusCode.OK);

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Channel = BotChannel.Web,
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _productsService.GetCustomerProductsByAccount(dialogData, 1).ConfigureAwait(true);

        response.Should().NotBeNull();
    }

    [Fact]
    public async Task Products_WorkAsExpected()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93", GetProducts(), HttpStatusCode.OK);
        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var result = await _productsService.GetCustomerProducts(dialogData).ConfigureAwait(true);
        result.Count.Should().Be(6);

        var product = result.First();
        product.Type.Name.Should().Be(ProductType.Electricity);
        product.Type.Category.Should().Be(ProductCategory.Energy);
        product.Description.Should().Be("Eneco HollandseWind & Zon");
        product.StartDate.Should().Be(DateTime.Today.AddYears(-1));
        product.EndDate.Should().BeNull();
        product.Id.Should().Be(1);
        product.AccountId.Should().Be(1);
        product.AgreementId.Should().Be(1);
        product.IsActive.Should().BeTrue();
        product.Duration.Should().Be(12);
    }

    [Fact]
    public void Products_ThrowsTechnicalException()
    {
        // Arrange
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93", null, HttpStatusCode.InternalServerError);

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        // Act & Assert
        FluentActions.Awaiting(() => _productsService.GetCustomerProducts(dialogData))
            .Should()
            .ThrowAsync<TechnicalException>();
    }

    [Fact]
    public async Task ProductsForAccount_WorkAsExpected()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93", GetProducts(), HttpStatusCode.OK);

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var result = await _productsService.GetCustomerProductsByAccount(dialogData, 1).ConfigureAwait(true);
        result.Count.Should().Be(6);

        var product = result.First();
        product.Type.Name.Should().Be(ProductType.Electricity);
        product.Type.Category.Should().Be(ProductCategory.Energy);
        product.Description.Should().Be("Eneco HollandseWind & Zon");
        product.StartDate.Should().Be(DateTime.Today.AddYears(-1));
        product.EndDate.Should().BeNull();
        product.Id.Should().Be(1);
        product.AccountId.Should().Be(1);
        product.AgreementId.Should().Be(1);
        product.IsActive.Should().BeTrue();
        product.Duration.Should().Be(12);
    }

    [Fact]
    public async Task ProductsForAccount_InActive_WorkAsExpected()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93", GetProducts(false), HttpStatusCode.OK);

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var result = await _productsService.GetCustomerProductsByAccount(dialogData, 1).ConfigureAwait(true);
        result.Count.Should().Be(0);
    }

    [Fact]
    public void ProductsForAccount_ThrowsTechnicalException()
    {
        // Arrange
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93", null, HttpStatusCode.InternalServerError);

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        // Act & Assert
        FluentActions.Awaiting(() => _productsService.GetCustomerProductsByAccount(dialogData, 1))
            .Should()
            .ThrowAsync<TechnicalException>();
    }

    [Fact]
    public void ProductsForAccount_IncludeProductRates_ThrowsTechnicalException()
    {
        // Arrange
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F94", null, HttpStatusCode.InternalServerError);

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        // Act & Assert
        FluentActions.Awaiting(() => _productsService.GetCustomerProductsByAccount(dialogData, 1))
            .Should()
            .ThrowAsync<TechnicalException>();
    }

    private void Setup(string errorCode, object response = null, HttpStatusCode statusCode = HttpStatusCode.OK,
        object productRates = null, long customerId = 1)
    {
        var pRepoMock = new Mock<IDcProductsRepository>();
        var lMock = new Mock<ILoggerFactory>();
        var cMock = new Mock<IConfiguration>();
        var cRepoMock = new Mock<IDcCustomersRepository>();

        _productsService = new ProductsService(lMock.Object, cMock.Object, cRepoMock.Object, pRepoMock.Object);

        cRepoMock.SetupCustomerTestData(customerId, 1);

        if (response == null)
        {
            if (customerId is 1 or 3)
                pRepoMock.Setup(x =>
                        x.GetProductRates(Label.Eneco, It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>()))
                    .ReturnsAsync(new HttpOperationResponse<object>
                    {
                        Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                        Body = new ResponseDataProductRates
                        {
                            Data = new ProductRates
                            {
                                Rates = new List<ProductRate>
                                {
                                    new ProductRate
                                    {
                                        Name = "Stukje zon",
                                        ProductType = ProductType.StukjeZon,
                                        ProductRateDetails = new List<ProductRateDetail>
                                        {
                                            new ProductRateDetail
                                            {
                                                Type = ProductRateDetailType.StukjeZon,
                                                Description = "Stukje zon",
                                                VATIncluded = decimal.Parse("-1,25"),
                                                ByPeriod = ByPeriod.M,
                                                IsVariable = false
                                            }
                                        }
                                    },
                                    new ProductRate
                                    {
                                        Name = "Toon",
                                        AgreementId = 1,
                                        ProductType = ProductType.ToonService,
                                        ProductRateDetails = new List<ProductRateDetail>
                                        {
                                            new ProductRateDetail
                                            {
                                                Type = ProductRateDetailType.ProjectDiscount,
                                                Description = "Toon dag",
                                                VATIncluded = decimal.Parse("1,25"),
                                                ByPeriod = ByPeriod.D,
                                                IsVariable = false
                                            },
                                            new ProductRateDetail
                                            {
                                                Type = ProductRateDetailType.ProjectDiscount,
                                                Description = "Toon uur",
                                                VATIncluded = decimal.Parse("1,25"),
                                                ByPeriod = ByPeriod.H,
                                                IsVariable = false
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    });

            pRepoMock.Setup(x => x.GetProductRates(Label.Oxxio, It.IsAny<BotChannel>(), 1, It.IsAny<int>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataProductRates
                    {
                        Data = new ProductRates
                        {
                            Rates = new List<ProductRate>
                            {
                                new ProductRate
                                {
                                    Name = "Gas",
                                    ProductType = ProductType.Gas,
                                    ProductRateDetails = new List<ProductRateDetail>
                                    {
                                        new ProductRateDetail
                                        {
                                            Type = ProductRateDetailType.Tariff,
                                            Description = "Normaaltarief",
                                            VATIncluded = decimal.Parse("0.92888"),
                                            ByPeriod = null,
                                            IsVariable = true,
                                            DenotationType = DenotationType.M3
                                        },
                                        new ProductRateDetail
                                        {
                                            Type = ProductRateDetailType.StandingCharge,
                                            Description = "Vaste leveringskosten",
                                            VATIncluded = decimal.Parse("92.99"),
                                            ByPeriod = ByPeriod.Y,
                                            IsVariable = false
                                        },
                                        new ProductRateDetail
                                        {
                                            Type = ProductRateDetailType.DeliveryCosts,
                                            Description = "Netbeheerkosten",
                                            VATIncluded = decimal.Parse("204.71"),
                                            ByPeriod = ByPeriod.Y,
                                            IsVariable = false
                                        }
                                    }
                                }
                            }
                        }
                    }
                });

            pRepoMock.Setup(x => x.GetProductRates(It.IsAny<Label>(), It.IsAny<BotChannel>(), 2, It.IsAny<int>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest },
                    Body = new ErrorResponse
                    {
                        Errors = new List<ErrorModel>
                        {
                            new ErrorModel
                            {
                                Code = errorCode,
                                Details = "MVS-23282",
                                Type = "TechnicalException"
                            }
                        }
                    }
                });

            pRepoMock.Setup(x => x.GetProductDiscontinueIntake(It.IsAny<Label>(), It.IsAny<BotChannel>(), 1,
                    It.IsAny<DiscontinueProductType>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataDiscontinueIntake
                    {
                        Data = new DiscontinueIntake
                        {
                            Allowed = true,
                            ProductName = "KetelComfort",
                            Accounts = new List<DiscontinueAddress>
                            {
                                new DiscontinueAddress
                                {
                                    AccountId = 1,
                                    AgreementId = 2,
                                    HouseNumber = 3,
                                    PostalCode = "1010AA"
                                }
                            },
                            Reasons = new List<Reason>
                            {
                                new Reason
                                {
                                    Text = "Beter aanbod",
                                    Code = "GDN",
                                    Explanation = true
                                },
                                new Reason
                                {
                                    Text = "Verhuizen",
                                    Code = "VER",
                                    Explanation = true
                                }
                            }
                        }
                    }
                });

            pRepoMock.Setup(x => x.GetProductDiscontinueIntake(It.IsAny<Label>(), It.IsAny<BotChannel>(), 2,
                    It.IsAny<DiscontinueProductType>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest },
                    Body = new ErrorResponse
                    {
                        Errors = new List<ErrorModel>
                        {
                            new ErrorModel
                            {
                                Code = errorCode,
                                Details = "MVS-23282",
                                Type = "TechnicalException"
                            }
                        }
                    }
                });

            if (customerId is 1)
                pRepoMock.Setup(x =>
                        x.PutProductOrderV2(It.IsAny<Label>(), It.IsAny<RequestDataProductOrderV2Model>()))
                    .ReturnsAsync(new HttpOperationResponse<object>
                    {
                        Response = new HttpResponseMessage { StatusCode = HttpStatusCode.Accepted }
                    });

            if (customerId is 2)
                pRepoMock.Setup(x =>
                        x.PutProductOrderV2(It.IsAny<Label>(), It.IsAny<RequestDataProductOrderV2Model>()))
                    .ReturnsAsync(new HttpOperationResponse<object>
                    {
                        Response = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest }
                    });

            if (customerId is 3)
                pRepoMock.Setup(x =>
                        x.PutProductOrderV2(It.IsAny<Label>(), It.IsAny<RequestDataProductOrderV2Model>()))
                    .ReturnsAsync(new HttpOperationResponse<object>
                    {
                        Response = new HttpResponseMessage { StatusCode = HttpStatusCode.InternalServerError },
                        Body = new ErrorResponse
                        {
                            Errors = new List<ErrorModel>
                            {
                                new ErrorModel
                                {
                                    Code = errorCode,
                                    Details = "MVS-23282",
                                    Type = "TechnicalException"
                                }
                            }
                        }
                    });

            pRepoMock.Setup(x =>
                    x.GetKetelComfortProductDetails(It.IsAny<Label>(), It.IsAny<BotChannel>(), 1, It.IsAny<int>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataKetelComfortProductDetails
                    {
                        Data = new KetelComfortProductDetails
                        {
                            Service = new ServiceProductService
                            {
                                PreviousAppointments = new List<MaintenanceAppointmentBase>
                                {
                                    new MaintenanceAppointmentBase
                                    {
                                        AppointmentDate = new DateTime(2021, 12, 31)
                                    }
                                }
                            }
                        }
                    }
                });

            pRepoMock.Setup(x =>
                    x.GetKetelComfortProductDetails(It.IsAny<Label>(), It.IsAny<BotChannel>(), 2, It.IsAny<int>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest },
                    Body = new ErrorResponse
                    {
                        Errors = new List<ErrorModel>
                        {
                            new ErrorModel
                            {
                                Code = errorCode,
                                Details = "MVS-23282",
                                Type = "TechnicalException"
                            }
                        }
                    }
                });

            if (errorCode == "dcp|ab")
                pRepoMock.Setup(x =>
                        x.GetHardwareProductSpecifications(It.IsAny<Label>(), It.IsAny<BotChannel>(), null))
                    .ReturnsAsync(new HttpOperationResponse<object>
                    {
                        Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                        Body = new ResponseDataIListHardwareProductSpecification
                        {
                            Data = new List<HardwareProductSpecification>
                            {
                                new HardwareProductSpecification
                                {
                                    Brand = new HardwareCodeDescription
                                    {
                                        Code = "ONBEKEND",
                                        Description = "Onbekend"
                                    },
                                    Group = new HardwareGroupDescription
                                    {
                                        Code = null,
                                        Description = GroupDescription.OnbekendOverig
                                    },
                                    Model = new HardwareCodeDescription
                                    {
                                        Code = "ONBEKEND",
                                        Description = "Onbekend"
                                    },
                                    Id = "STOB999999"
                                }
                            }
                        }
                    });

            if (errorCode != "dcp|ab")
                pRepoMock.Setup(x =>
                        x.GetHardwareProductSpecifications(It.IsAny<Label>(), It.IsAny<BotChannel>(), null))
                    .ReturnsAsync(new HttpOperationResponse<object>
                    {
                        Response = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest },
                        Body = new ErrorResponse
                        {
                            Errors = new List<ErrorModel>
                            {
                                new ErrorModel
                                {
                                    Code = errorCode,
                                    Details = "MVS-23282",
                                    Type = "TechnicalException"
                                }
                            }
                        }
                    });

            pRepoMock.Setup(x => x.CreateServiceOrder(It.IsAny<Label>(), It.IsAny<BotChannel>(), 1, It.IsAny<int>(),
                    It.IsAny<RequestDataServiceOrderRequestModel>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataString
                    {
                        Data = "planlink"
                    }
                });

            pRepoMock.Setup(x => x.CreateServiceOrder(It.IsAny<Label>(), It.IsAny<BotChannel>(), 2, It.IsAny<int>(),
                    It.IsAny<RequestDataServiceOrderRequestModel>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest },
                    Body = new ErrorResponse
                    {
                        Errors = new List<ErrorModel>
                        {
                            new ErrorModel
                            {
                                Code = errorCode,
                                Details = "MVS-23282",
                                Type = "TechnicalException"
                            }
                        }
                    }
                });

            pRepoMock.Setup(x => x.GetServiceOrderPlanLink(It.IsAny<Label>(), It.IsAny<BotChannel>(),
                    "ASOR001619087", It.IsAny<PlanLinkType?>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataString
                    {
                        Data = "planlink"
                    }
                });

            pRepoMock.Setup(x => x.GetServiceOrderPlanLink(It.IsAny<Label>(), It.IsAny<BotChannel>(),
                    "ASOR001619088", It.IsAny<PlanLinkType?>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest },
                    Body = new ErrorResponse
                    {
                        Errors = new List<ErrorModel>
                        {
                            new ErrorModel
                            {
                                Code = errorCode,
                                Details = "MVS-23282",
                                Type = "TechnicalException"
                            }
                        }
                    }
                });

            pRepoMock.Setup(x => x.GetServiceAgreementsForAgreementId(It.IsAny<Label>(), It.IsAny<BotChannel>(),
                    1, It.IsAny<bool?>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataListServiceAgreement
                    {
                        Data = new List<ServiceAgreement>
                        {
                            new ServiceAgreement
                            {
                                Number = "SCON012345",
                                Items = new List<ServiceAgreementItem>
                                {
                                    new ServiceAgreementItem
                                    {
                                        Customer = new ServiceAgreementCustomer
                                        {
                                            CustomerId = 1,
                                            AccountId = 1,
                                            AgreementId = 1
                                        },
                                        IsSelfServiceAllowed = true
                                    }
                                }
                            }
                        }
                    }
                });

            pRepoMock.Setup(x => x.GetServiceAgreementsForAgreementId(It.IsAny<Label>(), It.IsAny<BotChannel>(),
                    2, It.IsAny<bool?>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataListServiceAgreement
                    {
                        Data = new List<ServiceAgreement>()
                    }
                });

            pRepoMock.Setup(x => x.GetServiceAgreementsForAgreementId(It.IsAny<Label>(), It.IsAny<BotChannel>(),
                    3, It.IsAny<bool?>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataListServiceAgreement
                    {
                        Data = new List<ServiceAgreement>
                        {
                            new ServiceAgreement
                            {
                                Number = "SCON012345",
                                Items = new List<ServiceAgreementItem>
                                {
                                    new ServiceAgreementItem
                                    {
                                        Customer = new ServiceAgreementCustomer
                                        {
                                            CustomerId = 3,
                                            AccountId = 1,
                                            AgreementId = 1
                                        },
                                        IsSelfServiceAllowed = true
                                    }
                                }
                            },
                            new ServiceAgreement
                            {
                                Number = "SCON012346",
                                Items = new List<ServiceAgreementItem>
                                {
                                    new ServiceAgreementItem
                                    {
                                        Customer = new ServiceAgreementCustomer
                                        {
                                            CustomerId = 3,
                                            AccountId = 1,
                                            AgreementId = 2
                                        },
                                        IsSelfServiceAllowed = true
                                    }
                                }
                            }
                        }
                    }
                });

            pRepoMock.Setup(x => x.GetServiceOrdersFromServiceAgreement(It.IsAny<Label>(), It.IsAny<BotChannel>(),
                    It.Is<string>(x => x == "ASCON00333692"), It.IsAny<ServiceOrderStatus?>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataListServiceOrder
                    {
                        Data = new List<ServiceOrder>
                        {
                            new ServiceOrder
                            {
                                OrderDate = DateTime.Today.AddDays(-1),
                                Description = "Entreebeurt BPM",
                                Status = ServiceOrderStatus.Open,
                                SubStatusCode = "90",
                                SubStatusDescription = "administratief gereed",
                                Type = ServiceOrderKind.FirstMaintenance,
                                Items = new List<ServiceOrderItem>
                                {
                                    new ServiceOrderItem
                                    {
                                        Contact = new ServiceOrderItemContact
                                        {
                                            Name = "John",
                                            EmailAddress = "<EMAIL>",
                                            MobilePhoneNumber = "0612345678",
                                            PhoneNumber = "1234567890"
                                        },
                                        Appointment = new ServiceOrderItemAppointment
                                        {
                                            WindowStartDateTime = DateTime.Today,
                                            StartDateTime = DateTime.Today.AddDays(1),
                                            EndDateTime = DateTime.Today.AddDays(2),
                                            WindowEndDateTime = DateTime.Today.AddDays(3)
                                        },
                                        Service = new ServiceOrderItemService
                                        {
                                            Name = "Entreebeurt",
                                            Resource = new ServiceResource
                                            {
                                                BrandCode = "b1",
                                                BrandName = "b2",
                                                CategoryName = "c1",
                                                CategoryNumber = "c2",
                                                ModelCode = "m1",
                                                ModelName = "Model",
                                                Name = "name",
                                                Number = "123",
                                                SerialNumber = "321",
                                                YearOfManufacture = 2008
                                            }
                                        },
                                        PlanActions = new List<ServiceOrderPlanAction>
                                        {
                                            new ServiceOrderPlanAction
                                            {
                                                Channel = "channel",
                                                Description = "descript",
                                                ExecutedOnDate = DateTime.Today.AddDays(1),
                                                Id = "1",
                                                Initiator = ServiceOrderPlanActionInitiator.Agent,
                                                Type = ServiceOrderPlanActionType.Invite,
                                                PlanLink = new ServiceOrderPlanActionLink
                                                {
                                                    Url = "http://google.com",
                                                    ValidUntilDate = new DateTime(2021, 12, 1)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                });

            pRepoMock.Setup(x => x.GetServiceOrdersFromServiceAgreement(It.IsAny<Label>(), It.IsAny<BotChannel>(),
                    It.Is<string>(x => x == "SCON012345"), It.IsAny<ServiceOrderStatus?>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataListServiceOrder
                    {
                        Data = new List<ServiceOrder>()
                    }
                });
        }

        if (statusCode == HttpStatusCode.OK && response != null)
        {
            pRepoMock.Setup(i =>
                    i.GetCustomerProducts(Label.Eneco, It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
                .ReturnsAsync(GetResponse(response, statusCode));

            pRepoMock.Setup(i => i.GetCustomerAccountProducts(Label.Eneco, It.IsAny<BotChannel>(), It.IsAny<long>(),
                    It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
                .ReturnsAsync(GetResponse(response, statusCode));

            pRepoMock.Setup(i =>
                    i.GetProductRates(Label.Eneco, It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>()))
                .ReturnsAsync(GetResponse(productRates, statusCode));
        }
        else if (statusCode != HttpStatusCode.OK)
        {
            pRepoMock.Setup(i =>
                    i.GetCustomerProducts(Label.Eneco, It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
                .ThrowsAsync(GetHttpOperationExceptionWithDcException(statusCode));

            pRepoMock.Setup(i => i.GetCustomerAccountProducts(Label.Eneco, It.IsAny<BotChannel>(), It.IsAny<long>(),
                    It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
                .ThrowsAsync(GetHttpOperationExceptionWithDcException(statusCode));

            pRepoMock.Setup(i =>
                    i.GetProductRates(Label.Eneco, It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>()))
                .ThrowsAsync(GetHttpOperationExceptionWithDcException(statusCode));
        }
    }

    private static ResponseDataListProductModel GetProducts(bool active = true, bool includeProductRates = false)
    {
        if (!active)
            return new ResponseDataListProductModel { Data = new List<ProductModel> { } };

        return new ResponseDataListProductModel
        {
            Data = new List<ProductModel>
            {
                GetProduct(ProductType.Electricity, "Eneco HollandseWind & Zon", active, includeProductRates: includeProductRates),
                GetProduct(ProductType.Gas, "Eneco AardGas", active, includeProductRates: includeProductRates),
                GetProduct(ProductType.Admin, "Administratiekosten"),
                GetProduct(ProductType.ToonService, "Toon® van Eneco", active, includeProductRates: includeProductRates),
                GetProduct(ProductType.Toon, "Eenmalige kosten Toon", includeProductRates: includeProductRates),
                GetProduct(ProductType.StukjeZon, "Eneco StukjeZon gekocht", active, includeProductRates: includeProductRates)
            }
        };
    }

    private static ProductModel GetProduct(ProductType type, string description, bool isActive = true,
        DateTime? endDate = null, bool includeProductRates = false)
    {
        return new ProductModel
        {
            Id = 1,
            AccountId = 1,
            AgreementId = 1,
            Type = new ProductTypeModel
            {
                Name = type,
                Category = GetProductCategory(type)
            },
            Description = description,
            StartDate = DateTime.Today.AddYears(-1),
            EndDate = endDate,
            IsActive = isActive,
            Indefinite = false,
            Duration = type == ProductType.Electricity || type == ProductType.Gas ? 12 : 0,
            DeliveryId = 1,
            LocationId = 1,
            ProductRate = includeProductRates ?
            new BaseProductRate
            {
                ProductType = ProductType.Gas,
                ProductRateDetails = new List<ProductRateDetail>
                {
                    new ProductRateDetail
                    {
                        Type = ProductRateDetailType.Tariff,
                        Description = "Normaaltarief",
                        VATIncluded = decimal.Parse("0.92888"),
                        ByPeriod = null,
                        IsVariable = true,
                        DenotationType = DenotationType.M3
                    },
                    new ProductRateDetail
                    {
                        Type = ProductRateDetailType.StandingCharge,
                        Description = "Vaste leveringskosten",
                        VATIncluded = decimal.Parse("92.99"),
                        ByPeriod = ByPeriod.Y,
                        IsVariable = false
                    },
                    new ProductRateDetail
                    {
                        Type = ProductRateDetailType.DeliveryCosts,
                        Description = "Netbeheerkosten",
                        VATIncluded = decimal.Parse("204.71"),
                        ByPeriod = ByPeriod.Y,
                        IsVariable = false
                    }
                }
            } : null
        };
    }

    private static ProductCategory GetProductCategory(ProductType type)
    {
        return type switch
        {
            ProductType.Electricity => ProductCategory.Energy,
            ProductType.Gas => ProductCategory.Energy,
            ProductType.StukjeZon => ProductCategory.Energy,
            ProductType.Toon => ProductCategory.Hardware,
            ProductType.ToonService => ProductCategory.Service,
            ProductType.Admin => ProductCategory.Various,
            ProductType.Eenmalig => ProductCategory.Various,
            ProductType.Huurapparaat => ProductCategory.Rent,
            _ => ProductCategory.Various
        };
    }
}