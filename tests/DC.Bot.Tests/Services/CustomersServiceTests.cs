﻿using DC.Bot.BusinessLogic;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Base;
using DC.Customers.Client.Models;
using DC.Domain.Models.Customers;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using Moq;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Services;

public class CustomersServiceTests : BaseServiceUnitTest
{
    private CustomersService _customersService;

    [Fact]
    public async Task GetCustomer_WorksExpected()
    {
        Setup();

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _customersService.GetCustomer(dialogData).ConfigureAwait(true);

        response.Should().NotBeNull();
        response.Id.Should().Be(1);
        response.CustomerType.Should().Be(CustomerType.Person);
    }

    [Fact]
    public async Task GetCustomerV2_WorksExpected()
    {
        Setup();

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            },
            Channel = new BotChannel { }
        };

        var response = await _customersService.GetCustomerV2(Label.Eneco, dialogData.Channel, 1).ConfigureAwait(true);

        response.Should().NotBeNull();
        response.Id.Should().Be(1);
        response.CustomerType.Should().Be(CustomerType.Person);
    }

    [Theory]
    [InlineData(1, true)]
    [InlineData(9, false)]
    public async Task VerifyCustomer_WorksExpected(long customerId, bool IsVerified)
    {
        Setup();

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = customerId
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            },
            Channel = new BotChannel { }
        };

        var response = await _customersService.VerifyCustomer(dialogData).ConfigureAwait(true);

        response.Should().NotBeNull();
        response.Verification.IsVerified.Should().Be(IsVerified);
    }

    [Fact]
    public void EnrichDialogData_WorksExpected()
    {
        Setup();

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1,
                CustomerIdVerified = true
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var customer = new CustomerModel()
        {
            Id = 1,
            Person = new PersonModel(),
            CustomerType = CustomerType.Person
        };

        var nba = new NextBestAction
        {
            CustomerId = 1,
            ContextId = "CTX1",
            Actions = new List<ActionModel>()
        };

        dialogData.Verification.IsVerifiedWithToken = true;

        _customersService.EnrichDialogData(dialogData, customer, nba);
    }

    private void Setup()
    {
        var cRepoMock = new Mock<IDcCustomersRepository>();
        var pServiceMock = new Mock<IProductsService>();
        var lMock = new Mock<ILoggerFactory>();
        var cMock = new Mock<IConfiguration>();

        _customersService = new CustomersService(lMock.Object, cMock.Object, cRepoMock.Object, pServiceMock.Object, new Mock<IDcUsagesRepository>().Object);

        cRepoMock.Setup(x => x.GetCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 1)))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataCustomerModel
                {
                    Data = new CustomerModel()
                    {
                        Id = 1,
                        CustomerType = CustomerType.Person
                    }
                }
            });

        cRepoMock.Setup(x => x.GetCustomerV2(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 1)))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataCustomer
                {
                    Data = new Customer()
                    {
                        Id = 1,
                        CustomerType = CustomerType.Person
                    }
                }
            });

        pServiceMock.Setup(x => x.GetCustomerProductsByAccount(It.Is<DialogData>(x => x.Verification.CustomerId == 1), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>()))
            .ReturnsAsync(new List<Domain.Models.Products.ProductModel>());

        cRepoMock.Setup(x => x.VerifyCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<RequestDataCustomerVerification>(x => x.Data.CustomerId == 1)))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataCustomerVerificationResponse
                {
                    Data = new CustomerVerificationResponse { }
                }
            });

        cRepoMock.Setup(x => x.VerifyCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<RequestDataCustomerVerification>(x => x.Data.CustomerId == 9)))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.NotFound },
                Body = new ResponseDataCustomerVerificationResponse
                {
                    Data = new CustomerVerificationResponse { }
                }
            });
    }
}