﻿using DC.Bot.BusinessLogic;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Base;
using DC.Domain.Exceptions;
using DC.Domain.Exceptions.ResponseModels;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.General;
using DC.Domain.Models.Products;
using DC.Domain.Models.Usages;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using DC.Usages.Client.Models;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Services;

public class UsagesServiceTests : BaseServiceUnitTest
{
    private UsagesService _usagesService;

    [Fact]
    public async Task GetPastHalfYearUsages_WorksExpected()
    {
        Setup("dcp|ab");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _usagesService.GetPastHalfYearUsages(dialogData, 10).ConfigureAwait(true);
        response.Should().NotBeNull();
    }

    [Fact]
    public async Task GetPastHalfYearUsages_ThrowsException()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 208
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        await Assert.ThrowsAnyAsync<TechnicalException>(() => _usagesService.GetPastHalfYearUsages(dialogData, 10)).ConfigureAwait(true);
    }

    [Fact]
    public async Task GetPastHalfYearUsages_Returns_Null()
    {
        Setup("dcp|ab");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 400
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _usagesService.GetPastHalfYearUsages(dialogData, 10).ConfigureAwait(true);
        response.Should().BeNull();
    }

    [Fact]
    public async Task RegisterReportRequest_WorksExpected()
    {
        Setup("dcp|ab");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco,
                EmailAddress = "<EMAIL>"
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _usagesService.RegisterReportRequest(dialogData, 1).ConfigureAwait(true);
        response.Should().NotBeNull();
        response.EmailSent.Should().BeTrue();
    }

    [Theory]
    [InlineData(2, ReadingReportRequestErrorCode.CustomerNotFound)]
    [InlineData(3, ReadingReportRequestErrorCode.AlreadyRequested)]
    [InlineData(4, ReadingReportRequestErrorCode.NoActiveSmartMeterAgreement)]
    [InlineData(5, ReadingReportRequestErrorCode.EmailRequired)]
    [InlineData(7, ReadingReportRequestErrorCode.FailedSendEmail)]
    public async Task RegisterReportRequest_ReturnsError(long customerId, ReadingReportRequestErrorCode errorCode)
    {
        Setup("dcp|ab");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco,
                EmailAddress = "<EMAIL>"
            },
            Verification = new VerificationData
            {
                CustomerId = customerId
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _usagesService.RegisterReportRequest(dialogData, 1).ConfigureAwait(true);
        response.Should().NotBeNull();
        response.EmailSent.Should().BeFalse();
        response.ErrorCode.Should().NotBeNull();
        response.ErrorCode.Should().Be(errorCode);
    }

    [Theory]
    [InlineData(1, true, true)]
    [InlineData(2, true, false)]
    [InlineData(3, false, false)]
    public async Task CanRequestRegisterReportRequest_WorksExpected(long customerId, bool hasEGResult, bool hasSmartMeterResult)
    {
        Setup("dcp|ab");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco,
                EmailAddress = "<EMAIL>"
            },
            Verification = new VerificationData
            {
                CustomerId = customerId
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _usagesService.CanRequestRegisterReportRequest(dialogData).ConfigureAwait(true);
        response.Should().NotBeNull();
        response.HasEG.Should().Be(hasEGResult);
        response.HasSmartMeter.Should().Be(hasSmartMeterResult);
    }

    [Fact]
    public async Task RegisterReportRequest_ThrowsException()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 208
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        await Assert.ThrowsAnyAsync<TechnicalException>(() => _usagesService.RegisterReportRequest(dialogData, 1)).ConfigureAwait(true);
    }

    [Fact]
    public async Task GetReadingForCustomer_ThrowsException()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 208
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        await Assert.ThrowsAnyAsync<TechnicalException>(() => _usagesService.GetReadingForCustomer(dialogData)).ConfigureAwait(true);
    }

    [Fact]
    public async Task GetReadingsHistoryForCustomer_ThrowsException()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 208
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        await Assert.ThrowsAnyAsync<TechnicalException>(() => _usagesService.GetReadingsHistoryForCustomer(dialogData)).ConfigureAwait(true);
    }

    [Fact]
    public async Task GetOutstandingReadings_ThrowsException()
    {
        Setup("dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 208
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        await Assert.ThrowsAnyAsync<TechnicalException>(() => _usagesService.GetOutstandingReadings(dialogData)).ConfigureAwait(true);
    }

    private void Setup(string errorCode)
    {
        var uRepoMock = new Mock<IDcUsagesRepository>();
        var lMock = new Mock<ILoggerFactory>();
        var cMock = new Mock<IConfiguration>();
        var customerMock = new Mock<ICustomersService>();

        _usagesService = new UsagesService(lMock.Object, cMock.Object, uRepoMock.Object, customerMock.Object);

        uRepoMock.Setup(x => x.GetUsages(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 1), It.IsAny<int>(),
                It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<UsageAggregation>(), It.IsAny<UsageInterval>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataUsagesModel
                {
                    Data = new UsagesModel()
                    {
                        Metadata = new UsagesMetadata
                        {
                            Interval = UsageInterval.Day,
                            Aggregation = UsageAggregation.Month
                        },
                        Usages = new List<UsagesAggregation> {
                            new UsagesAggregation
                            {
                                Period = new AggregationPeriod
                                {
                                    FromProperty = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1),
                                    To = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(1)
                                },
                                Entries = new List<UsagesEntry>
                                {
                                    new UsagesEntry
                                    {

                                        Actual = new IntervalUsage
                                        {
                                            Date = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1),
                                            TotalCostInclVat = 0,
                                            Electricity = new UsageItem
                                            {
                                                High = 0,
                                                Low = 0,
                                                HighCostInclVat = 0,
                                                LowCostInclVat = 0,
                                                TotalCostInclVat = 0,
                                                FixedCostInclVat = 0,
                                                CollectorType = CollectorType.NotMeasured,
                                                IsDoubleMeter = false,
                                                IsDoubleTariff = false,
                                                Status = UsageStatus.NotMeasured
                                            },
                                            Gas = new UsageItem
                                            {
                                                High = 0,
                                                Low = 0,
                                                HighCostInclVat = 0,
                                                LowCostInclVat = 0,
                                                TotalCostInclVat = 0,
                                                FixedCostInclVat = 0,
                                                CollectorType = CollectorType.NotMeasured,
                                                IsDoubleMeter = false,
                                                IsDoubleTariff = false,
                                                Status = UsageStatus.NotMeasured
                                            }
                                        },
                                        PreviousYear = new IntervalUsage
                                        {
                                            Date = new DateTime(DateTime.Now.Year - 1, DateTime.Now.Month, 1),
                                            TotalCostInclVat = 0,
                                            Electricity = new UsageItem
                                            {
                                                High = 0,
                                                Low = 0,
                                                HighCostInclVat = 0,
                                                LowCostInclVat = 0,
                                                TotalCostInclVat = 0,
                                                FixedCostInclVat = 0,
                                                CollectorType = CollectorType.NotMeasured,
                                                IsDoubleMeter = false,
                                                IsDoubleTariff = false,
                                                Status = UsageStatus.NotMeasured
                                            },
                                            Gas = new UsageItem
                                            {
                                                High = 0,
                                                Low = 0,
                                                HighCostInclVat = 0,
                                                LowCostInclVat = 0,
                                                TotalCostInclVat = 0,
                                                FixedCostInclVat = 0,
                                                CollectorType = CollectorType.NotMeasured,
                                                IsDoubleMeter = false,
                                                IsDoubleTariff = false,
                                                Status = UsageStatus.NotMeasured
                                            }
                                        },
                                        Budget = new IntervalBudget
                                        {
                                            Electricity = 10,
                                            Gas = 20,
                                            TapWater = 0,
                                            Total = 30
                                        },
                                        Weather = new WeatherData {
                                            Date = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1),
                                            Location = new Location {
                                                Lat = 52.367565,
                                                Lon = 6.6787243,
                                                StationId = "290"
                                            },
                                            Temp =  14.6,
                                            WindSpeed = 2.6,
                                            Sunshine = 0,
                                            Radiation = 541
                                        }
                                    }
                                },
                                Summary = new AggregationSummary
                                {
                                    AggregationTotals = new AggregationTotal
                                    {
                                        Electricity = new UsagesTotal
                                        {
                                            High = 10,
                                            Low = 20,
                                            HighCostInclVat = 10,
                                            LowCostInclVat = 20,
                                            HighPreviousYear = 20,
                                            LowPreviousYear = 30,
                                            HighCostInclVatPreviousYear = 40,
                                            LowCostInclVatPreviousYear = 50,
                                            FixedCostInclVat = 5,
                                            IsMeterActivelyInError = false,
                                            TotalBudget = 60,
                                            BudgetUsage = 22,
                                            TotalCostInclVat = 30,
                                            TotalCostInclVatPreviousYear = 50
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            });

        uRepoMock.Setup(x => x.GetUsages(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 400), It.IsAny<int>(),
                It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<UsageAggregation>(), It.IsAny<UsageInterval>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest }
            });

        uRepoMock.Setup(x => x.GetUsages(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 208), It.IsAny<int>(),
                It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<UsageAggregation>(), It.IsAny<UsageInterval>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.NotFound },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel {
                            Code = errorCode,
                            Details = "details",
                            Type = "TechnicalException"
                        }
                    }
                }
            });

        customerMock.Setup(x => x.GetAgreements(It.Is<DialogData>(x => x.Verification.CustomerId == 1), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new Agreement()
                {
                    Connection = new Connection()
                    {
                        Meter = new MeteringDevice()
                        {
                            IsSmart = true,
                            IsSmartMeterReadingAllowed = true,
                            HasRedeliveryInMeter = true,
                            IsDoubleMeter = true,
                            IsDoubleMeterForRedelivery = true,
                            NextMeterReadingDate = new DateTime(2020,1,1),
                            Number = "123456",
                            Registers = new List<MeterRegister>()
                            {
                                new MeterRegister()
                                {
                                    EnergyFlowDirection = EnergyFlowDirection.Delivery,
                                    MeterReading = new LatestMeterRegisterReading()
                                    {
                                        Date = new DateTime(2020, 1, 1),
                                        Value = 123
                                    }
                                }
                            }
                        },
                        Address = new Address()
                        {
                            AddressSuffix = "AddressSuffix",
                            City = "City",
                            CountryCode = "CountryCode",
                            HouseNumber = 2,
                            HouseNumberSuffix = "HouseNumberSuffix",
                            PostalCode = "PostalCode",
                            Street = "Street",
                            Type = AddressType.Delivery
                        },
                        ConnectionPointEan = "**********"
                    },
                    AccountId = 1,
                    CustomerId = 1,
                    DeliveryAddress = new Address()
                    {
                        AddressSuffix = "AddressSuffix",
                        City = "City",
                        CountryCode = "CountryCode",
                        HouseNumber = 2,
                        HouseNumberSuffix = "HouseNumberSuffix",
                        PostalCode = "PostalCode",
                        Street = "Street",
                        Type = AddressType.Delivery
                    },
                    EndDate = new DateTime(2020,1,2),
                    Id = 123,
                    IsActive = true,
                    IntendedEndDate = new DateTime(2020,1,3),
                    IntendedStartDate = new DateTime(2020,1,4),
                    OwningBusinessPartyName = "",
                    CanRequestMeterReadingHistoryReport = true,
                    Products = new List<Product>()
                    {
                        new Product()
                        {
                            IsActive = true,
                            EndDate = new DateTime(2020, 1,7),
                            AccountId = 1,
                            AgreementId = 123456,
                            PriceDeterminationDate = new DateTime(2020,1,7),
                            ProductType = ProductType.Electricity

                        }
                    }
                }
            });

        customerMock.Setup(x => x.GetAgreements(It.Is<DialogData>(x => x.Verification.CustomerId == 2), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new Agreement()
                {
                    Connection = new Connection()
                    {
                        Meter = new MeteringDevice()
                        {
                            IsSmart = true,
                            IsSmartMeterReadingAllowed = true,
                            HasRedeliveryInMeter = true,
                            IsDoubleMeter = true,
                            IsDoubleMeterForRedelivery = true,
                            NextMeterReadingDate = new DateTime(2020,1,1),
                            Number = "123456",
                            Registers = new List<MeterRegister>()
                            {
                                new MeterRegister()
                                {
                                    EnergyFlowDirection = EnergyFlowDirection.Delivery,
                                    MeterReading = new LatestMeterRegisterReading()
                                    {
                                        Date = new DateTime(2020, 1, 1),
                                        Value = 123
                                    }
                                }
                            }
                        },
                        Address = new Address()
                        {
                            AddressSuffix = "AddressSuffix",
                            City = "City",
                            CountryCode = "CountryCode",
                            HouseNumber = 2,
                            HouseNumberSuffix = "HouseNumberSuffix",
                            PostalCode = "PostalCode",
                            Street = "Street",
                            Type = AddressType.Delivery
                        },
                        ConnectionPointEan = "**********"
                    },
                    AccountId = 1,
                    CustomerId = 1,
                    DeliveryAddress = new Address()
                    {
                        AddressSuffix = "AddressSuffix",
                        City = "City",
                        CountryCode = "CountryCode",
                        HouseNumber = 2,
                        HouseNumberSuffix = "HouseNumberSuffix",
                        PostalCode = "PostalCode",
                        Street = "Street",
                        Type = AddressType.Delivery
                    },
                    EndDate = new DateTime(2020,1,2),
                    Id = 123,
                    IsActive = true,
                    IntendedEndDate = new DateTime(2020,1,3),
                    IntendedStartDate = new DateTime(2020,1,4),
                    OwningBusinessPartyName = "",
                    CanRequestMeterReadingHistoryReport = false,
                    Products = new List<Product>()
                    {
                        new Product()
                        {
                            IsActive = true,
                            EndDate = new DateTime(2020, 1,7),
                            AccountId = 1,
                            AgreementId = 123456,
                            PriceDeterminationDate = new DateTime(2020,1,7),
                            ProductType = ProductType.Electricity

                        }
                    }
                }
            });

        customerMock.Setup(x => x.GetAgreements(It.Is<DialogData>(x => x.Verification.CustomerId == 3), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new Agreement()
                {
                    Connection = new Connection()
                    {
                        Meter = new MeteringDevice()
                        {
                            IsSmart = true,
                            IsSmartMeterReadingAllowed = true,
                            HasRedeliveryInMeter = true,
                            IsDoubleMeter = true,
                            IsDoubleMeterForRedelivery = true,
                            NextMeterReadingDate = new DateTime(2020,1,1),
                            Number = "123456",
                            Registers = new List<MeterRegister>()
                            {
                                new MeterRegister()
                                {
                                    EnergyFlowDirection = EnergyFlowDirection.Delivery,
                                    MeterReading = new LatestMeterRegisterReading()
                                    {
                                        Date = new DateTime(2020, 1, 1),
                                        Value = 123
                                    }
                                }
                            }
                        },
                        Address = new Address()
                        {
                            AddressSuffix = "AddressSuffix",
                            City = "City",
                            CountryCode = "CountryCode",
                            HouseNumber = 2,
                            HouseNumberSuffix = "HouseNumberSuffix",
                            PostalCode = "PostalCode",
                            Street = "Street",
                            Type = AddressType.Delivery
                        },
                        ConnectionPointEan = "**********"
                    },
                    AccountId = 1,
                    CustomerId = 1,
                    DeliveryAddress = new Address()
                    {
                        AddressSuffix = "AddressSuffix",
                        City = "City",
                        CountryCode = "CountryCode",
                        HouseNumber = 2,
                        HouseNumberSuffix = "HouseNumberSuffix",
                        PostalCode = "PostalCode",
                        Street = "Street",
                        Type = AddressType.Delivery
                    },
                    EndDate = new DateTime(2020,1,2),
                    Id = 123,
                    IsActive = true,
                    IntendedEndDate = new DateTime(2020,1,3),
                    IntendedStartDate = new DateTime(2020,1,4),
                    OwningBusinessPartyName = "",
                    CanRequestMeterReadingHistoryReport = false,
                    Products = new List<Product>()
                    {
                        new Product()
                        {
                            IsActive = true,
                            EndDate = new DateTime(2020, 1,7),
                            AccountId = 1,
                            AgreementId = 123456,
                            PriceDeterminationDate = new DateTime(2020,1,7),
                            ProductType = ProductType.Warmth

                        }
                    }
                }
            });

        uRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 1), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<ErrorResponse>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK
                }
            });

        uRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 2), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<ErrorResponse>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel {
                            Code = "dcp|525da9e1-646a-41d3-b9ea-32f97ef1392e",
                            Details = "MVS-23282",
                            Type = "FunctionalException"
                        }
                    }
                }
            });

        uRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 3), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<ErrorResponse>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel {
                            Code = "DCP-UG|7dff0c3e-147c-46bb-b536-7848948e8d90",
                            Details = "MVS-23284",
                            Type = "FunctionalException"
                        }
                    }
                }
            });

        uRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 4), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<ErrorResponse>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel {
                            Code = "dcp|85ba42ea-d374-4372-b04b-f2e235cf4b44",
                            Details = "MVS-23285",
                            Type = "FunctionalException"
                        }
                    }
                }
            });

        uRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 5), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<ErrorResponse>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel {
                            Code = "dcp|22989982-cd0d-4148-b29b-0f8424b331a5",
                            Details = "MVS-23283",
                            Type = "FunctionalException"
                        }
                    }
                }
            });
        uRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 6), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<ErrorResponse>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel {
                            Code = "dcp|19ddee98-4223-40b1-9467-6b8ab8269806",
                            Details = "MVS-23286",
                            Type = "FunctionalException"
                        }
                    }
                }
            });

        uRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 7), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<ErrorResponse>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel {
                            Code = "dcp|614d27b2-df65-4359-90a1-53bfdd1fc6c2",
                            Details = "MVS-23286",
                            Type = "FunctionalException"
                        }
                    }
                }
            });

        uRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 208), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<ErrorResponse>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                }
            });

        uRepoMock.Setup(x => x.GetReading(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 208), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                }
            });

        uRepoMock.Setup(x => x.GetReadingsHistory(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 208), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                }
            });

        uRepoMock.Setup(x => x.GetOutstandingReadings(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 208), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                }
            });
    }
}