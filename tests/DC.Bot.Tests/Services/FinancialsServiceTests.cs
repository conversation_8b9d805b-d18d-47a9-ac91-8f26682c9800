﻿using DC.Bot.BusinessLogic;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Base;
using DC.Domain.Exceptions;
using DC.Domain.Exceptions.ResponseModels;
using DC.Domain.Models.Financials;
using DC.Financials.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using Moq;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Services;

public class FinancialsServiceTests : BaseServiceUnitTest
{
    private FinancialsService _financialsService;

    private void Setup(string errorCode)
    {
        var fRepoMock = new Mock<IDcFinancialsRepository>();
        var lMock = new Mock<ILoggerFactory>();
        var cMock = new Mock<IConfiguration>();

        _financialsService = new FinancialsService(lMock.Object, cMock.Object, fRepoMock.Object);

        fRepoMock.Setup(x => x.GetPaymentPreferences(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataFinancialPreferences
                {
                    Data = new FinancialPreferences
                    {
                        BankAccount = new BankAccount
                        {
                            Number = "10"
                        },
                        PaymentDayOfMonth = 11,
                        PaymentMethodIsDirectDebit = true
                    }
                }
            });


        fRepoMock.Setup(x => x.GetFinancialsPreferences(It.IsAny<Label>(), It.IsAny<BotChannel>(), 2, It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.InternalServerError },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel{ Code = errorCode }
                    }
                }
            });

        fRepoMock.Setup(x => x.GetPaymentArrangement(It.IsAny<Label>(), It.IsAny<BotChannel>(), 2, It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.InternalServerError },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel{ Code = errorCode }
                    }
                }
            });

        fRepoMock.Setup(x => x.GetAdvancePayment(It.IsAny<Label>(), It.IsAny<BotChannel>(), 2, It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.InternalServerError },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel{ Code = errorCode }
                    }
                }
            });

        fRepoMock.Setup(x => x.UpdatePaymentPreferences(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 2), It.IsAny<int>(), It.IsAny<RequestDataFinancialPreferences>()))
            .ReturnsAsync(new HttpOperationResponse<ErrorResponse>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel{ Code = errorCode }
                    }
                }
            });

        fRepoMock.Setup(x => x.UpdatePaymentPreferences(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 1), It.IsAny<int>(), It.IsAny<RequestDataFinancialPreferences>()))
            .ReturnsAsync(new HttpOperationResponse<ErrorResponse>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel{ Code = errorCode }
                    }
                }
            });

        fRepoMock.Setup(x => x.UpdatePaymentPreferences(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 1), It.IsAny<int>(), It.IsAny<RequestDataFinancialPreferences>()))
            .ReturnsAsync(new HttpOperationResponse<ErrorResponse>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel{ Code = errorCode }
                    }
                }
            });

        fRepoMock.Setup(x => x.UpdatePaymentPreferences(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 208), It.IsAny<int>(), It.IsAny<RequestDataFinancialPreferences>()))
            .ReturnsAsync(new HttpOperationResponse<ErrorResponse>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.AlreadyReported }
            });

        fRepoMock.Setup(x => x.UpdatePaymentPreferences(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 204), It.IsAny<int>(), It.IsAny<RequestDataFinancialPreferences>()))
            .ReturnsAsync(new HttpOperationResponse<ErrorResponse>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.NoContent }
            });

        fRepoMock.Setup(x => x.PutAdvancePayment(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 1), It.IsAny<int>(), It.IsAny<RequestDataAdvancePaymentChangeRequest>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel{ Code = errorCode }
                    }
                }
            });

        fRepoMock.Setup(x => x.PutAdvancePayment(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 2), It.IsAny<int>(), It.IsAny<RequestDataAdvancePaymentChangeRequest>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK }
            });

        fRepoMock.Setup(x => x.GetAdvancePaymentAdviceV2(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 1), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel{ Code = errorCode }
                    }
                }
            });

        fRepoMock.Setup(x => x.GetAdvancePaymentAdviceV2(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 2), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataAdvancePaymentAdviceV2Response
                {
                    Data = new AdvancePaymentAdviceV2Response
                    {
                        Advice = 100,
                        AdviceStatus = AdvancePaymentAdviceStatus.Ok,
                        Current = 100,
                        StatusToggle = new AdviceStatusToggles
                        {
                            ShowAdvice = true,
                            ShowForecast = true
                        }
                    }
                }
            });
    }

    [Theory]
    [InlineData(1, "dcp|5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93", AdvancePaymentAdviceStatus.NotAvailable)]
    [InlineData(1, "dcp|C3A0BEFB-9C93-4AF0-BC73-FEA11E9E4309", AdvancePaymentAdviceStatus.NoAdviceYet)]
    [InlineData(1, "dcp|b863bd68-a964-48a6-9a90-adf42560bd5b", AdvancePaymentAdviceStatus.YearNoteTooClose)]
    [InlineData(1, "dcp|3286cf97-3126-4dd6-8276-59036e99ad47", AdvancePaymentAdviceStatus.ActiveDebtCollection)]
    [InlineData(1, "dcp|cbdfcf60-c705-4316-a383-75ede2a71fad", AdvancePaymentAdviceStatus.YearlyInvoiceEstimatedOnMeterReadings)]
    [InlineData(1, "dcp|3180497f-318e-4da7-a06a-6afdb157ec5e", AdvancePaymentAdviceStatus.PaymentAmountTooLow)]
    [InlineData(1, "dcp|abc", AdvancePaymentAdviceStatus.Default)]
    [InlineData(2, "", AdvancePaymentAdviceStatus.Ok)]
    public async Task UpdateAdvancePaymentAmount_Returns_Correct_Enum(long customerId, string errorCode, AdvancePaymentAdviceStatus returnStatus)
    {
        Setup(errorCode);

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = customerId
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _financialsService.UpdateAdvancePaymentAmount(dialogData, 10).ConfigureAwait(true);
        response.Status.Should().Be(returnStatus);
    }


    [Theory]
    [InlineData(1, "DCP|16cabc53-cfd0-4c72-a1f7-deef51de07ba", AdvancePaymentAdviceStatus.PaymentMethodShouldBeDirectDebit)]
    [InlineData(1, "DCP|1774e629-cba8-4f5a-ae89-166ce1b7d8d7", AdvancePaymentAdviceStatus.ActiveDebtCollection)]
    [InlineData(1, "DCP|b6c222f6-fc17-4275-bfbe-efc9ad4bc6c6", AdvancePaymentAdviceStatus.PaymentMethodShouldNotBeDirectDebit)]
    [InlineData(1, "DCP|ad03bf92-6df2-4b22-8d07-1b317dcf5721", AdvancePaymentAdviceStatus.IbanMissingOrIncorrect)]
    [InlineData(1, "abc", AdvancePaymentAdviceStatus.Default)]
    public async Task UpdatePaymentDayOfMonth_Returns_Correct_Enum(long customerId, string errorCode, AdvancePaymentAdviceStatus returnStatus)
    {
        Setup(errorCode);

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = customerId
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _financialsService.UpdatePaymentDayOfMonth(dialogData, 10).ConfigureAwait(true);
        response.Should().Be(returnStatus);
    }


    [Theory]
    [InlineData(208, AdvancePaymentAdviceStatus.Ok)]
    [InlineData(204, AdvancePaymentAdviceStatus.Ok)]
    public async Task UpdatePaymentDayOfMonth_Returns_OK(long customerId, AdvancePaymentAdviceStatus returnStatus)
    {
        Setup("");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = customerId
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _financialsService.UpdatePaymentDayOfMonth(dialogData, 10).ConfigureAwait(true);
        response.Should().Be(returnStatus);
    }

    [Fact]
    public async Task GetAdvancePaymentAdviceV2_Returns_Correct_Model()
    {
        Setup("");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 2
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _financialsService.GetAdvancePaymentAdviceV2(dialogData).ConfigureAwait(true);
        response.Should().NotBeNull();
        response.Current.Should().Be(100);
        response.Advice.Should().Be(100);
        response.AdviceStatus.Should().Be(AdvancePaymentAdviceStatus.Ok);
    }

    [Fact]
    public async Task GetAdvancePaymentAdviceV2_ResponseCode_IsNoSuccess()
    {
        Setup("");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        var response = await _financialsService.GetAdvancePaymentAdviceV2(dialogData).ConfigureAwait(true);
        response.Should().NotBeNull();
        response.AdviceStatus.Should().Be(AdvancePaymentAdviceStatus.NotAvailable);
    }

    [Fact]
    public void GetPaymentPreferences_ThrowsException()
    {
        Setup("");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 2
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        Assert.ThrowsAnyAsync<FunctionalException>(() => _financialsService.GetFinancialsPreferences(dialogData, 1)).ConfigureAwait(true);
    }

    [Fact]
    public void GetPaymentArrangement_ThrowsException()
    {
        Setup("");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 2
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        Assert.ThrowsAnyAsync<FunctionalException>(() => _financialsService.GetPaymentArrangement(dialogData, 1)).ConfigureAwait(true);
    }

    [Fact]
    public void GetAdvancePaymentAmount_ThrowsException()
    {
        Setup("");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 2
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        Assert.ThrowsAnyAsync<FunctionalException>(() => _financialsService.GetAdvancePaymentAmount(dialogData, 1)).ConfigureAwait(true);
    }

    [Fact]
    public void UpdateBankAccount_ThrowsException()
    {
        Setup("");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 2
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        Assert.ThrowsAnyAsync<FunctionalException>(() => _financialsService.UpdateBankAccount(dialogData, "1234")).ConfigureAwait(true);
    }

    [Fact]
    public void UpdatePaymentToDirectDebit_ThrowsException()
    {
        Setup("");

        var dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 2
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        Assert.ThrowsAnyAsync<FunctionalException>(() => _financialsService.UpdatePaymentToDirectDebit(dialogData, "1234", 1)).ConfigureAwait(true);
    }
}