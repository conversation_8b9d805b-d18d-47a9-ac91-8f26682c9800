﻿using DC.Api.Base.FeatureManagement;
using DC.Bot.BusinessLogic;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Base;
using DC.Domain.Models.NextBestAction;
using DC.ESP.Services;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Services;

public class NextBestActionServiceTests : BaseServiceUnitTest
{
    private readonly Mock<IDcProductsRepository> _productsRepoMock;
    private readonly Mock<IConfiguration> _configurationMock;
    private readonly Mock<IJourneyTrackerService> _journeyTrackerServiceMock;

    private readonly NextBestActionService _service;

    public NextBestActionServiceTests()
    {
        _productsRepoMock = new Mock<IDcProductsRepository>();
        _configurationMock = new Mock<IConfiguration>();
        _journeyTrackerServiceMock = new Mock<IJourneyTrackerService>();

        _service = new NextBestActionService(new NullLoggerFactory(), _configurationMock.Object, _productsRepoMock.Object, _journeyTrackerServiceMock.Object);
    }

    [Theory]
    [InlineData(true, 2)]
    [InlineData(false, 3)]
    public async Task GetNextBestActions_WorksAsExpected(bool filter, int expected)
    {
        _productsRepoMock.Setup(p => p.GetNextBestActions(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int?>()))
            .ReturnsAsync(GetResponse<object>(new ResponseDataNextBestAction
            {
                Data = new NextBestAction
                {
                    Actions = new List<ActionModel>
                        {
                            new() {
                                ActionType = "AccountMeterReading",
                                Score = 100,
                                ActionId = 90,
                                ServingPointId = 4,
                                TreatmentVariationId = 3
                            },
                            new() {
                                ActionType = "BankAccountNumber",
                                Score = 95,
                                ActionId = 95,
                                ServingPointId = 4,
                                TreatmentVariationId = 2
                            },
                            new() {
                                ActionType = "CustomerPhoneNumber",
                                Score = 98,
                                ActionId = 99,
                                ServingPointId = 4,
                                TreatmentVariationId = 1
                            }
                        },
                    ContextId = "ctx1"
                }
            }, System.Net.HttpStatusCode.OK));

        var nextBestAction = await _service.GetNextBestActions(new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 123
            },
            Channel = BotChannel.Web
        }, filter).ConfigureAwait(true);
        nextBestAction.Actions.Should().HaveCount(expected);
    }

    [Theory]
    [InlineData("CustomerPhoneNumber", true)]
    [InlineData("KetelComfortMaintenance", false)]
    public async Task AddFeedback_WorksAsExpected(string actionType, bool called)
    {
        DialogData dialogData = new()
        {
            NextBestAction = new NextBestActionData
            {
                CurrentTransactionIsNba = true,
                Data = new NextBestAction
                {
                    Actions = new List<ActionModel>
                    {
                        new()
                        {
                            ActionType = "CustomerPhoneNumber",
                            Score = 98,
                            ActionId = 99,
                            ServingPointId = 4,
                            TreatmentVariationId = 1
                        }
                    },
                    ContextId = "ctx1"
                }
            }
        };
        
        await _service.AddFeedback(dialogData, actionType, FeedbackStatus.Viewed).ConfigureAwait(true);
        _journeyTrackerServiceMock.Verify(d => d.PublishCustomerJourneyEvent(It.IsAny<ActionFeedbackData>(), Label.Default, 0), called ? Times.Once : Times.Never);
    }
}
