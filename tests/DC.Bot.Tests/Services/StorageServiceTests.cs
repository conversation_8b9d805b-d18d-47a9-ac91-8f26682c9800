﻿using DC.Bot.BusinessLogic;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Base;
using DC.Domain.Models;
using DC.Repositories.Base.Enumerations;
using DC.Storage.Client.Models;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Services;

public class StorageServiceTests : BaseServiceUnitTest
{
    private StorageService _storageService;

    [Fact]
    public async Task GetTextLabels_WorksExpected()
    {
        Setup();
        var response = await _storageService.GetTextLabels(Label.Eneco, "Chat_GeneralTextLabels", null).ConfigureAwait(true);
        response.Should().NotBeNull();
    }

    [Fact]
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Critical Code Smell", "S2699:Add at least one assertion to this test case", Justification = "")]

    public async Task ExportTextLabels_WorksExpected()
    {
        Setup();
        await _storageService.ExportTextLabels(Label.Eneco, true).ConfigureAwait(true);
    }

    private void Setup()
    {
        var sRepoMock = new Mock<IDcStorageRepository>();
        var lMock = new Mock<ILoggerFactory>();
        var cMock = GetConfig();

        _storageService = new StorageService(lMock.Object, cMock, sRepoMock.Object);

        sRepoMock.Setup(x => x.GetTextlabels(It.IsAny<Label>(), It.IsAny<RequestDataTextLabelRequest>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataIListTextLabelModel
                {
                    Data = new List<TextLabelModel>()
                }
            });
    }

    private static IConfiguration GetConfig()
    {
        var inMemorySettings = new Dictionary<string, string> {
            {"AllowTextLabelsExport", "true"},
        };

        return new ConfigurationBuilder()
            .AddInMemoryCollection(inMemorySettings)
            .Build();
    }
}
