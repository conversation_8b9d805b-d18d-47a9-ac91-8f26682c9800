﻿using DC.Bot.BusinessLogic;
using DC.Bot.BusinessLogic.Configuration;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Base;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using Moq;
using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Services
{
    public class ProductFineCalculationServiceTests : BaseServiceUnitTest
    {
        private ProductFineCalculationService _productFineCalculationService;
        private static DialogData dialogData = new DialogData
        {
            Customer = new CustomerData
            {
                Label = Label.Eneco
            },
            Verification = new VerificationData
            {
                CustomerId = 1,
                CustomerIdVerified = true
            },
            SelectedAccount = new AccountData
            {
                AccountId = 1
            }
        };

        public ProductFineCalculationServiceTests()
        {
            if (_mapper == null)
            {
                _mapper = DcAutoMapper.GetMapperConfiguration().CreateMapper();
            }
        }

        [Fact]
        public async Task CalculateFineCancelledProductTest()
        {
            Setup();
            var request = new ProductCancelRequestModel
            {
                SubscriptionId = 1,
                CancelDate = DateTime.Today
            };
            var result = await _productFineCalculationService.CalculateFineCancelledProduct(dialogData, request);

            result.Should().NotBeNull();
        }

        [Fact]
        public async Task CalculateFineSwitchedProductTest()
        {
            Setup();
            var request = new ProductSwitchRequestModel
            {
                SubscriptionId = 1,
                SwitchDate = DateTime.Today
            };
            var result = await _productFineCalculationService.CalculateFineSwitchedProduct(dialogData, request);

            result.Should().NotBeNull();
        }

        private void Setup()
        {
            var pRepoMock = new Mock<IDcProductsRepository>();
            var loggerMock = new Mock<ILoggerFactory>();
            ILogger logger = new Mock<ILogger>().Object;
            loggerMock.Setup(f => f.CreateLogger(It.IsAny<string>())).Returns(logger);
            var cMock = new Mock<IConfiguration>();

            _productFineCalculationService = new ProductFineCalculationService(loggerMock.Object, cMock.Object, _mapper, pRepoMock.Object);

            pRepoMock.Setup(c => c.CalculateFineCancelledProduct(It.IsAny<Label>(), It.IsAny<RequestDataProductCancelRequestModel>())).ReturnsAsync(
               new HttpOperationResponse<object>
               {
                   Response = new HttpResponseMessage
                   {
                       StatusCode = HttpStatusCode.OK,
                   },
                   Body = new ResponseDataProductSwitchResponseModel
                   {
                       Data = new ProductSwitchResponseModel
                       {
                           FineAmount = 50
                       }
                   }
               }
           );

            pRepoMock.Setup(c => c.CalculateFineSwitchedProduct(It.IsAny<Label>(), It.IsAny<RequestDataProductSwitchRequestModel>())).ReturnsAsync(
               new HttpOperationResponse<object>
               {
                   Response = new HttpResponseMessage
                   {
                       StatusCode = HttpStatusCode.OK
                   },
                   Body = new ResponseDataProductSwitchResponseModel
                   {
                       Data = new ProductSwitchResponseModel
                       {
                           FineAmount = 50
                       }
                   }
               });
        }
    }
}
