﻿using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Components;
using DC.Bot.BusinessLogic.Dialogs.Customers;
using DC.Bot.BusinessLogic.Dialogs.Customers.PhoneNumberDialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Dialogs.Financials;
using DC.Bot.BusinessLogic.Dialogs.Financials.Validators;
using DC.Bot.BusinessLogic.Dialogs.Products;
using DC.Bot.BusinessLogic.Dialogs.Products.Validators;
using DC.Bot.BusinessLogic.Dialogs.Usages;
using DC.Bot.BusinessLogic.Dialogs.UserAccounts;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Interfaces.DialogContainers;
using DC.Bot.BusinessLogic.Models;
using FluentAssertions;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.Bot.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Moq;
using System.Linq;
using Microsoft.Extensions.Time.Testing;
using Xunit;

namespace DC.Bot.Tests;

public class DialogConstructorTests
{
    [Fact]
    public void MainDialogCtorTest()
    {
        var containerMock = new Mock<IDialogContainer>();
        var validatorMock = new Mock<IDialogValidators>();
        var optionsMock = new Mock<IOptions<FeatureToggle>>();
        optionsMock.Setup(o => o.Value).Returns(new FeatureToggle());

        validatorMock.Setup(x => x.CustomerDialogValidator).Returns(new CustomerDialogValidator(new Mock<ISessionManager>().Object));
        validatorMock.Setup(x => x.FinancialsDialogValidator).Returns(new FinancialsDialogValidator(new Mock<ISessionManager>().Object));
        validatorMock.Setup(x => x.ProductsDialogValidator).Returns(new ProductsDialogValidator(new Mock<ISessionManager>().Object));

        containerMock.Setup(x => x.Verification.CustomerVerificationDialog).Returns(new CustomerVerificationDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            validatorMock.Object,
            new Mock<ICustomersService>().Object,
            new Mock<INextBestActionService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Verification.CustomerIdVerification).Returns(new CustomerIdVerification
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            validatorMock.Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Customers.CustomerAccountsDialog).Returns(new CustomerAccountsDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Customers.YearnoteDateDialog).Returns(new YearnoteDateDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Customers.ChangeEmailDialog).Returns(new ChangeEmailDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            validatorMock.Object,
            new Mock<ICustomersService>().Object,
            new Mock<INextBestActionService>().Object,
            new Mock<IStorageService>().Object,
            new Mock<IContactPreferencesService>().Object
        ));
        containerMock.Setup(x => x.Customers.ChangeIbanDialog).Returns(new ChangeIbanDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            validatorMock.Object,
            new Mock<IFinancialsService>().Object,
            new Mock<INextBestActionService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Customers.ChangePhoneNumberDialog).Returns(new ChangePhoneNumberDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            validatorMock.Object,
            new Mock<ICustomersService>().Object,
            new Mock<INextBestActionService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Customers.ChangeContactPreferencesDialog).Returns(new ChangeContactPreferencesDialog(
            new Mock<ILoggerFactory>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<IStorageService>().Object,
            new Mock<IContactPreferencesService>().Object));
        containerMock.Setup(x => x.Customers.RelocateDateDialog).Returns(new RelocateDateDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<IProductsService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Financials.AdvancePaymentAmountDialog).Returns(new AdvancePaymentAmountDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<IFinancialsService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Financials.GiroCardStepDialog).Returns(new GiroCardStepDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            validatorMock.Object,
            new Mock<IFinancialsService>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Financials.AdvancePaymentDayDialog).Returns(new AdvancePaymentDayDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            validatorMock.Object,
            new Mock<ICustomersService>().Object,
            new Mock<IFinancialsService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Financials.PaymentArrangementDialog).Returns(new PaymentArrangementDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<IFinancialsService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Financials.AdvancePaymentAdviceDialog).Returns(new AdvancePaymentAdviceDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<IFinancialsService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Products.ProductRatesDialog).Returns(new ProductRatesDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<IProductsService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Products.ProductEndDatesDialog).Returns(new ProductEndDatesDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<IProductsService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Products.ProductEndDatesAdviceDialog).Returns(new ProductEndDatesAdviceDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<IProductsService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Products.PersonalDiscontinueToonDialog).Returns(new DiscontinueToonDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<IProductsService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Products.PersonalDiscontinueServiceContractDialog).Returns(new DiscontinueServiceContractDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<IProductsService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Products.KetelComfortAppointmentDialog).Returns(new KetelComfortAppointmentDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<IProductsService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Products.CreateServiceOrderDialog).Returns(new CreateServiceOrderDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<IProductsService>().Object,
            new Mock<IStorageService>().Object,
            new Mock<IConfiguration>().Object
        ));
        containerMock.Setup(x => x.Products.NextBestActionDialog).Returns(new NextBestActionDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<INextBestActionService>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<IStorageService>().Object)
        );
        containerMock.Setup(x => x.UserAccounts.UsernameDialog).Returns(new UsernameDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<IUserAccountsService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Usages.ProductUsagesDialog).Returns(new ProductUsagesDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<IUsagesService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Usages.SaveReadingPersonalDialog).Returns(new SaveReadingPersonalDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<IUsagesService>().Object,
            new Mock<IStorageService>().Object,
            new Mock<ICustomersService>().Object,
            new FakeTimeProvider()
        ));
        containerMock.Setup(x => x.Usages.ReadingsReportRequestDialog).Returns(new ReadingsReportRequestDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            validatorMock.Object,
            new Mock<IUsagesService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Usages.IsmaMandateDialog).Returns(new IsmaMandateDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<IUsagesService>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<IStorageService>().Object
        ));
        containerMock.Setup(x => x.Products.ProductFineCalculationDialog).Returns(new ProductFineCalculationDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            validatorMock.Object,
            new Mock<ICustomersService>().Object,
            new Mock<IProductsService>().Object,
            new Mock<IProductFineCalculationService>().Object,
            new Mock<IStorageService>().Object,
            Options.Create(new FeatureToggle { NewFinePolicyDate = new System.DateTime(2023, 6, 1) })
        ));
        containerMock.Setup(x => x.Products.ZonOpDakDialog).Returns(new ZonOpDakDialog
        (
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<IProductsService>().Object,
            new Mock<IStorageService>().Object
        ));


        var mainDialog = new MainDialog(
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            containerMock.Object,
            new Mock<ICustomersService>().Object,
            new Mock<INextBestActionService>().Object);

        mainDialog.Dialogs.GetDialogs().Count().Should().BeGreaterThan(0);
        mainDialog.Dialogs.GetDialogs().Any(d => d.Id == "InitialDialog").Should().BeTrue();
        mainDialog.Dialogs.GetDialogs().Any(d => d.Id == "CustomerVerificationDialog").Should().BeTrue();
        mainDialog.Dialogs.GetDialogs().Any(d => d.Id == "AdvancePaymentAmountDialog").Should().BeTrue();
        mainDialog.Dialogs.GetDialogs().Any(d => d.Id == "YearnoteDateDialog").Should().BeTrue();
        mainDialog.Dialogs.GetDialogs().Any(d => d.Id == "AdvancePaymentAmountWaterfallDialog").Should().BeTrue();
        mainDialog.Dialogs.GetDialogs().Any(d => d.Id == "YearnoteDateWaterfallDialog").Should().BeTrue();
        mainDialog.Dialogs.GetDialogs().Any(d => d.Id == "RelocateDateDialog").Should().BeTrue();
        mainDialog.Dialogs.GetDialogs().Any(d => d.Id == "CreateServiceOrderDialog").Should().BeTrue();
        mainDialog.Dialogs.GetDialogs().Any(d => d.Id == "SaveReadingPersonalDialog").Should().BeTrue();
        mainDialog.Dialogs.GetDialogs().Any(d => d.Id == "ProductFineCalculationDialog").Should().BeTrue();
    }

    [Fact]
    public void CustomerVerificationDialogCtorTest()
    {
        var dialog = new CustomerVerificationDialog(
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new DialogValidators(null, new Mock<ICustomerValidator>().Object, null),
            new Mock<ICustomersService>().Object,
            new Mock<INextBestActionService>().Object,
            new Mock<IStorageService>().Object);

        dialog.Dialogs.GetDialogs().Count().Should().BeGreaterThan(0);
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "WaterfallDialog").Should().BeTrue();
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "AskForPostalCodeStep").Should().BeTrue();
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "AskForHouseNumberStep").Should().BeTrue();
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "AskForIbanSuffixStep").Should().BeTrue();
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "AskForDateOfBirthStep").Should().BeTrue();
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "AskForCustomerNumberStep").Should().BeTrue();
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "ProcessCustomerNumberStep").Should().BeTrue();
    }

    [Fact]
    public void YearnoteDateDialogCtorTest()
    {
        var dialog = new YearnoteDateDialog(
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            new Mock<ISessionManager>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<IStorageService>().Object);
        dialog.Dialogs.GetDialogs().Count().Should().BeGreaterThan(0);
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "WaterfallDialog").Should().BeTrue();
    }

    [Fact]
    public void AdvancePaymentAmountDialogCtorTest()
    {
        var sessionManager = new SessionManager(new Mock<IStatePropertyAccessor<ConversationData>>().Object,
            new Microsoft.ApplicationInsights.TelemetryClient(TelemetryConfiguration.CreateDefault()),
            new Mock<IConfiguration>().Object,
            new Mock<ILoggerFactory>().Object,
            new Mock<IConfigurationManager<OpenIdConnectConfiguration>>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<INextBestActionService>().Object);

        var dialog = new AdvancePaymentAmountDialog(
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            sessionManager,
            new Mock<ICustomersService>().Object,
            new Mock<IFinancialsService>().Object,
            new Mock<IStorageService>().Object);
        dialog.Dialogs.GetDialogs().Count().Should().BeGreaterThan(0);
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "WaterfallDialog").Should().BeTrue();
    }
    [Fact]
    public void ProductratesCtorTest()
    {
        var sessionManager = new SessionManager(new Mock<IStatePropertyAccessor<ConversationData>>().Object,
            new Microsoft.ApplicationInsights.TelemetryClient(TelemetryConfiguration.CreateDefault()),
            new Mock<IConfiguration>().Object,
            new Mock<ILoggerFactory>().Object,
            new Mock<IConfigurationManager<OpenIdConnectConfiguration>>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<INextBestActionService>().Object);
        var dialog = new ProductRatesDialog(
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            sessionManager,
            new Mock<ICustomersService>().Object,
            new Mock<IProductsService>().Object,
            new Mock<IStorageService>().Object);
        dialog.Dialogs.GetDialogs().Count().Should().BeGreaterThan(0);
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "WaterfallDialog").Should().BeTrue();
    }
    [Fact]
    public void UserNameDialogCtorTest()
    {
        var sessionManager = new SessionManager(new Mock<IStatePropertyAccessor<ConversationData>>().Object,
            new Microsoft.ApplicationInsights.TelemetryClient(TelemetryConfiguration.CreateDefault()),
            new Mock<IConfiguration>().Object,
            new Mock<ILoggerFactory>().Object,
            new Mock<IConfigurationManager<OpenIdConnectConfiguration>>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<INextBestActionService>().Object);
        var dialog = new UsernameDialog(
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            sessionManager,
            new Mock<IUserAccountsService>().Object,
            new Mock<IStorageService>().Object);
        dialog.Dialogs.GetDialogs().Count().Should().BeGreaterThan(0);
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "WaterfallDialog").Should().BeTrue();
    }
    [Fact]
    public void RelocateDateDialogCtorTest()
    {
        var sessionManager = new SessionManager(new Mock<IStatePropertyAccessor<ConversationData>>().Object,
            new Microsoft.ApplicationInsights.TelemetryClient(TelemetryConfiguration.CreateDefault()),
            new Mock<IConfiguration>().Object,
            new Mock<ILoggerFactory>().Object,
            new Mock<IConfigurationManager<OpenIdConnectConfiguration>>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<INextBestActionService>().Object);
        var dialog = new RelocateDateDialog(
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            sessionManager,
            new Mock<ICustomersService>().Object,
            new Mock<IProductsService>().Object,
            new Mock<IStorageService>().Object);
        dialog.Dialogs.GetDialogs().Count().Should().BeGreaterThan(0);
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "WaterfallDialog").Should().BeTrue();
    }
    [Fact]
    public void RegisterReportRequestCtorTest()
    {
        var sessionManager = new SessionManager(new Mock<IStatePropertyAccessor<ConversationData>>().Object,
            new Microsoft.ApplicationInsights.TelemetryClient(TelemetryConfiguration.CreateDefault()),
            new Mock<IConfiguration>().Object,
            new Mock<ILoggerFactory>().Object,
            new Mock<IConfigurationManager<OpenIdConnectConfiguration>>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<INextBestActionService>().Object);
        var dialog = new ReadingsReportRequestDialog(
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            sessionManager,
            new DialogValidators(null, new Mock<ICustomerValidator>().Object, null),
            new Mock<IUsagesService>().Object,
            new Mock<IStorageService>().Object);
        dialog.Dialogs.GetDialogs().Count().Should().BeGreaterThan(0);
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "WaterfallDialog").Should().BeTrue();
    }
    [Fact]
    public void CreateServiceOrderCtorTest()
    {
        var sessionManager = new SessionManager(new Mock<IStatePropertyAccessor<ConversationData>>().Object,
            new Microsoft.ApplicationInsights.TelemetryClient(TelemetryConfiguration.CreateDefault()),
            new Mock<IConfiguration>().Object,
            new Mock<ILoggerFactory>().Object,
            new Mock<IConfigurationManager<OpenIdConnectConfiguration>>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<INextBestActionService>().Object);
        var dialog = new CreateServiceOrderDialog(
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            sessionManager,
            new Mock<IProductsService>().Object,
            new Mock<IStorageService>().Object,
            new Mock<IConfiguration>().Object);
        dialog.Dialogs.GetDialogs().Count().Should().BeGreaterThan(0);
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "WaterfallDialog").Should().BeTrue();
    }
    [Fact]
    public void ProductEndDatesAdviceCtorTest()
    {
        var sessionManager = new SessionManager(new Mock<IStatePropertyAccessor<ConversationData>>().Object,
            new Microsoft.ApplicationInsights.TelemetryClient(TelemetryConfiguration.CreateDefault()),
            new Mock<IConfiguration>().Object,
            new Mock<ILoggerFactory>().Object,
            new Mock<IConfigurationManager<OpenIdConnectConfiguration>>().Object,
            new Mock<ICustomersService>().Object,
            new Mock<INextBestActionService>().Object);
        var dialog = new ProductEndDatesAdviceDialog(
            new Mock<ILoggerFactory>().Object,
            new Mock<ILoggingService>().Object,
            sessionManager,
            new Mock<ICustomersService>().Object,
            new Mock<IProductsService>().Object,
            new Mock<IStorageService>().Object);
        dialog.Dialogs.GetDialogs().Count().Should().BeGreaterThan(0);
        dialog.Dialogs.GetDialogs().Any(d => d.Id == "WaterfallDialog").Should().BeTrue();
    }
}
