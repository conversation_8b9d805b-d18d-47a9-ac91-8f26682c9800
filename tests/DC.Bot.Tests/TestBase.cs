﻿using DC.Api.Base.FeatureManagement;
using DC.Bot.BusinessLogic;
using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Components;
using DC.Bot.BusinessLogic.Dialogs.Customers;
using DC.Bot.BusinessLogic.Dialogs.Customers.PhoneNumberDialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Dialogs.Financials;
using DC.Bot.BusinessLogic.Dialogs.Financials.Validators;
using DC.Bot.BusinessLogic.Dialogs.Products;
using DC.Bot.BusinessLogic.Dialogs.Products.Validators;
using DC.Bot.BusinessLogic.Dialogs.Usages;
using DC.Bot.BusinessLogic.Dialogs.UserAccounts;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Extensions;
using DC.Customers.Client.Models;
using DC.Domain.Models.Agreements;
using DC.Repositories.Base.Enumerations;
using DC.Storage.Client.Models;
using DC.Telemetry.Models;
using DC.Usages.Client.Models;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Connector;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Microsoft.Extensions.Time.Testing;
using TextLabelModel = DC.Storage.Client.Models.TextLabelModel;

namespace DC.Bot.Tests;

public class TestBase : IDisposable
{
    internal ServiceCollection _services;
    internal ServiceProvider _serviceProvider;
    internal static List<TextLabelModel> TextLabelModels = new List<TextLabelModel>();
    public string TextLabelGroupName;

    protected TestBase()
    {
        _services = new ServiceCollection();
        SetupMockedServices();
    }

    public void Dispose()
    {
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// Setup services needed for DialogContainer
    /// </summary>
    private void SetupMockedServices()
    {
        var sessionManagerMock = new Mock<ISessionManager>();
        var lFactMock = new Mock<ILoggerFactory>();
        var vMock = new Mock<IDialogValidators>();
        var lConfMock = GetConfig();

        var fServiceMock = new Mock<IFinancialsService>();
        var pServiceMock = new Mock<IProductsService>();
        var uServiceMock = new Mock<IUsagesService>();
        var aServiceMock = new Mock<IUserAccountsService>();
        var cServiceMock = new Mock<ICustomersService>();

        var customersRepoMock = new Mock<IDcCustomersRepository>();
        var productsRepoMock = new Mock<IDcProductsRepository>();
        var userAccountRepoMock = new Mock<IDcUserAccountsRepository>();
        var usageRepoMock = new Mock<IDcUsagesRepository>();
        var storageRepoMock = new Mock<IDcStorageRepository>();

        var nbaMock = new Mock<INextBestActionService>();
        var featureManagerMock = new Mock<IDCFeatureManager>();

        vMock.Setup(x => x.CustomerDialogValidator)
            .Returns(new Mock<ICustomerValidator>().Object);
        vMock.Setup(x => x.FinancialsDialogValidator)
            .Returns(new Mock<IFinancialsDialogValidator>().Object);
        vMock.Setup(x => x.ProductsDialogValidator)
            .Returns(new Mock<IProductsDialogValidator>().Object);
        sessionManagerMock.Setup(x => x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(
                new DialogData
                {
                    Customer = new CustomerData
                    {
                        Label = Label.Eneco
                    }
                });

        usageRepoMock.Setup(m => m.GetSmartMeterInterruption(It.IsAny<Label>(), It.IsAny<Telemetry.Models.BotChannel>(), It.IsAny<long>(), It.IsAny<int>()))
            .ReturnsAsync(new Microsoft.Rest.HttpOperationResponse<object>
            {
                Body = new ResponseDataSmartMeterInterruptionModel
                {
                    Data = new SmartMeterInterruptionModel
                    {
                        HasSmartMeter = true,
                        SmartMeterHasInterruption = false
                    }
                },
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                }
            });

        GetAllTextLabelsFromExport();
        storageRepoMock.Setup(x => x.GetTextlabels(It.IsAny<Label>(), It.IsAny<Storage.Client.Models.RequestDataTextLabelRequest>(), It.IsAny<bool>()))
            .ReturnsAsync(new Microsoft.Rest.HttpOperationResponse<object>
            {
                Body = new ResponseDataIListTextLabelModel
                {
                    Data = TextLabelModels?.ToList()
                },
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                }
            });

        customersRepoMock.Setup(x => x.GetAgreements(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>()))
            .ReturnsAsync(new Microsoft.Rest.HttpOperationResponse<object>
            {
                Body = new ResponseDataListAgreement
                {
                    Data = new List<Agreement> { }
                },
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                }
            });

        _services.SwapTransient(provider => lConfMock);
        _services.SwapTransient(provider => customersRepoMock.Object);
        _services.SwapTransient(provider => usageRepoMock.Object);
        _services.SwapTransient(provider => cServiceMock.Object);
        _services.SwapTransient(provider => sessionManagerMock.Object);
        _services.SwapTransient(provider => lFactMock.Object);
        _services.SwapTransient(provider => fServiceMock.Object);
        _services.SwapTransient(provider => pServiceMock.Object);
        _services.SwapTransient(provider => uServiceMock.Object);
        _services.SwapTransient(provider => aServiceMock.Object);
        _services.SwapTransient(provider => vMock.Object);
        _services.SwapTransient(provider => productsRepoMock.Object);
        _services.SwapTransient(provider => userAccountRepoMock.Object);
        _services.SwapTransient(provider => nbaMock.Object);
        _services.SwapTransient(provider => storageRepoMock.Object);
        _services.SwapTransient(provider => featureManagerMock.Object);
    }

    /// <summary>
    /// Get main dialog with possible overwritten services by SwapMockedServices
    /// </summary>
    /// <returns></returns>
    internal DialogTestClient SetupMainDialog()
    {
        _serviceProvider = _services.BuildServiceProvider();
        var loggerMock = _serviceProvider.GetService<ILoggerFactory>();

        var loggerServiceMock = _serviceProvider.GetService<ILoggingService>();
        var smMock = _serviceProvider.GetService<ISessionManager>();
        var lConfMock = _serviceProvider.GetService<IConfiguration>();
        var customersRepoMock = _serviceProvider.GetService<IDcCustomersRepository>();
        var productsServiceMock = _serviceProvider.GetService<IProductsService>();
        var usagesRepoMock = _serviceProvider.GetService<IDcUsagesRepository>();


        var cServiceMock = new CustomersService(loggerMock, lConfMock, customersRepoMock, productsServiceMock, usagesRepoMock);
        var nbaMock = _serviceProvider.GetService<INextBestActionService>();

        var optionsMock = new Mock<IOptions<FeatureToggle>>();
        optionsMock.Setup(o => o.Value).Returns(new FeatureToggle());

        var containerMock = SetupDialogContainer();
        var sut = new MainDialog(loggerMock, loggerServiceMock, smMock, containerMock, cServiceMock, nbaMock);
        var testClient = new DialogTestClient(Channels.Test, sut);
        return testClient;
    }

    /// <summary>
    /// Get the dialog container
    /// </summary>
    /// <returns></returns>
    private DialogContainer SetupDialogContainer()
    {
        var lFactMock = _serviceProvider.GetService<ILoggerFactory>();
        var lServiceMock = _serviceProvider.GetService<ILoggingService>();
        var lConfMock = _serviceProvider.GetService<IConfiguration>();
        var smMock = _serviceProvider.GetService<ISessionManager>();
        var fServiceMock = _serviceProvider.GetService<IFinancialsService>();
        var vMock = _serviceProvider.GetService<IDialogValidators>();
        var cMock = GetConfig();

        var customersRepoMock = _serviceProvider.GetService<IDcCustomersRepository>();
        var productsRepMock = _serviceProvider.GetService<IDcProductsRepository>();
        var userAccountRepMock = _serviceProvider.GetService<IDcUserAccountsRepository>();
        var usageRepoMock = _serviceProvider.GetService<IDcUsagesRepository>();
        var storageRepoMock = _serviceProvider.GetService<IDcStorageRepository>();
        var customerServiceMock = _serviceProvider.GetService<ICustomersService>();
        var productsServiceMock = _serviceProvider.GetService<IProductsService>();
        var productFineCalcutionServiceMock = _serviceProvider.GetService<IProductFineCalculationService>();
        var nbaMock = _serviceProvider.GetService<INextBestActionService>();

        var cServiceMock = new CustomersService(lFactMock, lConfMock, customersRepoMock, productsServiceMock, usageRepoMock);
        var pServiceMock = new ProductsService(lFactMock, lConfMock, customersRepoMock, productsRepMock);
        var uaServiceMock = new UserAccountsService(lFactMock, lConfMock, userAccountRepMock);
        var uServiceMock = new UsagesService(lFactMock, lConfMock, usageRepoMock, customerServiceMock);
        var sServiceMock = new StorageService(lFactMock, lConfMock, storageRepoMock);

        var optionsMock = new Mock<IOptions<FeatureToggle>>();
        optionsMock.Setup(o => o.Value).Returns(new FeatureToggle { NewFinePolicyDate = new DateTime(2021, 6, 1) });

        var cDialogContainer =
            new CustomersDialogContainer
            (
                new CustomerAccountsDialog(lFactMock, lServiceMock, smMock, sServiceMock),
                new YearnoteDateDialog(lFactMock, lServiceMock, smMock, cServiceMock, sServiceMock),
                new RelocateDateDialog(lFactMock, lServiceMock, smMock, cServiceMock, pServiceMock, sServiceMock),
                new ChangeEmailDialog(lFactMock, lServiceMock, smMock, vMock, cServiceMock, nbaMock, sServiceMock, Mock.Of<IContactPreferencesService>()),
                new ChangeIbanDialog(lFactMock, lServiceMock, smMock, vMock, fServiceMock, nbaMock, sServiceMock),
                new ChangePhoneNumberDialog(lFactMock, lServiceMock, smMock, vMock, cServiceMock, nbaMock, sServiceMock),
                new ChangeContactPreferencesDialog(lFactMock, smMock, lServiceMock, sServiceMock, Mock.Of<IContactPreferencesService>())
            );
        var vDialogContainer =
            new CustomerVerificationDialogContainer
            (
                new CustomerVerificationDialog(lFactMock, lServiceMock, smMock, vMock, cServiceMock, nbaMock, sServiceMock),
                new CustomerIdVerification(lFactMock, lServiceMock, smMock, vMock, sServiceMock)
            );
        var fDialogContainer =
            new FinancialsDialogContainer
            (
                new AdvancePaymentAdviceDialog(lFactMock, lServiceMock, smMock, cServiceMock, fServiceMock, sServiceMock),
                new AdvancePaymentAmountDialog(lFactMock, lServiceMock, smMock, cServiceMock, fServiceMock, sServiceMock),
                new GiroCardStepDialog(lFactMock, lServiceMock, smMock, vMock, fServiceMock, cServiceMock, sServiceMock),
                new AdvancePaymentDayDialog(lFactMock, lServiceMock, smMock, vMock, cServiceMock, fServiceMock, sServiceMock),
                new PaymentArrangementDialog(lFactMock, lServiceMock, smMock, cServiceMock, fServiceMock, sServiceMock)
            );
        var pDialogContainer =
            new ProductsDialogContainer
            (
                new ProductRatesDialog(lFactMock, lServiceMock, smMock, cServiceMock, pServiceMock, sServiceMock),
                new ProductEndDatesDialog(lFactMock, lServiceMock, smMock, cServiceMock, pServiceMock, sServiceMock),
                new ProductEndDatesAdviceDialog(lFactMock, lServiceMock, smMock, cServiceMock, pServiceMock, sServiceMock),
                new KetelComfortAppointmentDialog(lFactMock, lServiceMock, smMock, pServiceMock, sServiceMock),
                new DiscontinueToonDialog(lFactMock, lServiceMock, smMock, pServiceMock, sServiceMock),
                new DiscontinueServiceContractDialog(lFactMock, lServiceMock, smMock, pServiceMock, sServiceMock),
                new CreateServiceOrderDialog(lFactMock, lServiceMock, smMock, pServiceMock, sServiceMock, cMock),
                new NextBestActionDialog(lFactMock, smMock, lServiceMock, nbaMock, cServiceMock, sServiceMock),
                new ProductFineCalculationDialog(lFactMock, lServiceMock, smMock, vMock, cServiceMock, pServiceMock, productFineCalcutionServiceMock, sServiceMock, optionsMock.Object),
                new ZonOpDakDialog(lFactMock, lServiceMock, smMock, cServiceMock, pServiceMock, sServiceMock)
            );
        var uDialogContainer =
        new UsagesDialogContainer
        (
                new ProductUsagesDialog(lFactMock, lServiceMock, smMock, cServiceMock, uServiceMock, sServiceMock),
                new ReadingsReportRequestDialog(lFactMock, lServiceMock, smMock, vMock, uServiceMock, sServiceMock),
                new SaveReadingPersonalDialog(lFactMock, smMock, lServiceMock, uServiceMock, sServiceMock, cServiceMock, new FakeTimeProvider()),
                new IsmaMandateDialog(lFactMock, smMock, lServiceMock, uServiceMock, cServiceMock, sServiceMock)
        );
        var aDialogContainer =
            new UserAccountsDialogContainer
            (
                new UsernameDialog(lFactMock, lServiceMock, smMock, uaServiceMock, sServiceMock)
            );

        return new DialogContainer(cDialogContainer, vDialogContainer, fDialogContainer, pDialogContainer, uDialogContainer, aDialogContainer);
    }

    internal static void GetAllTextLabelsFromExport()
    {
        foreach (var groupName in TextLabelGroups.AllGroupNames)
        {
            if (TextLabelModels?.Any(x => x.GroupName == groupName) == false)
            {
                TextLabelModels.AddRange(GetTextLabelFromExport(groupName));
            }
        }
    }

    internal static List<TextLabelModel> GetTextLabelFromExport(string groupName)
    {
        var exportFileName = OperatingSystem.IsWindows() ? $"Exports\\{groupName}.json" : $"Exports/{groupName}.json";

        if (File.Exists(exportFileName))
        {
            var json = File.ReadAllText(exportFileName);
            return JsonConvert.DeserializeObject<List<TextLabelModel>>(json);
        }

        return new List<TextLabelModel>();
    }

    internal static string GetTextLabelValue(string key, string groupName)
    {
        var text = TextLabelModels?.FirstOrDefault(x => x.Key == key && x.GroupName == groupName)?.Value ?? string.Empty;
        if (text.Contains('\n') || text.Contains('\r') || text.Contains("\r\n"))
            text = text
                .Replace("\r\n", "  \n  \n ")
                .Replace("\r", "  \n ")
                .Replace("\n", "  \n ");
        return text;
    }

    private static IConfiguration GetConfig()
    {
        Dictionary<string, string> inMemorySettings = new()
        {
            {"AllowTextLabelsExport", "false"},
        };

        return new ConfigurationBuilder()
            .AddInMemoryCollection(inMemorySettings)
            .Build();
    }
}
