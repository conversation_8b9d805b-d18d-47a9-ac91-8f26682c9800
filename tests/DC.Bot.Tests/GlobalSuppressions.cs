﻿// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.

using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Performance", "CA1861:Avoid constant arrays as arguments", Justification = "<Pending>", Scope = "member", Target = "~M:DC.Bot.Tests.Dialogs.Customers.ChangePhoneNumberDialogTests.Customer_DoesntWantToAdjustAnyPhoneNumber_returns_correct_answer(System.String[],System.String[],System.Boolean,System.Boolean)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Performance", "CA1861:Avoid constant arrays as arguments", Justification = "<Pending>", Scope = "member", Target = "~M:DC.Bot.Tests.Dialogs.Customers.ChangePhoneNumberDialogTests.Customer_WithNoPhonenumbers_WantsToAddNumber_returns_correct_answer(System.String[],<PERSON>.<PERSON>,System.Boolean)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Performance", "CA1861:Avoid constant arrays as arguments", Justification = "<Pending>", Scope = "member", Target = "~M:DC.Bot.Tests.Dialogs.Customers.ChangePhoneNumberDialogTests.Customer_WithNoPhonenumbers_WantsToAddNumber_WithNBA__returns_correct_answer(System.String[],System.Boolean,System.Boolean)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Performance", "CA1861:Avoid constant arrays as arguments", Justification = "<Pending>", Scope = "member", Target = "~M:DC.Bot.Tests.Dialogs.Customers.ChangePhoneNumberDialogTests.Customer_WantsToChange_Phonenumber_returns_correct_answer(System.String[],System.String[],System.Boolean,System.Boolean)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Performance", "CA1861:Avoid constant arrays as arguments", Justification = "<Pending>", Scope = "member", Target = "~M:DC.Bot.Tests.Extensions.DialogDataExtenionsTests.CheckApprovedPhoneNumbers_Test(System.String[],System.Int32)")]
