﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Models.Request;
using DC.Domain.Models.Bot;
using FluentAssertions;
using System;
using System.Collections.Generic;
using Xunit;

namespace DC.Bot.Tests.Extensions;

public class StorageExtensionsTests
{
    [Fact]
    public void Conversation_ToStorageModel_WorksAsExpected()
    {
        var request = new Conversation
        {
            CustomerId = "12345",
            SessionId = "session1",
            TransactionName = "transactionName",
            History = null
        };
        var result = request.ToStorageModel().Data;
        result.CustomerId.Should().Be(12345);
        result.ExternalSessionId.Should().Be("session1");
        result.TransactionName.Should().Be("transactionName");
        result.Messages.Should().BeNull();

        var dateTimeOffset = new DateTimeOffset(new DateTime(2021, 1, 1));
        request.History = new List<Message>
        {
            new Message
            {
                Body = "Hoi",
                FromClient = false,
                OccurredAt = dateTimeOffset.ToUnixTimeMilliseconds() * 1000 + 456, // random modulo, should not make a difference
                Type = "message"
            }
        };
        result = request.ToStorageModel().Data;
        result.Messages.Should().HaveCount(1);
    }

    [Fact]
    public void Message_ToStorageModel_WorksAsExpected()
    {
        var dateTimeOffset = new DateTimeOffset(new DateTime(2021, 1, 1));
        var request = new Message
        {
            Body = "Hoi",
            FromClient = false,
            OccurredAt = dateTimeOffset.ToUnixTimeMilliseconds() * 1000 + 123, // random modulo, should not make a difference
            Type = "message"
        };
        var result = request.ToStorageModel();
        result.Message.Should().Be("Hoi");
        result.Source.Should().Be(MessageSource.Bot);
        result.Timestamp.Should().BeCloseTo(dateTimeOffset.UtcDateTime, dateTimeOffset.Offset);
        result.Type.Should().Be("message");
    }
}