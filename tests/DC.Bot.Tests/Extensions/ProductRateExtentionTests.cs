﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Domain.Models.Products;
using DC.Domain.Models.Products.ProductRates;
using DC.Domain.Models.Usages;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using Xunit;

namespace DC.Bot.Tests.Extensions;

public class ProductRateExtentionTests
{
    [Fact]
    public void GetProductRateDetailTarrifs_Test()
    {
        Thread.CurrentThread.CurrentCulture = new CultureInfo("nl-NL");
        var productRate = new ProductRate
        {
            ProductType = ProductType.Electricity,
            IsDoubleTariff = true,
            ProductRateDetails = new List<ProductRateDetail>()
        };
        productRate.ProductRateDetails.Add(new ProductRateDetail()
        {
            Type = ProductRateDetailType.Tariff,
            Description = "Normaaltarief",
            VATIncluded = Decimal.Parse("0,23726"),
            ByPeriod = ByPeriod.M,
            DenotationType = DenotationType.Kwh
        });
        productRate.ProductRateDetails.Add(new ProductRateDetail()
        {
            Type = ProductRateDetailType.Huurapparaat,
            Description = "Huurapparaat",
            VATIncluded = 235.21m,
            ByPeriod = ByPeriod.Y
        });
        productRate.ProductRateDetails.Add(new ProductRateDetail()
        {
            Type = ProductRateDetailType.LowTariff,
            Description = "Daltarief",
            VATIncluded = Decimal.Parse("0,2219"),
            ByPeriod = ByPeriod.M,
            DenotationType = DenotationType.Kwh
        });

        var productRateDetails = productRate.GetProductRateDetailTariffs();
        productRateDetails.Should().NotBeNull();
        productRateDetails.Should().Contain("Normaaltarief: €0,23726 per kWh");
        productRateDetails.Should().Contain("Daltarief: €0,2219 per kWh");

        productRate.ProductRateDetails.Last().Type = ProductRateDetailType.KetelComfort;

        productRateDetails = productRate.GetProductRateDetailTariffs();
        productRateDetails.Should().Contain("Normaaltarief: €0,23726 per kWh");
    }

    [Fact]
    public void GetProductRateDetailMonthlyCosts_Test()
    {
        Thread.CurrentThread.CurrentCulture = new CultureInfo("nl-NL");
        var productRate = new ProductRate
        {
            ProductType = ProductType.Electricity,
            IsDoubleTariff = true,
            ProductRateDetails = new List<ProductRateDetail>
            {
                new ProductRateDetail()
                {
                    Description = "Normaaltarief",
                    VATIncluded = decimal.Parse("0,23726"),
                    ByPeriod = ByPeriod.M
                },
                new ProductRateDetail()
                {
                    Description = "Daltarief",
                    VATIncluded = decimal.Parse("0,2219"),
                    ByPeriod = ByPeriod.M
                }
            }
        };

        var detailsMonthleCosts = productRate.GetProductRateDetailCostsByPeriod(ByPeriod.M);
        detailsMonthleCosts.Should().NotBeNull();
        detailsMonthleCosts.Should().Contain("Daltarief: €0,2219 per maand");
        detailsMonthleCosts.Should().Contain("Normaaltarief: €0,23726 per maand");

        productRate.ProductRateDetails.Last().ByPeriod = ByPeriod.Y;
        detailsMonthleCosts = productRate.GetProductRateDetailCostsByPeriod(ByPeriod.M);
        detailsMonthleCosts.Should().Contain("Normaaltarief: €0,23726 per maand");
    }

    [Fact]
    public void GetProductRateDoubleTariff_Test()
    {
        Thread.CurrentThread.CurrentCulture = new CultureInfo("nl-NL");
        var productRate = new ProductRate
        {
            ProductType = ProductType.Electricity,
            IsDoubleTariff = true
        };
        productRate.HasDoubleTariffIndicator("Dubbeltarief: {doubleTariff}").Should().Be("Dubbeltarief: Ja");
        productRate.IsDoubleTariff = false;
        productRate.HasDoubleTariffIndicator("Dubbeltarief: {doubleTariff}").Should().Be("Dubbeltarief: Nee");
        productRate.IsDoubleTariff = true;
        productRate.ProductType = ProductType.Gas;
        productRate.HasDoubleTariffIndicator("Dubbeltarief: {doubleTariff}").Should().Be(string.Empty);
    }
    [Theory]
    [InlineData(ProductType.Electricity, ProductRateDetailType.Tariff)]
    [InlineData(ProductType.Electricity, ProductRateDetailType.LowTariff)]
    [InlineData(ProductType.Gas, ProductRateDetailType.Tariff)]
    [InlineData(ProductType.Warmth, ProductRateDetailType.Tariff)]

    public void IsMeasuredProductType_Test(ProductType productType, ProductRateDetailType productRateDetailType)
    {
        var productRate = new ProductRate
        {
            ProductType = productType,
            ProductRateDetails = new List<ProductRateDetail>()
        };
        productRate.ProductRateDetails.Add(new ProductRateDetail
        {
            Type = productRateDetailType,
            IsVariable = false,
            VATExcluded = 0,
            VATIncluded = 0,
            VAT = 0
        });
        productRate.IsMeasuredProductType().Should().BeTrue();
    }
    [Theory]
    [InlineData(ProductType.Huurapparaat, ProductRateDetailType.Huurapparaat)]
    [InlineData(ProductType.ServiceCVKetel, ProductRateDetailType.ServiceCVKetel)]
    [InlineData(ProductType.WarmteWisselaar, ProductRateDetailType.WarmtePomp)]
    public void IsNonMeasuredProductType_Test(ProductType productType, ProductRateDetailType productRateDetailType)
    {
        var productRate = new ProductRate
        {
            ProductType = productType,
            ProductRateDetails = new List<ProductRateDetail>()
        };
        productRate.ProductRateDetails.Add(new ProductRateDetail
        {
            Type = productRateDetailType,
            IsVariable = false,
            VATExcluded = 0,
            VATIncluded = 0,
            VAT = 0
        });
        productRate.IsNonMeasuredProductType().Should().BeTrue();
    }

    [Fact]
    public void IsStukjeZonProductType_Test()
    {
        Thread.CurrentThread.CurrentCulture = new CultureInfo("nl-NL");
        var productRate = new ProductRate
        {
            Name = "Stukje zon",
            ProductType = ProductType.StukjeZon,
            ProductRateDetails = new List<ProductRateDetail>()
        };
        productRate.ProductRateDetails.Add(new ProductRateDetail()
        {
            Type = ProductRateDetailType.StukjeZon,
            Description = "Stukje zon",
            VAT = (decimal)-1.25,
            ByPeriod = ByPeriod.M,
            IsVariable = false
        });
        productRate.IsStukjeZonProductType().Should().BeTrue();
    }

    [Fact]
    public void GetNonVariableProductRateDetails_Test()
    {
        Thread.CurrentThread.CurrentCulture = new CultureInfo("nl-NL");
        var productRate = new ProductRate
        {
            Name = "Abonnementskosten KetelComfort Standaard",
            ProductType = ProductType.KetelComfort,
            ProductRateDetails = new List<ProductRateDetail>
            {
                new ProductRateDetail()
                {
                    Type = ProductRateDetailType.KetelComfort,
                    Description = "Standaard Service - en onderhoudsabonnement",
                    VATIncluded = (decimal)8.95,
                    ByPeriod = ByPeriod.M,
                    IsVariable = false
                }
            }
        };
        productRate.GetNonVariableProductRateDetails().Should().Contain("Standaard Service - en onderhoudsabonnement");
    }

    [Fact]
    public void GetNonVariableProductRateDetails_For_BronWarmte_Test()
    {
        Thread.CurrentThread.CurrentCulture = new CultureInfo("nl-NL");
        var productRate = new ProductRate
        {
            Name = "Abonnementskosten KetelComfort Standaard",
            ProductType = ProductType.BronWarmte,
            ProductRateDetails = new List<ProductRateDetail>
            {
                new ProductRateDetail()
                {
                    Type = ProductRateDetailType.KetelComfort,
                    Description = "Standaard Service - en onderhoudsabonnement",
                    VATIncluded = (decimal)8.95,
                    ByPeriod = ByPeriod.M,
                    IsVariable = false
                }
            }
        };
        productRate.GetNonVariableProductRateDetails().Should().Contain("Standaard Service - en onderhoudsabonnement");
    }

    [Fact]
    public void GetProductRateDetailRedeliveryCostTariffs_Test()
    {
        Thread.CurrentThread.CurrentCulture = new CultureInfo("nl-NL");
        var productRate = new ProductRate
        {
            ProductType = ProductType.Electricity,
            IsDoubleTariff = true,
            ProductRateDetails = new List<ProductRateDetail>()
        };
        productRate.ProductRateDetails.Add(new ProductRateDetail()
        {
            Type = ProductRateDetailType.RedeliveryCostTariff,
            Description = "Terugleverkosten",
            VATIncluded = Decimal.Parse("0,115"),
            ByPeriod = ByPeriod.M,
            DenotationType = DenotationType.Kwh
        });

        var productRateDetails = productRate.GetProductRateDetailRedeliveryCostTariffs();
        productRateDetails.Should().NotBeNull();
        productRateDetails.Should().Contain("Terugleverkosten: €0,115 per kWh");
    }
}