﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Models;
using DC.Customers.Client.Models;
using DC.Domain.Models.Accounts;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.Customers;
using DC.Domain.Models.Products;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using Xunit;

namespace DC.Bot.Tests.Extensions;

public class CustomerExtensionsTests
{
    [Theory]
    [InlineData(CustomerType.Organisation, "A", "Albert", "")]
    [InlineData(CustomerType.Person, "A", "<PERSON>", "<PERSON>")]
    [InlineData(CustomerType.Person, "A", null, "A")]
    public void GetFirstname_Test(CustomerType ctype, string initials, string firstname, string expected)
    {
        var model = new CustomerModel
        {
            Person = new PersonModel
            {
                Initials = initials,
                SurnamePreposition = "van der",
                Surname = "Test",
                Name = firstname
            },
            Organisation = new OrganisationModel
            {
                Name = "Bedrijf"
            },
            CustomerType = ctype
        };
        model.GetFirstname().Should().Be(expected);
    }

    [Theory]
    [InlineData(CustomerType.Organisation, "A", "Albert", "", "<PERSON>", "Bedrijf")]
    [InlineData(CustomerType.Person, "A", null, null, "Test", "A Test")]
    [InlineData(CustomerType.Person, "A", "Albert Test", "van der", "Test", "A van der Test")]
    [InlineData(CustomerType.Person, null, "A van der Test", "van der", "Test", "van der Test")]
    [InlineData(CustomerType.Person, null, null, "van der", "Test", "van der Test")]
    [InlineData(CustomerType.Person, "A", null, "van der", "Test", "A van der Test")]
    public void GetName_Test(CustomerType ctype, string initials, string firstname, string surnamePreposition, string surname, string expected)
    {
        var model = new CustomerModel
        {
            Person = new PersonModel
            {
                Initials = initials,
                SurnamePreposition = surnamePreposition,
                Surname = surname,
                Name = firstname
            },
            Organisation = new OrganisationModel
            {
                Name = "Bedrijf"
            },
            CustomerType = ctype
        };
        model.GetName().Should().Be(expected);
    }

    [Theory]
    [InlineData(CustomerType.Organisation, "A", "Albert", "Bedrijf")]
    [InlineData(CustomerType.Person, "van der", "Test", "van der Test")]
    [InlineData(CustomerType.Person, null, "Test", "Test")]
    public void GetSurname_Test(CustomerType ctype, string surnamePreposition, string surname, string expected)
    {
        var model = new CustomerModel
        {
            Person = new PersonModel
            {
                Initials = "A",
                SurnamePreposition = surnamePreposition,
                Surname = surname,
                Name = "Albert"
            },
            Organisation = new OrganisationModel
            {
                Name = "Bedrijf"
            },
            CustomerType = ctype
        };
        model.GetSurname().Should().Be(expected);
    }

    [Theory]
    [InlineData(CustomerType.Organisation, Gender.Male, Domain.Models.Customers.Gender.Unknown)]
    [InlineData(CustomerType.Person, Gender.Male, Domain.Models.Customers.Gender.Male)]
    [InlineData(CustomerType.Person, Gender.Female, Domain.Models.Customers.Gender.Female)]
    [InlineData(CustomerType.Person, Gender.Unknown, Domain.Models.Customers.Gender.Unknown)]
    public void GetGender_Test(CustomerType ctype, Gender gender, Domain.Models.Customers.Gender expected)
    {
        var model = new CustomerModel
        {
            Person = new PersonModel
            {
                Gender = gender
            },
            Organisation = new OrganisationModel
            {
            },
            CustomerType = ctype
        };
        model.GetGender().Should().Be(expected);
    }

    [Fact]
    public void GetName_Organisation_Test()
    {
        var model = new CustomerModel
        {
            Organisation = new OrganisationModel
            {
                LegalForm = new OrganisationLegalFormModel
                {
                    Code = "VoF"
                },
                Name = "Testbedrijf"
            },
            CustomerType = CustomerType.Organisation
        };
        model.GetName().Should().Be("Testbedrijf");
    }

    [Fact]
    public void ToCustomerVerificationRequest_Test()
    {
        var userData = new DialogData
        {
            Verification = new VerificationData
            {
                CustomerId = 123,
                DateOfBirth = new DateTime(1987, 5, 23),
                HouseNumber = 4,
                IbanSuffix = "5678",
                PhoneNumber = "0699887766",
                PostalCode = "9876AB"
            }
        };
        var result = userData.ToCustomerVerificationRequest();
        result.Data.Addresses.Should().HaveCount(1);
        result.Data.Addresses.First().PostalCode.Should().Be("9876AB");
        result.Data.Addresses.First().HouseNumber.Should().Be(4);
        result.Data.DatesOfBirth.Should().HaveCount(1);
        result.Data.DatesOfBirth.First().Should().BeSameDateAs(new DateTime(1987, 5, 23));
        result.Data.IbanSuffixes.Should().HaveCount(1);
        result.Data.IbanSuffixes.First().Should().Be("5678");
        result.Data.CustomerId.Should().Be(123);
        result.Data.PhoneNumber.Should().Be("0699887766");
        userData.Verification.PhoneNumber = "    ";
        userData.ToCustomerVerificationRequest().Data.PhoneNumber.Should().BeNull();
    }

    [Fact]
    public void IsZZP_Test()
    {
        var customer = new Customer()
        {
            CustomerType = CustomerType.Person,
            CustomerSegment = ""
        };

        customer.IsZzp().Should().Be(false);

        customer.CustomerType = CustomerType.Organisation;
        customer.IsZzp().Should().Be(false);

        customer.CustomerSegment = "ZzZP";
        customer.IsZzp().Should().Be(false);

        customer.CustomerSegment = "ZZP";
        customer.IsZzp().Should().Be(true);

        customer.CustomerSegment = "ZZP Woonbedrijf";
        customer.IsZzp().Should().Be(true);

        customer.CustomerType = CustomerType.Person;
        customer.IsZzp().Should().Be(false);
    }

    [Theory]
    [InlineData(CustomerType.Person, true, true, false, false, null, "1900-01-01", false, ProductType.Electricity, ProductType.Gas, ProductType.Warmth)]
    [InlineData(CustomerType.Organisation, false, true, true, false, "2025-10-10", "", false, ProductType.Electricity, ProductType.Gas)]
    [InlineData(CustomerType.Organisation, false, false, false, false, "2025-10-10", "2000-02-01", false, ProductType.Warmth, ProductType.Tapwater)]
    [InlineData(CustomerType.Person, false, false, false, false, null, null, true, ProductType.Electricity, ProductType.WarmthEkv)]
    [InlineData(CustomerType.Person, false, true, true, false, "2025-10-10", "", false, ProductType.Electricity, ProductType.Gas)]
    [InlineData(CustomerType.Person, false, true, true, true, "2025-10-10", "", true, ProductType.Electricity, ProductType.Gas)]
    [InlineData(CustomerType.Person, false, true, true, true, "+60days", "", false, ProductType.Electricity, ProductType.Gas)]
    public void ToPersonalisationInfo_WorksAsExpected(
        CustomerType customerType,
        bool hasRedelivery,
        bool hasSmartMeter,
        bool smartAllowed,
        bool hasDynamicPricing,
        string nextChargeDateStr,
        string dateOfBirth,
        bool indefiniteProduct,
        params ProductType[] productTypes)
    {
        DateTime? dayOfBirth = null;
        if (dateOfBirth == "")
            dayOfBirth = DateTime.Today.AddYears(-25);
        else if (!string.IsNullOrEmpty(dateOfBirth))
            dayOfBirth = DateTime.Parse(dateOfBirth);

        DateTime? nextChargeDate = (DateTime?)null;
        if (nextChargeDateStr == "+60days")
        {
            nextChargeDate = DateTime.Now.AddDays(60);
        }
        else if (!string.IsNullOrEmpty(nextChargeDateStr))
        {
            nextChargeDate = DateTime.Parse(nextChargeDateStr);
        }

        var customer = new CustomerModel
        {
            Id = 1,

            CustomerType = customerType,
            Person = customerType == CustomerType.Person ? new PersonModel
            {
                DateOfBirth = dayOfBirth,
                Gender = Gender.Male,
                Initials = "Firstname",
                Surname = "Surname",
            } : null,
            Contact = new ContactModel
            {
                EmailAddress = "<EMAIL>"
            },
            Organisation = customerType == CustomerType.Organisation ? new OrganisationModel
            {
                Name = "OrganisationName"
            } : null,
            Accounts = new List<CustomerAccountModel>
            {
                new CustomerAccountModel
                {
                    Id = 1,
                    Active = true,
                    HasRedelivery = hasRedelivery,
                    MeterDetails = productTypes.Select(p => new MeterDetail
                    {
                        IsSmartMeter = hasSmartMeter,
                        IsSmartMeterReadingAllowed = smartAllowed,
                        ProductType = p.ToString()
                    }).ToList(),
                    NextChargeDate = nextChargeDate,
                    CanInsertReadings = !hasSmartMeter
                }
            }
        };
        var products = productTypes.Select(p => new Domain.Models.Products.ProductModel
        {
            IsActive = true,
            AccountId = 1,
            EndDate = nextChargeDate,
            EndDateContract = nextChargeDate,
            Indefinite = indefiniteProduct,
            Type = new Domain.Models.Products.ProductTypeModel
            {
                Name = p
            }
        }).ToList();

        var paymentPlans = new List<PaymentPlan>()
        {
            new PaymentPlan()
            {
                NextBillingCycle = new BillingCycle()
                {
                    ChargeDate = nextChargeDate,
                }
            }
        };

        var agreements = productTypes.Select(p => new Agreement
        {
            AccountId = 1,
            IsActive = true,
            Status = AgreementStatus.AKT,
            Products = new List<Product>
            {
                new Product {
                    AccountId= 1,
                    AgreementId = 1,
                    ProductType = p,
                    IsActive = true,
                    IsDynamicPricing = hasDynamicPricing,
                    EndDateContract= nextChargeDate,
                    ProductOffering = new ProductOffering()
                    {
                        IsIndefiniteDuration = indefiniteProduct
                    },
                    ProductSpecification = new ProductSpecification()
                    {
                        ProductType = ResolveProductType(p),
                    }
                }
            },
            Connection = new Connection
            {
                ConnectionPointEan = "ean",
                AccountId = 1,
                GridOperator = new DistributionNetworkOperator
                {
                    Name = "netbeheerder"
                }
            }
        }
        ).ToList();

        var orders = new List<OrderResponseModel>()
        {
            //Gas coolingOff = false
            new OrderResponseModel
            {
                ConsiderationEndDate = DateTime.Now.AddDays(-20),
                OrderItemsProductTypes = new List<ProductType?>() { ProductType.Gas }
            },
            //Electricity coolingOff = true
            new OrderResponseModel
            {
                ConsiderationEndDate = DateTime.Now.AddDays(+1),
                OrderItemsProductTypes = new List<ProductType?>() { ProductType.Electricity }
            }
        };

        var result = customer.ToPersonalisationInfo(products, paymentPlans, agreements, orders, true);
        result.CustomerId.Should().Be(customer.Id);
        if (customerType == CustomerType.Organisation)
            result.Firstname.Should().Be("");
        else
            result.Firstname.Should().Be("Firstname");
        if (customerType == CustomerType.Organisation)
            result.FirstnameLegacy.Should().Be("OrganisationName");
        else
            result.FirstnameLegacy.Should().Be("Firstname Surname");
        if (customerType == CustomerType.Organisation)
            result.Surname.Should().Be("OrganisationName");
        else
            result.Surname.Should().Be("Surname");
        if (customerType == CustomerType.Organisation)
            result.Gender.Should().Be(Gender.Unknown);
        else
            result.Gender.Should().Be(Gender.Male);
        if (dateOfBirth == "" && customerType == CustomerType.Person)
            result.DateOfBirthToday.Should().BeTrue();
        else
            result.DateOfBirthToday.Should().BeFalse();

        result.Email.Should().Be("<EMAIL>");
        result.IsOrganisation.Should().Be(customerType == CustomerType.Organisation);
        result.HasElectricity.Should().Be(productTypes.Contains(ProductType.Electricity));
        result.HasGas.Should().Be(productTypes.Contains(ProductType.Gas));
        result.HasToon.Should().Be(productTypes.Contains(ProductType.Toon));
        result.HasTapwater.Should().Be(productTypes.Contains(ProductType.Tapwater));
        result.HasWarmth.Should().Be(productTypes.Contains(ProductType.Warmth) || productTypes.Contains(ProductType.WarmthEkv));
        result.HasRedelivery.Should().Be(hasRedelivery);
        if (productTypes.Contains(ProductType.Electricity))
            result.HasReadableSmartElectricityMeter.Should().Be(hasSmartMeter && smartAllowed);
        else
            result.HasReadableSmartElectricityMeter.Should().BeNull();
        if (productTypes.Contains(ProductType.Gas))
            result.HasReadableSmartGasMeter.Should().Be(hasSmartMeter && smartAllowed);
        else
            result.HasReadableSmartGasMeter.Should().BeNull();
        if (productTypes.Contains(ProductType.Warmth))
            result.HasReadableSmartWarmthMeter.Should().Be(hasSmartMeter && smartAllowed);
        else if (productTypes.Contains(ProductType.WarmthEkv))
            result.HasReadableSmartWarmthMeter.Should().BeFalse();
        else
            result.HasReadableSmartWarmthMeter.Should().BeNull();
        result.HasNextChargeDate.Should().Be(nextChargeDate != null);
        result.ElectricityIsIndefinite.Should().Be(productTypes.Contains(ProductType.Electricity) && indefiniteProduct);
        result.GasIsIndefinite.Should().Be(productTypes.Contains(ProductType.Gas) && indefiniteProduct);
        result.ElectricityDynamicPricing.Should().Be(productTypes.Contains(ProductType.Electricity) && hasDynamicPricing);
        result.GasDynamicPricing.Should().Be(productTypes.Contains(ProductType.Gas) && hasDynamicPricing);
        result.InDebtCollection.Should().BeFalse();
        result.MeterMalfunction.Should().BeTrue();
        result.MeterDataRequest.Should().Be(!hasSmartMeter);
        if (hasSmartMeter && !smartAllowed)
            result.HasActiveMandate.Should().BeFalse();
        else if (!hasSmartMeter)
            result.HasActiveMandate.Should().BeNull();
        else
            result.HasActiveMandate.Should().BeTrue();
        if (productTypes.Contains(ProductType.Gas) || productTypes.Contains(ProductType.Electricity) || productTypes.Contains(ProductType.Warmth))
        {
            result.Netbeheerder.Should().Be("netbeheerder");
            result.NetbeheerderNames.Should().Be("Other");
        }
        else
        {
            result.Netbeheerder.Should().BeNull();
            result.NetbeheerderNames.Should().Be("Unknown");
        }

        result.CoolingOffGas.Should().BeFalse();
        result.CoolingOffElectricity.Should().BeTrue();

        if (nextChargeDateStr == "+60days")
        {
            result.AfcoElectricity.Should().BeTrue();
            result.AfcoGas.Should().BeTrue();
        }
        else
        {
            result.AfcoElectricity.Should().BeFalse();
            result.AfcoGas.Should().BeFalse();
        }
    }

    private static string ResolveProductType(ProductType productType)
    {
        return productType switch
        {
            ProductType.Electricity => "ELK",
            ProductType.Gas => "GAS",
            _ => "Unknown",
        };
    }

    [Theory]
    [InlineData("Stedin Netbeheer B.V.", "Stedin")]
    [InlineData("Endinet B.V.", "Endinet")]
    [InlineData("Coteq Netbeheer B.V.", "Coteq")]
    [InlineData("N.V. RENDO", "Rendo")]
    [InlineData("Westland Infra Netbeheer B.V.", "WestlandInfraNet")]
    [InlineData("Enexis B.V.", "Enexis")]
    [InlineData("Liander N.V.", "Liander")]
    [InlineData("Delta N.V.", "Delta")]
    [InlineData("Cogas", "Cogas")]
    [InlineData("Eneco", "Other")]
    [InlineData("", "Unknown")]
    [InlineData(null, "Unknown")]
    public void GetGridGridOperatorNames_WorksAsExpected(string input, string expected)
    {
        input.GetGridGridOperatorNames().Should().Be(expected);
    }

    [Theory]
    [InlineData(false, null, "")]
    [InlineData(true, "HR1", null)]
    [InlineData(true, "HR14")]
    [InlineData(true, "HR26", null)]
    [InlineData(true, "HR32")]
    [InlineData(true, "HR40", null, null)]
    [InlineData(true, "HR41A")]
    [InlineData(true, "HR41b")]
    [InlineData(false, "HR41C", null)]
    [InlineData(false, "none")]
    [InlineData(false, "empty")]
    public void DetermineDebtCollection_WorksAsExpected(bool expected, params string[] states)
    {
        var paymentPlans = new List<PaymentPlan>();
        if (states[0] == "none")
            paymentPlans = null;
        else if (states[0] != "empty")
        {
            foreach (var state in states)
            {
                paymentPlans.Add(new PaymentPlan
                {
                    DebtCollection = new DebtCollection
                    {
                        ProcessStep = new DebtCollectionActiveProcessStep
                        {
                            State = state
                        }
                    }
                });
            }
        }

        paymentPlans.DetermineDebtCollection().Should().Be(expected);
    }

    [Fact]
    public void GetAccount_WorksAsExpected()
    {
        var customer = new CustomerModel
        {
            Accounts = new List<CustomerAccountModel>
                {
                    new CustomerAccountModel
                    {
                        Active = true,
                        Address = new AddressModel
                        {
                            PostalCode = "1234ab",
                            HouseNumber = 1
                        }
                    },
                    new CustomerAccountModel
                    {
                        Active = false,
                        Address = new AddressModel
                        {
                            PostalCode = "4321zy",
                            HouseNumber = 26
                        }
                    }
                }
        };

        var dialogdata = new DialogData
        {
            Verification = new VerificationData
            {
                PostalCode = "1234ab",
                HouseNumber = 1
            }
        };

        var result = dialogdata.GetAccount(customer);
        result.Should().NotBeNull();
        result.Active.Should().BeTrue();
        result.Address.Should().NotBeNull();
        result.Address.PostalCode.Should().Be("1234ab");
        result.Address.HouseNumber.Should().Be(1);

        dialogdata.Verification.PostalCode = "4321zy";
        dialogdata.Verification.HouseNumber = 26;

        // result should be null because GetAccount only retrieves an active account.
        result = dialogdata.GetAccount(customer);
        result.Should().BeNull();
    }

    [Theory]
    [InlineData("**********", "**********", 2)]
    [InlineData("**********", null, 1)]
    [InlineData(null, "**********", 1)]
    [InlineData(null, null, 0)]
    public void GetPhoneNumbers_Test(string mobilePhoneNumber, string phoneNumber, int count)
    {
        var model = new CustomerModel
        {
            Contact = new ContactModel
            {
                PhoneNumber = phoneNumber,
                MobilePhoneNumber = mobilePhoneNumber
            }
        };

        model.GetPhoneNumbers().Should().HaveCount(count);
    }
}