﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Usages.Client.Models;
using FluentAssertions;
using Xunit;

namespace DC.Bot.Tests.Extensions;

public class UsageExtensionsTests
{
    [Fact]
    public void GetDescriptionByMonth_Test()
    {
        var usage = new UsageItem
        {
            Low = (decimal)151.43,
            High = (decimal)186.21,
            IsDoubleTariff = true
        };

        var description = usage.GetDescriptionByMonth(5, "kWh");
        description.Should().Be("mei: dal 151,43 kWh / normaal 186,21 kWh");

        usage.IsDoubleTariff = false;
        description = usage.GetDescriptionByMonth(5, "kWh");
        description.Should().Be("mei: 186,21 kWh");

        usage.High = (decimal)205.62;
        description = usage.GetDescriptionByMonth(10, "kWh");
        description.Should().Be("oktober: 205,62 kWh");
    }
}