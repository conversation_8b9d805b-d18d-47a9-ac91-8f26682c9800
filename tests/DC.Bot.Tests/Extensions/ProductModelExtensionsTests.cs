﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Domain.Models.Products;
using FluentAssertions;
using System;
using System.Collections.Generic;
using Xunit;

namespace DC.Bot.Tests.Extensions;

public class ProductModelExtensionsTests
{
    [Fact]
    public void GetEndDateStringTest()
    {
        var productModel = new ProductModel
        {
            EndDateContract = null,
            EndDatePrice = new DateTime(2020, 1, 1)
        };
        productModel.GetEndDateString().Should().NotBeNull();
        productModel.GetEndDateString().Should().Contain("onbepaalde tijd");

        productModel.EndDateContract = new DateTime(2020, 1, 1);
        productModel.GetEndDateString().Should().NotBeNull();
        productModel.GetEndDateString().Should().Contain("01-01-2020");

        productModel.EndDateContract = null;
        productModel.GetEndDateString().Should().NotBeNull();
        productModel.GetEndDateString().Should().Contain("onbepaalde tijd");
    }

    [Theory]
    [InlineData(ProductType.Warmth, true)]
    [InlineData(ProductType.WarmthProducts, true)]
    [InlineData(ProductType.Toon, false)]
    public void OnlyWarmthElements_returns_model(ProductType type, bool output)
    {
        var input = new List<ProductModel>
        {
            new ProductModel
            {
                Type = new ProductTypeModel
                {
                    Name = type
                }
            }
        };
        input.OnlyWarmthElements().Should().Be(output);
    }

    [Theory]
    [InlineData(ProductType.Electricity, null, true)]
    [InlineData(ProductType.Gas, null, false)]
    [InlineData(ProductType.Gas, ProductType.Electricity, false)]
    public void OnlyElectricity_returns_model(ProductType type, ProductType? type2, bool output)
    {
        var product2 = type2.HasValue ?
            new ProductModel
            {
                Type = new ProductTypeModel
                {
                    Name = type2.Value
                }
            } : null;
        var input = new List<ProductModel>
        {
            new ProductModel
            {
                Type = new ProductTypeModel
                {
                    Name = type
                }
            }
        };
        if (product2 != null)
            input.Add(product2);
        input.OnlyElectricity().Should().Be(output);
    }

    [Theory]
    [InlineData(ProductType.Electricity, null, false)]
    [InlineData(ProductType.Warmth, null, false)]
    [InlineData(ProductType.Gas, ProductType.Electricity, false)]
    [InlineData(ProductType.Warmth, ProductType.Electricity, true)]
    public void OnlyWarmthElementsAndElectricity_returns_model(ProductType type, ProductType? type2, bool output)
    {
        var product2 = type2.HasValue ?
            new ProductModel
            {
                Type = new ProductTypeModel
                {
                    Name = type2.Value
                }
            } : null;
        var input = new List<ProductModel>
        {
            new ProductModel
            {
                Type = new ProductTypeModel
                {
                    Name = type
                }
            }
        };
        if (product2 != null)
            input.Add(product2);

        input.OnlyWarmthElementsAndElectricity().Should().Be(output);
    }

    [Theory]
    [InlineData(ProductType.Warmth, true)]
    [InlineData(ProductType.Electricity, true)]
    [InlineData(ProductType.Gas, true)]
    [InlineData(ProductType.Toon, false)]
    [InlineData(ProductType.WarmteWisselaar, false)]
    [InlineData(ProductType.Admin, false)]
    [InlineData(ProductType.KetelComfort, false)]
    public void GetEGWProducts_returns_model(ProductType type, bool output)
    {
        var input = new List<ProductModel>
        {
            new ProductModel
            {
                Type = new ProductTypeModel
                {
                    Name = type
                }
            }
        };
        (input.GetMainEGWProducts().Count > 0).Should().Be(output);
    }
}