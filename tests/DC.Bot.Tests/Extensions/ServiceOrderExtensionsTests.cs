﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Products.Client.Models;
using FluentAssertions;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Extensions;

public class ServiceOrderExtensionsTests
{
    [Theory]
    [InlineData(GroupDescription.Geiser, ServiceOrderProductType.Geiser)]
    [InlineData(GroupDescription.Ventilatorgeiser, ServiceOrderProductType.Geiser)]
    [InlineData(GroupDescription.EBoiler, ServiceOrderProductType.Boiler)]
    [InlineData(GroupDescription.Warmtepompboiler, ServiceOrderProductType.Boiler)]
    [InlineData(GroupDescription.CvHaardofstaandecvKetel, ServiceOrderProductType.CVKetel)]
    [InlineData(GroupDescription.Voorzetbrander, ServiceOrderProductType.CVKetel)]
    [InlineData(GroupDescription.CvKetel, ServiceOrderProductType.CVKetel)]
    [InlineData(GroupDescription.Indirectgestookteboiler, ServiceOrderProductType.Unknown)]
    [InlineData(GroupDescription.Zonneboiler, ServiceOrderProductType.Unknown)]
    [InlineData(GroupDescription.ToonThermostaat, ServiceOrderProductType.Unknown)]
    [InlineData(GroupDescription.Binneninstallatiecv, ServiceOrderProductType.Unknown)]
    [InlineData(GroupDescription.MicroWKKcvKetel, ServiceOrderProductType.Unknown)]
    [InlineData(GroupDescription.GBoiler, ServiceOrderProductType.Unknown)]
    public void ToServiceOrderProductType_WorksAsExpected(GroupDescription product, ServiceOrderProductType productType)
    {
        var result = product.ToServiceOrderProductType();
        result.Should().Be(productType);
    }

    [Theory]
    [InlineData("geiser", ServiceOrderProductType.Geiser)]
    [InlineData("boiler", ServiceOrderProductType.Boiler)]
    [InlineData("", null)]
    [InlineData("cv-ketel", ServiceOrderProductType.CVKetel)]
    [InlineData("cvketel", ServiceOrderProductType.CVKetel)]
    public void ToServiceOrderProductType_HardwareValue_WorksAsExpected(string hardware, ServiceOrderProductType? productType)
    {
        var result = hardware.ToServiceOrderProductType();
        result.Should().Be(productType);
    }

    [Theory]
    [InlineData("Urgent", true)]
    [InlineData("Nee", false)]
    [InlineData("", false)]
    [InlineData(null, false)]
    public void ToServiceOrderUrgent_WorksAsExpected(string urgent, bool expectedResult)
    {
        var result = urgent.ToServiceOrderUrgent();
        result.Should().Be(expectedResult);
    }
}