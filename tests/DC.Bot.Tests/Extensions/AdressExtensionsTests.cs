﻿using DC.Bot.BusinessLogic.Extensions;
using FluentAssertions;
using Xunit;

namespace DC.Bot.Tests.Extensions;

public class AdressExtensionsTests
{
    [Fact]
    public void CityNameToFirstUpperLetter_Test()
    {
        string city = "RotterDam";
        city.CityNameToFirstUpperLetter().Should().Be("Rotterdam");
        city = "'S-HertogenBosch";
        city.CityNameToFirstUpperLetter().Should().Be("'s-Hertogenbosch");
        city = "'S-GRAVENHAGE";
        city.CityNameToFirstUpperLetter().Should().Be("'s-Gravenhage");
        city = "dEn haaG";
        city.CityNameToFirstUpperLetter().Should().Be("Den Haag");
    }
}