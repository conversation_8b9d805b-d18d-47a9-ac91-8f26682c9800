﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Models;
using DC.Repositories.Base.Enumerations;
using DC.Security.Encryption;
using DC.Telemetry.Models;
using FluentAssertions;
using Microsoft.Bot.Schema;
using Microsoft.Extensions.Configuration;
using Moq;
using Newtonsoft.Json;
using System.Dynamic;
using System.Linq;
using Xunit;

namespace DC.Bot.Tests.Extensions;

public class ActivityExtensionsTests
{
    [Fact]
    public void EventActivity_Test()
    {
        var activity = new Activity
        {
            Type = "event",
            Name = "startOfTransaction"
        };
        var eventActivity = activity.AsEventActivity();

        dynamic data = new ExpandoObject();
        data.label = Label.Eneco;
        data.channel = BotChannel.App;
        data.startCommand = "test";
        data.authorization = null;
        data.malfunctionDevice = null;
        data.malfunctionUrgency = null;
        data.malfunctionDescription = null;
        data.isNba = null;
        data.customerId = 1;

        eventActivity.Value = data;
        TransactionStartValue transactionStartValue = eventActivity.GetTransactionStartValue();
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Label.Should().Be(Label.Eneco);
        transactionStartValue.Channel.Should().Be(BotChannel.App);
        transactionStartValue.StartCommand.Should().Be(data.startCommand);
        transactionStartValue.Authorization.Should().BeEmpty();
        transactionStartValue.Description.Should().BeEmpty();
        transactionStartValue.Hardware.Should().BeEmpty();
        transactionStartValue.Urgent.Should().BeEmpty();
        transactionStartValue.IsNba.Should().BeFalse();
        transactionStartValue.CustomerId.Should().Be(1);

        data.label = "Eneco";
        data.authorization = "AuthHeader";
        transactionStartValue = eventActivity.GetTransactionStartValue();
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Label.Should().Be(Label.Eneco);
        transactionStartValue.Channel.Should().Be(BotChannel.App);
        transactionStartValue.Authorization.Should().Be("AuthHeader");

        data.label = "I cannot be parsed, so by default I am going to be an Eneco label instead. Yay!";
        transactionStartValue = eventActivity.GetTransactionStartValue();
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Label.Should().Be(Label.Eneco);

        data.label = Label.Oxxio;
        transactionStartValue = eventActivity.GetTransactionStartValue();
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Label.Should().Be(Label.Oxxio);

        data.channel = "Web";
        transactionStartValue = eventActivity.GetTransactionStartValue();
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Channel.Should().Be(BotChannel.Web);

        data.isNba = false;
        transactionStartValue = eventActivity.GetTransactionStartValue();
        transactionStartValue.IsNba.Should().BeFalse();

        data.isNba = true;
        transactionStartValue = eventActivity.GetTransactionStartValue();
        transactionStartValue.IsNba.Should().BeTrue();
    }

    [Fact]
    public void EventActivity_Test_WithTransactionStartValue()
    {
        var activity = new Activity
        {
            Type = "event",
            Name = "startOfTransaction"
        };
        var eventActivity = activity.AsEventActivity();
        eventActivity.Value = new TransactionStartValue
        {
            Label = Label.Oxxio,
            StartCommand = "dialoog",
            Channel = BotChannel.App,
            Authorization = "AuthHeader"
        };
        var result = eventActivity.GetTransactionStartValue();
        result.StartCommand.Should().Be("dialoog");
        result.Label.Should().Be(Label.Oxxio);
        result.Channel.Should().Be(BotChannel.App);
        result.Authorization.Should().Be("AuthHeader");
    }

    [Fact]
    public void MessageActivity_Test()
    {
        var activity = new Activity
        {
            Type = "message",
            Text = "test"
        };
        var messageActivity = activity.AsMessageActivity();

        TransactionStartValue transactionStartValue = messageActivity.GetTransactionStartValue();
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Label.Should().Be(Label.Eneco);

        messageActivity.Text = "test-LabelOxxio";
        transactionStartValue = messageActivity.GetTransactionStartValue();
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Label.Should().Be(Label.Oxxio);

        messageActivity.Text = "test-LabelOxxio-ChannelApp";
        transactionStartValue = messageActivity.GetTransactionStartValue();
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Channel.Should().Be(BotChannel.App);

        messageActivity.Text = "test -Authorization authheadermock";
        transactionStartValue = messageActivity.GetTransactionStartValue();
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Authorization.Should().Be("authheadermock");

        messageActivity.Text = "test |MalfunctionUrgencyUrgent|MalfunctionDevicecv-ketel|MalfunctionDescription testdescription mock";
        transactionStartValue = messageActivity.GetTransactionStartValue();
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Urgent.Should().Be("Urgent");
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Description.Should().Be("testdescription mock");
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Hardware.Should().Be("cv-ketel");

        messageActivity.Text = "test|MalfunctionUrgency Urgent";
        transactionStartValue = messageActivity.GetTransactionStartValue();
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Urgent.Should().Be("Urgent");

        messageActivity.Text = "test |MalfunctionDescription test description mock";
        transactionStartValue = messageActivity.GetTransactionStartValue();
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Description.Should().Be("test description mock");

        messageActivity.Text = "test |MalfunctionDevice hardware";
        transactionStartValue = messageActivity.GetTransactionStartValue();
        transactionStartValue.Should().NotBeNull();
        transactionStartValue.Hardware.Should().Be("hardware");
    }

    [Fact]
    public void IsStartOfTransactionActivity_WorksAsExpected()
    {
        var activity1 = new Activity
        {
            Type = "message",
            Text = "test"
        };
        var messageActivity = activity1.AsMessageActivity();

        var activity2 = new Activity
        {
            Type = "event",
            Name = "startOfTransaction"
        };
        var eventActivity1 = activity2.AsEventActivity();

        var activity3 = new Activity
        {
            Type = "event",
            Name = "otherEvent"
        };
        var eventActivity2 = activity3.AsEventActivity();

        messageActivity.IsStartOfTransactionActivity(out var outMessage).Should().BeFalse();
        outMessage.Should().BeNull();

        eventActivity1.IsStartOfTransactionActivity(out var outEvent1).Should().BeTrue();
        outEvent1.Should().NotBeNull();

        eventActivity2.IsStartOfTransactionActivity(out var outEvent2).Should().BeFalse();
        outEvent2.Should().NotBeNull();
    }

    [Fact]
    public void CreateEncryptedCustomerInfoEvent_WorksAsExpected()
    {
        // setup mock
        var testkey = AesEncryption.GenerateKey();
        var config = new Mock<IConfiguration>();
        config.SetReturnsDefault(testkey);

        // encrypt
        var customerInfo = new CustomerInfo { Name = "test" };
        var eventActivity = customerInfo.CreateEncryptedEvent(config.Object);

        // decrypt to verify 
        var value = (string)eventActivity.Value;
        var json = AesEncryption.Decrypt(value, testkey);
        var decryptedInfo = JsonConvert.DeserializeObject<CustomerInfo>(json);

        // should be the same
        decryptedInfo.Name.Should().Be(customerInfo.Name);
    }

    [Theory]
    [InlineData("Ik verwacht een bijlage", null)]
    [InlineData("Ik verwacht een bijlage van 2MB", 2000000)]
    public void CreateMessageWithExpectedAttachment_WorksAsExpected(string text, int? maxSize)
    {
        IMessageActivity message;
        if (maxSize.HasValue)
            message = BusinessLogic.Extensions.ActivityExtensions.CreateMessageWithExpectedAttachment(text, maxSize.Value);
        else
        {
            message = BusinessLogic.Extensions.ActivityExtensions.CreateMessageWithExpectedAttachment(text);
            maxSize = 4194304;
        }

        message.Text.Should().Be(text);
        message.Entities.Count.Should().Be(1);
        var entity = message.Entities.First();
        var entry = entity.Properties.ToObject<EntityProperties<ExpectedEntryEntity>>();
        entry.Type.Should().Be("expectedEntry");
        entry.Properties.Type.Should().Be(ExpectedEntryType.Upload);
        entry.Properties.Limit.Should().BeNull();
        entry.Properties.MaxSize.Should().Be(maxSize);
        entry.Properties.AllowedMimeTypes.Should().BeEquivalentTo(
        [
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "image/png",
            "image/jpeg"
        ]);
    }

    [Fact]
    public void ToFilename_WorksAsExpected()
    {
        var userData = new DialogData
        {
            Verification = new VerificationData
            {
                CustomerId = 1
            },
            SelectedAccount = new AccountData
            {
                AccountId = 2
            }
        };
        var result = userData.ToFilename("boetevergoeding", "image/jpeg");
        result.Should().Be("boetevergoeding_1-2.jpg");
    }
}