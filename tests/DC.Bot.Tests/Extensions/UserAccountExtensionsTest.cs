﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Exceptions;
using FluentAssertions;
using Xunit;
namespace DC.Bot.Tests.Extensions;

public class UserAccountExtensionsTest
{
    [Theory]
    [InlineData(true, "9876AB", 1, false)]
    [InlineData(false, null, null, true)]
    [InlineData(true, null, null, true)]
    [InlineData(true, null, 1, true)]
    [InlineData(true, "9876AB", null, true)]
    public void ToRequestDataValidationRequest_Test(bool hasVerification, string pc, int? hn, bool throws)
    {
        var userData = new DialogData
        {
            Verification = hasVerification ? 
                new VerificationData { 
                    PostalCode = pc,
                    HouseNumber = hn
                } : null
        };

        if (throws)
        {
            Assert.ThrowsAny<ValidationException>(() => userData.ToRequestDataValidationRequest());
        }
        else
        {
            var requestDataValidationRequest = userData.ToRequestDataValidationRequest();
            requestDataValidationRequest.Should().NotBeNull();
            requestDataValidationRequest.Data.Address.PostalCode.Should().Be(pc);
            requestDataValidationRequest.Data.Address.HouseNumber.Should().Be(hn.Value);
        }
    }    
}