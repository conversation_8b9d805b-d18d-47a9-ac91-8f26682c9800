﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Models;
using DC.Customers.Client.Models;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using Xunit;

namespace DC.Bot.Tests.Extensions;

public class DialogDataExtenionsTests
{
    [Fact]
    public void GetActiveAccountFromCustomerModel_Test()
    {
        var dialogData = new DialogData();
        var customerModel = new CustomerModel();

        dialogData.GetActiveAccountFromCustomerModel(customerModel).Should().BeNull();

        dialogData.SelectedAccount = new AccountData
        {
            AccountId = 1
        };

        dialogData.GetActiveAccountFromCustomerModel(customerModel).Should().BeNull();

        customerModel.Accounts = new List<CustomerAccountModel>();
        dialogData.GetActiveAccountFromCustomerModel(customerModel).Should().BeNull();

        customerModel.Accounts.Add(new CustomerAccountModel
        {
            Active = false
        });

        dialogData.GetActiveAccountFromCustomerModel(customerModel).Should().BeNull();

        customerModel.Accounts.FirstOrDefault().Active = false;
        dialogData.GetActiveAccountFromCustomerModel(customerModel).Should().BeNull();

        customerModel.Accounts.FirstOrDefault().Active = true;
        customerModel.Accounts.FirstOrDefault().Id = 1;
        dialogData.GetActiveAccountFromCustomerModel(customerModel).Should().NotBeNull();
        dialogData.GetActiveAccountFromCustomerModel(customerModel).Active.Should().BeTrue();

        customerModel.Accounts.FirstOrDefault().Id = 9;
        dialogData.GetActiveAccountFromCustomerModel(customerModel).Should().BeNull();
    }

    [Theory]
    [InlineData(true, 1, true)]
    [InlineData(false, 1, false)]
    [InlineData(true, 0, false)]
    public void IsVerified_Test(bool isVerified, int customerId, bool expectedResult)
    {
        var dialogData = new DialogData
        {
            Verification = new VerificationData
            {
                IsVerified = isVerified,
                CustomerId = customerId
            }
        };

        dialogData.IsVerified().Should().Be(expectedResult);
    }

    [Theory]
    [InlineData(true, 1, true)]
    [InlineData(false, 1, false)]
    [InlineData(true, 0, false)]
    public void IsCustomerIdVerified_Test(bool IsCustomerIdVerified, int customerId, bool expectedResult)
    {
        var dialogData = new DialogData
        {
            Verification = new VerificationData
            {
                CustomerIdVerified = IsCustomerIdVerified,
                CustomerId = customerId
            }
        };

        dialogData.IsCustomerIdVerified().Should().Be(expectedResult);
    }

    [Theory]
    [InlineData(1, true)]
    [InlineData(0, false)]
    public void HasActiveAccount_Test(int accountId, bool expectedResult)
    {
        var dialogData = new DialogData
        {
            SelectedAccount = new AccountData
            {
                AccountId = accountId
            }
        };

        dialogData.HasActiveAccount().Should().Be(expectedResult);
    }

    [Theory]
    [InlineData(new string[] { }, 0)]
    [InlineData(new string[] { "**********", "**********" }, 2)]
    [InlineData(new string[] { "**********", "12345" }, 1)]
    public void CheckApprovedPhoneNumbers_Test(string[] phonenumbers, int expectedResult)
    {
        var dialogData = new DialogData
        {
            PhoneNumber = new PhoneNumberDialogData
            {
                ApprovedPhoneNumbers = phonenumbers.ToList()
            }
        };

        dialogData.CheckApprovedPhoneNumbers();
        dialogData.PhoneNumber.ApprovedPhoneNumbers.Count.Should().Be(expectedResult);
    }
}