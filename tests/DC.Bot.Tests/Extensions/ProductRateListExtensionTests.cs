﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Domain.Models.Products;
using DC.Domain.Models.Products.ProductRates;
using DC.Domain.Models.Usages;
using FluentAssertions;
using System.Collections.Generic;
using System.Globalization;
using System.Threading;
using Xunit;

namespace DC.Bot.Tests.Extensions;

public class ProductRateListExtensionTests
{
    [Fact]
    public void GetStukjeZonSummary_Test()
    {
        Thread.CurrentThread.CurrentCulture = new CultureInfo("nl-NL");
        var productrates = new List<ProductRate>();
        var productRate = new ProductRate
        {
            Name = "Stukje zon",
            ProductType = ProductType.StukjeZon,
            ProductRateDetails = new List<ProductRateDetail>()
        };
        productRate.ProductRateDetails.Add(new ProductRateDetail()
        {
            Type = ProductRateDetailType.StukjeZon,
            Description = "Stukje zon",
            VATIncluded = decimal.Parse("-1,25"),
            ByPeriod = ByPeriod.M,
            IsVariable = false
        });
        var text = "Je neemt {numberOfStukjeZon} keer StukjeZon bij ons af, wat je in totaal €{priceStukjeZon} korting per jaar oplevert.";
        productrates.Add(productRate);
        productrates.GetStukjeZonSummary(text).Should().Be("Je neemt 1 keer StukjeZon bij ons af, wat je in totaal €15,00 korting per jaar oplevert.");
        productrates.Add(productRate);
        productrates.GetStukjeZonSummary(text).Should().Be("Je neemt 2 keer StukjeZon bij ons af, wat je in totaal €30,00 korting per jaar oplevert.");
    }

    [Theory]
    [InlineData(ProductType.Electricity, ProductRateDetailType.Tariff)]
    [InlineData(ProductType.Electricity, ProductRateDetailType.LowTariff)]
    [InlineData(ProductType.Gas, ProductRateDetailType.Tariff)]
    [InlineData(ProductType.Warmth, ProductRateDetailType.Tariff)]
    public void GetMeasuredProductRates_Test(ProductType productType, ProductRateDetailType productRateDetailType)
    {
        var productRates = new List<ProductRate>();
        var productRate = new ProductRate
        {
            ProductType = productType,
            ProductRateDetails = new List<ProductRateDetail>()
        };
        productRate.ProductRateDetails.Add(new ProductRateDetail
        {
            Type = productRateDetailType,
            IsVariable = false,
            VAT = 0,
            VATIncluded = 0,
            VATExcluded = 0
        });
        productRates.Add(productRate);
        productRates.GetMeasuredProductRates().Should().NotBeEmpty();
    }
    [Theory]
    [InlineData(ProductType.Huurapparaat, ProductRateDetailType.Huurapparaat)]
    [InlineData(ProductType.ServiceCVKetel, ProductRateDetailType.ServiceCVKetel)]
    [InlineData(ProductType.WarmteWisselaar, ProductRateDetailType.WarmtePomp)]
    public void NonMeasuredProductRates_Test(ProductType productType, ProductRateDetailType productRateDetailType)
    {
        var productRates = new List<ProductRate>();
        var productRate = new ProductRate
        {
            ProductType = productType,
            ProductRateDetails = new List<ProductRateDetail>()
        };
        productRate.ProductRateDetails.Add(new ProductRateDetail
        {
            Type = productRateDetailType,
            IsVariable = false,
            VAT = 0,
            VATIncluded = 0,
            VATExcluded = 0
        });
        productRates.Add(productRate);
        productRates.GetNonMeasuredProductRates().Should().NotBeEmpty();
    }
}