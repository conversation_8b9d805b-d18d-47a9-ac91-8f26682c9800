﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Domain.Models.Products;
using FluentAssertions;
using Xunit;

namespace DC.Bot.Tests.Extensions;

public class DenotationTypeExtensionsTests
{
    [Fact]
    public void ParseDenotationType_Test()
    {
        string output = DenotationType.GJ.ParseDenotationType();
        output.Should().Be("GJ");

        output = DenotationType.Kwh.ParseDenotationType();
        output.Should().Be("kWh");

        output = DenotationType.M3.ParseDenotationType();
        output.Should().Be("m³");
    }
}