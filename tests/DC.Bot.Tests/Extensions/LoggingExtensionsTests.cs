﻿using DC.Bot.BusinessLogic.Extensions.Logging;
using DC.Bot.BusinessLogic.Models;
using FluentAssertions;
using Microsoft.Bot.Schema;
using Newtonsoft.Json.Linq;
using Xunit;

namespace DC.Bot.Tests.Extensions;

public class LoggingExtensionsTests
{
    [Fact]
    public void CreateLoggingScope_StringValue()
    {
        var activity = new Activity
        {
            Conversation = new ConversationAccount
            {
                Id = "conid"
            },
            From = new ChannelAccount
            {
                Id = "fromid",
                Name = "fromname"
            },
            Recipient = new ChannelAccount
            {
                Id = "recid",
                Name = "recname"
            },
            Type = "message",
            Name = "bericht",
            Value = "string",
            Text = "txt"
        };
        var scope = activity.CreateLoggingScope();
        scope["Conversation ID"].Should().Be("conid");
        scope["DC-FromId"].Should().Be("fromid");
        scope["DC-FromName"].Should().Be("fromname");
        scope["DC-RecipientId"].Should().Be("recid");
        scope["DC-RecipientName"].Should().Be("recname");
        scope["DC-Name"].Should().Be("bericht");
        scope["DC-Type"].Should().Be("message");
        scope["DC-Value"].Should().Be("string");
        scope["DC-Text"].Should().Be("txt");
    }

    [Fact]
    public void CreateLoggingScope_ObjectValue()
    {
        var activity = new Activity
        {
            Conversation = new ConversationAccount
            {
                Id = "conid"
            },
            From = new ChannelAccount
            {
                Id = "fromid",
                Name = "fromname"
            },
            Recipient = new ChannelAccount
            {
                Id = "recid",
                Name = "recname"
            },
            Type = "event",
            Name = "startOfTransaction",
            Value = JToken.FromObject(new { Label = "Eneco", StartCommand = "$advancepaymentamount", IsStart = true }),
            Text = null
        };

        var scope = activity.CreateLoggingScope();
        scope["DC-Name"].Should().Be("startOfTransaction");
        scope["DC-Type"].Should().Be("event");
        scope["DC-Value-Label"].Should().Be("Eneco");
        scope["DC-Value-StartCommand"].Should().Be("$advancepaymentamount");
        scope["DC-Value-IsStart"].Should().Be("True");
        scope["DC-Text"].Should().BeNull();
    }

    [Fact]
    public void CreateLoggingScope_NullValue()
    {
        var activity = new Activity
        {
            Conversation = new ConversationAccount
            {
                Id = "conid"
            },
            From = new ChannelAccount
            {
                Id = "fromid",
                Name = "fromname"
            },
            Recipient = new ChannelAccount
            {
                Id = "recid",
                Name = "recname"
            },
            Type = "message",
            Name = null,
            Value = null,
            Text = "Je termijnbedrag is 5 euro."
        };
        var scope = activity.CreateLoggingScope();
        scope["DC-Name"].Should().BeNull();
        scope["DC-Type"].Should().Be("message");
        scope["DC-Value"].Should().BeNull();
        scope["DC-Text"].Should().Be("Je termijnbedrag is 5 euro.");
    }

    [Fact]
    public void ToCustomEventProperties_WorksAsExpected()
    {
        var convData = new ConversationData
        {
            DialogData = new DialogData
            {
                Verification = new VerificationData
                {
                    CustomerId = 123

                },
                Customer = new CustomerData
                {
                    Label = DC.Repositories.Base.Enumerations.Label.Eneco
                }
            },
            CurrentDialogAction = BusinessLogic.Enumerations.DialogAction.CustomerVerification
        };
        var negative = convData.ToCustomEventProperties(null, null, BusinessLogic.Enumerations.TransactionStatus.PermanentFailure, null);
        negative["Channel"].Should().Be("Web");
        negative["CustomerId"].Should().Be("123");
        negative["Label"].Should().Be("Eneco");
        negative["TransactionStatus"].Should().Be("PermanentFailure");
        negative["Success"].Should().Be("False");
        negative["Action"].Should().Be("CustomerVerification");
        var positive = convData.ToCustomEventProperties(null, null, BusinessLogic.Enumerations.TransactionStatus.Success, null);
        positive["Success"].Should().Be("True");
    }
}