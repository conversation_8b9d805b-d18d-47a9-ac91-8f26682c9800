﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Models.NextBestAction;
using DC.Products.Client.Models;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using Xunit;

namespace DC.Bot.Tests.Extensions
{
    public class NextBestActionsExtensionsTests
    {
        [Fact]
        public void FilterSupportedActions_WorksAsExpected()
        {
            var input = new NextBestAction
            {
                Actions = new List<ActionModel>
                {
                    new ActionModel
                    {
                        ActionType = "AccountMeterReading"
                    },
                    new ActionModel
                    {
                        ActionType = "BankAccountNumber"
                    },
                    new ActionModel
                    {
                        ActionType = "CustomerPhoneNumber"
                    },
                    new ActionModel
                    {
                        ActionType = "Isma"
                    }
                }
            };
            input = input.FilterSupportedActions();
            input.Actions.Should().HaveCount(3);
            input.Actions.FirstOrDefault(a => a.ActionType == "CustomerPhoneNumber").Should().NotBeNull();
            input.Actions.FirstOrDefault(a => a.ActionType == "BankAccountNumber").Should().NotBeNull();
            input.Actions.FirstOrDefault(a => a.ActionType == "AccountMeterReading").Should().BeNull();
            input.Actions.FirstOrDefault(a => a.ActionType == "Isma").Should().NotBeNull();
        }

        [Theory]
        [InlineData("AlwaysOn", false, null)]
        [InlineData("HappyPower", false, null)]
        [InlineData("CustomerPhoneNumber", true, "ChangePhoneNumberDialog")]
        [InlineData("BankAccountNumber", true, "ChangeIbanDialog")]
        [InlineData("Isma", true, "IsmaMandateDialog")]
        public void IsSupportedAction_WorksAsExpected(string actionType, bool result, string followUpDialog)
        {
            var nba = new NextBestAction
            {
                Actions = new List<ActionModel>
                {
                    new ActionModel
                    {
                        ActionType = actionType,
                        Channel = Channel.Chatbot,
                        ServingPointId = 4,
                        TreatmentVariationId = 1
                    }
                }
            };
            actionType.GetNextBestActionConfiguration(nba).IsSupported.Should().Be(result);
            actionType.GetNextBestActionConfiguration(nba).FollowUpDialogName.Should().Be(followUpDialog);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void ToNextBestActionInfo_WorksAsExpected(bool hasSupportedActions)
        {
            var input = new NextBestAction
            {
                Actions = new List<ActionModel>
                {
                    new ActionModel
                    {
                        ActionType = "AccountMeterReading",
                        Score = 100
                    },
                    new ActionModel
                    {
                        ActionType = hasSupportedActions ? "BankAccountNumber" : "Afco",
                        Score = 95
                    },
                    new ActionModel
                    {
                        ActionType = hasSupportedActions ? "CustomerPhoneNumber" : "DateOfBirth",
                        Score = 98
                    }
                }
            };
            var result = input.ToNextBestActionInfo();
            if (hasSupportedActions)
            {
                result.Available.Should().BeTrue();
                result.Type.Should().Be("CustomerPhoneNumber");
            }
            else
            {
                result.Available.Should().BeFalse();
                result.Type.Should().BeNull();
            }
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void EnrichActionFeedbackData_WorksAsExpected(bool currentIsNba)
        {
            var dialogData = new DialogData();
            dialogData.NextBestAction.CurrentTransactionIsNba = currentIsNba;
            dialogData.NextBestAction.Data = new NextBestAction
            {
                Actions = new List<ActionModel>
                {
                    new ActionModel
                    {
                        ActionType = "AccountMeterReading",
                        Score = 100,
                        ActionId = 90,
                        ServingPointId = 4,
                        TreatmentVariationId = 3
                    },
                    new ActionModel
                    {
                        ActionType = "BankAccountNumber",
                        Score = 95,
                        ActionId = 95,
                        ServingPointId = 4,
                        TreatmentVariationId = 2
                    },
                    new ActionModel
                    {
                        ActionType = "CustomerPhoneNumber",
                        Score = 98,
                        ActionId = 99,
                        ServingPointId = 4,
                        TreatmentVariationId = 1
                    }
                },
                ContextId = "ctx1"
            };
            var actionFeedbackData = new Domain.Models.NextBestAction.ActionFeedbackData();
            actionFeedbackData.EnrichActionFeedbackData("CustomerPhoneNumber", dialogData);

            if (currentIsNba)
            {
                actionFeedbackData.AccountId.Should().BeNull();
                actionFeedbackData.ActionId.Should().Be(99);
                actionFeedbackData.Channel.Should().Be(Domain.Models.NextBestAction.Channel.Chatbot);
                actionFeedbackData.ContextId.Should().Be("ctx1");
                actionFeedbackData.ServingPointId.Should().Be(4);
                actionFeedbackData.VariationId.Should().Be(1);
            }
            else
            {
                actionFeedbackData.VariationId.Should().BeNull();
            }
        }
    }
}
