﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Models;
using DC.Products.Client.Models;
using FluentAssertions;
using Xunit;

namespace DC.Bot.Tests.Extensions;

public class ProductOrderExtensionsTests
{
    [Fact]
    public void ToServiceOrderRequestModel_WorksAsExpected()
    {
        var userData = new DialogData
        {
            Verification = new VerificationData
            {

                PhoneNumber = "0623062306",
                HouseNumber = 1,
                HouseNumberSuffix = "A",
                PostalCode = "2222AA",
                CustomerId = 1
            },
            Customer = new CustomerData
            {
                EmailAddress = "<EMAIL>",
                Firstname = "Fi",
                Surname = "Su"
            },
            ServiceOrder = new ServiceOrderData
            {
                MalfunctionCode = "E064",
                Description = "Geen of onvoldoende warmwater",
                Type = ServiceOrderType.Installation,
                Comments = new ServiceOrderComments
                {
                    Customer = "Graag in de ochtend",
                    Mechanic = "Graag in de ochtend",
                    BackOffice = "Graag in de ochtend"
                }
            }
        };

        var result = userData.ToServiceOrderRequestModel();
        result.Data.MalfunctionCode.Should().Be("E064");
        result.Data.Description.Should().Be("Geen of onvoldoende warmwater");
        result.Data.Type.Should().Be(ServiceOrderType.Installation);
        result.Data.Comments.Customer.Should().Be("Graag in de ochtend");
        result.Data.Comments.Mechanic.Should().Be("Graag in de ochtend");
        result.Data.Comments.BackOffice.Should().Be("Graag in de ochtend");

        userData.ServiceOrder.Description = "Geen of onvoldoende warmwater. Graag wil ik snel een monteur hier laten komen want dit is niet te doen!";
        result = userData.ToServiceOrderRequestModel();

        // The expect a maximum length of 50 characters.
        result.Data.Description.Should().Be("Geen of onvoldoende warmwater. Graag wil ik snel e");


    }
}