﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Dialogs.Financials.Validators;
using DC.Bot.BusinessLogic.Dialogs.Products.Validators;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Customers.Client.Models;
using DC.Domain.Models.Financials;
using DC.Financials.Client.Models;
using DC.Repositories.Base.Enumerations;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Moq;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Financials;

[Collection("Sequential")]
public class AdvancePaymentDayDialogTests : TestBase
{
    [Fact]
    public async Task IfNotUpdatingPaymentDayOfMonth_ThenEndConversation()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ADVANCE_PAYMENT_DAY).ConfigureAwait(true);
        Assert.StartsWith(GetTextLabelValue("PayByDirectDebitPaymentDay", TextLabelGroupName).Replace("{paymentday}", "1"), testClient.GetNextReply().Text);

        Assert.StartsWith(GetTextLabelValue("AskOtherDay", TextLabelGroupName)
            + " (1) "
            + GetTextLabelValue("YesChoice", TextLabelGroupName)
            + " or (2) "
            + GetTextLabelValue("NoChoice", TextLabelGroupName), testClient.GetNextReply().Text);

        var reply = await testClient.SendActivityAsync("nee");
        reply.Text.Should().Be(GetTextLabelValue("FineEverythingStaysTheSame", TextLabelGroupName));
    }

    [Fact]
    public async Task IfPaymentWithGiroCard_ThenShouldEndWithExpectedMessage()
    {
        //Arrange
        SwapMockedServices(2);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ADVANCE_PAYMENT_DAY).ConfigureAwait(true);
        Assert.StartsWith(GetTextLabelValue("PayByGiroCard", TextLabelGroupName), testClient.GetNextReply().Text);
        Assert.StartsWith(GetTextLabelValue("ChooseDirectDebit", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task IfDirectPaymentAndHasSetPaymentDayOfMonth_ThenShouldEndWithExpectedMessage()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ADVANCE_PAYMENT_DAY).ConfigureAwait(true);
        Assert.StartsWith(GetTextLabelValue("PayByDirectDebitPaymentDay", TextLabelGroupName).Replace("{paymentday}", "1"), testClient.GetNextReply().Text);

        Assert.StartsWith(GetTextLabelValue("AskOtherDay", TextLabelGroupName)
            + " (1) "
            + GetTextLabelValue("YesChoice", TextLabelGroupName)
            + " or (2) "
            + GetTextLabelValue("NoChoice", TextLabelGroupName), testClient.GetNextReply().Text);

        var reply = await testClient.SendActivityAsync("ja");

        Assert.StartsWith(GetTextLabelValue("AskDayOfMonth", TextLabelGroupName), reply.Text);
        Assert.StartsWith(GetTextLabelValue("ChooseFromFirstTwentyEightDaysOfMonth", TextLabelGroupName), testClient.GetNextReply().Text);
        reply = await testClient.SendActivityAsync("30");
        reply.Text.Should().Be(GetTextLabelValue("IncorrectDayNumber", TextLabelGroupName));
        reply = await testClient.SendActivityAsync("15");
        reply.Text.Should().Be(GetTextLabelValue("PaymentDayChanged", TextLabelGroupName));
    }

    [Fact]
    public async Task IfDirectPaymentAndHasNotSetPaymentDayOfMonth_ThenShouldEndWithExpectedMessage()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ADVANCE_PAYMENT_DAY).ConfigureAwait(true);
        Assert.StartsWith(GetTextLabelValue("PayByDirectDebitPaymentDay", TextLabelGroupName).Replace("{paymentday}", "1"), testClient.GetNextReply().Text);

        Assert.StartsWith(GetTextLabelValue("AskOtherDay", TextLabelGroupName)
            + " (1) "
            + GetTextLabelValue("YesChoice", TextLabelGroupName)
            + " or (2) "
            + GetTextLabelValue("NoChoice", TextLabelGroupName), testClient.GetNextReply().Text);

        var reply = await testClient.SendActivityAsync("ja");

        Assert.StartsWith(GetTextLabelValue("AskDayOfMonth", TextLabelGroupName), reply.Text);
        Assert.StartsWith(GetTextLabelValue("ChooseFromFirstTwentyEightDaysOfMonth", TextLabelGroupName), testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("30");
        reply.Text.Should().Be(GetTextLabelValue("IncorrectDayNumber", TextLabelGroupName));

        reply = await testClient.SendActivityAsync("30");
        reply.Text.Should().Be(GetTextLabelValue("IncorrectDayNumber", TextLabelGroupName));

        reply = await testClient.SendActivityAsync("30");
        reply.Text.Should().Be(GetTextLabelValue("ThreeTimesWrongNumber", TextLabelGroupName));
    }


    [Theory]
    [InlineData(100, "Je betaalt per acceptgiro. Kies voor 'Automatische incasso instellen' als je op een vaste dag wil betalen.")]
    [InlineData(101, "Door een lopende betalingsregeling of een achterstand met betalen is er iets misgegaan. Kies voor contact, mijn collega's kijken met je mee.")]
    [InlineData(102, "Oeps! Er is helaas iets misgegaan. Kies voor contact, mijn collega's kijken met je mee.")]
    [InlineData(103, "Oeps! Er is helaas iets misgegaan. Kies voor contact, mijn collega's kijken met je mee.")]
    [InlineData(104, "Oeps! Er is helaas iets misgegaan. Probeer het opnieuw of kies voor contact. Dan kijken mijn collega's met je mee.")]
    public async Task DialogReturnsCorrectMsgBasedOnReturnStatus(long customerId, string replyTxt)
    {
        //Arrange
        SwapMockedServices(customerId);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ADVANCE_PAYMENT_DAY).ConfigureAwait(true);
        Assert.StartsWith(GetTextLabelValue("PayByDirectDebitPaymentDay", TextLabelGroupName).Replace("{paymentday}", "1"), testClient.GetNextReply().Text);

        Assert.StartsWith(GetTextLabelValue("AskOtherDay", TextLabelGroupName)
            + " (1) "
            + GetTextLabelValue("YesChoice", TextLabelGroupName)
            + " or (2) "
            + GetTextLabelValue("NoChoice", TextLabelGroupName), testClient.GetNextReply().Text);

        var reply = await testClient.SendActivityAsync("ja");

        Assert.StartsWith(GetTextLabelValue("AskDayOfMonth", TextLabelGroupName), reply.Text);
        Assert.StartsWith(GetTextLabelValue("ChooseFromFirstTwentyEightDaysOfMonth", TextLabelGroupName), testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("15");
        reply.Text.Should().Be(replyTxt);
    }

    [Fact]
    public async Task IfDirectPaymentAndHasSetPaymentDayOfMonth_with_UnverifiedCustomer()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithUnverifiedCustomer(DialogCommands._ADVANCE_PAYMENT_DAY);
        Assert.Contains(GetTextLabelValue("CouldNotFindData", "Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }

    private void SwapMockedServices(long customerId, bool activeAccount = true)
    {
        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        var financialsServiceMock = new Mock<IFinancialsService>();
        var sessionManagerMock = new Mock<ISessionManager>();
        var storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_AdvancePaymentDayDialog";

        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));
        customersRepositoryMock.SetupCustomerTestData(customerId, activeAccount: activeAccount);

        sessionManagerMock.Setup(x =>
                x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Customer = new CustomerData
                {
                    Label = Label.Eneco
                },
                Verification = new VerificationData
                {
                    CustomerId = customerId
                }
            });

        financialsServiceMock.Setup(x =>
                x.GetPaymentArrangement(It.Is<DialogData>(x => x.Verification.CustomerId > 1), It.IsAny<int>()))
            .ReturnsAsync(new PaymentArrangementModel
            {
                TotalPaid = 110,
                OutstandingAmount = 50,
                Invoices = new List<InvoiceInArrangement>
                {
                    new InvoiceInArrangement
                    {
                        Id = 1111
                    }
                },
                Terms = new List<Term>
                {
                    new Term { Id = 5555, Amount = 10, IsPaidStatus = false },
                    new Term { Id = 6666, Amount = 20, IsPaidStatus = false },
                    new Term { Id = 7777, Amount = 30, IsPaidStatus = true }
                }
            });

        financialsServiceMock.Setup(x => x.GetAdvancePaymentAmountAndDayOfPayment(It.Is<DialogData>(x => x.Verification.CustomerId >= 1), It.IsAny<CustomerAccountModel>()))
            .ReturnsAsync(new AdvancePayment
            {
                Amount = 250,
                Account = new CustomerAccountModel
                {
                    CustomerId = customerId,
                    Active = true,
                    Address = new AddressModel
                    {
                        Street = "Sesamstraat",
                        HouseNumber = 12,
                        City = "Rotterdam",
                        PostalCode = "1234AB"
                    }
                },
                Preferences = new FinancialPreferences
                {
                    PaymentDayOfMonth = 1,
                    PaymentMethodIsDirectDebit = customerId != 2
                }
            });


        financialsServiceMock.Setup(x => x.UpdatePaymentDayOfMonth(It.Is<DialogData>(x => x.Verification.CustomerId == 1), It.IsAny<int>()))
            .ReturnsAsync(AdvancePaymentAdviceStatus.Ok);

        financialsServiceMock.Setup(x => x.UpdatePaymentDayOfMonth(It.Is<DialogData>(x => x.Verification.CustomerId == 100), It.IsAny<int>()))
            .ReturnsAsync(AdvancePaymentAdviceStatus.PaymentMethodShouldBeDirectDebit);

        financialsServiceMock.Setup(x => x.UpdatePaymentDayOfMonth(It.Is<DialogData>(x => x.Verification.CustomerId == 101), It.IsAny<int>()))
            .ReturnsAsync(AdvancePaymentAdviceStatus.ActiveDebtCollection);

        financialsServiceMock.Setup(x => x.UpdatePaymentDayOfMonth(It.Is<DialogData>(x => x.Verification.CustomerId == 102), It.IsAny<int>()))
            .ReturnsAsync(AdvancePaymentAdviceStatus.PaymentMethodShouldNotBeDirectDebit);

        financialsServiceMock.Setup(x => x.UpdatePaymentDayOfMonth(It.Is<DialogData>(x => x.Verification.CustomerId == 103), It.IsAny<int>()))
            .ReturnsAsync(AdvancePaymentAdviceStatus.IbanMissingOrIncorrect);

        financialsServiceMock.Setup(x => x.UpdatePaymentDayOfMonth(It.Is<DialogData>(x => x.Verification.CustomerId == 104), It.IsAny<int>()))
            .ReturnsAsync(AdvancePaymentAdviceStatus.Default);

        var validator = new Mock<IDialogValidators>();
        validator.Setup(x => x.CustomerDialogValidator).Returns(new CustomerDialogValidator(sessionManagerMock.Object));
        validator.Setup(x => x.FinancialsDialogValidator).Returns(new FinancialsDialogValidator(sessionManagerMock.Object));
        validator.Setup(x => x.ProductsDialogValidator).Returns(new ProductsDialogValidator(sessionManagerMock.Object));

        _services.SwapTransient(provider => validator.Object);
        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => financialsServiceMock.Object);
        _services.SwapTransient(provider => sessionManagerMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }
}
