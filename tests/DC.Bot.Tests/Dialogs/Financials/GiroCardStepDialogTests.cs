﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Dialogs.Financials.Validators;
using DC.Bot.BusinessLogic.Dialogs.Products.Validators;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Customers.Client.Models;
using DC.Domain.Models.Financials;
using DC.Financials.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Storage.Client.Models;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Microsoft.Rest;
using Moq;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Financials;

[Collection("Sequential")]
public class GiroCardStepDialogTests : TestBase
{
    [Fact]
    public async Task IfPaymentIsNotWithGiroCard_ThenShouldExitDialog()
    {
        // Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ADVANCE_PAYMENT_DIRECTDEBIT).ConfigureAwait(true);
        Assert.StartsWith(GetTextLabelValue("PaymentWithGiroCardWithPaymentDay", TextLabelGroupName).Replace("{paymentday}", "1"), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task IfPaymentWithGiroCard_WithoutCustomerIdVerification()
    {
        // Arrange
        SwapMockedServices(2);
        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ADVANCE_PAYMENT_DIRECTDEBIT).ConfigureAwait(true);
        Assert.StartsWith(GetTextLabelValue("CurrentPaymentWithGiroCard", TextLabelGroupName), testClient.GetNextReply().Text);
        Assert.StartsWith(GetTextLabelValue("AskPaymentDirectDebit", TextLabelGroupName), testClient.GetNextReply().Text);

        var reply = await testClient.SendActivityAsync("ja");

        Assert.StartsWith(GetTextLabelValue("CurrentKnownIban", "Bot_ChangeIbanDialog").Replace("{iban}", "12*4abc"), reply.Text);
        reply = await testClient.SendActivityAsync("Ja");

        reply.Text.Should().Be(GetTextLabelValue("AdjustIban", "Bot_ChangeIbanDialog"));
        reply = await testClient.SendActivityAsync("4321xy");
        reply.Text.Should().Be(GetTextLabelValue("InvalidIban", "Bot_ChangeIbanDialog"));

        reply = await testClient.SendActivityAsync("******************");
        reply.Text.Should().Be("Oké. Op welke dag van de maand wil je je incassodatum instellen?");
        Assert.StartsWith("Je kan kiezen uit alle dagen tussen de 1e tot en met de 28e van de maand.", testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("9");
        reply.Text.Should().Be(GetTextLabelValue("ChangedToPaymentDirectDebit", TextLabelGroupName));
    }

    [Fact]
    public async Task IfPaymentWithGiroCard_KeepPaymentWithGiroCard_WithoutCustomerIdVerification()
    {
        // Arrange
        SwapMockedServices(2);
        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ADVANCE_PAYMENT_DIRECTDEBIT).ConfigureAwait(true);
        Assert.StartsWith(GetTextLabelValue("CurrentPaymentWithGiroCard", TextLabelGroupName), testClient.GetNextReply().Text);
        Assert.StartsWith(GetTextLabelValue("AskPaymentDirectDebit", TextLabelGroupName), testClient.GetNextReply().Text);

        var reply = await testClient.SendActivityAsync("nee");
        reply.Text.Should().Be(GetTextLabelValue("KeepPaymentWithGiroCard", TextLabelGroupName));
    }

    [Fact]
    public async Task IfAdvancePaymentModelAmountIsNull_WithoutCustomerIdVerification()
    {
        // Arrange
        SwapMockedServices(3);
        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ADVANCE_PAYMENT_DIRECTDEBIT).ConfigureAwait(true);
        Assert.StartsWith(GetTextLabelValue("SomethingWentWrong", "Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
        Assert.StartsWith(GetTextLabelValue("ContactUsSomethingWentWrong", "Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task IfPaymentIsNotWithGiroCard_With_UnverifiedCustomer()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithUnverifiedCustomer(DialogCommands._ADVANCE_PAYMENT_DIRECTDEBIT);
        Assert.Contains(GetTextLabelValue("CouldNotFindData", "Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }

    private void SwapMockedServices(long customerId, bool activeAccount = true)
    {
        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        var financialsServiceMock = new Mock<IFinancialsService>();
        var sessionManagerMock = new Mock<ISessionManager>();
        var storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_GiroCardStepDialog";
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(new List<Storage.Client.Models.TextLabelModel>
            {

            }));
        customersRepositoryMock.SetupCustomerTestData(customerId, activeAccount: activeAccount);

        sessionManagerMock.Setup(x =>
                x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Customer = new CustomerData
                {
                    Label = Label.Eneco
                },
                Verification = new VerificationData
                {
                    CustomerId = customerId
                },
                AdvancePayment = new AdvancePaymentData
                {
                    PaymentWithGiroCard = customerId > 1
                }
            });

        financialsServiceMock.Setup(x => x.GetAdvancePaymentAmountAndDayOfPayment(It.Is<DialogData>(x => x.Verification.CustomerId >= 1 && x.Verification.CustomerId != 3), It.IsAny<CustomerAccountModel>()))
            .ReturnsAsync(new AdvancePayment
            {
                Amount = 250,
                Account = new CustomerAccountModel
                {
                    CustomerId = customerId,
                    Active = true,
                    Address = new AddressModel
                    {
                        Street = "Sesamstraat",
                        HouseNumber = 12,
                        City = "Rotterdam",
                        PostalCode = "1234AB"
                    }
                },
                Preferences = new FinancialPreferences
                {
                    PaymentDayOfMonth = 1,
                    PaymentMethodIsDirectDebit = customerId != 2
                }
            });

        financialsServiceMock.Setup(x => x.GetAdvancePaymentAmountAndDayOfPayment(It.Is<DialogData>(x => x.Verification.CustomerId == 3), It.IsAny<CustomerAccountModel>()))
            .ReturnsAsync(new AdvancePayment
            {
                Amount = 0,
                Account = new CustomerAccountModel
                {
                    CustomerId = customerId,
                    Active = true,
                    Address = new AddressModel
                    {
                        Street = "Sesamstraat",
                        HouseNumber = 12,
                        City = "Rotterdam",
                        PostalCode = "1234AB"
                    }
                },
                Preferences = new FinancialPreferences
                {
                    PaymentDayOfMonth = 1,
                    PaymentMethodIsDirectDebit = customerId != 2
                }
            });


        financialsServiceMock.Setup(x => x.UpdatePaymentDayOfMonth(It.Is<DialogData>(x => x.Verification.CustomerId > 1), It.IsAny<int>()))
            .ReturnsAsync(AdvancePaymentAdviceStatus.Ok);


        financialsServiceMock.Setup(x => x.UpdatePaymentToDirectDebit(It.Is<DialogData>(x => x.Verification.CustomerId > 1), It.IsAny<string>(), It.IsAny<int>()))
            .ReturnsAsync(AdvancePaymentAdviceStatus.Ok);

        financialsServiceMock.Setup(x =>
                x.GetFinancialsPreferences(It.IsAny<DialogData>(), It.IsAny<int>()))
            .ReturnsAsync(
                new FinancialPreferences
                {
                    BankAccount = new BankAccount
                    {
                        Number = "1234abc"
                    }
                }
            );

        GetAllTextLabelsFromExport();
        var storageRepoMock = new Mock<IDcStorageRepository>();
        storageRepoMock.Setup(x => x.GetTextlabels(It.IsAny<Label>(), It.IsAny<RequestDataTextLabelRequest>(), It.IsAny<bool>()))
            .ReturnsAsync(
                new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataIListTextLabelModel
                    {
                        Data = TextLabelModels?.ToList()
                    }
                });
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));

        var validator = new Mock<IDialogValidators>();
        validator.Setup(x => x.CustomerDialogValidator).Returns(new CustomerDialogValidator(sessionManagerMock.Object));
        validator.Setup(x => x.FinancialsDialogValidator).Returns(new FinancialsDialogValidator(sessionManagerMock.Object));
        validator.Setup(x => x.ProductsDialogValidator).Returns(new ProductsDialogValidator(sessionManagerMock.Object));

        _services.SwapTransient(provider => validator.Object);
        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => financialsServiceMock.Object);
        _services.SwapTransient(provider => sessionManagerMock.Object);
        _services.SwapTransient(provider => storageRepoMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }
}
