﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Dialogs.Financials.Validators;
using DC.Bot.BusinessLogic.Dialogs.Products.Validators;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.Financial;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Extensions;
using DC.Customers.Client.Models;
using DC.Domain.Models.Accounts;
using DC.Domain.Models.Customers;
using DC.Domain.Models.Financials;
using DC.Financials.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Financials;

[Collection("Sequential")]
public class AdvancePaymentAmountDialogTests : TestBase
{
    [Fact]
    public async Task User_Without_AdvancePaymentAdvice_Gets_Correct_Message()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Equal(GetTextLabelValue("SomethingWentWrong", "Bot_GeneralTextLabels"), reply.Text);
        Assert.Contains(GetTextLabelValue("ContactUsSomethingWentWrong", "Bot_GeneralTextLabels"),
            testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task User_With_Short_NextChargeDate_Gets_Correct_Message()
    {
        //Arrange
        SwapMockedServices(3);
        DialogTestClient testClient = SetupMainDialog();

        var replyText = GetTextLabelValue("UpdateStatusPeriodBlock", TextLabelGroupName);

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Equal(replyText, reply.Text);
    }

    [Fact]
    public async Task User_With_Yearnote_too_close_Gets_Correct_Message()
    {
        //Arrange
        SwapMockedServices(5, AdvancePaymentAdviceStatus.YearNoteTooClose);
        DialogTestClient testClient = SetupMainDialog();

        var replyText = GetTextLabelValue("AdviceYearNoteTooClose", TextLabelGroupName);

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Equal(replyText, reply.Text);
    }

    [Fact]
    public async Task User_With_PaymentAdvice_ActiveDebtCollect_Gets_Correct_Message()
    {
        //Arrange
        SwapMockedServices(5, AdvancePaymentAdviceStatus.ActiveDebtCollection);
        DialogTestClient testClient = SetupMainDialog();

        var replyText = GetTextLabelValue("CurrentAdvancePaymentAmount", TextLabelGroupName).Replace("{amount}", "100");

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Equal(replyText, reply.Text);
        Assert.Equal(GetTextLabelValue("AdviceActiveDebtCollection", TextLabelGroupName),
            testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task User_Without_PaymentWithGiroCard_Gets_Correct_Message()
    {
        //Arrange
        SwapMockedServices(20);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Equal(GetTextLabelValue("PaymentDataWithoutDirectDebit", TextLabelGroupName).Replace("{amount}", "250"),
            reply.Text);
        Assert.Contains(GetTextLabelValue("AskToChangeAmount", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    [Theory]
    [InlineData(AdvancePaymentAdviceStatus.Ok, 1000, "AdvancePaymentAdviceHigher")]
    [InlineData(AdvancePaymentAdviceStatus.NoAdviceYet, 2000, "AdvancePaymentAdviceHigher")]
    [InlineData(AdvancePaymentAdviceStatus.NoAdviceYet, 120, "AdvancePaymentAdviceLower")]
    public async Task User_With_IsRecurringPaymentWithPreferedPaymentDay_PaymentWithGiroCard_Gets_Correct_Message(
        AdvancePaymentAdviceStatus status, int advice, string key)
    {
        //Arrange
        SwapMockedServices(21, status, advice);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Equal(GetTextLabelValue("PaymentDataDirectDebitWithPreferedPaymentDay", TextLabelGroupName)
            .Replace("{amount}", "250")
            .Replace("{paymentday}", "1"), reply.Text);

        FormattableString formattedAmount = $"{advice:N0}";
        Assert.Equal(
            GetTextLabelValue(key, TextLabelGroupName)
                .Replace("{amount}", formattedAmount.ToString(new CultureInfo("NL-nl"))),
            testClient.GetNextReply().Text);
        Assert.Contains(GetTextLabelValue("AskToChangeAmount", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task
        User_Cannot_Update_Amount_WithAdviceStatusNotAvailable_DueToCannotUpdatePropertyInAdvancePaymentLimits()
    {
        SwapMockedServices(30, AdvancePaymentAdviceStatus.NotAvailable);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Contains(GetTextLabelValue("AdviceCannotUpdate", TextLabelGroupName)
            .Replace("{amount}", "250"), reply.Text);
        Assert.Contains(GetTextLabelValue("AdviceCannotUpdateContactUs", TextLabelGroupName),
            testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task User_Cannot_Update_Amount_WithAdviceStatusOK_DueToCannotUpdatePropertyInAdvancePaymentLimits()
    {
        SwapMockedServices(30, AdvancePaymentAdviceStatus.Ok);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Contains(GetTextLabelValue("CurrentAdvancePaymentAmount", TextLabelGroupName)
            .Replace("{amount}", "250"), reply.Text);
        Assert.Contains(GetTextLabelValue("AdvancePaymentAdviceIsTheSameCannotUpdate", TextLabelGroupName),
            testClient.GetNextReply().Text);
        Assert.Contains(GetTextLabelValue("AdviceCannotUpdateWithoutAmount", TextLabelGroupName),
            testClient.GetNextReply().Text);
        Assert.Contains(GetTextLabelValue("AdviceCannotUpdateContactUs", TextLabelGroupName),
            testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task User_Changes_PaymentAmount_With_One_ValidationCheck()
    {
        //Arrange
        SwapMockedServices(22);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Contains(GetTextLabelValue("PaymentDataDirectDebitWithPreferedPaymentDay", TextLabelGroupName)
            .Replace("{amount}", "250")
            .Replace("{paymentday}", "1"), reply.Text);
        Assert.Contains(GetTextLabelValue("AskToChangeAmount", TextLabelGroupName), testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("ja");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmountPrompt", TextLabelGroupName), reply.Text);

        reply = await testClient.SendActivityAsync("5");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmount", TextLabelGroupName)
            .Replace("{minAmount}", "100")
            .Replace("{maxAmount}", "700"), reply.Text);

        reply = await testClient.SendActivityAsync("Probeer opnieuw");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmountPrompt", TextLabelGroupName), reply.Text);

        reply = await testClient.SendActivityAsync("155");
        Assert.StartsWith(GetTextLabelValue("SavedAmountStatusOk", TextLabelGroupName)
            .Replace("{amount}", "155"), reply.Text);
    }

    [Fact]
    public async Task User_Changes_PaymentAmount_With_ChoiceSkip()
    {
        //Arrange
        SwapMockedServices(22);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Contains(GetTextLabelValue("PaymentDataDirectDebitWithPreferedPaymentDay", TextLabelGroupName)
            .Replace("{amount}", "250")
            .Replace("{paymentday}", "1"), reply.Text);
        Assert.Contains(GetTextLabelValue("AskToChangeAmount", TextLabelGroupName), testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("ja");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmountPrompt", TextLabelGroupName), reply.Text);

        reply = await testClient.SendActivityAsync("5");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmount", TextLabelGroupName)
            .Replace("{minAmount}", "100")
            .Replace("{maxAmount}", "700"), reply.Text);
        // providing a choice
        reply.SuggestedActions.Actions.Any().Should().BeTrue();

        // but sending amount right away, should work also
        reply = await testClient.SendActivityAsync("155");
        Assert.StartsWith(GetTextLabelValue("SavedAmountStatusOk", TextLabelGroupName)
            .Replace("{amount}", "155"), reply.Text);
    }

    [Fact]
    public async Task User_Unable_ToChange_PaymentAmount_Error_ActiveDebtCollection()
    {
        //Arrange
        SwapMockedServices(24);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Contains(GetTextLabelValue("PaymentDataDirectDebitWithPreferedPaymentDay", TextLabelGroupName)
            .Replace("{amount}", "250")
            .Replace("{paymentday}", "1"), reply.Text);
        Assert.Contains(GetTextLabelValue("AskToChangeAmount", TextLabelGroupName), testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("ja");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmountPrompt", TextLabelGroupName), reply.Text);

        reply = await testClient.SendActivityAsync("5");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmount", TextLabelGroupName)
            .Replace("{minAmount}", "100")
            .Replace("{maxAmount}", "700"), reply.Text);

        reply = await testClient.SendActivityAsync("Probeer opnieuw");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmountPrompt", TextLabelGroupName), reply.Text);

        reply = await testClient.SendActivityAsync("155");
        Assert.StartsWith(GetTextLabelValue("SavedAmountStatusActiveDebtCollection", TextLabelGroupName)
            .Replace("{amount}", "250"), reply.Text);
        Assert.StartsWith(GetTextLabelValue("SavedAmountStatusActiveDebtCollectionAnswer", TextLabelGroupName),
            testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task User_Unable_ToChange_PaymentAmount_Error_YearlyInvoiceEstimatedOnMeterReadings()
    {
        //Arrange
        SwapMockedServices(25);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Contains(GetTextLabelValue("PaymentDataDirectDebitWithPreferedPaymentDay", TextLabelGroupName)
            .Replace("{amount}", "250")
            .Replace("{paymentday}", "1"), reply.Text);
        Assert.Contains(GetTextLabelValue("AskToChangeAmount", TextLabelGroupName), testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("ja");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmountPrompt", TextLabelGroupName), reply.Text);

        reply = await testClient.SendActivityAsync("5");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmount", TextLabelGroupName)
            .Replace("{minAmount}", "100")
            .Replace("{maxAmount}", "700"), reply.Text);

        reply = await testClient.SendActivityAsync("Probeer opnieuw");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmountPrompt", TextLabelGroupName), reply.Text);

        reply = await testClient.SendActivityAsync("155");
        Assert.Contains(GetTextLabelValue("SavedAmountStatusYearlyInvoiceEstimatedOnMeterReadings", TextLabelGroupName)
            .Replace("{amount}", "250"), reply.Text);
        Assert.StartsWith(GetTextLabelValue("YearlyInvoiceEstimatedOnMeterReadingsContactUs", TextLabelGroupName),
            testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task User_Unable_ToChange_PaymentAmount_Error_PaymentAmountTooLow()
    {
        //Arrange
        SwapMockedServices(26);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Contains(GetTextLabelValue("PaymentDataDirectDebitWithPreferedPaymentDay", TextLabelGroupName)
            .Replace("{amount}", "250")
            .Replace("{paymentday}", "1"), reply.Text);
        Assert.Contains(GetTextLabelValue("AskToChangeAmount", TextLabelGroupName), testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("ja");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmountPrompt", TextLabelGroupName), reply.Text);

        reply = await testClient.SendActivityAsync("5");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmount", TextLabelGroupName)
            .Replace("{minAmount}", "100")
            .Replace("{maxAmount}", "700"), reply.Text);

        reply = await testClient.SendActivityAsync("Probeer opnieuw");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmountPrompt", TextLabelGroupName), reply.Text);

        reply = await testClient.SendActivityAsync("155");
        Assert.Contains(GetTextLabelValue("SavedAmountStatusPaymentAmountTooLow", TextLabelGroupName), reply.Text);
        Assert.StartsWith(GetTextLabelValue("SavedAmountStatusPaymentAmountTooLowAnswer", TextLabelGroupName),
            testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task User_Unable_ToChange_PaymentAmount_Error_NotAvailable()
    {
        //Arrange
        SwapMockedServices(27);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Contains(GetTextLabelValue("PaymentDataDirectDebitWithPreferedPaymentDay", TextLabelGroupName)
            .Replace("{amount}", "250")
            .Replace("{paymentday}", "1"), reply.Text);
        Assert.Contains(GetTextLabelValue("AskToChangeAmount", TextLabelGroupName), testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("ja");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmountPrompt", TextLabelGroupName), reply.Text);

        reply = await testClient.SendActivityAsync("5");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmount", TextLabelGroupName)
            .Replace("{minAmount}", "100")
            .Replace("{maxAmount}", "700"), reply.Text);

        reply = await testClient.SendActivityAsync("Probeer opnieuw");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmountPrompt", TextLabelGroupName), reply.Text);

        reply = await testClient.SendActivityAsync("155");
        Assert.Contains(GetTextLabelValue("SavedAmountStatusNotAvailable", TextLabelGroupName)
            .Replace("{amount}", "250"), reply.Text);
        Assert.StartsWith(GetTextLabelValue("SavedAmountStatusNotAvailableAnswer", TextLabelGroupName),
            testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task User_ChoseAdvancePaymentBelowMin_PaymentAmount_Error_NotAvailable()
    {
        //Arrange
        SwapMockedServices(27);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Contains(GetTextLabelValue("PaymentDataDirectDebitWithPreferedPaymentDay", TextLabelGroupName)
            .Replace("{amount}", "250")
            .Replace("{paymentday}", "1"), reply.Text);
        Assert.Contains(GetTextLabelValue("AskToChangeAmount", TextLabelGroupName), testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("ja");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmountPrompt", TextLabelGroupName), reply.Text);

        reply = await testClient.SendActivityAsync("5");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmount", TextLabelGroupName)
            .Replace("{minAmount}", "100")
            .Replace("{maxAmount}", "700"), reply.Text);
        reply.SuggestedActions.Actions.Any(a => (string)a.Value == "Lager termijnbedrag kiezen").Should().BeTrue();

        reply = await testClient.SendActivityAsync(GetTextLabelValue("LowerAmountChoice", TextLabelGroupName))
            ;
        Assert.Null(reply?.Text);
    }

    [Fact]
    public async Task User_ChoseAdvancePaymentBeyondMax_PaymentAmount_Error_NotAvailable()
    {
        //Arrange
        SwapMockedServices(27);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);
        Assert.Contains(GetTextLabelValue("PaymentDataDirectDebitWithPreferedPaymentDay", TextLabelGroupName)
            .Replace("{amount}", "250")
            .Replace("{paymentday}", "1"), reply.Text);
        Assert.Contains(GetTextLabelValue("AskToChangeAmount", TextLabelGroupName), testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("ja");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmountPrompt", TextLabelGroupName), reply.Text);

        reply = await testClient.SendActivityAsync("5000");
        Assert.StartsWith(GetTextLabelValue("AskForValueAdvancePaymentAmount", TextLabelGroupName)
            .Replace("{minAmount}", "100")
            .Replace("{maxAmount}", "700"), reply.Text);
        reply.SuggestedActions.Actions.Any(a => (string)a.Value == "Hoger termijnbedrag kiezen").Should().BeTrue();

        reply = await testClient.SendActivityAsync(GetTextLabelValue("HigherAmountChoice", TextLabelGroupName));
        Assert.Null(reply?.Text);
    }

    [Fact]
    public async Task User_With_Advance_Payment_Less_Than_Nine_Euros_Gets_Correct_Message()
    {
        // Arrange
        const int customerIdWith8EurosAdvancePayment = 4;
        const string textLabelName = "PaymentOnlyOnYearNoteDisclaimer";

        SwapMockedServices(customerIdWith8EurosAdvancePayment);
        var testClient = SetupMainDialog();

        var expectedTextLabel = GetTextLabelValue(textLabelName, TextLabelGroupName);

        // Act
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT);

        // Assert
        reply.Text.Should().Be(expectedTextLabel);
    }

    private void SwapMockedServices(int customerId,
        AdvancePaymentAdviceStatus advancePaymentAdviceStatus = AdvancePaymentAdviceStatus.Ok, decimal advice = 1000)
    {
        var sessionManagerMock = new Mock<ISessionManager>();
        var financialsServiceMock = new Mock<IFinancialsService>();
        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        var storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_AdvancePaymentAmountDialog";
        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(new List<Storage.Client.Models.TextLabelModel> { }));
        sessionManagerMock.Setup(x =>
                x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Customer = new CustomerData { Label = Label.Eneco },
                Verification =
                    new VerificationData
                    {
                        CustomerId = customerId,
                        PostalCode = "3025CB",
                        HouseNumber = 9,
                        IsFound = true,
                        IsVerified = true,
                        MfaSuccessful = true,
                        VerificationCommunicated = false
                    },
                AdvancePayment = new AdvancePaymentData
                {
                    NextChargeDate = customerId < 6 ? DateTime.Now.AddDays(3) : DateTime.Now.AddMonths(3),
                    AdvancePaymentCurrentAmount = 100
                },
                SelectedAccount = new AccountData { AccountId = 2 },
                ActiveAccounts = new List<AccountData> { new() { AccountId = 2 } }
            });

        customersRepositoryMock.Setup(x =>
                x.VerifyCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(),
                    It.Is<RequestDataCustomerVerification>(x => x.Data.CustomerId >= 20)))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataCustomerVerificationResponse { Data = new CustomerVerificationResponse { } }
            });

        customersRepositoryMock.Setup(x =>
                x.GetCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataCustomerModel
                {
                    Data = new CustomerModel
                    {
                        Id = customerId,
                        Person =
                            new PersonModel { Initials = "Paul", Surname = "Testman", Gender = Gender.Male },
                        CustomerType = CustomerType.Person,
                        Accounts = new List<CustomerAccountModel>
                        {
                            new CustomerAccountModel
                            {
                                CustomerId = customerId,
                                Id = 2,
                                Active = true,
                                NextChargeDate =
                                    customerId < 6
                                        ? DateTime.Now.AddDays(3)
                                        : DateTime.Now.AddMonths(3),
                                Address = new AddressModel
                                {
                                    PostalCode = "3025CB", HouseNumber = 9, HouseNumberSuffix = "A"
                                }
                            }
                        }
                    }
                }
            });


        financialsServiceMock.Setup(x =>
                x.GetAdvancePaymentAmountAndDayOfPayment(It.Is<DialogData>(x => x.Verification.CustomerId >= 20),
                    It.IsAny<CustomerAccountModel>()))
            .ReturnsAsync(new AdvancePayment
            {
                Amount = 250,
                Account = new CustomerAccountModel
                {
                    CustomerId = customerId,
                    Active = true,
                    Address = new AddressModel
                    {
                        Street = "Sesamstraat", HouseNumber = 12, City = "Rotterdam", PostalCode = "1234AB"
                    }
                },
                Preferences = new FinancialPreferences
                {
                    PaymentDayOfMonth = 1, PaymentMethodIsDirectDebit = customerId > 20
                }
            });

        financialsServiceMock.Setup(x =>
                x.GetAdvancePaymentAmountAndDayOfPayment(It.Is<DialogData>(x => x.Verification.CustomerId == 2),
                    It.IsAny<CustomerAccountModel>()))
            .ReturnsAsync(new AdvancePayment { Amount = 0 });

        financialsServiceMock.Setup(x =>
                x.GetAdvancePaymentAmountAndDayOfPayment(It.Is<DialogData>(x => x.Verification.CustomerId == 3),
                    It.IsAny<CustomerAccountModel>()))
            .ReturnsAsync(new AdvancePayment
            {
                Amount = 10, Preferences = new FinancialPreferences { PaymentDayOfMonth = 1 }
            });

        financialsServiceMock.Setup(x =>
                x.GetAdvancePaymentAmountAndDayOfPayment(It.Is<DialogData>(x => x.Verification.CustomerId == 4),
                    It.IsAny<CustomerAccountModel>()))
            .ReturnsAsync(new AdvancePayment
            {
                Amount = 8, Preferences = new FinancialPreferences { PaymentDayOfMonth = 1 }
            });

        financialsServiceMock.Setup(x =>
                x.GetAdvancePaymentAmountAndDayOfPayment(It.Is<DialogData>(x => x.Verification.CustomerId == 5),
                    It.IsAny<CustomerAccountModel>()))
            .ReturnsAsync(new AdvancePayment
            {
                Amount = 80,
                Account = new CustomerAccountModel { NextChargeDate = DateTime.Today.AddDays(3) },
                Preferences = new FinancialPreferences { PaymentDayOfMonth = 1 }
            });

        customersRepositoryMock.Setup(x => x.GetPaymentPlan(It.IsAny<Label>(), It.IsAny<BotChannel>(),
                It.Is<long>(c => customerId < 6), It.IsAny<int>()))
            .ReturnsAsync(
                new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new DC.Customers.Client.Models.ResponseDataPaymentPlan
                    {
                        Data = new PaymentPlan
                        {
                            CustomerId = customerId,
                            NextBillingCycle = new BillingCycle
                            {
                                ChargeDate = customerId < 6
                                    ? DateTime.Now.AddDays(3)
                                    : DateTime.Now.AddMonths(3)
                            }
                        }
                    }
                });

        customersRepositoryMock
            .Setup(mock => mock.GetPaymentPlan(It.IsAny<Label>(), It.IsAny<BotChannel>(),
                It.Is<long>(c => customerId == 4), It.IsAny<int>()))
            .ReturnsAsync(
                new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new DC.Customers.Client.Models.ResponseDataPaymentPlan
                    {
                        Data = new PaymentPlan
                        {
                            CustomerId = customerId,
                            NextBillingCycle = new BillingCycle { ChargeDate = DateTime.Today.AddMonths(3) }
                        }
                    }
                });

        customersRepositoryMock.Setup(x => x.GetPaymentPlan(It.IsAny<Label>(), It.IsAny<BotChannel>(),
                It.Is<long>(c => customerId >= 6), It.IsAny<int>()))
            .ReturnsAsync(
                new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new DC.Customers.Client.Models.ResponseDataPaymentPlan
                    {
                        Data = new PaymentPlan { CustomerId = customerId }
                    }
                });

        financialsServiceMock.Setup(x =>
                x.GetAdvancePaymentAdviceV2(It.Is<DialogData>(x => x.Verification.CustomerId == 2)))
            .ReturnsAsync(new AdvancePaymentAdviceV2Response
            {
                Limits = new AdvancePaymentAdviceV2Limits { MaximumRange = 100 }
            });

        financialsServiceMock.Setup(x =>
                x.GetAdvancePaymentAdviceV2(It.Is<DialogData>(x => x.Verification.CustomerId == 3)))
            .ReturnsAsync(new AdvancePaymentAdviceV2Response
            {
                Limits = new AdvancePaymentAdviceV2Limits { MaximumRange = 100 },
                AdviceStatus = advancePaymentAdviceStatus
            });

        financialsServiceMock.Setup(x =>
                x.GetAdvancePaymentAdviceV2(It.Is<DialogData>(x =>
                    x.Verification.CustomerId >= 4 && x.Verification.CustomerId <= 5)))
            .ReturnsAsync(new AdvancePaymentAdviceV2Response
            {
                Limits = new AdvancePaymentAdviceV2Limits { MaximumRange = 100 },
                AdviceStatus = advancePaymentAdviceStatus
            });

        financialsServiceMock.Setup(x =>
                x.GetAdvancePaymentAdviceV2(It.Is<DialogData>(x => x.Verification.CustomerId == 20)))
            .ReturnsAsync(new AdvancePaymentAdviceV2Response
            {
                Current = 100,
                Limits = new AdvancePaymentAdviceV2Limits { MinimumRange = 100, MaximumRange = 700 },
                AdviceStatus = advancePaymentAdviceStatus
            });

        financialsServiceMock.Setup(x =>
                x.GetAdvancePaymentAdviceV2(It.Is<DialogData>(x => x.Verification.CustomerId == 21)))
            .ReturnsAsync(new AdvancePaymentAdviceV2Response
            {
                Current = 100,
                Limits = new AdvancePaymentAdviceV2Limits { MinimumRange = 100, MaximumRange = 700 },
                Advice = advice,
                AdviceStatus = advancePaymentAdviceStatus
            });

        financialsServiceMock.Setup(x =>
                x.GetAdvancePaymentAdviceV2(It.Is<DialogData>(x => x.Verification.CustomerId >= 22)))
            .ReturnsAsync(new AdvancePaymentAdviceV2Response
            {
                Current = 100,
                Limits = new AdvancePaymentAdviceV2Limits { MinimumRange = 100, MaximumRange = 700 },
                AdviceStatus = advancePaymentAdviceStatus
            });

        financialsServiceMock.Setup(x =>
                x.GetAdvancePaymentAdviceV2(It.Is<DialogData>(x => x.Verification.CustomerId == 30)))
            .ReturnsAsync(new AdvancePaymentAdviceV2Response
            {
                Limits = new AdvancePaymentAdviceV2Limits
                {
                    MaximumRange = 100, UpdateStatus = AdvancePaymentUpdateStatus.CannotUpdate
                },
                AdviceStatus = advancePaymentAdviceStatus,
                Advice = 250
            });

        SetupUpdateAdvancePaymentAmount(financialsServiceMock, 22, AdvancePaymentAdviceStatus.Ok);
        SetupUpdateAdvancePaymentAmount(financialsServiceMock, 23, AdvancePaymentAdviceStatus.YearNoteTooClose);
        SetupUpdateAdvancePaymentAmount(financialsServiceMock, 24, AdvancePaymentAdviceStatus.ActiveDebtCollection);
        SetupUpdateAdvancePaymentAmount(financialsServiceMock, 25,
            AdvancePaymentAdviceStatus.YearlyInvoiceEstimatedOnMeterReadings);
        SetupUpdateAdvancePaymentAmount(financialsServiceMock, 26, AdvancePaymentAdviceStatus.PaymentAmountTooLow);
        SetupUpdateAdvancePaymentAmount(financialsServiceMock, 27, AdvancePaymentAdviceStatus.NotAvailable);
        SetupUpdateAdvancePaymentAmount(financialsServiceMock, 28, AdvancePaymentAdviceStatus.Default);

        var validator = new Mock<IDialogValidators>();
        validator.Setup(x => x.CustomerDialogValidator).Returns(new CustomerDialogValidator(sessionManagerMock.Object));
        validator.Setup(x => x.FinancialsDialogValidator)
            .Returns(new FinancialsDialogValidator(sessionManagerMock.Object));
        validator.Setup(x => x.ProductsDialogValidator).Returns(new ProductsDialogValidator(sessionManagerMock.Object));

        _services.SwapTransient(provider => validator.Object);
        _services.SwapTransient(provider => financialsServiceMock.Object);
        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => sessionManagerMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }

    private static void SetupUpdateAdvancePaymentAmount(
        Mock<IFinancialsService> financialsServiceMock,
        long customerId,
        AdvancePaymentAdviceStatus advancePaymentAdviceStatus)
    {
        financialsServiceMock.Setup(x =>
                x.UpdateAdvancePaymentAmount(It.Is<DialogData>(x => x.Verification.CustomerId == customerId),
                    It.IsAny<int>()))
            .ReturnsAsync(
                new AdvancedPaymentAdviceExtraStatus { Status = advancePaymentAdviceStatus, ErrorCodes = [] });
    }
}
