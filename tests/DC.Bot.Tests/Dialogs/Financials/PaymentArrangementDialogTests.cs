﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Financials.Client.Models;
using DC.Repositories.Base.Enumerations;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Moq;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;


namespace DC.Bot.Tests.Dialogs.Financials;

[Collection("Sequential")]
public class PaymentArrangementDialogTests : TestBase
{
    private readonly Mock<IDcCustomersRepository> _customersRepositoryMock;
    private readonly Mock<IFinancialsService> _financialsServiceMock;
    private readonly Mock<IStorageService> _storageServiceMock;

    public PaymentArrangementDialogTests()
    {
        _customersRepositoryMock = new Mock<IDcCustomersRepository>();
        _financialsServiceMock = new Mock<IFinancialsService>();
        _storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_PaymentArrangementDialog";
        GetAllTextLabelsFromExport();

        _storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));
    }

    [Fact]
    public async Task GetArrangement_WithInActiveAccount()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PAYMENTARRANGEMENT);
        Assert.Contains(GetTextLabelValue("CouldNotFindActivePaymentArrangement", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetArrangement_WithActiveAccount()
    {
        //Arrange
        SwapMockedServices(2);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PAYMENTARRANGEMENT);

        Assert.Contains(GetTextLabelValue("OutstandingAmountForAddress", TextLabelGroupName)
                            .Replace("{account}", "2")
                            .Replace("{street}", "Straat")
                            .Replace("{housenumber}", "9")
                            .Replace("{housenumbersuffix}", "")
                            .Replace("{city}", "Roffa")
                            .Replace("{outstandingAmount}", $"50.00"),
                        testClient.GetNextReply().Text);

        var nextReplyText = testClient.GetNextReply().Text;
        nextReplyText.Contains(GetTextLabelValue("PaymentArrangementForInvoiceNumber", TextLabelGroupName));
        nextReplyText.Contains("1111");

        Assert.Contains(GetTextLabelValue("WhereToViewInvoices", TextLabelGroupName)
            .Replace("{mijnEnecoOpenstaandeNotasContent}", "[Mijn Eneco](https://www.eneco.nl/mijn-eneco/notas/openstaand/)"),
            testClient.GetNextReply().Text);
        Assert.Contains(GetTextLabelValue("CurrentPaidAmount", TextLabelGroupName)
            .Replace("{amountPaid}", "€110"), testClient.GetNextReply().Text);

        Assert.Contains(GetTextLabelValue("NumberOfPaymentTerms", TextLabelGroupName)
            .Replace("{numberOfTerms}", "2"), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetArrangement_With_UnverifiedCustomer()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithUnverifiedCustomer(DialogCommands._PAYMENTARRANGEMENT);
        Assert.Contains(GetTextLabelValue("CouldNotFindData", "Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }

    private void SwapMockedServices(long customerId, bool activeAccount = true)
    {
        _customersRepositoryMock.SetupCustomerTestData(customerId, activeAccount: activeAccount);

        _financialsServiceMock.Setup(x =>
                x.GetPaymentArrangement(It.Is<DialogData>(x => x.Verification.CustomerId == 2), It.IsAny<int>()))
            .ReturnsAsync(new PaymentArrangementModel
            {
                TotalPaid = 110,
                OutstandingAmount = 50,
                Invoices = new List<InvoiceInArrangement>
                {
                    new() {
                        Id = 1111
                    }
                },
                Terms = new List<Term>
                {
                    new() { Id = 5555, Amount = 10, IsPaidStatus = false },
                    new() { Id = 6666, Amount = 20, IsPaidStatus = false },
                    new() { Id = 7777, Amount = 30, IsPaidStatus = true }
                }
            });

        var validator = new Mock<IDialogValidators>();
        validator.SetupDialogValidatorWithSessionManager(customerId);
        _services.SwapTransient(provider => validator.Object);

        _services.SwapTransient(provider => validator.Object);
        _services.SwapTransient(provider => _customersRepositoryMock.Object);
        _services.SwapTransient(provider => _financialsServiceMock.Object);
        _services.SwapTransient(provider => _storageServiceMock.Object);
    }
}
