﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Customers.Client.Models;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.Financials;
using DC.Domain.Models.Products;
using DC.Financials.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using TransactionStatus = DC.Bot.BusinessLogic.Enumerations.TransactionStatus;

namespace DC.Bot.Tests.Dialogs.Financials;

[Collection("Sequential")]
public class AdvancePaymentAdviceDialogTests : TestBase
{
    [Fact]
    public async Task GetAdvancePaymentAdvice_with_VerifiedCustomer_WithInActiveAccount()
    {
        var sessionManager = GetSessionsManager(1);
        //Arrange
        SwapMockedServices(sessionManager, 2, false);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ADVANCE_PAYMENT_ADVICE);

        Assert.Contains(GetTextLabelValue("CouldNotFindActiveAccount", "Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }

    [Theory]
    [InlineData(AdvancePaymentAdviceStatus.NotAvailable, TransactionStatus.Unhappy, false, false, true, false)]
    [InlineData(AdvancePaymentAdviceStatus.NotAvailable, TransactionStatus.Unhappy, false, false, false, false)]
    [InlineData(AdvancePaymentAdviceStatus.HasRedelivery, TransactionStatus.UnhappyRedelivery, true)]
    [InlineData(AdvancePaymentAdviceStatus.HasWarmth, TransactionStatus.UnhappyWarmth, false, true)]
    [InlineData(AdvancePaymentAdviceStatus.NewCustomer, TransactionStatus.UnhappyNewCustomer)]
    [InlineData(AdvancePaymentAdviceStatus.SmartAndDumbMeter, TransactionStatus.Unhappy)]
    [InlineData(AdvancePaymentAdviceStatus.YearNoteTooClose, TransactionStatus.UnhappyCloseToYearNote)]
    [InlineData(AdvancePaymentAdviceStatus.NoAdviceYet, TransactionStatus.Unhappy)]
    [InlineData(AdvancePaymentAdviceStatus.MeterChanged, TransactionStatus.Unhappy)]
    [InlineData(AdvancePaymentAdviceStatus.MeterInError, TransactionStatus.Unhappy)]
    [InlineData(AdvancePaymentAdviceStatus.ActiveDebtCollection, TransactionStatus.Unhappy)]
    [InlineData(AdvancePaymentAdviceStatus.NoActiveMandate, TransactionStatus.Unhappy)]
    [InlineData(AdvancePaymentAdviceStatus.HasRedelivery, TransactionStatus.UnhappyWarmthAndRedelivery, true, true)]
    [InlineData(AdvancePaymentAdviceStatus.Ok, TransactionStatus.Success, false, false, true, true, 15)]
    [InlineData(AdvancePaymentAdviceStatus.AdviceTooOld, TransactionStatus.UnhappyMeterreadings)]
    [InlineData(AdvancePaymentAdviceStatus.AdviceFromLastYear, TransactionStatus.Unhappy)]
    [InlineData(AdvancePaymentAdviceStatus.UpdateAdvice, TransactionStatus.UnhappyMeterreadings)]
    [InlineData(AdvancePaymentAdviceStatus.AdviceTooLow, TransactionStatus.Unhappy)]
    [InlineData(AdvancePaymentAdviceStatus.AdviceTooHigh, TransactionStatus.Unhappy)]
    public async Task GetAdvancePaymentAdvice_Returns_Correct_TransactionStatus(AdvancePaymentAdviceStatus adviceStatus, TransactionStatus transactionStatus, bool hasRedelivery = false, bool hasWarmth = false, bool isSmart = true, bool isSmartReadingAllowed = true, int? enddatePriceInDays = null)
    {
        DateTime? enddateProduct = enddatePriceInDays.HasValue ? DateTime.Today.AddDays(enddatePriceInDays.Value) : null;
        var sessionManager = GetSessionsManager(1);

        //Arrange
        SwapMockedServices(sessionManager, 1,
            advancePaymentAdviceStatus: adviceStatus,
            hasWarmth: hasWarmth,
            hasRedelivery: hasRedelivery,
            isSmart: isSmart,
            isSmartReadingAllowed: isSmartReadingAllowed,
            enddatePrice: enddateProduct);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ADVANCE_PAYMENT_ADVICE);
        var text = testClient.GetNextReply()?.Text;
        if (transactionStatus == TransactionStatus.SuccessAdviceAlMostKnown)
        {
            Assert.Contains(GetTextLabelValue("AdviceAlMostAvailable", TextLabelGroupName).Replace("{date}", $"{enddateProduct:dd-MM-yyyy}"), text);
        }
        else if (transactionStatus == TransactionStatus.Success)
        {
            Assert.Contains(GetTextLabelValue("AdviceAvailable", TextLabelGroupName), text);
        }
        else if (adviceStatus == AdvancePaymentAdviceStatus.YearNoteTooClose)
        {
            Assert.Contains(GetTextLabelValue("YearNoteTooClose", TextLabelGroupName).Replace("{yearnotedate}", $"{DateTime.Today:dd-MM-yyyy}"), text);
        }
        else if (adviceStatus == AdvancePaymentAdviceStatus.NewCustomer)
        {
            Assert.Contains(GetTextLabelValue("NoAdviceYet", TextLabelGroupName), text);
        }
        else if (adviceStatus == AdvancePaymentAdviceStatus.HasWarmth && !hasRedelivery)
        {
            Assert.Contains(GetTextLabelValue("Warmth", TextLabelGroupName), text);
        }
        else if (adviceStatus == AdvancePaymentAdviceStatus.HasRedelivery && !hasWarmth)
        {
            Assert.Contains(GetTextLabelValue("Redelivery", TextLabelGroupName), text);
        }
        else if ((adviceStatus is
            AdvancePaymentAdviceStatus.HasRedelivery or
            AdvancePaymentAdviceStatus.HasWarmth) && hasWarmth && hasRedelivery)
        {
            Assert.Contains(GetTextLabelValue("WarmthAndRedelivery", TextLabelGroupName), text);
        }
        else if (adviceStatus is
            AdvancePaymentAdviceStatus.UpdateAdvice or
            AdvancePaymentAdviceStatus.AdviceTooOld)
        {
            Assert.Contains(GetTextLabelValue("UpdateAdvice", TextLabelGroupName), text);
        }
        else if (adviceStatus is
            AdvancePaymentAdviceStatus.SmartAndDumbMeter or
            AdvancePaymentAdviceStatus.ActiveDebtCollection or
            AdvancePaymentAdviceStatus.MeterChanged or
            AdvancePaymentAdviceStatus.NotAvailable or
            AdvancePaymentAdviceStatus.NoActiveMandate or
            AdvancePaymentAdviceStatus.MeterInError or
            AdvancePaymentAdviceStatus.AdviceTooLow or
            AdvancePaymentAdviceStatus.AdviceTooHigh or
            AdvancePaymentAdviceStatus.NoMeters or
            AdvancePaymentAdviceStatus.PaymentAmountIncorrect or
            AdvancePaymentAdviceStatus.PaymentAmountTooLow or
            AdvancePaymentAdviceStatus.PaymentAmountNotFilled or
            AdvancePaymentAdviceStatus.PaymentAmountTooLowToInvoice or
            AdvancePaymentAdviceStatus.PaymentMethodShouldBeDirectDebit or
            AdvancePaymentAdviceStatus.PaymentMethodShouldNotBeDirectDebit or
            AdvancePaymentAdviceStatus.YearlyInvoiceEstimatedOnMeterReadings or
            AdvancePaymentAdviceStatus.IbanMissingOrIncorrect)
        {
            Assert.Contains(GetTextLabelValue("AdviceNotAvailable", TextLabelGroupName), text);
        }

        if (transactionStatus is TransactionStatus.Success)
        {
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    transactionStatus,
                    It.IsAny<CancellationToken>()), Times.Never());

            Assert.Contains(GetTextLabelValue("CurrentAdvancePaymentAmount", "Bot_AdvancePaymentAmountDialog").Replace("{amount}", $"100"),
                            testClient.GetNextReply().Text);
        }
        else
        {
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    transactionStatus,
                    It.IsAny<CancellationToken>()), Times.Once());
        }
    }

    [Fact]
    public async Task GetAdvancePaymentAdvice_CustomerHasOutstandingDebt_ReturnsExpectedResult()
    {
        // Arrange
        var sessionManager = GetSessionsManager(1);

        SwapMockedServices(sessionManager, 1,
            advancePaymentAdviceStatus: AdvancePaymentAdviceStatus.NotAvailable,
            mvsErrorCode: "MVS-17116");
        DialogTestClient testClient = SetupMainDialog();

        //Act
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ADVANCE_PAYMENT_ADVICE);
        var text = testClient.GetNextReply()?.Text;

        // Assert
        Assert.Equal(GetTextLabelValue("AdviceNotAvailable", "Bot_AdvancePaymentAdviceDialog"), text);
        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.UnhappyOutstandingDebt,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    private void SwapMockedServices(
        Mock<ISessionManager> sessionManagerMock,
        long customerId,
        bool activeAccount = true,
        bool multiAccount = false,
        AdvancePaymentAdviceStatus advancePaymentAdviceStatus = AdvancePaymentAdviceStatus.Ok,
        bool hasRedelivery = false,
        bool hasWarmth = false,
        bool isSmart = true,
        bool isSmartReadingAllowed = true,
        DateTime? enddatePrice = null,
        string mvsErrorCode = null)
    {
        var financialsServiceMock = new Mock<IFinancialsService>();
        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        var storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_AdvancePaymentAdviceDialog";
        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));
        customersRepositoryMock.SetupCustomerTestData(customerId, activeAccount: activeAccount, multiAccount: multiAccount);
        if (hasWarmth)
        {
            customersRepositoryMock.Setup(c => c.GetAgreements(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListAgreement
                {
                    Data = new List<Agreement>
                    {
                        new Agreement
                        {
                            CustomerId = customerId,
                            AccountId  = 1,
                            IsActive = true,
                            Connection = new Connection
                            {
                                Meter = new MeteringDevice
                                {
                                    HasRedeliveryInMeter = hasRedelivery,
                                    IsSmart = isSmart,
                                    IsSmartMeterReadingAllowed = isSmartReadingAllowed
                                },
                                HasReturnDelivery = hasRedelivery,
                                UtilityType = UtilityType.Electricity
                            },
                            Products = new List<Product>
                            {
                                new Product
                                {
                                    EndDate = enddatePrice,
                                    IsActive = true,
                                    ProductType = ProductType.Electricity,
                                }
                            }
                        },
                        new Agreement
                        {
                            CustomerId = customerId,
                            AccountId  = 1,
                            IsActive = true,
                            Connection = new Connection
                            {
                                Meter = new MeteringDevice
                                {
                                    HasRedeliveryInMeter = false,
                                    IsSmart = false,
                                    IsSmartMeterReadingAllowed = false
                                },
                                HasReturnDelivery = false,
                                UtilityType = UtilityType.Heat
                            }
                        }
                    }
                }
            });
        }
        else
        {
            customersRepositoryMock.Setup(c => c.GetAgreements(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListAgreement
                {
                    Data = new List<Agreement>
                    {
                        new Agreement
                        {
                            CustomerId = customerId,
                            AccountId  = 1,
                            IsActive = true,
                            Connection = new Connection
                            {
                                Meter = new MeteringDevice
                                {
                                    HasRedeliveryInMeter = hasRedelivery,
                                    IsSmart = isSmart,
                                    IsSmartMeterReadingAllowed = isSmartReadingAllowed
                                },
                                HasReturnDelivery = hasRedelivery,
                                UtilityType = UtilityType.Electricity
                            },
                            Products = new List<Product>
                            {
                                new Product
                                {
                                    EndDate = enddatePrice,
                                    IsActive = true,
                                    ProductType = ProductType.Electricity
                                }
                            }
                        }
                    }
                }
            });
        }
        financialsServiceMock.Setup(x => x.GetAdvancePaymentAdviceV2(It.IsAny<DialogData>()))
            .ReturnsAsync(new AdvancePaymentAdviceV2Response
            {
                Advice = 100,
                Current = 100,
                YearNoteDate = DateTime.Today,
                AdviceStatus = advancePaymentAdviceStatus,
                MvsErrorCode = mvsErrorCode,
                StatusToggle = new AdviceStatusToggles
                {
                    ShowAdvice = true,
                    ShowForecast = true
                },
                Limits = new AdvancePaymentAdviceV2Limits
                {
                    MaximumRange = 500,
                    MaximumRangeWarning = 500,
                    MinimumRange = 150,
                    MinimumRangeWarning = 150,
                    UpdateStatus = AdvancePaymentUpdateStatus.CanUpdate
                }
            });
        financialsServiceMock.Setup(x => x.GetAdvancePaymentAmountAndDayOfPayment(It.IsAny<DialogData>(), It.IsAny<CustomerAccountModel>()))
        .ReturnsAsync(new AdvancePayment
        {
            Amount = 100,
            Preferences = new()
            {
                PaymentDayOfMonth = 2
            }
        });
        var validator = new Mock<IDialogValidators>();
        validator.SetupDialogValidatorWithSessionManager(customerId);
        _services.SwapTransient(provider => validator.Object);

        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => financialsServiceMock.Object);
        _services.SwapTransient(provider => sessionManagerMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }

    private static Mock<ISessionManager> GetSessionsManager(long customerId, Label label = Label.Eneco)
    {
        var sessionManagerMock = new Mock<ISessionManager>();
        sessionManagerMock.Setup(x =>
                x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Customer = new CustomerData
                {
                    Label = label
                },
                Verification = new VerificationData
                {
                    CustomerId = customerId,
                    CustomerIdVerified = true
                }
            });

        return sessionManagerMock;
    }
}
