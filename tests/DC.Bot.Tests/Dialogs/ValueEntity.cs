﻿using Newtonsoft.Json;

namespace DC.Bot.BusinessLogic.Models;

/// <summary>
/// Template class of an entry in the Activity.Entities property to hold metadata.
/// </summary>
/// <typeparam name="T"></typeparam>
public class ValueEntity<T>
{
    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="type"></param>
    /// <param name="value"></param>
    public ValueEntity(string type, T value)
    {
        Type = type;
        Value = value;
    }

    /// <summary>
    /// Type of the metadata.
    /// </summary>
    [JsonProperty("type")]
    public string Type { get; set; }

    /// <summary>
    /// Value of the metadata of type T.
    /// </summary>
    [JsonProperty("value")]
    public T Value { get; set; }
}
