﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Extensions;
using DC.Customers.Client.Models;
using DC.Domain.Models.Products;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Usages;

[Collection("Sequential")]
public class IsmaMandateDialogTests : TestBase
{
    [Fact]
    public async Task Mandate_Unavailable200_NoSmartMeters_Test()
    {
        SwapMockedServices(true, false, false, false, false);
        DialogTestClient testClient = SetupMainDialog();
        var reply = await testClient.SendActivityAsync(DialogCommands._ISMA_MANDATE).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("NoSmartMeters", TextLabelGroupName));
    }

    [Fact]
    public async Task Mandate_Unavailable200_SmartMeters_Test()
    {
        SwapMockedServices(true, false, false, false, true);
        DialogTestClient testClient = SetupMainDialog();
        var reply = await testClient.SendActivityAsync(DialogCommands._ISMA_MANDATE).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("UnhappyFlow", TextLabelGroupName));
    }

    [Fact]
    public async Task Mandate_Unavailable404Test()
    {
        SwapMockedServices(false, false, false, false, false);
        DialogTestClient testClient = SetupMainDialog();
        var reply = await testClient.SendActivityAsync(DialogCommands._ISMA_MANDATE).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("NoSmartMeters", TextLabelGroupName));
    }

    [Fact]
    public async Task GetMandate_500Test()
    {
        SwapMockedServices(null, false, false, false, true);
        DialogTestClient testClient = SetupMainDialog();
        var reply = await testClient.SendActivityAsync(DialogCommands._ISMA_MANDATE).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("SomethingWentWrongContactUs", TextLabelGroupName));
    }

    [Fact]
    public async Task MandateInactive_DoNothingTest()
    {
        SwapMockedServices(true, true, false, true, true);
        DialogTestClient testClient = SetupMainDialog();
        var reply = await testClient.SendActivityAsync(DialogCommands._ISMA_MANDATE).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("AskForEnableIsmaMandate", TextLabelGroupName));
        reply = await testClient.SendActivityAsync("Niet nu").ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("DoNothingIsmaMandateInActive", TextLabelGroupName));
    }

    [Fact]
    public async Task MandateInactive_ActivateTest()
    {
        SwapMockedServices(true, true, false, true, true);
        DialogTestClient testClient = SetupMainDialog();
        var reply = await testClient.SendActivityAsync(DialogCommands._ISMA_MANDATE).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("AskForEnableIsmaMandate", TextLabelGroupName));
        reply = await testClient.SendActivityAsync("Akkoord").ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("IsmaMandateEnabled", TextLabelGroupName));
    }

    [Fact]
    public async Task MandateInactive_Activate_Exception_Test()
    {
        SwapMockedServices(true, true, false, false, true);
        DialogTestClient testClient = SetupMainDialog();
        var reply = await testClient.SendActivityAsync(DialogCommands._ISMA_MANDATE).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("AskForEnableIsmaMandate", TextLabelGroupName));
        reply = await testClient.SendActivityAsync("Akkoord").ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("SomethingWentWrongContactUs", TextLabelGroupName));
    }

    [Fact]
    public async Task MandateActive_DoNothingTest()
    {
        SwapMockedServices(true, true, true, true, true);
        DialogTestClient testClient = SetupMainDialog();
        var reply = await testClient.SendActivityAsync(DialogCommands._ISMA_MANDATE).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("AskForPermissionIsmaMandate", TextLabelGroupName));
        reply = await testClient.SendActivityAsync("Niet nu").ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("DoNothingIsmaMandateActive", TextLabelGroupName));
    }

    [Fact]
    public async Task MandateActive_Deactivate_DoNothingTest()
    {
        SwapMockedServices(true, true, true, true, true);
        DialogTestClient testClient = SetupMainDialog();
        var reply = await testClient.SendActivityAsync(DialogCommands._ISMA_MANDATE).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("AskForPermissionIsmaMandate", TextLabelGroupName));
        reply = await testClient.SendActivityAsync("Toestemming aanpassen").ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("AskForDisableIsmaMandate", TextLabelGroupName));
        reply = await testClient.SendActivityAsync("Toestemming behouden").ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("DoNothingIsmaMandateActive", TextLabelGroupName));
    }

    [Fact]
    public async Task MandateActive_Deactivate_ConfirmTest()
    {
        SwapMockedServices(true, true, true, true, true);
        DialogTestClient testClient = SetupMainDialog();
        var reply = await testClient.SendActivityAsync(DialogCommands._ISMA_MANDATE).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("AskForPermissionIsmaMandate", TextLabelGroupName));
        reply = await testClient.SendActivityAsync("Toestemming aanpassen").ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("AskForDisableIsmaMandate", TextLabelGroupName));
        reply = await testClient.SendActivityAsync("Toestemming intrekken").ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("IsmaMandateDisabled", TextLabelGroupName));
    }

    [Fact]
    public async Task MandateActive_Deactivate_Confirm_ExceptionTest()
    {
        SwapMockedServices(true, true, true, false, true);
        DialogTestClient testClient = SetupMainDialog();
        var reply = await testClient.SendActivityAsync(DialogCommands._ISMA_MANDATE).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("AskForPermissionIsmaMandate", TextLabelGroupName));
        reply = await testClient.SendActivityAsync("Toestemming aanpassen").ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("AskForDisableIsmaMandate", TextLabelGroupName));
        reply = await testClient.SendActivityAsync("Toestemming intrekken").ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("SomethingWentWrongContactUs", TextLabelGroupName));
    }

    private void SwapMockedServices(bool? getSuccess, bool isAvailable, bool isActive, bool updateSuccess, bool hasSmartMeters)
    {
        var sessionManagerMock = new Mock<ISessionManager>();
        var usagesRepositoryMock = new Mock<IDcUsagesRepository>();
        var customersServiceMock = new Mock<ICustomersService>();
        var customerRepositoryMock = new Mock<IDcCustomersRepository>();
        var storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_IsmaMandateDialog";
        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));
        sessionManagerMock.Setup(x => x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Customer = new CustomerData
                {
                    Label = Label.Eneco
                },
                Verification = new VerificationData
                {
                    CustomerId = 1,
                    PostalCode = "3025CB",
                    HouseNumber = 9,
                    IsFound = true,
                    IsVerified = true,
                    MfaSuccessful = true,
                    VerificationCommunicated = false
                },
                SelectedAccount = new AccountData
                {
                    AccountId = 1
                },
                ActiveAccounts = new List<AccountData>
                {
                    new()
                    {
                        AccountId = 1
                    }
                }
            });

        if (getSuccess == true)
        {
            usagesRepositoryMock.Setup(m => m.GetMandate(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), DC.Usages.Client.Models.ServiceProductType.Isma))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Body = new DC.Usages.Client.Models.ResponseDataListServiceProductVersionModel
                    {
                        Data = new List<DC.Usages.Client.Models.ServiceProductVersionModel>
                        {
                            new DC.Usages.Client.Models.ServiceProductVersionModel
                            {
                                IsActive = isActive,
                                IsAvailable = isAvailable,
                                ProductType = DC.Usages.Client.Models.ServiceProductType.Isma
                            }
                        }
                    },
                    Response = new System.Net.Http.HttpResponseMessage
                    {
                        StatusCode = System.Net.HttpStatusCode.OK
                    }
                });
        }
        else if (getSuccess == false)
        {
            usagesRepositoryMock.Setup(m => m.GetMandate(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), DC.Usages.Client.Models.ServiceProductType.Isma))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Body = new DC.Usages.Client.Models.ResponseDataListServiceProductVersionModel
                    {
                        Data = new List<DC.Usages.Client.Models.ServiceProductVersionModel>
                        {
                            new DC.Usages.Client.Models.ServiceProductVersionModel
                            {
                                IsActive = isActive,
                                IsAvailable = isAvailable,
                                ProductType = DC.Usages.Client.Models.ServiceProductType.Int
                            }
                        }
                    },
                    Response = new System.Net.Http.HttpResponseMessage
                    {
                        StatusCode = System.Net.HttpStatusCode.OK
                    }
                });
        }
        else
        {
            usagesRepositoryMock.Setup(m => m.GetMandate(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), DC.Usages.Client.Models.ServiceProductType.Isma))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Body = new Domain.Exceptions.ResponseModels.ErrorResponse
                    {
                        Errors = new List<Domain.Exceptions.ResponseModels.ErrorModel>
                        {
                            new Domain.Exceptions.ResponseModels.ErrorModel
                            {
                                Reference = Guid.NewGuid(),
                                Type = "TechnicalException"
                            }
                        }
                    },
                    Response = new System.Net.Http.HttpResponseMessage
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError
                    }
                });
        }

        if (updateSuccess)
        {
            usagesRepositoryMock.Setup(m => m.EnableMandate(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), DC.Usages.Client.Models.ServiceProductType.Isma))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Body = null,
                    Response = new System.Net.Http.HttpResponseMessage
                    {
                        StatusCode = System.Net.HttpStatusCode.NoContent
                    }
                });
            usagesRepositoryMock.Setup(m => m.DisableMandate(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), DC.Usages.Client.Models.ServiceProductType.Isma))
                .ReturnsAsync(new HttpOperationResponse<Domain.Exceptions.ResponseModels.ErrorResponse>
                {
                    Body = null,
                    Response = new System.Net.Http.HttpResponseMessage
                    {
                        StatusCode = System.Net.HttpStatusCode.NoContent
                    }
                });
        }
        else
        {
            usagesRepositoryMock.Setup(m => m.EnableMandate(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), DC.Usages.Client.Models.ServiceProductType.Isma))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Body = new Domain.Exceptions.ResponseModels.ErrorResponse
                    {
                        Errors = new List<Domain.Exceptions.ResponseModels.ErrorModel>
                        {
                            new Domain.Exceptions.ResponseModels.ErrorModel
                            {
                                Reference = Guid.NewGuid(),
                                Type = "TechnicalException"
                            }
                        }
                    },
                    Response = new System.Net.Http.HttpResponseMessage
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError
                    }
                });
            usagesRepositoryMock.Setup(m => m.DisableMandate(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), DC.Usages.Client.Models.ServiceProductType.Isma))
                .ReturnsAsync(new HttpOperationResponse<Domain.Exceptions.ResponseModels.ErrorResponse>
                {
                    Body = new Domain.Exceptions.ResponseModels.ErrorResponse
                    {
                        Errors = new List<Domain.Exceptions.ResponseModels.ErrorModel>
                        {
                            new Domain.Exceptions.ResponseModels.ErrorModel
                            {
                                Reference = Guid.NewGuid(),
                                Type = "TechnicalException"
                            }
                        }
                    },
                    Response = new System.Net.Http.HttpResponseMessage
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError
                    }
                });
        }

        customersServiceMock.Setup(m => m.GetCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(new CustomerModel
            {
                Accounts = new List<CustomerAccountModel>
                {
                    new CustomerAccountModel
                    {
                        Id = 1,
                        Active = true,
                        MeterDetails = new List<MeterDetail>
                        {
                            new MeterDetail
                            {
                                IsSmartMeter = hasSmartMeters,
                                IsSmartMeterReadingAllowed = hasSmartMeters,
                                ProductType = ProductType.Electricity.ToString()
                            }
                        },
                        Address = new AddressModel
                        {

                        }
                    }
                }
            });

        customerRepositoryMock.Setup(m => m.GetCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Body = new ResponseDataCustomerModel
                {
                    Data = new CustomerModel
                    {
                        Accounts = new List<CustomerAccountModel>
                        {
                            new CustomerAccountModel
                            {
                                Id = 1,
                                Active = true,
                                MeterDetails = new List<MeterDetail>
                                {
                                    new MeterDetail
                                    {
                                        IsSmartMeter = hasSmartMeters,
                                        IsSmartMeterReadingAllowed = hasSmartMeters,
                                        ProductType = ProductType.Electricity.ToString()
                                    }
                                },
                                Address = new AddressModel
                                {

                                }
                            }
                        }
                    }
                },
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                }
            });

        _services.SwapTransient(provider => usagesRepositoryMock.Object);
        _services.SwapTransient(provider => customersServiceMock.Object);
        _services.SwapTransient(provider => sessionManagerMock.Object);
        _services.SwapTransient(provider => customerRepositoryMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }
}
