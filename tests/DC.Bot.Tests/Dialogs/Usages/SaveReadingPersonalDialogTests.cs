﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using DC.Bot.BusinessLogic.Dialogs.Usages;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.General;
using DC.Domain.Models.Products;
using DC.Domain.Models.Usages;
using DC.Repositories.Base.Enumerations;
using DC.Usages.Client.Models;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Connector;
using Microsoft.Bot.Schema;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Time.Testing;
using Moq;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Usages;

[Collection("Sequential")]
public class SaveReadingPersonalDialogTests : TestBase
{
    private readonly Mock<IStorageService> _storageServiceMock = new();
    private readonly Mock<ISessionManager> _sessionManagerMock = new();
    private readonly Mock<ICustomersService> _customersServiceMock = new();
    private readonly Mock<IUsagesService> _usagesServiceMock = new();

    private readonly FakeTimeProvider _timeProvider = new();

    private readonly SaveReadingPersonalDialog _sut;

    public SaveReadingPersonalDialogTests()
    {
        TextLabelGroupName = "Bot_SaveReadingPersonalDialog";

        GetAllTextLabelsFromExport();

        _storageServiceMock
            .Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));

        _sut = new SaveReadingPersonalDialog(
            Mock.Of<ILoggerFactory>(),
            _sessionManagerMock.Object,
            Mock.Of<ILoggingService>(),
            _usagesServiceMock.Object,
            _storageServiceMock.Object,
            _customersServiceMock.Object,
            _timeProvider);
    }

    [Fact]
    public async Task CustomerIsNotVerified()
    {
        _sessionManagerMock
            .Setup(mock => mock.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Verification = new VerificationData
                {
                    IsVerified = false
                },
                Customer = new CustomerData
                {
                    Label = Label.Eneco
                }
            });

        var testClient = new DialogTestClient(Channels.Msteams, _sut);

        var reply = await testClient.SendActivityAsync(string.Empty);

        Assert.Equal(GetTextLabelValue("CouldNotFindData", TextLabelGroupName), reply.Text);
    }

    [Fact]
    public async Task CustomerDoesNotHaveActiveAccount()
    {
        _sessionManagerMock
            .Setup(mock => mock.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Verification = new VerificationData
                {
                    IsVerified = true,
                    CustomerId = 1
                },
                Customer = new CustomerData
                {
                    Label = Label.Eneco
                },
                SelectedAccount = null
            });

        var testClient = new DialogTestClient(Channels.Msteams, _sut);

        var reply = await testClient.SendActivityAsync(string.Empty);

        Assert.Equal(GetTextLabelValue("CouldNotFindData", TextLabelGroupName), reply.Text);
    }

    [Fact]
    public async Task CustomerDoesNotHaveAgreementForCurrentAccount()
    {
        _sessionManagerMock
            .Setup(mock => mock.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Verification = new VerificationData
                {
                    IsVerified = true,
                    CustomerId = 1
                },
                Customer = new CustomerData
                {
                    Label = Label.Eneco
                },
                SelectedAccount = new AccountData
                {
                    AccountId = 1
                }
            });

        _customersServiceMock
            .Setup(mock => mock.GetAgreements(It.IsAny<DialogData>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new()
                {
                    AccountId = 2
                }
            });

        var testClient = new DialogTestClient(Channels.Msteams, _sut);

        var reply = await testClient.SendActivityAsync(string.Empty);

        Assert.Equal(GetTextLabelValue("NoActiveAgreementForAccount", TextLabelGroupName), reply.Text);
        _sessionManagerMock
            .Verify(mock => mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.Is<TransactionStatus>(status => status == TransactionStatus.UnhappyNoContract),
                It.IsAny<CancellationToken>()),
                Times.Once);
    }

    [Theory]
    [InlineData(ProductType.Warmth, "NoReadingAvailableECOWarmth", TransactionStatus.UnhappyEcoWarmth)]
    [InlineData(ProductType.WarmthEkv, "NoReadingAvailableEKVWarmth", TransactionStatus.UnhappyEkvWarmth)]
    public async Task CustomerHasWarmthProduct(
        ProductType productType,
        string expectedTextLabel,
        TransactionStatus expectedTransactionStatus)
    {
        _sessionManagerMock
            .Setup(mock => mock.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Verification = new VerificationData
                {
                    IsVerified = true,
                    CustomerId = 1
                },
                Customer = new CustomerData
                {
                    Label = Label.Eneco
                },
                SelectedAccount = new AccountData
                {
                    AccountId = 1
                }
            });

        _customersServiceMock
            .Setup(mock => mock.GetAgreements(It.IsAny<DialogData>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new()
                {
                    AccountId = 1,
                    Connection = new Connection
                    {
                        AccountId = 1,
                        Meter = new MeteringDevice
                        {
                            IsSmartMeterReadingAllowed = false
                        }
                    },
                    Products = new List<Product>
                    {
                        new()
                        {
                            ProductType = productType
                        }
                    }
                }
            });

        var testClient = new DialogTestClient(Channels.Msteams, _sut);

        var reply = await testClient.SendActivityAsync(string.Empty);

        Assert.Equal(GetTextLabelValue(expectedTextLabel, TextLabelGroupName), reply.Text);

        _sessionManagerMock
            .Verify(mock => mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.Is<TransactionStatus>(status => status == expectedTransactionStatus),
                    It.IsAny<CancellationToken>()),
                Times.Once);
    }

    [Theory]
    [InlineData("meterstand doorgeven", TransactionStatus.SuccessReadingAddedLink)]
    [InlineData("niets doen", TransactionStatus.SuccessReadingAddedSkip)]
    [InlineData("correctie", TransactionStatus.MBFChatReadingAddedCorrection)]
    public async Task CustomerHasSubmittedReadingsLessThanSevenDaysAgo(
        string customerChoice,
        TransactionStatus expectedEndStatus)
    {
        _sessionManagerMock
            .Setup(mock => mock.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Verification = new VerificationData
                {
                    IsVerified = true,
                    CustomerId = 1
                },
                Customer = new CustomerData
                {
                    Label = Label.Eneco,
                    CurrentDomain = "test"
                },
                SelectedAccount = new AccountData
                {
                    AccountId = 1
                }
            });

        _customersServiceMock
            .Setup(mock => mock.GetAgreements(It.IsAny<DialogData>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new()
                {
                    AccountId = 1,
                    Connection = new Connection
                    {
                        AccountId = 1,
                        Meter = new MeteringDevice
                        {
                            IsSmartMeterReadingAllowed = false
                        }
                    },
                    Products = new List<Product>
                    {
                        new()
                        {
                            ProductType = ProductType.Electricity
                        }
                    }
                }
            });

        _usagesServiceMock
            .Setup(mock => mock.GetReadingForCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(new GetReadingOutputModel { ReadingId = 1, Address = new Address { HouseNumber = 123 } });

        _usagesServiceMock
            .Setup(mock => mock.GetReadingsHistoryForCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(
            [
                new()
                {
                    Date = _timeProvider.GetUtcNow().AddDays(-6).Date, Type = CollectorType.Periodic,
                }
            ]);

        var testClient = new DialogTestClient(Channels.Msteams, _sut);

        var reply = await testClient.SendActivityAsync(string.Empty);

        Assert.Equal(GetTextLabelValue("ReadingAddedWithin7DaysOfYearnote", TextLabelGroupName), reply.Text);

        reply = testClient.GetNextReply();

        Assert.Equal(GetTextLabelValue("IfNotCorrectAskForCorrection", TextLabelGroupName), reply.Text);

        reply = testClient.GetNextReply();

        Assert.Equal(GetTextLabelValue("YouCanAddReadingForInsights", TextLabelGroupName), reply.Text);

        await testClient.SendActivityAsync<IMessageActivity>(customerChoice);

        _sessionManagerMock
            .Verify(mock => mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.Is<TransactionStatus>(status => status == expectedEndStatus),
                It.IsAny<CancellationToken>()),
                Times.Once);
    }

    [Theory]
    [InlineData("meterstand doorgeven", ReadingType.PER, TransactionStatus.NoReadingPermissionLink)]
    [InlineData("meterstand doorgeven", ReadingType.TME, TransactionStatus.NoReadingPermissionTmeLink)]
    [InlineData("niets doen", ReadingType.PER, TransactionStatus.NoReadingPermissionSkip)]
    [InlineData("niets doen", ReadingType.TME, TransactionStatus.NoReadingPermissionTmeSkip)]
    public async Task CustomerHasSmartMeterReadingMandateDisabled(
        string customerChoice,
        ReadingType readingType,
        TransactionStatus expectedEndStatus)
    {
        _sessionManagerMock
            .Setup(mock => mock.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Verification = new VerificationData
                {
                    IsVerified = true,
                    CustomerId = 1
                },
                Customer = new CustomerData
                {
                    Label = Label.Eneco,
                    CurrentDomain = "test"
                },
                SelectedAccount = new AccountData
                {
                    AccountId = 1
                }
            });

        _customersServiceMock
            .Setup(mock => mock.GetAgreements(It.IsAny<DialogData>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new()
                {
                    AccountId = 1,
                    Connection = new Connection
                    {
                        AccountId = 1,
                        Meter = new MeteringDevice
                        {
                            IsSmartMeterReadingAllowed = false
                        }
                    },
                    Products = new List<Product>
                    {
                        new()
                        {
                            ProductType = ProductType.Electricity
                        }
                    }
                }
            });

        _usagesServiceMock
            .Setup(mock => mock.GetReadingForCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(new GetReadingOutputModel
            {
                ReadingId = 1,
                Address = new Address { HouseNumber = 123 },
                Meters = new List<GetReadingMeterModel>
                {
                    new()
                    {
                        IsSmart = true,
                        Saveable = false
                    }
                },
                ReadingType = readingType
            });

        _usagesServiceMock
            .Setup(mock => mock.GetReadingsHistoryForCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(
            [
                new()
                {
                    Date = _timeProvider.GetUtcNow().AddDays(-8).Date, Type = CollectorType.Periodic,
                }
            ]);

        _usagesServiceMock
            .Setup(mock => mock.GetOutstandingReadings(It.IsAny<DialogData>()))
            .ReturnsAsync([new() { ReadingType = readingType, IsOpen = true }]);

        var testClient = new DialogTestClient(Channels.Msteams, _sut);

        await testClient.SendActivityAsync(string.Empty);

        await testClient.SendActivityAsync<IMessageActivity>(customerChoice);

        _sessionManagerMock
            .Verify(mock => mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.Is<TransactionStatus>(status => status == expectedEndStatus),
                It.IsAny<CancellationToken>()),
                Times.Once);
    }

    [Theory]
    [InlineData("meterstand doorgeven", ReadingType.PER, TransactionStatus.NoReadingPermissionLink)]
    [InlineData("meterstand doorgeven", ReadingType.TME, TransactionStatus.NoReadingPermissionTmeLink)]
    [InlineData("niets doen", ReadingType.PER, TransactionStatus.NoReadingPermissionSkip)]
    [InlineData("niets doen", ReadingType.TME, TransactionStatus.NoReadingPermissionTmeSkip)]
    public async Task CustomerHasSmartMeterReadingMandateDisabledAndWantsToProvidePermission(
        string customerChoice,
        ReadingType readingType,
        TransactionStatus expectedEndStatus)
    {
        _sessionManagerMock
            .Setup(mock => mock.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Verification = new VerificationData
                {
                    IsVerified = true,
                    CustomerId = 1
                },
                Customer = new CustomerData
                {
                    Label = Label.Eneco,
                    CurrentDomain = "test"
                },
                SelectedAccount = new AccountData
                {
                    AccountId = 1
                }
            });

        _customersServiceMock
            .Setup(mock => mock.GetAgreements(It.IsAny<DialogData>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new()
                {
                    AccountId = 1,
                    Connection = new Connection
                    {
                        AccountId = 1,
                        Meter = new MeteringDevice
                        {
                            IsSmartMeterReadingAllowed = false
                        }
                    },
                    Products = new List<Product>
                    {
                        new()
                        {
                            ProductType = ProductType.Electricity
                        }
                    }
                }
            });

        _usagesServiceMock
            .Setup(mock => mock.GetReadingForCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(new GetReadingOutputModel
            {
                ReadingId = 1,
                Address = new Address { HouseNumber = 123 },
                Meters = new List<GetReadingMeterModel>
                {
                    new()
                    {
                        IsSmart = true,
                        Saveable = false
                    }
                },
                ReadingType = readingType
            });

        _usagesServiceMock
            .Setup(mock => mock.GetReadingsHistoryForCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(
            [
                new()
                {
                    Date = _timeProvider.GetUtcNow().AddDays(-8).Date, Type = CollectorType.Periodic,
                }
            ]);

        _usagesServiceMock
            .Setup(mock => mock.GetOutstandingReadings(It.IsAny<DialogData>()))
            .ReturnsAsync([new() { ReadingType = readingType, IsOpen = true }]);

        var testClient = new DialogTestClient(Channels.Msteams, _sut);

        _ = await testClient.SendActivityAsync(string.Empty);

        _ = await testClient.SendActivityAsync<IMessageActivity>("toestemming geven");

        _ = await testClient.SendActivityAsync<IMessageActivity>(customerChoice);

        _sessionManagerMock
            .Verify(mock => mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.Is<TransactionStatus>(status => status == expectedEndStatus),
                It.IsAny<CancellationToken>()),
                Times.Once);
    }

    [Theory]
    [InlineData("meterstanden doorgeven", ReadingType.PER, TransactionStatus.MBFChatSaveReadingPerSmn)]
    [InlineData("meterstanden doorgeven", ReadingType.TME, TransactionStatus.MBFChatSaveReadingTmeSmn)]
    [InlineData("niets doen", ReadingType.PER, TransactionStatus.SuccessSaveReadingPerSmnSkip)]
    [InlineData("niets doen", ReadingType.TME, TransactionStatus.SuccessSaveReadingTmeSmnSkip)]
    public async Task SmartMeterIsNotReadable(
        string customerChoice,
        ReadingType readingType,
        TransactionStatus expectedEndStatus)
    {
        _sessionManagerMock
            .Setup(mock => mock.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Verification = new VerificationData
                {
                    IsVerified = true,
                    CustomerId = 1
                },
                Customer = new CustomerData
                {
                    Label = Label.Eneco,
                    CurrentDomain = "test"
                },
                SelectedAccount = new AccountData
                {
                    AccountId = 1
                }
            });

        _customersServiceMock
            .Setup(mock => mock.GetAgreements(It.IsAny<DialogData>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new()
                {
                    AccountId = 1,
                    Connection = new Connection
                    {
                        AccountId = 1,
                        Meter = new MeteringDevice
                        {
                            IsSmartMeterReadingAllowed = true,
                            ReadoutState = MeterReadoutState.NotReadable
                        }
                    },
                    Products = new List<Product>
                    {
                        new()
                        {
                            ProductType = ProductType.Electricity
                        }
                    }
                }
            });

        _usagesServiceMock
            .Setup(mock => mock.GetReadingForCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(new GetReadingOutputModel
            {
                ReadingId = 1,
                Address = new Address { HouseNumber = 123 },
                Meters = new List<GetReadingMeterModel>
                {
                    new()
                    {
                        IsSmart = true,
                        Saveable = false
                    }
                },
                ReadingType = readingType
            });

        _usagesServiceMock
            .Setup(mock => mock.GetReadingsHistoryForCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(
            [
                new()
                {
                    Date = _timeProvider.GetUtcNow().AddDays(-8).Date, Type = CollectorType.Periodic,
                }
            ]);

        _usagesServiceMock
            .Setup(mock => mock.GetOutstandingReadings(It.IsAny<DialogData>()))
            .ReturnsAsync([new() { ReadingType = readingType, IsOpen = true }]);

        var testClient = new DialogTestClient(Channels.Msteams, _sut);

        _ = await testClient.SendActivityAsync(string.Empty);

        _ = await testClient.SendActivityAsync<IMessageActivity>("toestemming geven");

        _ = await testClient.SendActivityAsync<IMessageActivity>(customerChoice);

        _sessionManagerMock
            .Verify(mock => mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.Is<TransactionStatus>(status => status == expectedEndStatus),
                It.IsAny<CancellationToken>()),
                Times.Once);
    }

    [Fact]
    public async Task SmartMeterIsReadable()
    {
        _sessionManagerMock
            .Setup(mock => mock.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Verification = new VerificationData
                {
                    IsVerified = true,
                    CustomerId = 1
                },
                Customer = new CustomerData
                {
                    Label = Label.Eneco,
                    CurrentDomain = "test"
                },
                SelectedAccount = new AccountData
                {
                    AccountId = 1
                }
            });

        _customersServiceMock
            .Setup(mock => mock.GetAgreements(It.IsAny<DialogData>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new()
                {
                    AccountId = 1,
                    Connection = new Connection
                    {
                        AccountId = 1,
                        Meter = new MeteringDevice
                        {
                            IsSmartMeterReadingAllowed = true,
                            ReadoutState = MeterReadoutState.Readable
                        }
                    },
                    Products = new List<Product>
                    {
                        new()
                        {
                            ProductType = ProductType.Electricity
                        }
                    }
                }
            });

        _usagesServiceMock
            .Setup(mock => mock.GetReadingForCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(new GetReadingOutputModel
            {
                ReadingId = 1,
                Address = new Address { HouseNumber = 123 },
                Meters = new List<GetReadingMeterModel>
                {
                    new()
                    {
                        IsSmart = true,
                        Saveable = false
                    }
                },
                ReadingType = ReadingType.PER
            });

        _usagesServiceMock
            .Setup(mock => mock.GetReadingsHistoryForCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(
            [
                new()
                {
                    Date = _timeProvider.GetUtcNow().AddDays(-8).Date, Type = CollectorType.Periodic,
                }
            ]);

        _usagesServiceMock
            .Setup(mock => mock.GetOutstandingReadings(It.IsAny<DialogData>()))
            .ReturnsAsync([new() { ReadingType = ReadingType.PER, IsOpen = true }]);

        var testClient = new DialogTestClient(Channels.Msteams, _sut);

        _ = await testClient.SendActivityAsync(string.Empty);

        _sessionManagerMock
            .Verify(mock => mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.Is<TransactionStatus>(status => status == TransactionStatus.SuccessSmNoSaveReading),
                It.IsAny<CancellationToken>()),
                Times.Once);
    }

    [Theory]
    [InlineData("meterstanden doorgeven", ReadingType.PER, TransactionStatus.SuccessSaveReadingLink)]
    [InlineData("meterstanden doorgeven", ReadingType.TME, TransactionStatus.SuccessSaveReadingTmeLink)]
    [InlineData("niets doen", ReadingType.PER, TransactionStatus.SuccessSaveReadingSkip)]
    [InlineData("niets doen", ReadingType.TME, TransactionStatus.SuccessSaveReadingTmeSkip)]
    public async Task ManualMeterReadingFlowWithOpenReadingRequest(
        string customerChoice,
        ReadingType readingType,
        TransactionStatus expectedEndStatus)
    {
        _sessionManagerMock
            .Setup(mock => mock.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Verification = new VerificationData
                {
                    IsVerified = true,
                    CustomerId = 1
                },
                Customer = new CustomerData
                {
                    Label = Label.Eneco,
                    CurrentDomain = "test"
                },
                SelectedAccount = new AccountData
                {
                    AccountId = 1
                }
            });

        _customersServiceMock
            .Setup(mock => mock.GetAgreements(It.IsAny<DialogData>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new()
                {
                    AccountId = 1,
                    Connection = new Connection
                    {
                        AccountId = 1,
                        Meter = new MeteringDevice
                        {
                            IsSmartMeterReadingAllowed = false
                        }
                    },
                    Products = new List<Product>
                    {
                        new()
                        {
                            ProductType = ProductType.Electricity
                        }
                    }
                }
            });

        _usagesServiceMock
            .Setup(mock => mock.GetReadingForCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(new GetReadingOutputModel
            {
                ReadingId = 1,
                Address = new Address { HouseNumber = 123 },
                Meters = new List<GetReadingMeterModel>
                {
                    new()
                    {
                        IsSmart = false,
                        Saveable = false
                    }
                },
                ReadingType = readingType
            });

        _usagesServiceMock
            .Setup(mock => mock.GetReadingsHistoryForCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(
            [
                new()
                {
                    Date = _timeProvider.GetUtcNow().AddDays(-8).Date, Type = CollectorType.Periodic,
                }
            ]);

        _usagesServiceMock
            .Setup(mock => mock.GetOutstandingReadings(It.IsAny<DialogData>()))
            .ReturnsAsync([new() { ReadingType = readingType, IsOpen = true }]);

        var testClient = new DialogTestClient(Channels.Msteams, _sut);

        _ = await testClient.SendActivityAsync(string.Empty);

        _ = await testClient.SendActivityAsync<IMessageActivity>(customerChoice);

        _sessionManagerMock
            .Verify(mock => mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.Is<TransactionStatus>(status => status == expectedEndStatus),
                It.IsAny<CancellationToken>()),
                Times.Once);
    }

    [Theory]
    [InlineData("meterstanden doorgeven", TransactionStatus.SuccessNoPeriodicReadingLink)]
    [InlineData("niets doen", TransactionStatus.SuccessNoPeriodicReadingSkip)]
    public async Task ManualMeterReadingFlowWithoutOpenReadingRequest(
        string customerChoice,
        TransactionStatus expectedEndStatus)
    {
        _sessionManagerMock
            .Setup(mock => mock.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Verification = new VerificationData
                {
                    IsVerified = true,
                    CustomerId = 1
                },
                Customer = new CustomerData
                {
                    Label = Label.Eneco,
                    CurrentDomain = "test"
                },
                SelectedAccount = new AccountData
                {
                    AccountId = 1
                }
            });

        _customersServiceMock
            .Setup(mock => mock.GetAgreements(It.IsAny<DialogData>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new()
                {
                    AccountId = 1,
                    Connection = new Connection
                    {
                        AccountId = 1,
                        Meter = new MeteringDevice
                        {
                            IsSmartMeterReadingAllowed = false
                        }
                    },
                    Products = new List<Product>
                    {
                        new()
                        {
                            ProductType = ProductType.Electricity
                        }
                    }
                }
            });

        _usagesServiceMock
            .Setup(mock => mock.GetReadingForCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(new GetReadingOutputModel
            {
                ReadingId = 1,
                Address = new Address { HouseNumber = 123 },
                Meters = new List<GetReadingMeterModel>
                {
                    new()
                    {
                        IsSmart = false,
                        Saveable = false
                    }
                },
                ReadingType = ReadingType.PER
            });

        _usagesServiceMock
            .Setup(mock => mock.GetReadingsHistoryForCustomer(It.IsAny<DialogData>()))
            .ReturnsAsync(
            [
                new()
                {
                    Date = _timeProvider.GetUtcNow().AddDays(-8).Date, Type = CollectorType.Periodic,
                }
            ]);

        _usagesServiceMock
            .Setup(mock => mock.GetOutstandingReadings(It.IsAny<DialogData>()))
            .ReturnsAsync([new() { ReadingType = ReadingType.PER, IsOpen = false }]);

        var testClient = new DialogTestClient(Channels.Msteams, _sut);

        _ = await testClient.SendActivityAsync(string.Empty);

        _ = await testClient.SendActivityAsync<IMessageActivity>(customerChoice);

        _sessionManagerMock
            .Verify(mock => mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.Is<TransactionStatus>(status => status == expectedEndStatus),
                It.IsAny<CancellationToken>()),
                Times.Once);
    }

}
