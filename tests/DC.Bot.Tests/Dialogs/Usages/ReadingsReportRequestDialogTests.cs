﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Dialogs.Financials.Validators;
using DC.Bot.BusinessLogic.Dialogs.Products.Validators;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.Extensions;
using DC.Domain.Models.General;
using DC.Domain.Models.Products;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using DC.Usages.Client.Models;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Usages;

[Collection("Sequential")]
public class ReadingsReportRequestDialogTests : TestBase
{
    [Fact]
    public async Task HappyFlow_WithVerifiedCustomerId()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 1, 1, "<EMAIL>");

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._READINGS_REPORT_REQUEST).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ReadingsAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("CustomerLessThan24Month", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskToSendReadingsReport", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("Meterstanden opsturen");
        answer.Text.Should().StartWith(GetTextLabelValue("SendReadingsReportRequestEmailSent", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("SendReadingsReportRequestEmailSentWithInThreeDay", TextLabelGroupName));
    }

    [Fact]
    public async Task HappyFlow_WithVerifiedCustomerId_FailedSendEmail()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 7, 1, "<EMAIL>");

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._READINGS_REPORT_REQUEST).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ReadingsAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("CustomerLessThan24Month", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskToSendReadingsReport", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("Meterstanden opsturen");
        answer.Text.Should().StartWith(GetTextLabelValue("SendReadingsReportRequestFailedSendEmail", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("SendReadingsReportRequestFailedSendEmailWithInThreeDay", TextLabelGroupName));
    }

    [Fact]
    public async Task UnHappyFlow_CustomerNotFound_WithVerifiedCustomerId()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 2, 1, "<EMAIL>");

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._READINGS_REPORT_REQUEST).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ReadingsAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("CustomerLessThan24Month", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskToSendReadingsReport", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("Meterstanden opsturen");
        answer.Text.Should().StartWith(GetTextLabelValue("SendReadingsReportRequestCustomerNotFound", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("SendReadingsReportRequestCustomerNotFoundContactUs", TextLabelGroupName));
        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.TemporaryFailure,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task HappyFlow_AlreadyReported_WithVerifiedCustomerId()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 3, 1, "<EMAIL>");

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._READINGS_REPORT_REQUEST).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ReadingsAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("CustomerLessThan24Month", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskToSendReadingsReport", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("Meterstanden opsturen");
        answer.Text.Should().StartWith(GetTextLabelValue("SendReadingsReportRequestAlreadyRequested", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("SendReadingsReportRequestAlreadyRequestedWithInThreeDay", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("SendReadingsReportRequestAlreadyRequestedSendTo", TextLabelGroupName).Replace("{emailaddress}", "<EMAIL>".ObfuscateEmail()));
        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Success,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task UnHappyFlow_NoSmartMeters_WithVerifiedCustomerId()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 4, 1, "<EMAIL>");

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._READINGS_REPORT_REQUEST).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ReadingsAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("CustomerLessThan24Month", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskToSendReadingsReport", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("Meterstanden opsturen");
        answer.Text.Should().StartWith(GetTextLabelValue("SendReadingsReportRequestNoSmartMeters", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("SendReadingsReportRequestNoSmartMeters2", TextLabelGroupName));
        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Unhappy,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task UnHappyFlow_SomethingWentWrong_WithVerifiedCustomerId()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 5, 1, "<EMAIL>");

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._READINGS_REPORT_REQUEST).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ReadingsAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("CustomerLessThan24Month", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskToSendReadingsReport", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("Meterstanden opsturen");
        answer.Text.Should().StartWith(GetTextLabelValue("SomethingWentWrong", "Bot_GeneralTextLabels"));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("TryAgianSomethingWentWrong", "Bot_GeneralTextLabels"));
        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.PermanentFailure,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task HappyFlow_WithVerifiedCustomerId_WithoutEmail()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 1, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._READINGS_REPORT_REQUEST).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ReadingsAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("CustomerLessThan24Month", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskToSendReadingsReport", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("Meterstanden opsturen");
        answer.Text.Should().StartWith(GetTextLabelValue("NoEmailAddress", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("DoesntSaveEmailAddress", TextLabelGroupName));
        answer = await testClient.SendActivityAsync("doorgeven e-mailadres");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForEmailAddressPromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("<EMAIL>");
        var dialogData = await sessionManager.Object.GetDialogData(It.IsAny<ITurnContext>()).ConfigureAwait(true);
        dialogData.Customer.EmailAddress.Should().Be("<EMAIL>");
        answer.Text.Should().StartWith(GetTextLabelValue("OKEmailAddress", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("SendReadingsReportRequestEmailSent", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("SendReadingsReportRequestEmailSentWithInThreeDay", TextLabelGroupName));
        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Success,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task UnHappyFlow_WithVerifiedCustomerId_WithoutEmail()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 1, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._READINGS_REPORT_REQUEST).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ReadingsAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("CustomerLessThan24Month", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskToSendReadingsReport", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("Meterstanden opsturen");
        answer.Text.Should().StartWith(GetTextLabelValue("NoEmailAddress", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("DoesntSaveEmailAddress", TextLabelGroupName));
        answer = await testClient.SendActivityAsync("doorgeven e-mailadres");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForEmailAddressPromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("tester");
        answer.Text.Should().StartWith(GetTextLabelValue("InvalidEmailAddress", TextLabelGroupName));
        answer = await testClient.SendActivityAsync("tester");
        answer.Text.Should().StartWith(GetTextLabelValue("InvalidEmailAddress", TextLabelGroupName));
        answer = await testClient.SendActivityAsync("tester");
        answer.Text.Should().StartWith(GetTextLabelValue("ThreeTimesWrongEmailAddress", TextLabelGroupName));
        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Unhappy,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task HappyFlow_DoNothing()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 1, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._READINGS_REPORT_REQUEST).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ReadingsAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("CustomerLessThan24Month", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskToSendReadingsReport", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("nee");
        answer.Text.Should().StartWith(GetTextLabelValue("DoNothing", TextLabelGroupName));
        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Success,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task HappyFlow_WithoutEmail_DoNothing()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 1, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._READINGS_REPORT_REQUEST).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ReadingsAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("CustomerLessThan24Month", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskToSendReadingsReport", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("Meterstanden opsturen");
        answer.Text.Should().StartWith(GetTextLabelValue("NoEmailAddress", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("DoesntSaveEmailAddress", TextLabelGroupName));
        answer = await testClient.SendActivityAsync("nee");
        answer.Text.Should().StartWith(GetTextLabelValue("DoNothing", TextLabelGroupName));
        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Success,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task UnHappyFlow_HasNoEG_WithVerifiedCustomerId()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 8, 1, "<EMAIL>");

        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._READINGS_REPORT_REQUEST).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("HasNoEG", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("HasNoEGContactUs", TextLabelGroupName));
        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Unhappy,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task UnHappyFlow_HasNoSmartMeter_WithVerifiedCustomerId()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 9, 1, "<EMAIL>");

        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._READINGS_REPORT_REQUEST).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("HasNoSmartMeter", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("HasNoSmartMeter2", TextLabelGroupName));
        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Unhappy,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    private void SwapMockedServices(Mock<ISessionManager> sessionManagerMock, long customerId, int accountId, string email = "")
    {
        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        customersRepositoryMock.SetupCustomerTestData(customerId, accountId, email: email);

        var usagesRepoMock = new Mock<IDcUsagesRepository>();
        var customerMock = new Mock<ICustomersService>();
        var aMock = new Mock<SAPI.Interfaces.IApigeeAgreementsV3Repository>();
        var storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_ReadingsReportRequestDialog";
        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));

        usagesRepoMock.Setup(m => m.GetSmartMeterInterruption(It.IsAny<Label>(), It.IsAny<Telemetry.Models.BotChannel>(), It.IsAny<long>(), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Body = new ResponseDataSmartMeterInterruptionModel
                {
                    Data = new SmartMeterInterruptionModel
                    {
                        HasSmartMeter = true,
                        SmartMeterHasInterruption = false
                    }
                },
                Response = new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK
                }
            });

        customerMock.Setup(x => x.GetAgreements(It.Is<DialogData>(x => x.Verification.CustomerId != 8 && x.Verification.CustomerId != 9), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new Agreement()
                {
                    Connection = new Connection()
                    {
                        Meter = new MeteringDevice()
                        {
                            IsSmart = true,
                            IsSmartMeterReadingAllowed = true,
                            HasRedeliveryInMeter = true,
                            IsDoubleMeter = true,
                            IsDoubleMeterForRedelivery = true,
                            NextMeterReadingDate = new DateTime(2020,1,1),
                            Number = "123456",
                            Registers = new List<MeterRegister>()
                            {
                                new MeterRegister()
                                {
                                    EnergyFlowDirection = EnergyFlowDirection.Delivery,
                                    MeterReading = new LatestMeterRegisterReading()
                                    {
                                        Date = new DateTime(2020, 1, 1),
                                        Value = 123
                                    }
                                }
                            }
                        },
                        Address = new Address()
                        {
                            AddressSuffix = "AddressSuffix",
                            City = "City",
                            CountryCode = "CountryCode",
                            HouseNumber = 2,
                            HouseNumberSuffix = "HouseNumberSuffix",
                            PostalCode = "PostalCode",
                            Street = "Street",
                            Type = AddressType.Delivery
                        },
                        ConnectionPointEan = "**********"
                    },
                    AccountId = 1,
                    CustomerId = 1,
                    DeliveryAddress = new Address()
                    {
                        AddressSuffix = "AddressSuffix",
                        City = "City",
                        CountryCode = "CountryCode",
                        HouseNumber = 2,
                        HouseNumberSuffix = "HouseNumberSuffix",
                        PostalCode = "PostalCode",
                        Street = "Street",
                        Type = AddressType.Delivery
                    },
                    EndDate = new DateTime(2020,1,2),
                    Id = 123,
                    IsActive = true,
                    IntendedEndDate = new DateTime(2020,1,3),
                    IntendedStartDate = new DateTime(2020,1,4),
                    OwningBusinessPartyName = "",
                    CanRequestMeterReadingHistoryReport = true,
                    Products = new List<Product>()
                    {
                        new Product()
                        {
                            IsActive = true,
                            EndDate = new DateTime(2020, 1,7),
                            AccountId = 1,
                            AgreementId = 123456,
                            PriceDeterminationDate = new DateTime(2020,1,7),
                            ProductType = ProductType.Electricity

                        }
                    }
                }
            });
        customerMock.Setup(x => x.GetAgreements(It.Is<DialogData>(x => x.Verification.CustomerId == 9), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new Agreement()
                {
                    Connection = new Connection()
                    {
                        Meter = new MeteringDevice()
                        {
                            IsSmart = true,
                            IsSmartMeterReadingAllowed = true,
                            HasRedeliveryInMeter = true,
                            IsDoubleMeter = true,
                            IsDoubleMeterForRedelivery = true,
                            NextMeterReadingDate = new DateTime(2020,1,1),
                            Number = "123456",
                            Registers = new List<MeterRegister>()
                            {
                                new MeterRegister()
                                {
                                    EnergyFlowDirection = EnergyFlowDirection.Delivery,
                                    MeterReading = new LatestMeterRegisterReading()
                                    {
                                        Date = new DateTime(2020, 1, 1),
                                        Value = 123
                                    }
                                }
                            }
                        },
                        Address = new Address()
                        {
                            AddressSuffix = "AddressSuffix",
                            City = "City",
                            CountryCode = "CountryCode",
                            HouseNumber = 2,
                            HouseNumberSuffix = "HouseNumberSuffix",
                            PostalCode = "PostalCode",
                            Street = "Street",
                            Type = AddressType.Delivery
                        },
                        ConnectionPointEan = "**********"
                    },
                    AccountId = 1,
                    CustomerId = 1,
                    DeliveryAddress = new Address()
                    {
                        AddressSuffix = "AddressSuffix",
                        City = "City",
                        CountryCode = "CountryCode",
                        HouseNumber = 2,
                        HouseNumberSuffix = "HouseNumberSuffix",
                        PostalCode = "PostalCode",
                        Street = "Street",
                        Type = AddressType.Delivery
                    },
                    EndDate = new DateTime(2020,1,2),
                    Id = 123,
                    IsActive = true,
                    IntendedEndDate = new DateTime(2020,1,3),
                    IntendedStartDate = new DateTime(2020,1,4),
                    OwningBusinessPartyName = "",
                    CanRequestMeterReadingHistoryReport = false,
                    Products = new List<Product>()
                    {
                        new Product()
                        {
                            IsActive = true,
                            EndDate = new DateTime(2020, 1,7),
                            AccountId = 1,
                            AgreementId = 123456,
                            PriceDeterminationDate = new DateTime(2020,1,7),
                            ProductType = ProductType.Electricity

                        }
                    }
                }
            });
        customerMock.Setup(x => x.GetAgreements(It.Is<DialogData>(x => x.Verification.CustomerId == 8), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<int?>()))
            .ReturnsAsync(new List<Agreement>
            {
                new Agreement()
                {
                    Connection = new Connection()
                    {
                        Meter = new MeteringDevice()
                        {
                            IsSmart = true,
                            IsSmartMeterReadingAllowed = true,
                            HasRedeliveryInMeter = true,
                            IsDoubleMeter = true,
                            IsDoubleMeterForRedelivery = true,
                            NextMeterReadingDate = new DateTime(2020,1,1),
                            Number = "123456",
                            Registers = new List<MeterRegister>()
                            {
                                new MeterRegister()
                                {
                                    EnergyFlowDirection = EnergyFlowDirection.Delivery,
                                    MeterReading = new LatestMeterRegisterReading()
                                    {
                                        Date = new DateTime(2020, 1, 1),
                                        Value = 123
                                    }
                                }
                            }
                        },
                        Address = new Address()
                        {
                            AddressSuffix = "AddressSuffix",
                            City = "City",
                            CountryCode = "CountryCode",
                            HouseNumber = 2,
                            HouseNumberSuffix = "HouseNumberSuffix",
                            PostalCode = "PostalCode",
                            Street = "Street",
                            Type = AddressType.Delivery
                        },
                        ConnectionPointEan = "**********"
                    },
                    AccountId = 1,
                    CustomerId = 1,
                    DeliveryAddress = new Address()
                    {
                        AddressSuffix = "AddressSuffix",
                        City = "City",
                        CountryCode = "CountryCode",
                        HouseNumber = 2,
                        HouseNumberSuffix = "HouseNumberSuffix",
                        PostalCode = "PostalCode",
                        Street = "Street",
                        Type = AddressType.Delivery
                    },
                    EndDate = new DateTime(2020,1,2),
                    Id = 123,
                    IsActive = true,
                    IntendedEndDate = new DateTime(2020,1,3),
                    IntendedStartDate = new DateTime(2020,1,4),
                    OwningBusinessPartyName = "",
                    CanRequestMeterReadingHistoryReport = false,
                    Products = new List<Product>()
                    {
                        new Product()
                        {
                            IsActive = true,
                            EndDate = new DateTime(2020, 1,7),
                            AccountId = 1,
                            AgreementId = 123456,
                            PriceDeterminationDate = new DateTime(2020,1,7),
                            ProductType = ProductType.Warmth

                        }
                    }
                }
            });

        aMock.Setup(x => x.GetAgreementsForCustomerAccount(It.IsAny<Label>(), It.Is<long>(x => x != 8 && x != 9), It.IsAny<int>(),
                It.IsAny<bool?>(), It.IsAny<DateTime?>(), It.IsAny<DateTime?>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<DateTime?>(), It.IsAny<DateTime?>(), It.IsAny<bool>()))
            .ReturnsAsync(new List<Domain.Models.Agreements.Agreement>()
            {
                new Domain.Models.Agreements.Agreement()
                {
                    Connection = new Domain.Models.Agreements.Connection()
                    {
                        Meter = new Domain.Models.Agreements.MeteringDevice()
                        {
                            IsSmart = true,
                            IsSmartMeterReadingAllowed = true,
                            HasRedeliveryInMeter = true,
                            IsDoubleMeter = true,
                            IsDoubleMeterForRedelivery = true,
                            NextMeterReadingDate = new DateTime(2020,1,1),
                            Number = "123456",
                            Registers = new List<Domain.Models.Agreements.MeterRegister>()
                            {
                                new Domain.Models.Agreements.MeterRegister()
                                {
                                    EnergyFlowDirection = Domain.Models.General.EnergyFlowDirection.Delivery,
                                    MeterReading = new Domain.Models.Agreements.LatestMeterRegisterReading()
                                    {
                                        Date = new DateTime(2020, 1, 1),
                                        Value = 123
                                    }
                                }
                            }
                        },
                        Address = new Domain.Models.General.Address()
                        {
                            AddressSuffix = "AddressSuffix",
                            City = "City",
                            CountryCode = "CountryCode",
                            HouseNumber = 2,
                            HouseNumberSuffix = "HouseNumberSuffix",
                            PostalCode = "PostalCode",
                            Street = "Street",
                            Type = Domain.Models.General.AddressType.Delivery
                        },
                        ConnectionPointEan = "**********"
                    },
                    AccountId = 1,
                    CustomerId = 1,
                    DeliveryAddress = new Domain.Models.General.Address()
                    {
                        AddressSuffix = "AddressSuffix",
                        City = "City",
                        CountryCode = "CountryCode",
                        HouseNumber = 2,
                        HouseNumberSuffix = "HouseNumberSuffix",
                        PostalCode = "PostalCode",
                        Street = "Street",
                        Type = Domain.Models.General.AddressType.Delivery
                    },
                    EndDate = new DateTime(2020,1,2),
                    Id = 123,
                    IsActive = true,
                    IntendedEndDate = new DateTime(2020,1,3),
                    IntendedStartDate = new DateTime(2020,1,4),
                    OwningBusinessPartyName = "",
                    CanRequestMeterReadingHistoryReport = true,
                    Products = new List<Domain.Models.Agreements.Product>()
                    {
                        new Domain.Models.Agreements.Product()
                        {
                            IsActive = true,
                            EndDate = new DateTime(2020, 1,7),
                            AccountId = 1,
                            AgreementId = 123456,
                            PriceDeterminationDate = new DateTime(2020,1,7),
                            ProductType = Domain.Models.Products.ProductType.Electricity

                        }
                    }
                }
            });
        aMock.Setup(x => x.GetAgreementsForCustomerAccount(It.IsAny<Label>(), It.Is<long>(x => x == 9), It.IsAny<int>(),
                It.IsAny<bool?>(), It.IsAny<DateTime?>(), It.IsAny<DateTime?>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<DateTime?>(), It.IsAny<DateTime?>(), It.IsAny<bool>()))
            .ReturnsAsync(new List<Domain.Models.Agreements.Agreement>()
            {
                new Domain.Models.Agreements.Agreement()
                {
                    Connection = new Domain.Models.Agreements.Connection()
                    {
                        Meter = new Domain.Models.Agreements.MeteringDevice()
                        {
                            IsSmart = true,
                            IsSmartMeterReadingAllowed = true,
                            HasRedeliveryInMeter = true,
                            IsDoubleMeter = true,
                            IsDoubleMeterForRedelivery = true,
                            NextMeterReadingDate = new DateTime(2020,1,1),
                            Number = "123456",
                            Registers = new List<Domain.Models.Agreements.MeterRegister>()
                            {
                                new Domain.Models.Agreements.MeterRegister()
                                {
                                    EnergyFlowDirection = Domain.Models.General.EnergyFlowDirection.Delivery,
                                    MeterReading = new Domain.Models.Agreements.LatestMeterRegisterReading()
                                    {
                                        Date = new DateTime(2020, 1, 1),
                                        Value = 123
                                    }
                                }
                            }
                        },
                        Address = new Domain.Models.General.Address()
                        {
                            AddressSuffix = "AddressSuffix",
                            City = "City",
                            CountryCode = "CountryCode",
                            HouseNumber = 2,
                            HouseNumberSuffix = "HouseNumberSuffix",
                            PostalCode = "PostalCode",
                            Street = "Street",
                            Type = Domain.Models.General.AddressType.Delivery
                        },
                        ConnectionPointEan = "**********"
                    },
                    AccountId = 1,
                    CustomerId = 1,
                    DeliveryAddress = new Domain.Models.General.Address()
                    {
                        AddressSuffix = "AddressSuffix",
                        City = "City",
                        CountryCode = "CountryCode",
                        HouseNumber = 2,
                        HouseNumberSuffix = "HouseNumberSuffix",
                        PostalCode = "PostalCode",
                        Street = "Street",
                        Type = Domain.Models.General.AddressType.Delivery
                    },
                    EndDate = new DateTime(2020,1,2),
                    Id = 123,
                    IsActive = true,
                    IntendedEndDate = new DateTime(2020,1,3),
                    IntendedStartDate = new DateTime(2020,1,4),
                    OwningBusinessPartyName = "",
                    CanRequestMeterReadingHistoryReport = false,
                    Products = new List<Domain.Models.Agreements.Product>()
                    {
                        new Domain.Models.Agreements.Product()
                        {
                            IsActive = true,
                            EndDate = new DateTime(2020, 1,7),
                            AccountId = 1,
                            AgreementId = 123456,
                            PriceDeterminationDate = new DateTime(2020,1,7),
                            ProductType = Domain.Models.Products.ProductType.Warmth

                        }
                    }
                }
            });
        aMock.Setup(x => x.GetAgreementsForCustomerAccount(It.IsAny<Label>(), It.Is<long>(x => x == 8), It.IsAny<int>(),
                It.IsAny<bool?>(), It.IsAny<DateTime?>(), It.IsAny<DateTime?>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<DateTime?>(), It.IsAny<DateTime?>(), It.IsAny<bool>()))
            .ReturnsAsync(new List<Domain.Models.Agreements.Agreement>()
            {
                new Domain.Models.Agreements.Agreement()
                {
                    Connection = new Domain.Models.Agreements.Connection()
                    {
                        Meter = new Domain.Models.Agreements.MeteringDevice()
                        {
                            IsSmart = true,
                            IsSmartMeterReadingAllowed = true,
                            HasRedeliveryInMeter = true,
                            IsDoubleMeter = true,
                            IsDoubleMeterForRedelivery = true,
                            NextMeterReadingDate = new DateTime(2020,1,1),
                            Number = "123456",
                            Registers = new List<Domain.Models.Agreements.MeterRegister>()
                            {
                                new Domain.Models.Agreements.MeterRegister()
                                {
                                    EnergyFlowDirection = Domain.Models.General.EnergyFlowDirection.Delivery,
                                    MeterReading = new Domain.Models.Agreements.LatestMeterRegisterReading()
                                    {
                                        Date = new DateTime(2020, 1, 1),
                                        Value = 123
                                    }
                                }
                            }
                        },
                        Address = new Domain.Models.General.Address()
                        {
                            AddressSuffix = "AddressSuffix",
                            City = "City",
                            CountryCode = "CountryCode",
                            HouseNumber = 2,
                            HouseNumberSuffix = "HouseNumberSuffix",
                            PostalCode = "PostalCode",
                            Street = "Street",
                            Type = Domain.Models.General.AddressType.Delivery
                        },
                        ConnectionPointEan = "**********"
                    },
                    AccountId = 1,
                    CustomerId = 1,
                    DeliveryAddress = new Domain.Models.General.Address()
                    {
                        AddressSuffix = "AddressSuffix",
                        City = "City",
                        CountryCode = "CountryCode",
                        HouseNumber = 2,
                        HouseNumberSuffix = "HouseNumberSuffix",
                        PostalCode = "PostalCode",
                        Street = "Street",
                        Type = Domain.Models.General.AddressType.Delivery
                    },
                    EndDate = new DateTime(2020,1,2),
                    Id = 123,
                    IsActive = true,
                    IntendedEndDate = new DateTime(2020,1,3),
                    IntendedStartDate = new DateTime(2020,1,4),
                    OwningBusinessPartyName = "",
                    CanRequestMeterReadingHistoryReport = false,
                    Products = new List<Domain.Models.Agreements.Product>()
                    {
                        new Domain.Models.Agreements.Product()
                        {
                            IsActive = true,
                            EndDate = new DateTime(2020, 1,7),
                            AccountId = 1,
                            AgreementId = 123456,
                            PriceDeterminationDate = new DateTime(2020,1,7),
                            ProductType = Domain.Models.Products.ProductType.Electricity

                        }
                    }
                }
            });

        usagesRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 1), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<Domain.Exceptions.ResponseModels.ErrorResponse>
            {
                Response = new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK
                }
            });

        usagesRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 2), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<Domain.Exceptions.ResponseModels.ErrorResponse>
            {
                Response = new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                },
                Body = new Domain.Exceptions.ResponseModels.ErrorResponse
                {
                    Errors = new List<Domain.Exceptions.ResponseModels.ErrorModel>
                    {
                        new Domain.Exceptions.ResponseModels.ErrorModel {
                            Code = "dcp|525da9e1-646a-41d3-b9ea-32f97ef1392e",
                            Details = "MVS-23282",
                            Type = "FunctionalException"
                        }
                    }
                }
            });

        usagesRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 3), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<Domain.Exceptions.ResponseModels.ErrorResponse>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                },
                Body = new Domain.Exceptions.ResponseModels.ErrorResponse
                {
                    Errors = new List<Domain.Exceptions.ResponseModels.ErrorModel>
                    {
                        new Domain.Exceptions.ResponseModels.ErrorModel {
                            Code = "dcp|7dff0c3e-147c-46bb-b536-7848948e8d90",
                            Details = "MVS-23284",
                            Type = "FunctionalException"
                        }
                    }
                }
            });

        usagesRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 4), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<Domain.Exceptions.ResponseModels.ErrorResponse>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                },
                Body = new Domain.Exceptions.ResponseModels.ErrorResponse
                {
                    Errors = new List<Domain.Exceptions.ResponseModels.ErrorModel>
                    {
                        new Domain.Exceptions.ResponseModels.ErrorModel {
                            Code = "dcp|85ba42ea-d374-4372-b04b-f2e235cf4b44",
                            Details = "MVS-23285",
                            Type = "FunctionalException"
                        }
                    }
                }
            });

        usagesRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 5), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<Domain.Exceptions.ResponseModels.ErrorResponse>
            {
                Response = new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                },
                Body = new Domain.Exceptions.ResponseModels.ErrorResponse
                {
                    Errors = new List<Domain.Exceptions.ResponseModels.ErrorModel>
                    {
                        new Domain.Exceptions.ResponseModels.ErrorModel {
                            Code = "dcp|22989982-cd0d-4148-b29b-0f8424b331a7",
                            Details = "MVS-23283",
                            Type = "FunctionalException"
                        }
                    }
                }
            });
        usagesRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 6), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<Domain.Exceptions.ResponseModels.ErrorResponse>
            {
                Response = new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                },
                Body = new Domain.Exceptions.ResponseModels.ErrorResponse
                {
                    Errors = new List<Domain.Exceptions.ResponseModels.ErrorModel>
                    {
                        new Domain.Exceptions.ResponseModels.ErrorModel {
                            Code = "dcp|19ddee98-4223-40b1-9467-6b8ab8269806",
                            Details = "MVS-23286",
                            Type = "FunctionalException"
                        }
                    }
                }
            });

        usagesRepoMock.Setup(x => x.RegisterReportRequest(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 7), It.IsAny<int>(), It.IsAny<RequestDataReportRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<Domain.Exceptions.ResponseModels.ErrorResponse>
            {
                Response = new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                },
                Body = new Domain.Exceptions.ResponseModels.ErrorResponse
                {
                    Errors = new List<Domain.Exceptions.ResponseModels.ErrorModel>
                    {
                        new Domain.Exceptions.ResponseModels.ErrorModel {
                            Code = "dcp|614d27b2-df65-4359-90a1-53bfdd1fc6c2",
                            Details = "MVS-23286",
                            Type = "FunctionalException"
                        }
                    }
                }
            });

        var validator = new Mock<IDialogValidators>();
        var loggingService = new Mock<ILoggingService>();
        validator.Setup(x => x.CustomerDialogValidator).Returns(new CustomerDialogValidator(sessionManagerMock.Object));
        validator.Setup(x => x.FinancialsDialogValidator).Returns(new FinancialsDialogValidator(sessionManagerMock.Object));
        validator.Setup(x => x.ProductsDialogValidator).Returns(new ProductsDialogValidator(sessionManagerMock.Object));

        _services.SwapTransient(provider => loggingService.Object);
        _services.SwapTransient(provider => validator.Object);
        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => usagesRepoMock.Object);
        _services.SwapTransient(provider => customerMock.Object);
        _services.SwapTransient(provider => aMock.Object);
        _services.SwapTransient(provider => sessionManagerMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }

    private static Mock<ISessionManager> GetSessionsManager(long customerId)
    {
        var sessionManagerMock = new Mock<ISessionManager>();
        sessionManagerMock.Setup(x =>
                x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Customer = new CustomerData
                {
                    Label = Label.Eneco
                },
                Verification = new VerificationData
                {
                    CustomerId = customerId,
                    CustomerIdVerified = true
                }
            });

        return sessionManagerMock;
    }
}
