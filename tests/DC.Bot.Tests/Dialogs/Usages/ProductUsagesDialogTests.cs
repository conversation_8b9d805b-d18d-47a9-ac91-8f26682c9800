﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Customers.Client.Models;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.Customers;
using DC.Domain.Models.Products;
using DC.Domain.Models.Usages;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using DC.Usages.Client.Models;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Usages;

[Collection("Sequential")]
public class ProductUsagesDialogTests : TestBase
{
    [Fact]
    public async Task GetUsage_with_NotActiveCustomer_returns_correct_reply()
    {
        //Arrange
        SwapMockedServices(1, false);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTUSAGES);

        Assert.Contains(GetTextLabelValue("CouldNotFindActiveAccount", "Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetUsage_NoSmartMeterHasNoUsages_returns_correct_reply()
    {
        //Arrange
        SwapMockedServices(3, true);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTUSAGES);

        var replyText = testClient.GetNextReply().Text;
        Assert.StartsWith(GetTextLabelValue("NoSmartMeterHasNoUsages", TextLabelGroupName), replyText);

        replyText = testClient.GetNextReply().Text;
        Assert.StartsWith(GetTextLabelValue("HasNoYearNote", TextLabelGroupName)
            .Replace("{usages}", GetProductUsages()), replyText);
    }

    [Fact]
    public async Task GetUsage_SmartMeterHasNoUsages_returns_correct_reply()
    {
        //Arrange
        SwapMockedServices(4, true);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTUSAGES);

        var replyText = testClient.GetNextReply().Text;
        Assert.StartsWith(GetTextLabelValue("SmartMeterHasNoUsages", TextLabelGroupName), replyText);

        replyText = testClient.GetNextReply().Text;
        Assert.StartsWith(GetTextLabelValue("HasYearNote", TextLabelGroupName)
            .Replace("{usages}", GetProductUsages()), replyText);
    }

    [Fact]
    public async Task GetUsage_NoSmartMeter_with_Usages_returns_correct_reply()
    {
        //Arrange
        SwapMockedServices(1, true);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTUSAGES);
        var replyTxt = testClient.GetNextReply().Text;
        Assert.Contains(GetTextLabelValue("NoSmartMeterHasUsages", TextLabelGroupName), replyTxt);

        replyTxt = testClient.GetNextReply().Text;
        Assert.Contains("**Stroom**", replyTxt);
        Assert.Contains("februari: dal 500 kWh / normaal 1000 kWh", replyTxt);

        replyTxt = testClient.GetNextReply().Text;
        Assert.Contains("**Gas**", replyTxt);
        Assert.Contains("januari: 2000 m³", replyTxt);

        replyTxt = testClient.GetNextReply().Text;
        Assert.StartsWith(GetTextLabelValue("HasNoYearNote", TextLabelGroupName)
            .Replace("{usages}", GetProductUsages()), replyTxt);
    }

    [Fact]
    public async Task GetUsage_SmartMeter_with_Usages_returns_correct_reply()
    {
        //Arrange
        SwapMockedServices(2, true);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTUSAGES);

        var replyTxt = testClient.GetNextReply().Text;
        Assert.Contains("Je hebt een slimme meter.", replyTxt);

        replyTxt = testClient.GetNextReply().Text;
        Assert.Contains("**Stroom**", replyTxt);
        Assert.Contains("februari: dal 500 kWh / normaal 1000 kWh", replyTxt);

        replyTxt = testClient.GetNextReply().Text;
        Assert.Contains("**Gas**", replyTxt);
        Assert.Contains("januari: 2000 m³", replyTxt);

        replyTxt = testClient.GetNextReply().Text;
        Assert.StartsWith(GetTextLabelValue("HasYearNote", TextLabelGroupName)
            .Replace("{usages}", GetProductUsages()), replyTxt);
    }


    [Fact]
    public async Task GetUsage_SmartMeter_with_Usages_AllUsages_returns_correct_reply()
    {
        //Arrange
        SwapMockedServices(5, true);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTUSAGES);

        var replyTxt = testClient.GetNextReply().Text;
        Assert.Contains("Je hebt een slimme meter.", replyTxt);

        replyTxt = testClient.GetNextReply().Text;
        Assert.Contains("**Stroom**", replyTxt);
        Assert.Contains("januari: dal 500 kWh / normaal 1000 kWh", replyTxt);
        Assert.Contains("februari: dal 500 kWh / normaal 1000 kWh", replyTxt);

        replyTxt = testClient.GetNextReply().Text;
        Assert.Contains("**Teruglevering**", replyTxt);
        Assert.Contains("januari: dal 100 kWh / normaal 200 kWh", replyTxt);
        Assert.Contains("februari: dal 100 kWh / normaal 200 kWh", replyTxt);

        replyTxt = testClient.GetNextReply().Text;
        Assert.Contains("**Gas**", replyTxt);
        Assert.Contains("januari: 2000 m³", replyTxt);
        Assert.Contains("februari: 2000 m³", replyTxt);

        replyTxt = testClient.GetNextReply().Text;
        Assert.Contains("**Warmte**", replyTxt);
        Assert.Contains("januari: 2000 GJ", replyTxt);
        Assert.Contains("februari: 2000 GJ", replyTxt);

        replyTxt = testClient.GetNextReply().Text;
        Assert.Contains("**Warm tapwater**", replyTxt);
        Assert.Contains("januari: 2000 m³", replyTxt);
        Assert.Contains("februari: 2000 m³", replyTxt);

        replyTxt = testClient.GetNextReply().Text;
        Assert.StartsWith(GetTextLabelValue("HasYearNote", TextLabelGroupName)
            .Replace("{usages}", GetProductUsagesAllCombinations()), replyTxt);

        replyTxt = testClient.GetNextReply().Text;
        Assert.Contains(GetTextLabelValue("ProductUsagesYearNote", TextLabelGroupName), replyTxt);
    }

    [Fact]
    public async Task GetUsage_with_UnverifiedCustomer()
    {
        //Arrange
        SwapMockedServices(1, true);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithUnverifiedCustomer(DialogCommands._PRODUCTUSAGES);
        Assert.Contains(GetTextLabelValue("CouldNotFindData", "Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetUsage_with_NoActiveEnergyContract()
    {
        //Arrange
        SwapMockedServices(10, true);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTUSAGES);
        Assert.Contains(GetTextLabelValue("NoContract", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    private void SwapMockedServices(long customerId, bool activeAccount)
    {
        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        var usageRepoMock = new Mock<IDcUsagesRepository>();
        var storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_ProductUsagesDialog";
        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));
        customersRepositoryMock.SetupCustomerTestData(customerId, activeAccount: activeAccount);

        customersRepositoryMock.Setup(x =>
                    x.GetCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataCustomerModel
                    {
                        Data = new CustomerModel
                        {
                            Id = (int)customerId,
                            Person = new PersonModel
                            {
                                Initials = "Paul",
                                Surname = "Testman",
                                Gender = Gender.Male
                            },
                            Contact = new ContactModel
                            {
                                EmailAddress = ""
                            },
                            CustomerType = CustomerType.Person,
                            Accounts = new List<CustomerAccountModel>
                                {
                                    new CustomerAccountModel
                                    {
                                        Active = activeAccount,
                                        Id = 1,
                                        StartDate = customerId == 1 || customerId == 3 ? DateTime.Today.AddMonths(-6) : DateTime.Today.AddYears(-3),
                                        NextChargeDate = null,
                                        Address = new AddressModel
                                        {
                                            PostalCode = "3025CB",
                                            HouseNumber = 9,
                                            Street = "Straat",
                                            City = "Roffa"
                                        },
                                        Bankaccount = new BankaccountModel
                                        {
                                            Bank = "ABN Amro",
                                            Country = "Nederland",
                                            Number = "******************",
                                            Holder = "P Testman",
                                            IsStandard = true
                                        },
                                        MeterDetails = new List<MeterDetail>
                                        {
                                            new MeterDetail
                                            {
                                                ProductType = ProductType.Electricity.ToString(),
                                                IsSmartMeter = customerId == 2 || customerId == 4 || customerId == 5,
                                                IsSmartMeterReadingAllowed = customerId == 2 || customerId == 4 || customerId == 5,
                                            },
                                            new MeterDetail
                                            {
                                                ProductType = ProductType.Gas.ToString(),
                                                IsSmartMeter = customerId == 2 || customerId == 4|| customerId == 5,
                                                IsSmartMeterReadingAllowed = customerId == 2 || customerId == 4|| customerId == 5,
                                            }
                                        }
                                    }
                                }
                        }
                    }
                }
                );

        usageRepoMock.Setup(m => m.GetSmartMeterInterruption(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Body = new ResponseDataSmartMeterInterruptionModel
                {
                    Data = new SmartMeterInterruptionModel
                    {
                        HasSmartMeter = true,
                        SmartMeterHasInterruption = false
                    }
                },
                Response = new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK
                }
            });

        customersRepositoryMock.Setup(x => x.GetAgreements(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x <= 2), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListAgreement
                {
                    Data = new List<Agreement>
                    {
                        new Agreement
                        {
                            IsActive  = true,
                            Products = new List<Product>
                            {
                                new Product
                                {
                                    ProductType = ProductType.Electricity,
                                    IsActive = true,
                                    ProductOffering = new ProductOffering
                                    {
                                        IsIndefiniteDuration = customerId != 1
                                    }
                                }
                            },
                            ProductUsages = new List<ProductUsage>
                            {
                                new ProductUsage
                                {
                                    Quantity = 1465,
                                    QuantityType = QuantityType.Derived,
                                    CounterType = CounterType.ElectricityLow,
                                    DenotationType = DenotationType.Kwh
                                },
                                new ProductUsage
                                {
                                    Quantity = 1757,
                                    QuantityType = QuantityType.Derived,
                                    CounterType = CounterType.ElectricityHigh,
                                    DenotationType = DenotationType.Kwh
                                }
                            }
                        },
                        new Agreement
                        {
                            IsActive  = true,
                            Products = new List<Product>
                            {
                                new Product
                                {
                                    ProductType = ProductType.Gas,
                                    IsActive = true,
                                    ProductOffering = new ProductOffering
                                    {
                                        IsIndefiniteDuration = customerId != 1
                                    }
                                }
                            },
                            ProductUsages = new List<ProductUsage>
                            {
                                new ProductUsage
                                {
                                    Quantity = 402,
                                    QuantityType = QuantityType.Derived,
                                    CounterType = CounterType.Gas,
                                    DenotationType = DenotationType.M3
                                }
                            }
                        }
                    }
                }
            });

        customersRepositoryMock.Setup(x => x.GetAgreements(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x > 2 && x != 5), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListAgreement
                {
                    Data = new List<Agreement>
                    {
                        new Agreement
                        {
                            IsActive  = true,
                            Products = new List<Product>
                            {
                                new Product
                                {
                                    ProductType = ProductType.Electricity,
                                    IsActive = true,
                                    ProductOffering = new ProductOffering
                                    {
                                        IsIndefiniteDuration = customerId != 1
                                    }
                                }
                            },
                            ProductUsages = new List<ProductUsage>
                            {
                                new ProductUsage
                                {
                                    Quantity = 1465,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.ElectricityLow,
                                    DenotationType = DenotationType.Kwh
                                },
                                new ProductUsage
                                {
                                    Quantity = 1757,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.ElectricityHigh,
                                    DenotationType = DenotationType.Kwh
                                }
                            }
                        },
                        new Agreement
                        {
                            IsActive  = true,
                            Products = new List<Product>
                            {
                                new Product
                                {
                                    ProductType = ProductType.Gas,
                                    IsActive = true,
                                    ProductOffering = new ProductOffering
                                    {
                                        IsIndefiniteDuration = customerId != 1
                                    }
                                }
                            },
                            ProductUsages = new List<ProductUsage>
                            {
                                new ProductUsage
                                {
                                    Quantity = 402,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.Gas,
                                    DenotationType = DenotationType.M3
                                }
                            }
                        }
                    }
                }
            });

        customersRepositoryMock.Setup(x => x.GetAgreements(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 5), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListAgreement
                {
                    Data = new List<Agreement>
                    {
                        new Agreement
                        {
                            IsActive  = true,
                            Products = new List<Product>
                            {
                                new Product
                                {
                                    ProductType = ProductType.Electricity,
                                    IsActive = true,
                                    ProductOffering = new ProductOffering
                                    {
                                        IsIndefiniteDuration = customerId != 1
                                    }
                                }
                            },
                            ProductUsages = new List<ProductUsage>
                            {
                                new ProductUsage
                                {
                                    Quantity = 1465,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.ElectricityLow,
                                    DenotationType = DenotationType.Kwh,
                                    StartDate = DateTime.Today.AddDays(-10).AddYears(-1),
                                    EndDate = DateTime.Today.AddDays(-10)
                                },
                                new ProductUsage
                                {
                                    Quantity = 1757,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.ElectricityHigh,
                                    DenotationType = DenotationType.Kwh,
                                    StartDate = DateTime.Today.AddDays(-10).AddYears(-1),
                                    EndDate = DateTime.Today.AddDays(-10)
                                },
                                new ProductUsage
                                {
                                    Quantity = 1465,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.ElectricityLow,
                                    DenotationType = DenotationType.Kwh,
                                    StartDate = DateTime.Today.AddDays(-10)
                                },
                                new ProductUsage
                                {
                                    Quantity = 1757,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.ElectricityHigh,
                                    DenotationType = DenotationType.Kwh,
                                    StartDate = DateTime.Today.AddDays(-10)
                                },
                                /////
                                 new ProductUsage
                                {
                                    Quantity = 1465,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.ElectricityRedeliveryLow,
                                    DenotationType = DenotationType.Kwh,
                                    StartDate = DateTime.Today.AddDays(-10).AddYears(-1),
                                    EndDate = DateTime.Today.AddDays(-10)
                                },
                                new ProductUsage
                                {
                                    Quantity = 1757,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.ElectricityRedeliveryHigh,
                                    DenotationType = DenotationType.Kwh,
                                    StartDate = DateTime.Today.AddDays(-10).AddYears(-1),
                                    EndDate = DateTime.Today.AddDays(-10)
                                },
                                new ProductUsage
                                {
                                    Quantity = 1465,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.ElectricityRedeliveryLow,
                                    DenotationType = DenotationType.Kwh,
                                    StartDate = DateTime.Today.AddDays(-10)
                                },
                                new ProductUsage
                                {
                                    Quantity = 1757,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.ElectricityRedeliveryHigh,
                                    DenotationType = DenotationType.Kwh,
                                    StartDate = DateTime.Today.AddDays(-10)
                                }
                            }
                        },
                        new Agreement
                        {
                            IsActive  = true,
                            Products = new List<Product>
                            {
                                new Product
                                {
                                    ProductType = ProductType.Gas,
                                    IsActive = true,
                                    ProductOffering = new ProductOffering
                                    {
                                        IsIndefiniteDuration = customerId != 1
                                    }
                                }
                            },
                            ProductUsages = new List<ProductUsage>
                            {
                                new ProductUsage
                                {
                                    Quantity = 402,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.Gas,
                                    DenotationType = DenotationType.M3,
                                    StartDate = DateTime.Today.AddDays(-10).AddYears(-1),
                                    EndDate = DateTime.Today.AddDays(-10)
                                },
                                new ProductUsage
                                {
                                    Quantity = 402,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.Gas,
                                    DenotationType = DenotationType.M3,
                                    StartDate = DateTime.Today.AddDays(-10)
                                }
                            }
                        },
                        new Agreement
                        {
                            IsActive  = true,
                            Products = new List<Product>
                            {
                                new Product
                                {
                                    ProductType = ProductType.Warmth,
                                    IsActive = true,
                                    ProductOffering = new ProductOffering
                                    {
                                        IsIndefiniteDuration = customerId != 1
                                    }
                                }
                            },
                            ProductUsages = new List<ProductUsage>
                            {
                                new ProductUsage
                                {
                                    Quantity = 402,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.Warmth,
                                    DenotationType = DenotationType.GJ,StartDate = DateTime.Today.AddDays(-10).AddYears(-1),
                                    EndDate = DateTime.Today.AddDays(-10)
                                },
                                new ProductUsage
                                {
                                    Quantity = 402,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.Warmth,
                                    DenotationType = DenotationType.GJ,StartDate = DateTime.Today.AddDays(-10)
                                }
                            }
                        },
                        new Agreement
                        {
                            IsActive  = true,
                            Products = new List<Product>
                            {
                                new Product
                                {
                                    ProductType = ProductType.Tapwater,
                                    IsActive = true,
                                    ProductOffering = new ProductOffering
                                    {
                                        IsIndefiniteDuration = customerId != 1
                                    }
                                }
                            },
                            ProductUsages = new List<ProductUsage>
                            {
                                new ProductUsage
                                {
                                    Quantity = 402,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.Water,
                                    DenotationType = DenotationType.M3
                                }
                            }
                        }
                    }
                }
            });

        //No Agreements
        customersRepositoryMock.Setup(x => x.GetAgreements(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 10), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>()))
           .ReturnsAsync(new HttpOperationResponse<object>
           {
               Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
               Body = new ResponseDataListAgreement
               {
                   Data = new List<Agreement>
                   {
                   }
               }
           });

        usageRepoMock.Setup(x =>
                x.GetUsages(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x > 2 && x != 5), It.IsAny<int>(), It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<UsageAggregation>(), It.IsAny<UsageInterval>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataUsagesModel
                {
                    Data = new UsagesModel
                    {
                        Usages = new List<UsagesAggregation>
                        {

                        }
                    }
                }
            });

        usageRepoMock.Setup(x =>
                x.GetUsages(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 5), It.IsAny<int>(), It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<UsageAggregation>(), It.IsAny<UsageInterval>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataUsagesModel
                {
                    Data = new UsagesModel
                    {
                        Usages = new List<UsagesAggregation>
                        {
                            new UsagesAggregation
                            {
                                Period = new AggregationPeriod
                                {
                                    FromProperty = new DateTime(2021,1,1),
                                    To = new DateTime(2021,2,2)
                                },
                                Entries = new List<UsagesEntry>
                                {
                                    new UsagesEntry
                                    {
                                        Actual = new IntervalUsage
                                        {
                                            Date = new DateTime(2021,1,1),
                                            Electricity = new UsageItem
                                            {
                                                IsDoubleTariff = true,
                                                High = 1000,
                                                Low = 500
                                            },
                                            Redelivery = new UsageItem
                                            {
                                                IsDoubleTariff = true,
                                                High = 200,
                                                Low = 100
                                            },
                                            Gas = new UsageItem
                                            {
                                                High = 2000,
                                                Low = 1500
                                            },
                                            Warmth = new UsageItem
                                            {
                                                High = 2000,
                                                Low = 1500
                                            },
                                            TapWater = new UsageItem
                                            {
                                                High = 2000,
                                                Low = 1500
                                            }
                                        }
                                    }
                                }
                            },
                            new UsagesAggregation
                            {
                                Period = new AggregationPeriod
                                {
                                    FromProperty = new DateTime(2021,2,1),
                                    To = new DateTime(2021,2,3)
                                },
                                Entries = new List<UsagesEntry>
                                {
                                    new UsagesEntry
                                    {
                                        Actual = new IntervalUsage
                                        {
                                            Date = new DateTime(2021,2,1),
                                            Electricity = new UsageItem
                                            {
                                                IsDoubleTariff = true,
                                                High = 1000,
                                                Low = 500
                                            },
                                            Redelivery = new UsageItem
                                            {
                                                IsDoubleTariff = true,
                                                High = 200,
                                                Low = 100
                                            },
                                            Gas = new UsageItem
                                            {
                                                High = 2000,
                                                Low = 1500
                                            },
                                            Warmth = new UsageItem
                                            {
                                                High = 2000,
                                                Low = 1500
                                            },
                                            TapWater = new UsageItem
                                            {
                                                High = 2000,
                                                Low = 1500
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            });

        usageRepoMock.Setup(x =>
                x.GetUsages(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x <= 2), It.IsAny<int>(), It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<UsageAggregation>(), It.IsAny<UsageInterval>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataUsagesModel
                {
                    Data = new UsagesModel
                    {
                        Usages = new List<UsagesAggregation>
                        {
                            new UsagesAggregation
                            {
                                Period = new AggregationPeriod
                                {
                                    FromProperty = new DateTime(2021,1,1),
                                    To = new DateTime(2021,2,2)
                                },
                                Entries = new List<UsagesEntry>
                                {
                                    new UsagesEntry
                                    {
                                        Actual = new IntervalUsage
                                        {
                                            Date = new DateTime(2021,1,1),
                                            Electricity = new UsageItem
                                            {
                                                IsDoubleTariff = true,
                                                High = 1000,
                                                Low = 500
                                            },
                                            Gas = new UsageItem
                                            {
                                                High = 2000,
                                                Low = 1500
                                            }
                                        }
                                    }
                                }
                            },
                            new UsagesAggregation
                            {
                                Period = new AggregationPeriod
                                {
                                    FromProperty = new DateTime(2021,2,1),
                                    To = new DateTime(2021,2,3)
                                },
                                Entries = new List<UsagesEntry>
                                {
                                    new UsagesEntry
                                    {
                                        Actual = new IntervalUsage
                                        {
                                            Date = new DateTime(2021,2,1),
                                            Electricity = new UsageItem
                                            {
                                                IsDoubleTariff = true,
                                                High = 1000,
                                                Low = 500
                                            },
                                            Gas = new UsageItem
                                            {
                                                High = 2000,
                                                Low = 1500
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            });

        var validator = new Mock<IDialogValidators>();
        validator.SetupDialogValidatorWithSessionManager(customerId);
        _services.SwapTransient(provider => validator.Object);

        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => usageRepoMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }

    public static string GetProductUsages()
    {
        return $"Stroom: normaal 1757 kWh / dal 1465 kWh  \n Stroom totaal: 3222 kWh  \n Gas: 402 m³";
    }

    public static string GetProductUsagesAllCombinations()
    {
        return $"Stroom: normaal 1757 kWh / dal 1465 kWh  \n Stroom totaal: 3222 kWh  \n Gas: 402 m³  \n Warmte: 402 GJ  \n Warm tapwater: 402 m³";
    }
}
