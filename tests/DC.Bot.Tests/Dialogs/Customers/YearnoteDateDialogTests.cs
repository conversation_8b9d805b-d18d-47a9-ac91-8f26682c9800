﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Repositories.Base.Enumerations;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Moq;
using System;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Customers;

[Collection("Sequential")]
public class YearnoteDateDialogTests : TestBase
{
    [Fact]
    public async Task GetNextYearNoteDate_with_VerifiedCustomer_returns_correct_reply()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._NEXT_YEARNOTE_DATE);
        Assert.StartsWith(GetTextLabelValue("DateReceiveYearNote", TextLabelGroupName).Replace("{nextYearnoteDate}", DateTime.Now.ToString("d MMMM yyyy", new CultureInfo("NL-nl"))), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetNextYearNoteDate_with_VerifiedCustomer_returns_correct_reply2()
    {
        //Arrange
        SwapMockedServices(2);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._NEXT_YEARNOTE_DATE);

        Assert.StartsWith(GetTextLabelValue("CouldNotFindData", "Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetNextYearNoteDate_with_UnverifiedCustomer()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithUnverifiedCustomer(DialogCommands._NEXT_YEARNOTE_DATE);
        Assert.Contains(GetTextLabelValue("CouldNotFindData", "Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetNextYearNoteDate_ErrorResponse()
    {
        SwapMockedServices(4);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._NEXT_YEARNOTE_DATE).ConfigureAwait(true);
        Assert.Contains(GetTextLabelValue("CouldNotFindData", "Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }

    private void SwapMockedServices(long customerId)
    {
        TextLabelGroupName = "Bot_YearnoteDateDialog";

        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        customersRepositoryMock.SetupCustomerTestData(customerId, activeAccount: true, multiAccount: customerId == 3, nextChargeDate: customerId == 1 || customerId == 3 ? DateTime.Now : null, returnsError: customerId == 4);
        var storageServiceMock = new Mock<IStorageService>();

        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));

        var validator = new Mock<IDialogValidators>();
        validator.SetupDialogValidatorWithSessionManager(customerId);
        _services.SwapTransient(provider => validator.Object);

        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }
}
