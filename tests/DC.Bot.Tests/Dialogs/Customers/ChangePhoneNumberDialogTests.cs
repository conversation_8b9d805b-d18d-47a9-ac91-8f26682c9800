﻿using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.PhoneNumberDialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Customers.Client.Models;
using DC.Domain.Models.Customers;
using DC.Domain.Models.Extensions;
using DC.Domain.Models.NextBestAction;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Connector;
using Microsoft.Extensions.Logging;
using Moq;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Customers;

[Collection("Sequential")]
public class ChangePhoneNumberDialogTests : TestBase
{
    [Theory]
    [InlineData(
        new string[] { },
        new string[]
        {
            "UnknownPhoneNumbers",
            "AskToAddPhoneNumber",
            "CustomerDoesNotChangePhoneNumber"
        }, true, false)]
    [InlineData(
        new string[] { },
        new string[]
        {
            "UnknownPhoneNumbers",
            "AskToAddPhoneNumber",
            "CustomerDoesNotChangePhoneNumber"
        }, false, false)]
    [InlineData(
        new string[] { "0612345678" },
        new string[]
        {
            "CurrentKnownPhoneNumber",
            "AskToAdjustPhoneNumber",
            "KeepPhoneNumberOne",
            "CustomerDoesNotChangePhoneNumber"
        }, true, false)]
    [InlineData(
        new string[] { "0612345678" },
        new string[]
        {
            "CurrentKnownPhoneNumber",
            "AskToAdjustPhoneNumber",
            "KeepPhoneNumberOne",
            "CustomerDoesNotChangePhoneNumber"
        }, true, true)]
    [InlineData(
        new string[] { "0612345678" },
        new string[]
        {
            "CurrentKnownPhoneNumber",
            "AskToAdjustPhoneNumber",
            "KeepPhoneNumberOne",
            "CustomerDoesNotChangePhoneNumber"
        }, false, false)]
    [InlineData(
        new string[] { "0612345678", "0101234567" },
        new string[]
        {
            "CurrentKnownPhoneNumbers",
            "AdjustPhoneNumberOne",
            "AdjustPhoneNumberTwo",
            "CustomerDoesNotChangePhoneNumber"
        }, true, false)]
    [InlineData(
        new string[] { "0612345678", "0101234567" },
        new string[]
        {
            "CurrentKnownPhoneNumbers",
            "AdjustPhoneNumberOne",
            "AdjustPhoneNumberTwo",
            "CustomerDoesNotChangePhoneNumber"
        }, false, false)]
    [InlineData(
        new string[] { "0612345678", "0101234567" },
        new string[]
        {
            "CurrentKnownPhoneNumbers",
            "AdjustPhoneNumberOne",
            "AdjustPhoneNumberTwo",
            "CustomerDoesNotChangePhoneNumber"
        }, true, true)]
    public async Task Customer_DoesntWantToAdjustAnyPhoneNumber_returns_correct_answer(string[] phoneNumbers, string[] responses, bool isVerified, bool isNba)
    {
        // Arrange
        var sut = SetupChangePhoneNumberDialog(out var nbaServiceMock, isVerified, phoneNumbers: phoneNumbers.OfType<string>().ToList(), currentTransActionIsNba: isNba);

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        for (var i = 0; i < responses.Length; i++)
        {
            responses[i] = GetTextLabelValue(responses[i], TextLabelGroupName);
            if (phoneNumbers.Length >= 1)
                responses[i] = responses[i].Replace("{phonenumber1}", phoneNumbers[0].ObfuscatePhoneNumber());
            if (phoneNumbers.Length == 2)
                responses[i] = responses[i].Replace("{phonenumber2}", phoneNumbers[1].ObfuscatePhoneNumber());
        }

        // Assert
        var reply = await testClient.SendActivityAsync(string.Empty);
        if (isVerified)
        {
            reply.Text.Should().Be(responses[0]);
            reply = await testClient.SendActivityAsync("Nee");
            Assert.Equal(responses[1], reply.Text);

            if (phoneNumbers.Length > 1)
            {
                reply = await testClient.SendActivityAsync("Nee");
                Assert.Equal(responses[2], reply.Text);
            }
        }
        else
            reply.Should().BeNull();

        if (isNba)
            nbaServiceMock.Verify(m => m.AddFeedback(It.IsAny<DialogData>(), "CustomerPhoneNumber", FeedbackStatus.Conversion_success), Times.Once);
    }

    [Theory]
    [InlineData(
        new string[]
        {
            "UnknownPhoneNumbers",
            "AskToAddPhoneNumber",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "AskToAddPhoneNumberTwo",
            "NewPhoneNumber"
        },
        false, true)]
    [InlineData(
        new string[]
        {
            "UnknownPhoneNumbers",
            "AskToAddPhoneNumber",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "AskToAddPhoneNumberTwo",
            "NewPhoneNumber"
        },
        false, false)]
    [InlineData(
        new string[]
        {
            "UnknownPhoneNumbers",
            "AskToAddPhoneNumber",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "AskToAddPhoneNumberTwo",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "NewPhoneNumbers"
        },
        true, true)]
    [InlineData(
        new string[]
        {
            "UnknownPhoneNumbers",
            "AskToAddPhoneNumber",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "AskToAddPhoneNumberTwo",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "NewPhoneNumbers"
        },
        true, false)]
    public async Task Customer_WithNoPhonenumbers_WantsToAddNumber_returns_correct_answer(string[] responses, bool adjustSecondPhoneNumber, bool isVerified)
    {
        // Arrange
        var sut = SetupChangePhoneNumberDialog(out var _, isVerified);

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        for (var i = 0; i < responses.Length; i++)
        {
            responses[i] = GetTextLabelValue(responses[i], TextLabelGroupName)
                ?.Replace("{phonenumber1}", "0612345678")
                ?.Replace("{phonenumber2}", "0107654321");
        }

        // Assert
        var reply = await testClient.SendActivityAsync(string.Empty);
        if (isVerified)
        {
            Assert.Equal(responses[0], reply.Text);

            reply = await testClient.SendActivityAsync("Ja");
            Assert.Equal(responses[1], reply.Text);

            reply = await testClient.SendActivityAsync("0612345678");

            Assert.Equal(responses[2], reply.Text);

            var _adjustSecondPhoneNumber = adjustSecondPhoneNumber ? "Ja" : "Nee";
            reply = await testClient.SendActivityAsync(_adjustSecondPhoneNumber);
            Assert.Equal(responses[3], reply.Text);

            if (adjustSecondPhoneNumber)
            {
                reply = await testClient.SendActivityAsync("0107654321");
                Assert.Equal(responses[4], reply.Text);
            }
        }
        else
            reply.Should().BeNull();
    }
    [Theory]
    [InlineData(
        new[]
        {
            "NbaAddPhoneNumber",
            "PhoneNumberSaved",
            "AskToAddPhoneNumberTwo",
            "NewPhoneNumber"
        },
        true, false)]
    [InlineData(
        new[]
        {
            "NbaAddPhoneNumber",
            "PhoneNumberSaved",
            "AskToAddPhoneNumberTwo",
            "NbaAddPhoneNumber",
            "PhoneNumberSaved",
            "NewPhoneNumbers"
        },
        true, true)]
    [InlineData(
        new[]
        {
            "NbaAddPhoneNumber",
            "PhoneNumberSaved",
            "AskToAddPhoneNumberTwo",
            "NbaAddPhoneNumber",
            "PhoneNumberSaved",
            "NewPhoneNumbers"
        },
        false, true)]
    public async Task Customer_WithNoPhonenumbers_WantsToAddNumber_WithNBA__returns_correct_answer(string[] responses, bool adjustSecondPhoneNumber, bool isVerified)
    {
        // Arrange
        var sut = SetupChangePhoneNumberDialog(out var _, isVerified, false, 1, null, true);

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        for (var i = 0; i < responses.Length; i++)
        {
            responses[i] = GetTextLabelValue(responses[i], TextLabelGroupName)
                ?.Replace("{phonenumber1}", "0612345678")
                ?.Replace("{phonenumber2}", "0107654321");
        }

        // Assert
        var reply = await testClient.SendActivityAsync(string.Empty);
        if (isVerified)
        {
            Assert.Equal(responses[0], reply.Text);

            reply = await testClient.SendActivityAsync("0612345678");

            Assert.Equal(responses[1], reply.Text);

            var _adjustSecondPhoneNumber = adjustSecondPhoneNumber ? "Ja" : "Nee";
            reply = await testClient.SendActivityAsync(_adjustSecondPhoneNumber);
            Assert.Equal(responses[2], reply.Text);

            if (adjustSecondPhoneNumber)
            {
                reply = await testClient.SendActivityAsync("0107654321");
                Assert.Equal(responses[3], reply.Text);
            }
        }
        else
            reply.Should().BeNull();
    }


    [Theory]
    [InlineData(
        new string[] { "0612345678" },
        new string[]
        {
            "CurrentKnownPhoneNumber",
            "AskToAdjustPhoneNumber",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "AskToAddPhoneNumberTwo",
            "NewPhoneNumber"
        },
        false, true)]
    [InlineData(
        new string[] { "0612345678" },
        new string[]
        {
            "CurrentKnownPhoneNumber",
            "AdjustPhoneNumberOne",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "AskToAddPhoneNumberTwo",
            "NewPhoneNumber"
        },
        false, false)]
    [InlineData(
        new string[] { "0612345678", "0101234567" },
        new string[]
        {
            "CurrentKnownPhoneNumbers",
            "AdjustPhoneNumberOne",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "AdjustPhoneNumberTwo",
            "NewPhoneNumbers"
        },
        false, true)]
    [InlineData(
        new string[] { "0612345678", "0101234567" },
        new string[]
        {
            "CurrentKnownPhoneNumbers",
            "AdjustPhoneNumberOne",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "AdjustPhoneNumberTwo",
            "NewPhoneNumbers"
        },
        false, false)]
    [InlineData(
        new string[] { "0612345678" },
        new string[]
        {
            "CurrentKnownPhoneNumber",
            "AskToAdjustPhoneNumber",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "AskToAddPhoneNumberTwo",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "NewPhoneNumber"
        },
        true, true)]
    [InlineData(
        new string[] { "0612345678" },
        new string[]
        {
            "CurrentKnownPhoneNumber",
            "AskToAdjustPhoneNumber",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "AskToAddPhoneNumberTwo",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "NewPhoneNumber"
        },
        true, false)]
    [InlineData(
        new string[] { "0612345678", "0101234567" },
        new string[]
        {
            "CurrentKnownPhoneNumbers",
            "AdjustPhoneNumberOne",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "AdjustPhoneNumberTwo",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "NewPhoneNumbers"
        },
        true, true)]
    [InlineData(
        new string[] { "0612345678" },
        new string[]
        {
            "CurrentKnownPhoneNumber",
            "AdjustPhoneNumberOne",
            "AddPhoneNumber",
            "PhoneNumberSaved",
            "AskToAddPhoneNumberTwo",
            "NewPhoneNumber"
        },
        true, false)]
    public async Task Customer_WantsToChange_Phonenumber_returns_correct_answer(string[] phoneNumbers, string[] responses, bool adjustSecondPhoneNumber, bool isVerified)
    {
        // Arrange
        var sut = SetupChangePhoneNumberDialog(out var _, isVerified, phoneNumbers: phoneNumbers.OfType<string>().ToList());

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        for (var i = 0; i < responses.Length; i++)
        {
            responses[i] = GetTextLabelValue(responses[i], TextLabelGroupName);
            if (phoneNumbers.Length >= 1)
                responses[i] = responses[i]?.Replace("{phonenumber1}", phoneNumbers[0].ObfuscatePhoneNumber());
            if (phoneNumbers.Length == 2)
                responses[i] = responses[i]?.Replace("{phonenumber2}", phoneNumbers[1].ObfuscatePhoneNumber());
        }

        // Assert
        var reply = await testClient.SendActivityAsync(string.Empty);
        if (isVerified)
        {
            reply.Text.Should().Be(responses[0]);

            testClient.GetNextReply();
            reply = await testClient.SendActivityAsync("Ja");
            Assert.Equal(responses[2], reply.Text);

            reply = await testClient.SendActivityAsync("0687654321");
            Assert.Equal(responses[3], reply.Text);

            var _adjustSecondPhoneNumber = adjustSecondPhoneNumber ? "Ja" : "Nee";
            reply = await testClient.SendActivityAsync(_adjustSecondPhoneNumber);
            Assert.Equal(responses[4], reply.Text);

            if (adjustSecondPhoneNumber)
            {
                reply = await testClient.SendActivityAsync("0107654321");
                Assert.Equal(responses[5], reply.Text);
            }
        }
        else
            reply.Should().Be(null);
    }

    [Fact]
    public async Task IfCustomerNotVerified_ThenShouldExitDialog()
    {
        // Arrange
        var sut = SetupChangePhoneNumberDialog(out var nbaServiceMock, false);

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        // Assert
        var reply = await testClient.SendActivityAsync(string.Empty);
        reply.Should().Be(null);
    }

    public ChangePhoneNumberDialog SetupChangePhoneNumberDialog(out Mock<INextBestActionService> nbaServiceMock, bool isVerified = true, bool customerIdVerified = true, long? customerId = 1, List<string> phoneNumbers = null, bool currentTransActionIsNba = false)
    {
        TextLabelGroupName = "Bot_ChangePhoneNumberDialog";
        var mockedLogger = new Mock<ILoggerFactory>();
        var sessionManagerMock = new Mock<ISessionManager>();
        var storageServiceMock = new Mock<IStorageService>();
        nbaServiceMock = new Mock<INextBestActionService>();

        var validator = new Mock<IDialogValidators>();
        validator.Setup(x => x.CustomerDialogValidator).Returns(new CustomerDialogValidator(sessionManagerMock.Object));

        var logger = new Mock<ILogger>().Object;

        mockedLogger.Setup(f => f.CreateLogger(It.IsAny<string>())).Returns(logger);

        var validUserData = new DialogData
        {
            Verification = new VerificationData
            {
                IsVerified = isVerified,
                CustomerIdVerified = customerIdVerified,
                CustomerId = customerId
            },
            SelectedAccount = new AccountData
            {
                AccountId = 10
            }
        };

        if (currentTransActionIsNba)
        {
            validUserData.NextBestAction = new NextBestActionData
            {
                CurrentTransactionIsNba = true
            };
        }

        validator.Setup(x => x.CustomerDialogValidator.CustomerPhoneValidator(It.IsAny<PromptValidatorContext<string>>(), It.IsAny<CancellationToken>())).ReturnsAsync(true);

        var customerToReturn = new Customer
        {
            Id = (int)customerId.Value,
            Contact = new Contact
            {
                PhoneNumbers = phoneNumbers
            }
        };

        var customerServiceMock = new Mock<ICustomersService>();

        customerServiceMock
            .Setup(x => x.GetCustomerV2(It.IsAny<DC.Repositories.Base.Enumerations.Label>(), It.IsAny<BotChannel>(), customerId.Value))
            .ReturnsAsync(customerToReturn);

        nbaServiceMock.Setup(m => m.GetNextBestActions(It.IsAny<DialogData>(), It.IsAny<bool>()))
            .ReturnsAsync(new DC.Products.Client.Models.NextBestAction
            {
                CustomerId = 123,
                ContextId = "ctx1",
                Actions = new List<DC.Products.Client.Models.ActionModel> {
                new DC.Products.Client.Models.ActionModel
                {
                    AccountId = 1,
                    ActionType = "CustomerPhoneNumber",
                    ActionId = 1,
                    Channel = Channel.Chatbot,
                    ServingPointId = 4,
                    TreatmentVariationId = 2,
                    Score = 100
                }
                }
            });

        customerServiceMock
            .Setup(x => x.PatchCustomerProfile(It.IsAny<DialogData>()))
            .ReturnsAsync(new ChangeCustomerProfileResponse { EmailSentSucces = true });

        sessionManagerMock.Setup(x => x.GetDialogData(It.IsAny<ITurnContext>())).ReturnsAsync(validUserData).Verifiable();

        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));

        return new ChangePhoneNumberDialog(mockedLogger.Object, new Mock<ILoggingService>().Object, sessionManagerMock.Object, validator.Object, customerServiceMock.Object, nbaServiceMock.Object, storageServiceMock.Object);
    }
}
