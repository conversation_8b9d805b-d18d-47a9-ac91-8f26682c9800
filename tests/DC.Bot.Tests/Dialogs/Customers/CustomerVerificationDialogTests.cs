﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Dialogs.Financials.Validators;
using DC.Bot.BusinessLogic.Dialogs.Products.Validators;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Customers.Client.Models;
using DC.Domain.Models.Accounts;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.Customers;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using DC.Utilities.Environments;
using DC.Utilities.Formatters;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;
using AddressModel = DC.Customers.Client.Models.AddressModel;
using Gender = DC.Domain.Models.Customers.Gender;

namespace DC.Bot.Tests.Dialogs.Customers;

[Collection("Sequential")]
public class CustomerVerificationDialogTests : TestBase
{
    private readonly Mock<ISessionManager> _sessionManagerMock;
    private readonly Mock<IFinancialsService> _financialsServiceMock;
    private readonly Mock<IDcCustomersRepository> _customersRepositoryMock;
    private readonly Mock<IDcProductsRepository> _productsRepositoryMock;
    private readonly Mock<IStorageService> _storageServiceMock;

    public CustomerVerificationDialogTests()
    {
        _sessionManagerMock = new Mock<ISessionManager>();
        _financialsServiceMock = new Mock<IFinancialsService>();
        _customersRepositoryMock = new Mock<IDcCustomersRepository>();
        _productsRepositoryMock = new Mock<IDcProductsRepository>();
        _storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_CustomerVerificationDialog";
        GetAllTextLabelsFromExport();
    }

    private Mock<ISessionManager> SwapMockedServices(int customerId, bool withAddress = true, int houseNumber = 9)
    {
        _storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));

        _sessionManagerMock.Setup(x =>
                x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Customer = new CustomerData
                {
                    Label = Label.Eneco
                },
                Verification = new VerificationData
                {
                    IsPossiblyOrganisation = false
                }
            });

        _customersRepositoryMock.Setup(x =>
                x.VerifyCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<RequestDataCustomerVerification>(x => x.Data.Addresses.First().HouseNumber == 111)))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.NoContent },
                Body = new ResponseDataCustomerVerificationResponse
                {
                    Data = new CustomerVerificationResponse(1,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    false,
                    false,
                    false)
                    {

                    }
                }
            });

        _customersRepositoryMock.Setup(x =>
                x.VerifyCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<RequestDataCustomerVerification>(x => x.Data.Addresses.First().HouseNumber == houseNumber)))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.PreconditionRequired },
                Body = new ResponseDataCustomerVerificationResponse
                {
                    Data = new CustomerVerificationResponse(1,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    false,
                    false,
                    false)
                    {

                    }
                }
            });

        _customersRepositoryMock.Setup(x =>
                x.VerifyCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<RequestDataCustomerVerification>(
                    y => y.Data.CustomerId == customerId
                         && (y.Data.Addresses.First().HouseNumber == 9 || y.Data.Addresses.First().HouseNumber == 19)
                         && y.Data.Addresses.First().PostalCode == "3025CB"
                         && y.Data.IbanSuffixes.Contains("1234")
                         && y.Data.DatesOfBirth.Contains(new DateTime(1988, 2, 1))
                         && y.Data.PhoneNumber == null)))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataCustomerVerificationResponse
                {
                    Data = new CustomerVerificationResponse(1,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    false,
                    false,
                    false)
                    {

                    }
                }
            });

        _customersRepositoryMock.Setup(x =>
                x.VerifyCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<RequestDataCustomerVerification>(
                    y => y.Data.CustomerId == customerId
                         && y.Data.Addresses.First().HouseNumber == 10
                         && y.Data.Addresses.First().PostalCode == "3025CB"
                         && y.Data.IbanSuffixes.Contains("1234")
                         && y.Data.DatesOfBirth.Contains(new DateTime(1988, 2, 1))
                         && y.Data.PhoneNumber == null)))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataCustomerVerificationResponse
                {
                    Data = new CustomerVerificationResponse(1,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                    ValidationStatus.NotChecked,
                   false,
                    false,
                    false)
                    {

                    }
                }
            });

        _customersRepositoryMock.Setup(x => x.GetCustomerOrders(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>()))
           .ReturnsAsync(new HttpOperationResponse<object>
           {
               Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
               Body = new ResponseDataListOrderResponseModel
               {
                   Data = new List<OrderResponseModel>()
                   {

                   }
               }
           });

        _productsRepositoryMock.Setup(x => x.GetCustomerProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>())).ReturnsAsync(
            new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<Domain.Models.Products.ProductModel>()
                }
            });

        _customersRepositoryMock.Setup(x => x.GetAgreements(
            It.IsAny<Label>(),
            It.IsAny<BotChannel>(),
            It.IsAny<long>(),
            It.IsAny<int>(),
            It.IsAny<bool?>(),
            It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Body = new ResponseDataListAgreement
                {
                    Data = new List<Agreement> {
                        new Agreement
                        {
                            AccountId = 1,
                            IsActive = true,
                            Products = new List<Domain.Models.Agreements.Product>
                            {
                                new Domain.Models.Agreements.Product {
                                    AccountId= 1,
                                    AgreementId = 1,
                                    ProductType = Domain.Models.Products.ProductType.Electricity,
                                    IsActive = true,
                                }
                            },
                            Connection = new Connection
                            {
                                ConnectionPointEan = "ean",
                                AccountId = 1,
                                GridOperator = new DistributionNetworkOperator
                                {
                                    Name = "netbeheerder"
                                }
                            }
                        }
                    }
                },
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                }
            });

        _customersRepositoryMock.Setup(x => x.GetCustomerAgreements(
            It.IsAny<Label>(),
            It.IsAny<BotChannel>(),
            It.IsAny<long>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Body = new ResponseDataListAgreement
                {
                    Data = new List<Agreement> {
                        new Agreement
                        {
                            AccountId = 1,
                            IsActive = true,
                            Products = new List<Domain.Models.Agreements.Product>
                            {
                                new Domain.Models.Agreements.Product {
                                    AccountId= 1,
                                    AgreementId = 1,
                                    ProductType = Domain.Models.Products.ProductType.Electricity,
                                    IsActive = true,
                                }
                            },
                            Connection = new Connection
                            {
                                ConnectionPointEan = "ean",
                                AccountId = 1,
                                GridOperator = new DistributionNetworkOperator
                                {
                                    Name = "netbeheerder"
                                }
                            }
                        }
                    }
                },
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                }
            });

        _customersRepositoryMock.Setup(x => x.GetPaymentPlan(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>())).ReturnsAsync(
            new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataPaymentPlan
                {
                    Data = new PaymentPlan()
                }
            });

        AddressModel address = null;
        if (withAddress)
        {
            address = new AddressModel
            {
                PostalCode = "3025CB",
                HouseNumber = houseNumber,
                HouseNumberSuffix = "A"
            };
        }

        _customersRepositoryMock.Setup(x =>
                x.GetCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataCustomerModel
                {
                    Data = new CustomerModel
                    {
                        Id = customerId,
                        Person = new PersonModel
                        {
                            Initials = "Paul",
                            Surname = "Testman",
                            Gender = Gender.Male
                        },
                        CustomerType = CustomerType.Person,
                        Accounts = new List<CustomerAccountModel>
                        {
                            new CustomerAccountModel
                            {
                                CustomerId = customerId,
                                Active = true,
                                Id = 2,
                                Address = address
                            }
                        }
                    }
                }
            });

        var validator = new Mock<IDialogValidators>();
        validator.Setup(x => x.CustomerDialogValidator).Returns(new CustomerDialogValidator(_sessionManagerMock.Object));
        validator.Setup(x => x.FinancialsDialogValidator).Returns(new FinancialsDialogValidator(_sessionManagerMock.Object));
        validator.Setup(x => x.ProductsDialogValidator).Returns(new ProductsDialogValidator(_sessionManagerMock.Object));

        _services.SwapTransient(provider => validator.Object);
        _services.SwapTransient(provider => _financialsServiceMock.Object);
        _services.SwapTransient(provider => _customersRepositoryMock.Object);
        _services.SwapTransient(provider => _productsRepositoryMock.Object);
        _services.SwapTransient(provider => _sessionManagerMock.Object);
        _services.SwapTransient(provider => _storageServiceMock.Object);

        return _sessionManagerMock;
    }

    [Fact]
    public async Task NoAddressFound_And_Dialog_Stops_After_three_times()
    {
        //Arrange
        SwapMockedServices(1234);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT).ConfigureAwait(true);
        Assert.Equal(GetTextLabelValue("HaveToAskValidationQuestions", TextLabelGroupName), reply.Text);

        for (int i = 0; i < 3; i++)
        {
            Assert.Contains(GetTextLabelValue("AskPostalCode", TextLabelGroupName), testClient.GetNextReply().Text);
            reply = await testClient.SendActivityAsync("302ACB").ConfigureAwait(true);

            Assert.Equal(GetTextLabelValue("ValidationErrorPostalCodeRetry", TextLabelGroupName), reply.Text);
            reply = await testClient.SendActivityAsync("3025CB").ConfigureAwait(true);

            Assert.Equal(GetTextLabelValue("AskHouseNumber", TextLabelGroupName), reply.Text);
            reply = await testClient.SendActivityAsync("9A").ConfigureAwait(true);

            Assert.Equal(GetTextLabelValue("ValidationErrorHouseNumberRetry", TextLabelGroupName), reply.Text);
            reply = await testClient.SendActivityAsync("111").ConfigureAwait(true);

            if (i < 2)
            {
                Assert.Equal(GetTextLabelValue("DataNotFound", TextLabelGroupName), reply.Text);
            }
            else
            {
                Assert.Equal(GetTextLabelValue("CustomerIdNotFound", TextLabelGroupName), reply.Text);
            }

        }
    }


    [Fact]
    public async Task DialogStops_when_customer_is_unverified()
    {
        //Arrange
        SwapMockedServices(1234);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT).ConfigureAwait(true);
        Assert.Equal(GetTextLabelValue("HaveToAskValidationQuestions", TextLabelGroupName), reply.Text);

        Assert.Contains(GetTextLabelValue("AskPostalCode", TextLabelGroupName), testClient.GetNextReply().Text);
        reply = await testClient.SendActivityAsync("3025CB").ConfigureAwait(true);

        Assert.Equal(GetTextLabelValue("AskHouseNumber", TextLabelGroupName), reply.Text);
        reply = await testClient.SendActivityAsync("9").ConfigureAwait(true);

        Assert.Equal(GetTextLabelValue("AskIbanDigits", TextLabelGroupName), reply.Text);
        reply = await testClient.SendActivityAsync("4321").ConfigureAwait(true);

        Assert.Equal(GetTextLabelValue("InputNotValid", TextLabelGroupName), reply.Text);
        Assert.Equal(GetTextLabelValue("AskDateOfBirth", TextLabelGroupName), testClient.GetNextReply().Text);
        reply = await testClient.SendActivityAsync("11-12-1988").ConfigureAwait(true);

        Assert.Equal(GetTextLabelValue("AskCustomerId", TextLabelGroupName), reply.Text);
        reply = await testClient.SendActivityAsync("4321").ConfigureAwait(true);

        testClient.DialogTurnResult.Status.Should().Be(DialogTurnStatus.Complete);
        reply.Should().BeNull();
    }


    [Theory]
    [InlineData("3025CB", "9", "1234", "01-02-1988", "1234")]
    [InlineData("3025 CB", "1 9", "1 2 3 4", "0 1 - 0 2 - 1 9 8 8", "1 2 3 4")]
    [InlineData("3 0 2 5 CB", "1 9", "1 2 3 4", "0 1 - 0 2 - 1 9 8 8", "1 2 3 4")]
    public async Task VerificationProces_Uses_Correct_Input_Finds_User_And_Goes_Back_To_Requested_Dialog(string postalCode, string houseNumber, string iban, string dateOfBirth, string customerId)
    {
        //Arrange
        SwapMockedServices(1234, houseNumber: int.Parse(houseNumber.RemoveWhitespaces()));
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT).ConfigureAwait(true);
        Assert.Equal(GetTextLabelValue("HaveToAskValidationQuestions", TextLabelGroupName), reply.Text);

        Assert.Contains(GetTextLabelValue("AskPostalCode", TextLabelGroupName), testClient.GetNextReply().Text);
        reply = await testClient.SendActivityAsync(postalCode).ConfigureAwait(true);

        Assert.Equal(GetTextLabelValue("AskHouseNumber", TextLabelGroupName), reply.Text);
        reply = await testClient.SendActivityAsync(houseNumber).ConfigureAwait(true);

        Assert.Equal(GetTextLabelValue("AskIbanDigits", TextLabelGroupName), reply.Text);
        reply = await testClient.SendActivityAsync(iban).ConfigureAwait(true);

        Assert.Equal(GetTextLabelValue("InputNotValid", TextLabelGroupName), reply.Text);
        Assert.Equal(GetTextLabelValue("AskDateOfBirth", TextLabelGroupName), testClient.GetNextReply().Text);
        reply = await testClient.SendActivityAsync(dateOfBirth).ConfigureAwait(true);

        Assert.Equal(GetTextLabelValue("AskCustomerId", TextLabelGroupName), reply.Text);
        reply = await testClient.SendActivityAsync(customerId).ConfigureAwait(true);
        if (DigitalCoreEnvironment.IsProduction())
        {
            reply = await testClient.SendActivityAsync<Activity>(DialogTestExtension.GetMfaSuccessActivity()).ConfigureAwait(true);
        }
        Assert.Equal(GetTextLabelValue("DataFoundWelcomeMessage", TextLabelGroupName).Replace("{customerName}", "Paul Testman"), reply.Text);
        Assert.Contains(GetTextLabelValue("SomethingWentWrong", $"Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task VerificationProces_Uses_Correct_Input_Finds_User_With_An_Account_Without_Address()
    {
        //Arrange
        SwapMockedServices(1234, false);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT).ConfigureAwait(true);
        Assert.Equal(GetTextLabelValue("HaveToAskValidationQuestions", TextLabelGroupName), reply.Text);

        Assert.Contains(GetTextLabelValue("AskPostalCode", TextLabelGroupName), testClient.GetNextReply().Text);
        reply = await testClient.SendActivityAsync("3025CB").ConfigureAwait(true);

        Assert.Equal(GetTextLabelValue("AskHouseNumber", TextLabelGroupName), reply.Text);
        reply = await testClient.SendActivityAsync("9").ConfigureAwait(true);

        Assert.Equal(GetTextLabelValue("AskIbanDigits", TextLabelGroupName), reply.Text);
        reply = await testClient.SendActivityAsync("1234").ConfigureAwait(true);

        Assert.Equal(GetTextLabelValue("InputNotValid", TextLabelGroupName), reply.Text);
        Assert.Equal(GetTextLabelValue("AskDateOfBirth", TextLabelGroupName), testClient.GetNextReply().Text);
        reply = await testClient.SendActivityAsync("01-02-1988").ConfigureAwait(true);

        Assert.Equal(GetTextLabelValue("AskCustomerId", TextLabelGroupName), reply.Text);
        reply = await testClient.SendActivityAsync("1234").ConfigureAwait(true);

        if (DigitalCoreEnvironment.IsProduction())
        {
            reply = await testClient.SendActivityAsync<Activity>(DialogTestExtension.GetMfaSuccessActivity()).ConfigureAwait(true);
        }

        Assert.Equal(GetTextLabelValue("DataFoundWelcomeMessage", TextLabelGroupName).Replace("{customerName}", "Paul Testman"), reply.Text);

        Assert.Contains(GetTextLabelValue("CouldNotFindActiveAccount", $"Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }



    [Fact]
    public async Task TooManyFormatErrors_ForPostalCode_TextLabels()
    {
        //Arrange
        SwapMockedServices(1234);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT).ConfigureAwait(true);
        Assert.Equal(GetTextLabelValue("HaveToAskValidationQuestions", TextLabelGroupName), reply.Text);
        Assert.Contains(GetTextLabelValue("AskPostalCode", TextLabelGroupName), testClient.GetNextReply().Text);

        for (int i = 0; i < 3; i++)
        {
            reply = await testClient.SendActivityAsync("3535QQQQQ").ConfigureAwait(true);

            if (i < 2)
            {
                Assert.Equal(GetTextLabelValue("ValidationErrorPostalCodeRetry", TextLabelGroupName), reply.Text);
            }
            else
            {
                Assert.Equal(GetTextLabelValue("ValidationError", TextLabelGroupName).Replace("{validationErrorReason}", GetTextLabelValue("ValidationErrorPostalCode", TextLabelGroupName)), reply.Text);
            }
        }
    }

    [Fact]
    public async Task TooManyFormatErrors_ForHouseNumber_TextLabels()
    {
        //Arrange
        SwapMockedServices(1234);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        var reply = await testClient.SendActivityAsync(DialogCommands._ADVANCE_PAYMENT_AMOUNT).ConfigureAwait(true);
        Assert.Equal(GetTextLabelValue("HaveToAskValidationQuestions", TextLabelGroupName), reply.Text);
        Assert.Contains(GetTextLabelValue("AskPostalCode", TextLabelGroupName), testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("3535QQ").ConfigureAwait(true);
        Assert.Equal(GetTextLabelValue("AskHouseNumber", TextLabelGroupName), reply.Text);

        for (int i = 0; i < 3; i++)
        {
            reply = await testClient.SendActivityAsync("aaagr").ConfigureAwait(true);

            if (i < 2)
            {
                Assert.Equal(GetTextLabelValue("ValidationErrorHouseNumberRetry", TextLabelGroupName), reply.Text);
            }
            else
            {
                Assert.Equal(GetTextLabelValue("ValidationError", TextLabelGroupName).Replace("{validationErrorReason}", GetTextLabelValue("ValidationErrorHouseNumber", TextLabelGroupName)), reply.Text);
            }
        }
    }
}
