﻿using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Models.Extensions;
using DC.Domain.Models.Financials;
using DC.Domain.Models.NextBestAction;
using DC.Financials.Client.Models;
using DC.Repositories.Base.Enumerations;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Connector;
using Microsoft.Bot.Schema;
using Microsoft.Extensions.Logging;
using Moq;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Customers;

[Collection("Sequential")]
public class ChangeIbanDialogTests : TestBase
{
    [Fact]
    public async Task IfCustomerNotVerified_ThenShouldExitDialog()
    {
        // Arrange
        var sut = SetupMockDialog(out var _, false);

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        // Assert
        var reply = await testClient.SendActivityAsync(string.Empty);
        reply.Should().Be(null);
    }

    [Theory]
    [InlineData(null, "UnknownIban")]
    [InlineData("NL099BLA123456", "CurrentKnownIban")]
    public async Task Customer_WithOrWithoutIBAN_Return_Correct_Option(string iban, string response)
    {
        // Arrange
        var sut = SetupMockDialog(out var _, iban: iban);

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        // Assert
        var reply = await testClient.SendActivityAsync(string.Empty);
        reply.Text.Should().Be(GetTextLabelValue(response, TextLabelGroupName).Replace("{iban}", iban?.ObfuscateBankAccount()));
    }

    [Fact]
    public async Task Customer_can_add_new_IBAN()
    {
        // Arrange
        var sut = SetupMockDialog(out var _, customerIdVerified: true);

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        // Assert
        var reply = await testClient.SendActivityAsync(string.Empty);
        reply.Text.Should().Be(GetTextLabelValue("UnknownIban", TextLabelGroupName));

        reply = await testClient.SendActivityAsync<IMessageActivity>("Ja");
        Assert.Equal(GetTextLabelValue("AddIban", TextLabelGroupName), reply.Text);

        // Vraag: 
        reply = await testClient.SendActivityAsync<IMessageActivity>("NL099NEW123456");
        Assert.Equal(GetTextLabelValue("NewIbanSaved", TextLabelGroupName), reply.Text);
    }

    [Fact]
    public async Task Customer_can_update_IBAN_keep_IBAN()
    {
        // Arrange
        var sut = SetupMockDialog(out var nbaServiceMock, customerIdVerified: true, iban: "NL099BLA123456", currentTransActionIsNba: true);

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        var reply = await testClient.SendActivityAsync(string.Empty).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("CurrentKnownIban", TextLabelGroupName).Replace("{iban}", "NL099BLA123456"?.ObfuscateBankAccount()));

        reply = await testClient.SendActivityAsync<IMessageActivity>("Nee");
        Assert.Equal(GetTextLabelValue("NoAnswerKnownIban", TextLabelGroupName), reply.Text);

        nbaServiceMock.Verify(m => m.AddFeedback(It.IsAny<DialogData>(), "BankAccountNumber", FeedbackStatus.Conversion_success), Times.Once);
    }

    public ChangeIbanDialog SetupMockDialog(out Mock<INextBestActionService> nbaServiceMock, bool isVerified = true, bool customerIdVerified = true, string iban = null, bool currentTransActionIsNba = false)
    {
        TextLabelGroupName = "Bot_ChangeIbanDialog";
        var MockedLogger = new Mock<ILoggerFactory>();
        var serviceMock = new Mock<IFinancialsService>();
        nbaServiceMock = new Mock<INextBestActionService>();
        var sessionManagerMock = new Mock<ISessionManager>();
        var storageServiceMock = new Mock<IStorageService>();
        var validator = new Mock<IDialogValidators>();

        var logger = new Mock<ILogger>().Object;

        MockedLogger.Setup(f => f.CreateLogger(It.IsAny<string>())).Returns(logger);

        DialogData ValidUserData = new()
        {
            Verification = new VerificationData
            {
                IsVerified = isVerified,
                CustomerIdVerified = customerIdVerified,
                CustomerId = 100
            },
            SelectedAccount = new AccountData
            {
                AccountId = 10

            }

        };

        if (currentTransActionIsNba)
        {
            ValidUserData.NextBestAction = new NextBestActionData
            {
                CurrentTransactionIsNba = true
            };
        }

        validator
            .Setup(x => x.FinancialsDialogValidator.IbanValidator(It.IsAny<PromptValidatorContext<string>>(), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(true));

        serviceMock
            .Setup(x => x.UpdateBankAccount(It.IsAny<DialogData>(), It.IsAny<string>()))
            .Returns(Task.FromResult(AdvancePaymentAdviceStatus.Ok));

        serviceMock
            .Setup(x => x.GetFinancialsPreferences(It.IsAny<DialogData>(), It.IsAny<int>()))
            .Returns(Task.FromResult(
                new FinancialPreferences
                {
                    BankAccount = new BankAccount
                    {
                        Number = iban
                    }
                }));

        sessionManagerMock.Setup(x => x.GetDialogData(It.IsAny<ITurnContext>())).ReturnsAsync(ValidUserData).Verifiable();

        nbaServiceMock.Setup(m => m.GetNextBestActions(It.IsAny<DialogData>(), It.IsAny<bool>()))
            .ReturnsAsync(new DC.Products.Client.Models.NextBestAction
            {
                CustomerId = 123,
                ContextId = "ctx1",
                Actions = new List<DC.Products.Client.Models.ActionModel> {
                new DC.Products.Client.Models.ActionModel
                {
                    AccountId = 1,
                    ActionType = "BankAccountNumber",
                    ActionId = 1,
                    Channel = Channel.Chatbot,
                    ServingPointId = 4,
                    TreatmentVariationId = 2,
                    Score = 100
                }
                }
            });

        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));

        return new ChangeIbanDialog(MockedLogger.Object, new Mock<ILoggingService>().Object, sessionManagerMock.Object, validator.Object, serviceMock.Object, nbaServiceMock.Object, storageServiceMock.Object);
    }
}
