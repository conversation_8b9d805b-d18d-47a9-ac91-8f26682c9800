﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Customers;

[Collection("Sequential")]
public class RelocateDateDialogTests : TestBase
{
    [Fact]
    public async Task GetRelocateDate_with_VerifiedCustomer_WithInActiveAccount()
    {
        //Arrange
        SwapMockedServices(1, false);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._RELOCATEDATE);
        Assert.Contains("Ik kan helaas geen actief account vinden bij je klantnummer en kan je daarom niet verder helpen", testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetRelocateDate_with_UnverifiedCustomer()
    {
        //Arrange
        SwapMockedServices(1, false);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithUnverifiedCustomer(DialogCommands._RELOCATEDATE);
        Assert.Contains(GetTextLabelValue("CouldNotFindData", "Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetRelocateDate_WithoutRelocationDates()
    {
        //Arrange
        SwapMockedServices(3);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._RELOCATEDATE);
        Assert.Contains(GetTextLabelValue("NoMoveOutDate", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetRelocateDate_CustomerWithMoveInDate()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._RELOCATEDATE);
        Assert.Contains(GetTextLabelValue("MoveInContract", TextLabelGroupName)
            .Replace("{street}", $"nieuweStraat")
            .Replace("{housenumber}", $"2")
            .Replace("{housenumbersuffix}", $"")
            .Replace("{city}", "Damsko")
            .Replace("{date}", $"{DateTime.Now:dd-MM-yyyy}"), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetRelocateDate_CustomerWithMoveOutDate()
    {
        //Arrange
        SwapMockedServices(2);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._RELOCATEDATE);
        Assert.Contains(
            GetTextLabelValue("MoveOutContract", TextLabelGroupName)
            .Replace("{street}", $"nieuweStraat")
            .Replace("{housenumber}", $"2")
            .Replace("{housenumbersuffix}", $"")
            .Replace("{city}", "Damsko")
            .Replace("{date}", $"{DateTime.Now:dd-MM-yyyy}"), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetRelocateDate_CustomerWithMoveOut_WithOut_Date()
    {
        //Arrange
        SwapMockedServices(4);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._RELOCATEDATE);

        Assert.Contains(
             GetTextLabelValue("MoveOutContractNoDate", TextLabelGroupName)
            .Replace("{street}", $"nieuweStraat")
            .Replace("{housenumber}", $"2")
            .Replace("{housenumbersuffix}", $"")
            .Replace("{city}", "Damsko")
            .Replace("{accountId}", $"2"), testClient.GetNextReply().Text);
        Assert.Contains(GetTextLabelValue("AskToStopContract", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    private void SwapMockedServices(long customerId, bool activeAccount = true)
    {
        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        var productsRepositoryMock = new Mock<IDcProductsRepository>();
        var userAccountRepoMock = new Mock<IDcUserAccountsRepository>();
        var storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_RelocateDateDialog";
        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));
        customersRepositoryMock.SetupCustomerTestData(customerId, activeAccount: activeAccount);

        var relocationData = new Relocation
        {
            Id = 1,
            Address = new AddressModel
            {
                Street = "nieuweStraat",
                PostalCode = "4321bb",
                HouseNumber = 2,
                City = "Damsko"
            }
        };

        if (customerId != 4)
        {
            relocationData.Dates = new RelocationDate
            {
                Current = DateTime.Now
            };
        }

        productsRepositoryMock.Setup(x =>
                x.GetCustomerRelocations(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x != 3)))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListRelocations
                {
                    Data = new List<Relocations>
                    {
                        new Relocations
                        {
                            MoveIn = customerId == 1 ? relocationData : null,
                            MoveOut = customerId != 1 ? relocationData : null
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerRelocations(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 3)))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListRelocations
                {
                    Data = new List<Relocations>()
                }
            });


        var validator = new Mock<IDialogValidators>();
        validator.SetupDialogValidatorWithSessionManager(customerId);
        _services.SwapTransient(provider => validator.Object);

        _services.SwapTransient(provider => productsRepositoryMock.Object);
        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => userAccountRepoMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }
}
