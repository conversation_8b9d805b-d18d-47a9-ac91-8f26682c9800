﻿using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Customers.Client.Models;
using DC.Domain.Models.NextBestAction;
using DC.Repositories.Base.Enumerations;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Connector;
using Microsoft.Bot.Schema;
using Microsoft.Extensions.Logging;
using Moq;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DC.Bot.BusinessLogic;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Customers;

[Collection("Sequential")]
public class ChangeEmailDialogTests : TestBase
{
    [Theory]
    [InlineData(null, "UnknownEmail")]
    [InlineData("tes*@test.nl", "CurrentKnownEmail")]
    public async Task Customer_WithOrWithoutEmail_Return_Correct_Option(string email, string response)
    {
        // Arrange
        var sut = SetupMockDialog(out var _, email: email);

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        // Assert
        var reply = await testClient.SendActivityAsync(string.Empty);
        reply.Text.Should().Be(GetTextLabelValue(response, TextLabelGroupName).Replace("{email}", email));
    }

    [Fact]
    public async Task Customer_can_add_new_email()
    {
        // Arrange
        var sut = SetupMockDialog(out var _, customerIdVerified: true);

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        var reply = await testClient.SendActivityAsync(string.Empty).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("UnknownEmail", TextLabelGroupName));

        reply = await testClient.SendActivityAsync(string.Empty);
        Assert.Equal(GetTextLabelValue("AskWhatEmailToUse", TextLabelGroupName), reply.Text);

        // Vraag:
        await testClient.SendActivityAsync<IMessageActivity>("<EMAIL>");
        reply = await testClient.SendActivityAsync<IMessageActivity>("<EMAIL>");
        Assert.Equal(GetTextLabelValue("NewEmailSaved", TextLabelGroupName).Replace("{email}", "<EMAIL>"), reply.Text);
    }

    [Fact]
    public async Task Customer_can_update_email()
    {
        // Arrange
        var sut = SetupMockDialog(out var _, customerIdVerified: true, email: "<EMAIL>");

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        var reply = await testClient.SendActivityAsync(string.Empty).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("CurrentKnownEmail", TextLabelGroupName).Replace("{email}", "<EMAIL>"));

        reply = testClient.GetNextReply();
        reply.Text.Should().Be(GetTextLabelValue("WantToAdjust", TextLabelGroupName));

        reply = await testClient.SendActivityAsync<IMessageActivity>("Ja");
        Assert.Equal(GetTextLabelValue("AskWhatEmailToUse", TextLabelGroupName), reply.Text);

        // Vraag:
        reply = await testClient.SendActivityAsync<IMessageActivity>("<EMAIL>");
        Assert.Equal(GetTextLabelValue("NewEmailSaved", TextLabelGroupName).Replace("{email}", "<EMAIL>"), reply.Text);
    }

    [Fact]
    public async Task Customer_can_update_email_keep_current_email()
    {
        // Arrange
        var sut = SetupMockDialog(out var nbaServiceMock, customerIdVerified: true, email: "<EMAIL>", currentTransActionIsNba: true);

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        var reply = await testClient.SendActivityAsync(string.Empty).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("CurrentKnownEmail", TextLabelGroupName).Replace("{email}", "<EMAIL>"));

        reply = testClient.GetNextReply();
        reply.Text.Should().Be(GetTextLabelValue("WantToAdjust", TextLabelGroupName));

        reply = await testClient.SendActivityAsync<IMessageActivity>("Nee");
        Assert.Equal(GetTextLabelValue("CustomerKeepsSameEmail", TextLabelGroupName).Replace("{email}", "<EMAIL>"), reply.Text);

        nbaServiceMock.Verify(m => m.AddFeedback(It.IsAny<DialogData>(), "CustomerEmailAddress", FeedbackStatus.Conversion_success), Times.Once);
    }

    [Fact]
    public async Task Customer_can_add_new_email_FirstTimeWrong()
    {
        // Arrange
        var sut = SetupMockDialog(out var _, customerIdVerified: true);

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        var reply = await testClient.SendActivityAsync(string.Empty).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("UnknownEmail", TextLabelGroupName));

        reply = await testClient.SendActivityAsync<IMessageActivity>("Ja");
        Assert.Equal(GetTextLabelValue("AskWhatEmailToUse", TextLabelGroupName), reply.Text);

        // Vraag:
        reply = await testClient.SendActivityAsync<IMessageActivity>("geen geldig email");
        Assert.Equal(GetTextLabelValue("InvalidEmailRetry", TextLabelGroupName), reply.Text);
        reply = await testClient.SendActivityAsync<IMessageActivity>("<EMAIL>");
        reply = await testClient.SendActivityAsync<IMessageActivity>("<EMAIL>");
        Assert.Equal(GetTextLabelValue("NewEmailSaved", TextLabelGroupName).Replace("{email}", "<EMAIL>"), reply.Text);
    }

    [Fact]
    public async Task Customer_can_update_email_FirstTimeWrong()
    {
        // Arrange
        var sut = SetupMockDialog(out var _, customerIdVerified: true, email: "<EMAIL>");

        // Act
        var testClient = new DialogTestClient(Channels.Msteams, sut);

        var reply = await testClient.SendActivityAsync(string.Empty).ConfigureAwait(true);
        reply.Text.Should().Be(GetTextLabelValue("CurrentKnownEmail", TextLabelGroupName).Replace("{email}", "<EMAIL>"));

        reply = testClient.GetNextReply();
        reply.Text.Should().Be(GetTextLabelValue("WantToAdjust", TextLabelGroupName));

        reply = await testClient.SendActivityAsync<IMessageActivity>("Ja");
        Assert.Equal(GetTextLabelValue("AskWhatEmailToUse", TextLabelGroupName), reply.Text);

        // Vraag:
        reply = await testClient.SendActivityAsync<IMessageActivity>("geen geldig email");
        Assert.Equal(GetTextLabelValue("InvalidEmailRetry", TextLabelGroupName), reply.Text);
        reply = await testClient.SendActivityAsync<IMessageActivity>("<EMAIL>");
        Assert.Equal(GetTextLabelValue("NewEmailSaved", TextLabelGroupName).Replace("{email}", "<EMAIL>"), reply.Text);
    }

    public ChangeEmailDialog SetupMockDialog(out Mock<INextBestActionService> nbaServiceMock, bool isVerified = true, bool customerIdVerified = true, string email = null, bool currentTransActionIsNba = false)
    {
        TextLabelGroupName = "Bot_ChangeEmailDialog";
        var mockedLogger = new Mock<ILoggerFactory>();
        var customerServiceMock = new Mock<ICustomersService>();
        nbaServiceMock = new Mock<INextBestActionService>();
        var storageServiceMock = new Mock<IStorageService>();
        var sessionManagerMock = new Mock<ISessionManager>();
        var validator = new Mock<IDialogValidators>();
        validator.Setup(x => x.CustomerDialogValidator).Returns(new CustomerDialogValidator(sessionManagerMock.Object));

        var logger = new Mock<ILogger>().Object;

        mockedLogger.Setup(f => f.CreateLogger(It.IsAny<string>())).Returns(logger);

        var validUserData = new DialogData
        {
            Verification = new VerificationData
            {
                IsVerified = isVerified,
                CustomerIdVerified = customerIdVerified,
                CustomerId = 100
            },
            Customer = new CustomerData
            {
                Label = Label.Eneco,
                EmailAddress = email
            },
            SelectedAccount = new AccountData
            {
                AccountId = 10
            }

        };

        if (currentTransActionIsNba)
        {
            validUserData.NextBestAction = new NextBestActionData
            {
                CurrentTransactionIsNba = true
            };
        }

        customerServiceMock
            .Setup(x => x.UpdateEmailAddress(It.IsAny<DialogData>(), It.IsAny<string>()))
            .ReturnsAsync(new ChangeCustomerProfileResponse
            {
                EmailSentSucces = true
            });

        nbaServiceMock.Setup(m => m.GetNextBestActions(It.IsAny<DialogData>(), It.IsAny<bool>()))
            .ReturnsAsync(new DC.Products.Client.Models.NextBestAction
            {
                CustomerId = 123,
                ContextId = "ctx1",
                Actions = new List<DC.Products.Client.Models.ActionModel> {
                new DC.Products.Client.Models.ActionModel
                {
                    AccountId = 1,
                    ActionType = "CustomerEmailAddress",
                    ActionId = 1,
                    Channel = Channel.Chatbot,
                    ServingPointId = 4,
                    TreatmentVariationId = 2,
                    Score = 100
                }
                }
            });

        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));

        sessionManagerMock.Setup(x => x.GetDialogData(It.IsAny<ITurnContext>())).ReturnsAsync(validUserData).Verifiable();

        var contactPreferencesMock = new Mock<IContactPreferencesService>();

        contactPreferencesMock
            .Setup(x => x.GetEmailContactPreferences(It.IsAny<Label>(), It.IsAny<long>()))
            .ReturnsAsync(new EmailContactPreferences(true, true));

        return new ChangeEmailDialog(
            mockedLogger.Object,
            new Mock<ILoggingService>().Object,
            sessionManagerMock.Object,
            validator.Object,
            customerServiceMock.Object,
            nbaServiceMock.Object,
            storageServiceMock.Object,
            contactPreferencesMock.Object);
    }
}
