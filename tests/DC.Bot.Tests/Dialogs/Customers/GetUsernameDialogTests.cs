﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Domain.Exceptions.ResponseModels;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using DC.UserAccounts.Client.Models;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Microsoft.Rest;
using Moq;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;
using ErrorResponse = DC.Domain.Exceptions.ResponseModels.ErrorResponse;

namespace DC.Bot.Tests.Dialogs;

[Collection("Sequential")]
public class GetUsernameDialogTests : TestBase
{
    [Fact]
    public async Task GetUsername_with_VerifiedCustomer_WithoutContract()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._USERNAME);
        Assert.Equal(GetTextLabelValue("UsernameNotFound", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetUsername_with_VerifiedCustomer_WithContract()
    {
        //Arrange
        SwapMockedServices(2);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._USERNAME);
        Assert.StartsWith(GetTextLabelValue("SendMailUsername", TextLabelGroupName).Replace("{username}", "Si*******as"), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetUsername_with_VerifiedCustomer_CustomerNotFound()
    {
        //Arrange
        SwapMockedServices(3);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._USERNAME);
        Assert.StartsWith(GetTextLabelValue("SomethingWentWrong", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetUsername_with_VerifiedCustomer_WithInactiveAccount()
    {
        //Arrange
        SwapMockedServices(2, 0);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._USERNAME);
        Assert.StartsWith(GetTextLabelValue("CouldNotFindActiveAccount", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetUsername_with_UnverifiedCustomer()
    {
        //Arrange
        SwapMockedServices(2);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithUnverifiedCustomer(DialogCommands._USERNAME);
        Assert.StartsWith(GetTextLabelValue("CouldNotFindActiveAccount", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    private void SwapMockedServices(long customerId, int accountId = 2)
    {
        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        var userAccountRepMock = new Mock<IDcUserAccountsRepository>();
        var storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_UsernameDialog";
        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));

        customersRepositoryMock.SetupCustomerTestData(customerId, accountId);

        userAccountRepMock.Setup(x =>
                x.GetUsername(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<RequestDataValidationRequest>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataUserAccount
                {
                    Data = null
                }
            });

        userAccountRepMock.Setup(x =>
                x.GetUsername(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(y => y == 2), It.IsAny<RequestDataValidationRequest>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataUserAccount
                {
                    Data = new UserAccount
                    {
                        Username = "Sinterklaas",
                        AuthenticationMethod = Domain.Models.UserAccounts.AuthenticationMethod.Password
                    }
                }
            });

        userAccountRepMock.Setup(x =>
                x.GetUsername(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(y => y == 3), It.IsAny<RequestDataValidationRequest>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage(HttpStatusCode.NotFound),
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel> {
                        new ErrorModel {
                            Code = "code|11dd1484-6ecc-4097-a44d-72d2d4320336",
                            Type = "DataNotFoundException",
                            Details = "details"
                        }
                    }
                }
            });

        var validator = new Mock<IDialogValidators>();
        validator.SetupDialogValidatorWithSessionManager(customerId);
        _services.SwapTransient(provider => validator.Object);

        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => userAccountRepMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }
}
