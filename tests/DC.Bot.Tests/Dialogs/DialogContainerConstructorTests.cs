﻿using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Components;
using DC.Bot.BusinessLogic.Dialogs.Customers;
using DC.Bot.BusinessLogic.Dialogs.Customers.PhoneNumberDialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Dialogs.Financials;
using DC.Bot.BusinessLogic.Dialogs.Financials.Validators;
using DC.Bot.BusinessLogic.Dialogs.Products;
using DC.Bot.BusinessLogic.Dialogs.Products.Validators;
using DC.Bot.BusinessLogic.Dialogs.Usages;
using DC.Bot.BusinessLogic.Dialogs.UserAccounts;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Time.Testing;
using Moq;
using Xunit;

namespace DC.Bot.Tests.Dialogs;

public class DialogContainerConstructorTests
{
    [Fact]
    public void DialogContainer_Ctor_WorksAsExpected()
    {
        var lFactMock = new Mock<ILoggerFactory>();
        var lServiceMock = new Mock<ILoggingService>();
        var smMock = new Mock<ISessionManager>();
        var cServiceMock = new Mock<ICustomersService>();
        var fServiceMock = new Mock<IFinancialsService>();
        var pServiceMock = new Mock<IProductsService>();
        var pfServiceMock = new Mock<IProductFineCalculationService>();
        var sServiceMock = new Mock<IStorageService>();
        var uServiceMock = new Mock<IUsagesService>();
        var aServiceMock = new Mock<IUserAccountsService>();
        var vMock = new Mock<IDialogValidators>();
        var cMock = new Mock<IConfiguration>();
        var nbaMock = new Mock<INextBestActionService>();

        vMock.Setup(x => x.CustomerDialogValidator).Returns(new Mock<ICustomerValidator>().Object);
        vMock.Setup(x => x.FinancialsDialogValidator).Returns(new Mock<IFinancialsDialogValidator>().Object);
        vMock.Setup(x => x.ProductsDialogValidator).Returns(new Mock<IProductsDialogValidator>().Object);

        var optionsMock = new Mock<IOptions<FeatureToggle>>();
        optionsMock.Setup(o => o.Value).Returns(new FeatureToggle());

        var cDialogContainer =
            new CustomersDialogContainer
            (
                new CustomerAccountsDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, sServiceMock.Object),
                new YearnoteDateDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, cServiceMock.Object, sServiceMock.Object),
                new RelocateDateDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, cServiceMock.Object, pServiceMock.Object, sServiceMock.Object),
                new ChangeEmailDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, vMock.Object, cServiceMock.Object, nbaMock.Object, sServiceMock.Object, Mock.Of<IContactPreferencesService>()),
                new ChangeIbanDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, vMock.Object, fServiceMock.Object, nbaMock.Object, sServiceMock.Object),
                new ChangePhoneNumberDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, vMock.Object, cServiceMock.Object, nbaMock.Object, sServiceMock.Object),
                new ChangeContactPreferencesDialog(lFactMock.Object, smMock.Object, lServiceMock.Object, sServiceMock.Object, Mock.Of<IContactPreferencesService>())
            );
        var vDialogContainer =
            new CustomerVerificationDialogContainer
            (
                new CustomerVerificationDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, vMock.Object, cServiceMock.Object, nbaMock.Object, sServiceMock.Object),
                new CustomerIdVerification(lFactMock.Object, lServiceMock.Object, smMock.Object, vMock.Object, sServiceMock.Object)
            );
        var fDialogContainer =
            new FinancialsDialogContainer
            (
                new AdvancePaymentAdviceDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, cServiceMock.Object, fServiceMock.Object, sServiceMock.Object),
                new AdvancePaymentAmountDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, cServiceMock.Object, fServiceMock.Object, sServiceMock.Object),
                new GiroCardStepDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, vMock.Object, fServiceMock.Object, cServiceMock.Object, sServiceMock.Object),
                new AdvancePaymentDayDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, vMock.Object, cServiceMock.Object, fServiceMock.Object, sServiceMock.Object),
                new PaymentArrangementDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, cServiceMock.Object, fServiceMock.Object, sServiceMock.Object)
            );
        var pDialogContainer =
            new ProductsDialogContainer
            (
                new ProductRatesDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, cServiceMock.Object, pServiceMock.Object, sServiceMock.Object),
                new ProductEndDatesDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, cServiceMock.Object, pServiceMock.Object, sServiceMock.Object),
                new ProductEndDatesAdviceDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, cServiceMock.Object, pServiceMock.Object, sServiceMock.Object),
                new KetelComfortAppointmentDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, pServiceMock.Object, sServiceMock.Object),
                new DiscontinueToonDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, pServiceMock.Object, sServiceMock.Object),
                new DiscontinueServiceContractDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, pServiceMock.Object, sServiceMock.Object),
                new CreateServiceOrderDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, pServiceMock.Object, sServiceMock.Object, cMock.Object),
                new NextBestActionDialog(lFactMock.Object, smMock.Object, lServiceMock.Object, nbaMock.Object, cServiceMock.Object, sServiceMock.Object),
                new ProductFineCalculationDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, vMock.Object, cServiceMock.Object, pServiceMock.Object, pfServiceMock.Object, sServiceMock.Object, Options.Create(new FeatureToggle { NewFinePolicyDate = new System.DateTime(2023, 6, 1) })),
                new ZonOpDakDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, cServiceMock.Object, pServiceMock.Object, sServiceMock.Object)
            );
        var uDialogContainer =
            new UsagesDialogContainer
            (
                new ProductUsagesDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, cServiceMock.Object, uServiceMock.Object, sServiceMock.Object),
                new ReadingsReportRequestDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, vMock.Object, uServiceMock.Object, sServiceMock.Object),
                new SaveReadingPersonalDialog(lFactMock.Object, smMock.Object, lServiceMock.Object, uServiceMock.Object, sServiceMock.Object, cServiceMock.Object, new FakeTimeProvider()),
                new IsmaMandateDialog(lFactMock.Object, smMock.Object, lServiceMock.Object, uServiceMock.Object, cServiceMock.Object, sServiceMock.Object)
            );
        var aDialogContainer =
            new UserAccountsDialogContainer
            (
                new UsernameDialog(lFactMock.Object, lServiceMock.Object, smMock.Object, aServiceMock.Object, sServiceMock.Object)
            );


        var dialogContainer = new DialogContainer(cDialogContainer, vDialogContainer, fDialogContainer, pDialogContainer, uDialogContainer, aDialogContainer);
        dialogContainer.Customers.ChangeEmailDialog.Should().NotBeNull();
        dialogContainer.Customers.ChangeIbanDialog.Should().NotBeNull();
        dialogContainer.Customers.ChangePhoneNumberDialog.Should().NotBeNull();
        dialogContainer.Customers.CustomerAccountsDialog.Should().NotBeNull();
        dialogContainer.Customers.RelocateDateDialog.Should().NotBeNull();
        dialogContainer.Customers.YearnoteDateDialog.Should().NotBeNull();
        dialogContainer.Verification.CustomerIdVerification.Should().NotBeNull();
        dialogContainer.Verification.CustomerVerificationDialog.Should().NotBeNull();
        dialogContainer.Financials.AdvancePaymentAmountDialog.Should().NotBeNull();
        dialogContainer.Financials.AdvancePaymentDayDialog.Should().NotBeNull();
        dialogContainer.Financials.GiroCardStepDialog.Should().NotBeNull();
        dialogContainer.Financials.PaymentArrangementDialog.Should().NotBeNull();
        dialogContainer.Products.ProductEndDatesDialog.Should().NotBeNull();
        dialogContainer.Products.ProductRatesDialog.Should().NotBeNull();
        dialogContainer.Usages.ProductUsagesDialog.Should().NotBeNull();
        dialogContainer.Usages.ReadingsReportRequestDialog.Should().NotBeNull();
        dialogContainer.UserAccounts.UsernameDialog.Should().NotBeNull();
    }
}
