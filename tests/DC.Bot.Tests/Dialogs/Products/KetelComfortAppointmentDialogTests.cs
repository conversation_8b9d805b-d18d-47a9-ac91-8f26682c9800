﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Customers.Client.Models;
using DC.Domain.Models.Agreements;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Microsoft.Rest;
using Moq;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;
using Product = DC.Domain.Models.Agreements.Product;

namespace DC.Bot.Tests.Dialogs.Products;

[Collection("Sequential")]
public class KetelComfortAppointmentDialogTests : TestBase
{
    private CultureInfo ci = new("nl-NL");

    private void SwapMockedServices(int customerId = 1, int houseNumber = 1)
    {
        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        var productsRepoMock = new Mock<IDcProductsRepository>();
        var storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_KetelComfortAppointmentDialog";
        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));
        customersRepositoryMock.SetupCustomerTestData(customerId, houseNumber: houseNumber);
        customersRepositoryMock.Setup(x =>
                x.GetAgreements(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListAgreement
                {
                    Data = new List<Agreement>
                    {
                        new Agreement
                        {
                            Products = new List<Product>
                            {
                                new Product
                                {
                                    ProductType = Domain.Models.Products.ProductType.Electricity,
                                    IsActive = true,
                                    ProductOffering = new ProductOffering
                                    {
                                        IsIndefiniteDuration = customerId != 1
                                    }
                                }
                            }
                        }
                    }
                }
            });


        productsRepoMock.Setup(x => x.GetKetelComfortProductDetails(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 1), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataKetelComfortProductDetails { }
            });

        productsRepoMock.Setup(x => x.GetKetelComfortProductDetails(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 2), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataKetelComfortProductDetails
                {
                    Data = new KetelComfortProductDetails
                    {
                        Service = new ServiceProductService
                        {
                            PreviousAppointments = new List<MaintenanceAppointmentBase>
                            {
                                new MaintenanceAppointmentBase
                                {
                                    AppointmentDate = new System.DateTime(2021,12,31)
                                }
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetKetelComfortProductDetails(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 3), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataKetelComfortProductDetails
                {
                    Data = new KetelComfortProductDetails
                    {
                        Service = new ServiceProductService
                        {
                            UpcomingAppointment = new MaintenanceAppointment
                            {
                                Type = PlanActionType.Expired
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetKetelComfortProductDetails(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 3), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataKetelComfortProductDetails
                {
                    Data = new KetelComfortProductDetails
                    {
                        Service = new ServiceProductService
                        {
                            UpcomingAppointment = new MaintenanceAppointment
                            {
                                Type = PlanActionType.Unknown
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetKetelComfortProductDetails(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 4), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataKetelComfortProductDetails
                {
                    Data = new KetelComfortProductDetails
                    {
                        Service = new ServiceProductService
                        {
                            UpcomingAppointment = new MaintenanceAppointment
                            {
                                Type = PlanActionType.Invite
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetKetelComfortProductDetails(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 5), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataKetelComfortProductDetails
                {
                    Data = new KetelComfortProductDetails
                    {
                        Service = new ServiceProductService
                        {
                            UpcomingAppointment = new MaintenanceAppointment
                            {
                                PlanWindow = new MaintenancePlanWindow
                                {
                                    Start = new System.DateTime(2021, 10, 21),
                                    End = new System.DateTime(2021, 12, 31)
                                }
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetKetelComfortProductDetails(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 6), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataKetelComfortProductDetails
                {
                    Data = new KetelComfortProductDetails
                    {
                        Service = new ServiceProductService
                        {
                            UpcomingAppointment = new MaintenanceAppointment
                            {
                                Type = PlanActionType.Previousonly
                            }
                        }
                    }
                }
            });

        var validator = new Mock<IDialogValidators>();
        validator.SetupDialogValidatorWithSessionManager(customerId);
        _services.SwapTransient(provider => validator.Object);

        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => productsRepoMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }

    [Fact]
    public async Task Customer_WithNoContracts_GetCorrectReponse()
    {
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._KETELCOMFORT_APPOINTMENT).ConfigureAwait(true);
        Assert.Contains(GetTextLabelValue("NoServiceContract", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task Customer_AlreadyHadACheckUp_GetCorrectReponse()
    {
        SwapMockedServices(2);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._KETELCOMFORT_APPOINTMENT).ConfigureAwait(true);
        var text = testClient.GetNextReply().Text;
        Assert.Contains(GetTextLabelValue("LatestAppointment", TextLabelGroupName)
            .Replace("{date}", new System.DateTime(2021, 12, 31).ToString("dd MMMM yyyy", ci)), text);
    }

    [Fact]
    public async Task Customer_WithUnknownStatus_GetCorrectReponse()
    {
        SwapMockedServices(3);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._KETELCOMFORT_APPOINTMENT).ConfigureAwait(true);
        Assert.Contains(GetTextLabelValue("UpcomingAppointmentNewCustomerOrUnknown", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task Customer_WithInviteStatys_GetCorrectReponse()
    {
        SwapMockedServices(4);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._KETELCOMFORT_APPOINTMENT).ConfigureAwait(true);
        Assert.Contains(GetTextLabelValue("UpcomingAppointmentInvite", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task Customer_WithPlanWindow_GetCorrectReponse()
    {
        SwapMockedServices(5);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._KETELCOMFORT_APPOINTMENT).ConfigureAwait(true);
        Assert.Contains(GetTextLabelValue("UpcomingAppointmentPlanWindow", TextLabelGroupName)
            .Replace("{planWindowStartTime}", new System.DateTime(2021, 10, 31).ToString("MMMM yyyy", ci))
            .Replace("{planWindowEndTime}", new System.DateTime(2021, 12, 31).ToString("MMMM yyyy", ci)), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task Customer_WithNoMatchingState_GetDefaultReponse()
    {
        SwapMockedServices(6);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._KETELCOMFORT_APPOINTMENT).ConfigureAwait(true);
        Assert.Contains(GetTextLabelValue("UpcomingAppointmentNotFound", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task Customer__with_UnverifiedCustomer()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithUnverifiedCustomer(DialogCommands._KETELCOMFORT_APPOINTMENT);
        Assert.Contains(GetTextLabelValue("CouldNotFindData", "Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }
}
