﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Domain.Exceptions;
using DC.Domain.Models.Products;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Products;

[Collection("Sequential")]
public class ProductEndDatesAdviceDialogTests : TestBase
{
    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithoutContract()
    {
        var sessionManager = GetSessionsManager(1);
        //Arrange
        SwapMockedServices(sessionManager, 1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);
        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceNoProducts,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithContract()
    {
        var sessionManager = GetSessionsManager(2);
        //Arrange
        SwapMockedServices(sessionManager, 2);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains(GetTextLabelValue("ProductEndDate", "Bot_ProductEndDatesAdviceDialog").Replace("endDateContract", $"{DateTime.Now:dd-MM-yyyy}"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceDefiniteIn90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithInActiveAccount()
    {
        var sessionManager = GetSessionsManager(2);
        //Arrange
        SwapMockedServices(sessionManager, 2, false);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("CouldNotFindActiveAccount", "Bot_GeneralTextLabels"));
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithMulitpleAccount_WithInActiveAccount()
    {
        var sessionManager = GetSessionsManager(3);
        //Arrange
        SwapMockedServices(sessionManager, 3, false, true);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceNoProducts,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_UnverifiedCustomer()
    {
        var sessionManager = GetSessionsManager(1);
        //Arrange
        SwapMockedServices(sessionManager, 1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithUnverifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);
        var result = GetTextLabelValue("CouldNotFindData", "Bot_GeneralTextLabels");
        Assert.Contains(result, testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithOnlyElecContract()
    {
        var sessionManager = GetSessionsManager(4);
        //Arrange
        SwapMockedServices(sessionManager, 4);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var answer = testClient.GetNextReply().Text.Should();
        answer.Contain($"**Product A**");
        answer.Contain(GetTextLabelValue("ProductEndDate", "Bot_ProductEndDatesAdviceDialog").Replace("{endDateContract}", $"{DateTime.Now.AddDays(60):dd-MM-yyyy}"));
        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceDefiniteIn90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithNOEGWContract()
    {
        var sessionManager = GetSessionsManager(6);
        //Arrange
        SwapMockedServices(sessionManager, 6);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceNoProducts,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithOnlyWarmthContract()
    {
        var sessionManager = GetSessionsManager(5);
        //Arrange
        SwapMockedServices(sessionManager, 5);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);
        var answer = testClient.GetNextReply().Text.Should();
        answer.Contain($"**Product A**");
        answer.Contain(GetTextLabelValue("ProductEndDate", "Bot_ProductEndDatesAdviceDialog").Replace("{endDateContract}", $"{DateTime.Now:dd-MM-yyyy}"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceWarmth,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithElecAndWarmthDefiniteBefore90DaysContract()
    {
        var sessionManager = GetSessionsManager(7);
        //Arrange
        SwapMockedServices(sessionManager, 7);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains(GetTextLabelValue("ProductEndDate", "Bot_ProductEndDatesAdviceDialog").Replace("endDateContract", $"{DateTime.Now.AddDays(60):dd-MM-yyyy}"));

        endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product B");
        endDateText.Contains(GetTextLabelValue("ProductEndDate", "Bot_ProductEndDatesAdviceDialog").Replace("endDateContract", $"{DateTime.Now.AddDays(60):dd-MM-yyyy}"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceDefiniteElecAndWarmthIn90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithElecAndWarmthDefiniteAfter90DaysContract()
    {
        var sessionManager = GetSessionsManager(9);
        //Arrange
        SwapMockedServices(sessionManager, 9);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains(GetTextLabelValue("ProductEndDate", "Bot_ProductEndDatesAdviceDialog").Replace("endDateContract", $"{DateTime.Now.AddDays(100):dd-MM-yyyy}"));

        endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product B");
        endDateText.Contains(GetTextLabelValue("ProductEndDate", "Bot_ProductEndDatesAdviceDialog").Replace("endDateContract", $"{DateTime.Now.AddDays(100):dd-MM-yyyy}"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceDefiniteElecAndWarmthAfter90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithElecAndWarmthIndefiniteBefore90DaysContract()
    {
        var sessionManager = GetSessionsManager(10);
        //Arrange
        SwapMockedServices(sessionManager, 10);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains(GetTextLabelValue("ProductIndefinite", "Bot_ProductEndDatesAdviceDialog"));
        endDateText.Contains($"Je tarief verandert per: {DateTime.Today.AddDays(60):dd-MM-yyyy} ");

        endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product B");
        endDateText.Contains(GetTextLabelValue("ProductIndefinite", "Bot_ProductEndDatesAdviceDialog"));
        endDateText.Contains($"Je tarief verandert per: {DateTime.Today.AddDays(60):dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceIndefiniteElecAndWarmthIn90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithElecAndWarmthIndefiniteAfter90DaysContract()
    {
        var sessionManager = GetSessionsManager(11);
        //Arrange
        SwapMockedServices(sessionManager, 11);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains(GetTextLabelValue("ProductIndefinite", "Bot_ProductEndDatesAdviceDialog"));
        endDateText.Contains($"Je tarief verandert per: {DateTime.Today.AddDays(100):dd-MM-yyyy} ");

        endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product B");
        endDateText.Contains(GetTextLabelValue("ProductIndefinite", "Bot_ProductEndDatesAdviceDialog"));
        endDateText.Contains($"Je tarief verandert per: {DateTime.Today.AddDays(100):dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceIndefiniteElecAndWarmthAfter90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithGasDefiniteBefore90DaysContract()
    {
        var sessionManager = GetSessionsManager(8);
        //Arrange
        SwapMockedServices(sessionManager, 8);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains(GetTextLabelValue("ProductEndDate", "Bot_ProductEndDatesAdviceDialog").Replace("endDateContract", $"{DateTime.Now.AddDays(100):dd-MM-yyyy}"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceDefiniteIn90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithGasDefiniteAfter90DaysContract()
    {
        var sessionManager = GetSessionsManager(12);
        //Arrange
        SwapMockedServices(sessionManager, 12);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains(GetTextLabelValue("ProductEndDate", "Bot_ProductEndDatesAdviceDialog").Replace("endDateContract", $"{DateTime.Now.AddDays(100):dd-MM-yyyy}"));

        endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product B");
        endDateText.Contains(GetTextLabelValue("ProductEndDate", "Bot_ProductEndDatesAdviceDialog").Replace("endDateContract", $"{DateTime.Now.AddDays(100):dd-MM-yyyy}"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceDefiniteAfter90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithGasIndefinitBefore90DaysContract()
    {
        var sessionManager = GetSessionsManager(13);
        //Arrange
        SwapMockedServices(sessionManager, 13);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains(GetTextLabelValue("ProductIndefinite", "Bot_ProductEndDatesAdviceDialog"));
        endDateText.Contains($"Je tarief verandert per: {DateTime.Today.AddDays(60):dd-MM-yyyy} ");

        endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product B");
        endDateText.Contains(GetTextLabelValue("ProductIndefinite", "Bot_ProductEndDatesAdviceDialog"));
        endDateText.Contains($"Je tarief verandert per: {DateTime.Today.AddDays(60):dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceIndefiniteIn90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithGasIndefiniteAfter90DaysContract()
    {
        var sessionManager = GetSessionsManager(14);
        //Arrange
        SwapMockedServices(sessionManager, 14);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains("Contract: onbepaalde tijd");
        endDateText.Contains($"Je tarief verandert per: {DateTime.Today.AddDays(100):dd-MM-yyyy} ");

        endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product B");
        endDateText.Contains("Contract: onbepaalde tijd");
        endDateText.Contains($"Je tarief verandert per: {DateTime.Today.AddDays(100):dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceIndefiniteAfter90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithElecDefiniteBefore90DaysContract()
    {
        var sessionManager = GetSessionsManager(15);
        //Arrange
        SwapMockedServices(sessionManager, 15);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains($"Einddatum: {DateTime.Today.AddDays(60):dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceDefiniteIn90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithElecDefiniteAfter90DaysContract()
    {
        var sessionManager = GetSessionsManager(16);
        //Arrange
        SwapMockedServices(sessionManager, 16);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains($"Einddatum: {DateTime.Today.AddDays(100):dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceDefiniteAfter90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithElecIndefinitBefore90DaysContract()
    {
        var sessionManager = GetSessionsManager(17);
        //Arrange
        SwapMockedServices(sessionManager, 17);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains("Contract: onbepaalde tijd");
        endDateText.Contains($"Je tarief verandert per: {DateTime.Today.AddDays(60):dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceIndefiniteIn90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithElecIndefiniteAfter90DaysContract()
    {
        var sessionManager = GetSessionsManager(18);
        //Arrange
        SwapMockedServices(sessionManager, 18);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains("Contract: onbepaalde tijd");
        endDateText.Contains($"Je tarief verandert per: {DateTime.Today.AddDays(100):dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceIndefiniteAfter90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithElecIndefiniteWithoutEndDateContract()
    {
        var sessionManager = GetSessionsManager(19);
        //Arrange
        SwapMockedServices(sessionManager, 19);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains("Contract: onbepaalde tijd");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceIndefiniteAfter90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_WithContractDuration1Month()
    {
        var sessionManager = GetSessionsManager(20);
        //Arrange
        SwapMockedServices(sessionManager, 20);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains($"Einddatum: {DateTime.Now:dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ProductAdviceDefiniteIn90Days,
                It.IsAny<CancellationToken>()), Times.Once());
    }


    [Fact]
    public async Task GetEndDateAdvice_with_VerifiedCustomer_IsCombinationOfElectricityGasIndefiniteAndModelContract_True()
    {
        var sessionManager = GetSessionsManager(21);
        //Arrange
        SwapMockedServices(sessionManager, 21);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A - Modelcontract");
        endDateText.Contains($"Einddatum: {DateTime.Now:dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Success,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task GetEndDateAdvice_UnhappyFlow()
    {
        var sessionManager = GetSessionsManager(666);
        //Arrange
        SwapMockedServices(sessionManager, 666);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATESADVICE);

        var errorText = testClient.GetNextReply().Text;
        errorText.Should().StartWith(GetTextLabelValue("SomethingWentWrong", "Bot_GeneralTextLabels"));
    }

    private void SwapMockedServices(Mock<ISessionManager> sessionManagerMock, long customerId, bool activeAccount = true, bool multiAccount = false)
    {
        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        var productsRepositoryMock = new Mock<IDcProductsRepository>();
        var userAccountRepMock = new Mock<IDcUserAccountsRepository>();
        var storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_ProductEndDateAdviceDialog";

        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(new List<Storage.Client.Models.TextLabelModel>
            {

            }));
        customersRepositoryMock.SetupCustomerTestData(customerId: customerId, activeAccount: activeAccount, multiAccount: multiAccount);

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => (x >= 200 && x != 666)), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity
                            },
                            Description = "Product A",
                            EndDateContract = customerId <= 201 ? null : customerId == 202 ? DateTime.Today.AddDays(45) : DateTime.Today.AddDays(75),
                            EndDatePrice = (customerId == 201 || customerId == 202) ? DateTime.Today.AddDays(45) : customerId != 204 ? DateTime.Today.AddDays(75) : null,
                        }
                    }
                }
            });
        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 666), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .Throws(new TechnicalException(new Guid("********-8e7a-4a4e-902d-681f9063c1ff"), "Error, customer 666 is evil"));

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 21), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                                MarketingCode = "PAMOD"
                            },
                            Description = "Product A - Modelcontract",
                            EndDatePrice = null,
                            StartDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1).AddMonths(-1),
                            Duration = 1,
                            Indefinite = true
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Product B",
                            StartDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1).AddMonths(-1),
                            EndDatePrice = null,
                            PriceDeterminationDate = null,
                            EndDateContract = null,
                            Duration = 1,
                            Indefinite = true
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.StukjeZon
                            },
                            Description = "StukjeZon"
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 20), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = null,
                            StartDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1).AddMonths(-1),
                            Duration = 1,
                            EndDateContract = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1).AddMonths(1),
                            Indefinite = false
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Product B",
                            StartDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1).AddMonths(-1),
                            EndDatePrice = null,
                            PriceDeterminationDate = null,
                            EndDateContract = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1).AddMonths(1),
                            Duration = 1,
                            Indefinite = false
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.StukjeZon
                            },
                            Description = "StukjeZon"
                        }
                    }
                }
            });
        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 19), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDateContract = null,
                            Indefinite = true,
                            Duration = 12
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 18), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(100),
                            EndDateContract = null
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 17), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(60),
                            EndDateContract = null
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 16), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(100),
                            EndDateContract = DateTime.Today.AddDays(100)
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 15), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(60),
                            EndDateContract = DateTime.Today.AddDays(60)
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 14), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(100),
                            EndDateContract = null
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Product B",
                            EndDatePrice = DateTime.Today.AddDays(100),
                            EndDateContract = null,
                            PriceDeterminationDate = DateTime.Now.AddMonths(-6),
                            Duration = 12
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 13), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(60),
                            EndDateContract = null
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Product B",
                            EndDatePrice = DateTime.Today.AddDays(60),
                            EndDateContract = null,
                            PriceDeterminationDate = DateTime.Now.AddMonths(-6),
                            Duration = 12
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 12), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(100),
                            EndDateContract = DateTime.Today.AddDays(100),
                            Indefinite = false
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Product B",
                            EndDatePrice = DateTime.Today.AddDays(100),
                            EndDateContract = DateTime.Today.AddDays(100),
                            Indefinite = false,
                            PriceDeterminationDate = DateTime.Now.AddMonths(-6),
                            Duration = 12
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 11), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(100),
                            EndDateContract = null
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Warmth,
                            },
                            Description = "Product B",
                            EndDatePrice = DateTime.Today.AddDays(100),
                            EndDateContract = null,
                            Duration = 12
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 10), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(60),
                            EndDateContract = null
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Warmth,
                            },
                            Description = "Product B",
                            EndDatePrice = DateTime.Today.AddDays(60),
                            EndDateContract = null,
                            Duration = 12
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 9), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(100),
                            EndDateContract = DateTime.Today.AddDays(100),
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Warmth,
                            },
                            Description = "Product B",
                            EndDatePrice = DateTime.Today.AddDays(100),
                            EndDateContract = DateTime.Today.AddDays(100),
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 8), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(60),
                            EndDateContract = DateTime.Today.AddDays(60),
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 7), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(60),
                            EndDateContract = DateTime.Today.AddDays(60),
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Warmth,
                            },
                            Description = "Product B",
                            EndDatePrice = DateTime.Now,
                            EndDateContract = DateTime.Now,
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 6), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.ToonService,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(60),
                            EndDateContract = null
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 5), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Warmth,
                            },
                            Description = "Product A",
                            EndDateContract = DateTime.Now,
                            EndDatePrice = DateTime.Now
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 4), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(60),
                            EndDateContract = DateTime.Today.AddDays(60)
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 3), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = null
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 2), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Now
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Product B",
                            EndDatePrice = DateTime.Now,
                            PriceDeterminationDate = DateTime.Now.AddMonths(-6),
                            EndDateContract = DateTime.Now,
                            Duration = 12
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.StukjeZon
                            },
                            Description = "StukjeZon"
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 1), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = null
                }
            });

        var validator = new Mock<IDialogValidators>();
        var loggingService = new Mock<ILoggingService>();
        validator.SetupDialogValidatorWithSessionManager(customerId);
        _services.SwapTransient(provider => validator.Object);

        _services.SwapTransient(provider => loggingService.Object);
        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => productsRepositoryMock.Object);
        _services.SwapTransient(provider => userAccountRepMock.Object);
        _services.SwapTransient(provider => sessionManagerMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }

    private static Mock<ISessionManager> GetSessionsManager(long customerId, Label label = Label.Eneco)
    {
        var sessionManagerMock = new Mock<ISessionManager>();
        sessionManagerMock.Setup(x =>
                x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Customer = new CustomerData
                {
                    Label = label
                },
                Verification = new VerificationData
                {
                    CustomerId = customerId,
                    CustomerIdVerified = true
                }
            });

        return sessionManagerMock;
    }
}
