﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Dialogs.Financials.Validators;
using DC.Bot.BusinessLogic.Dialogs.Products.Validators;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.FinePolicy;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Customers.Client.Models;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.Products;
using DC.Domain.Models.Usages;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using ProductModel = DC.Domain.Models.Products.ProductModel;
using ProductTypeModel = DC.Domain.Models.Products.ProductTypeModel;

namespace DC.Bot.Tests.Dialogs.Products
{
    [Collection("Sequential")]
    public class ProductFineCalculationDialogTests : TestBase
    {
        private static DateTime endContractDate = DateTime.Today.AddMonths(3);
        private static DateTime cancelDate = DateTime.Today.AddDays(10);

        [Fact]
        public async Task ProductFineCalculation_with_VerifiedCustomer_WithoutEGContract()
        {
            var sessionManager = GetSessionsManager(1);
            //Arrange
            SwapMockedServices(sessionManager, 1);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("NoEGContract", TextLabelGroupName));
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Unhappy,
                    It.IsAny<CancellationToken>()), Times.Once());
        }

        [Fact]
        public async Task ProductFineCalculation_with_VerifiedCustomer_WithOTContract()
        {
            var sessionManager = GetSessionsManager(2);
            //Arrange
            SwapMockedServices(sessionManager, 2);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("OTContract", TextLabelGroupName));
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Unhappy,
                    It.IsAny<CancellationToken>()), Times.Once());
        }

        [Fact]
        public async Task ProductFineCalculation_with_VerifiedCustomer_WithContracts_InReconsiderationPeriod()
        {
            var sessionManager = GetSessionsManager(11);
            //Arrange
            SwapMockedServices(sessionManager, 11);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("ContractReconsiderationPeriod", TextLabelGroupName));
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Unhappy,
                    It.IsAny<CancellationToken>()), Times.Once());
        }

        [Fact]
        public async Task ProductFineCalculation_with_VerifiedCustomer_OldFinePolicy()
        {
            var sessionManager = GetSessionsManager(3);
            //Arrange
            SwapMockedServices(sessionManager, 3);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("OldFinePolicyProductFine", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{enddate}", $"{endContractDate:dd-MM-yyyy}")
                .Replace("{fine}", "50,00"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("OldFinePolicyProductFine", TextLabelGroupName)
                .Replace("{product}", "Gas")
                .Replace("{enddate}", $"{endContractDate:dd-MM-yyyy}")
                .Replace("{fine}", "50,00"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("OldFinePolicyTotalFine", TextLabelGroupName)
                .Replace("{totalFine}", "100,00"));
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Success,
                    It.IsAny<CancellationToken>()), Times.Once());
        }

        [Fact]
        public async Task ProductFineCalculation_with_VerifiedCustomer_OldFinePolicy_WithOTContract()
        {
            var sessionManager = GetSessionsManager(8);
            //Arrange
            SwapMockedServices(sessionManager, 8);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("OldFinePolicyProductFine", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{enddate}", $"{endContractDate:dd-MM-yyyy}")
                .Replace("{fine}", "50,00"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("OldFinePolicyTotalFine", TextLabelGroupName)
                .Replace("{totalFine}", "50,00"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("OldFinePolicyOTContract", TextLabelGroupName)
                .Replace("{product}", "Gas"));
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Success,
                    It.IsAny<CancellationToken>()), Times.Once());
        }

        [Fact]
        public async Task ProductFineCalculation_with_VerifiedCustomer_NewFinePolicyWithOneProduct()
        {
            var sessionManager = GetSessionsManager(4);
            //Arrange
            SwapMockedServices(sessionManager, 4);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Gas")
                .Replace("{enddate}", $"{endContractDate:dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyFine", TextLabelGroupName));
            reply = await testClient.SendActivityAsync(GetTextLabelValue("CalculateFine", TextLabelGroupName)).ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FineDependsOnCancelDate", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskCancelDate", TextLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("CancelDateFormat", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"{DateTime.Today.AddDays(10):dd-MM-yyyy}").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyTariffGas", TextLabelGroupName)
                .Replace("{product}", "Gas")
                .Replace("{tariff}", $"1.61")
                .Replace("{referenceProduct}", "Eneco Gas 3 jaar")
                .Replace("{referenceTariff}", $"0.47")
                .Replace("{tariffDifference}", $"1.14"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyRemainingUsagesGas", TextLabelGroupName)
                .Replace("{enddate}", $"{cancelDate:dd-MM-yyyy}")
                .Replace("{totalUsages}", $"{402}")
                .Replace("{usages}", $"{119}")
                .Replace("{remaingUsages}", $"{283}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyFine", TextLabelGroupName)
                .Replace("{product}", "Gas")
                .Replace("{tariffDifference}", $"1.14")
                .Replace("{denotationType}", $"m³")
                .Replace("{remaingUsages}", $"{283}")
                .Replace("{fine}", $"50.00"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyTotalFine", TextLabelGroupName)
                .Replace("{fine}", $"50.00")
                .Replace("{fineWithVat}", $"60.50"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FineDisclaimer", TextLabelGroupName)
                .Replace("&ast;", "*"));
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Success,
                    It.IsAny<CancellationToken>()), Times.Once());
        }

        [Fact]
        public async Task ProductFineCalculation_with_VerifiedCustomer_NewFinePolicyWithTwoProducts()
        {
            var sessionManager = GetSessionsManager(5);
            //Arrange
            SwapMockedServices(sessionManager, 5);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{enddate}", $"{endContractDate:dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Gas")
                .Replace("{enddate}", $"{endContractDate:dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyFine", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskProductCalculateFine", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"beide").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FineDependsOnCancelDate", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskCancelDate", TextLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("CancelDateFormat", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"{DateTime.Today.AddDays(10):dd-MM-yyyy}").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyTariffElectricity", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{tariff}", $"0.37")
                .Replace("{referenceProduct}", "Eneco HollandseWind & Zon 3 jaar")
                .Replace("{referenceTariff}", $"0.11")
                .Replace("{tariffDifference}", $"0.26"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyRemainingUsagesElectricity", TextLabelGroupName)
                .Replace("{enddate}", $"{cancelDate:dd-MM-yyyy}")
                .Replace("{usagesSJV}", $"{1096}")
                .Replace("{usagesSJI}", $"{1894}")
                .Replace("{totalUsages}", $"{3222}")
                .Replace("{remaingUsages}", $"{-798}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyFine", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{tariffDifference}", $"0.26")
                .Replace("{denotationType}", $"kWh")
                .Replace("{remaingUsages}", $"{-798}")
                .Replace("{fine}", $"50.00"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyTariffGas", TextLabelGroupName)
                .Replace("{product}", "Gas")
                .Replace("{tariff}", $"1.61")
                .Replace("{referenceProduct}", "Eneco Gas 3 jaar")
                .Replace("{referenceTariff}", $"0.47")
                .Replace("{tariffDifference}", $"1.14"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyRemainingUsagesGas", TextLabelGroupName)
                .Replace("{enddate}", $"{cancelDate:dd-MM-yyyy}")
                .Replace("{totalUsages}", $"{402}")
                .Replace("{usages}", $"{119}")
                .Replace("{remaingUsages}", $"{283}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyFine", TextLabelGroupName)
                .Replace("{product}", "Gas")
                .Replace("{tariffDifference}", $"1.14")
                .Replace("{denotationType}", $"m³")
                .Replace("{remaingUsages}", $"{283}")
                .Replace("{fine}", $"50.00"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyTotalFine", TextLabelGroupName)
                .Replace("{fine}", $"100.00")
                .Replace("{fineWithVat}", $"121.00"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FineDisclaimer", TextLabelGroupName)
                .Replace("&ast;", "*"));
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Success,
                    It.IsAny<CancellationToken>()), Times.Once());
        }

        [Theory]
        [InlineData("23391")]
        [InlineData("23392")]
        [InlineData("23393")]
        [InlineData("23394")]
        [InlineData("23395")]
        public async Task ProductFineCalculation_with_VerifiedCustomer_NewFinePolicyWithOneProduct_GetMVSError(string mvsErrorCode)
        {
            var sessionManager = GetSessionsManager(6);
            //Arrange
            SwapMockedServices(sessionManager, 6, mvsErrorCode: mvsErrorCode);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{enddate}", $"{endContractDate:dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyFine", TextLabelGroupName));
            reply = await testClient.SendActivityAsync(GetTextLabelValue("CalculateFine", TextLabelGroupName)).ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FineDependsOnCancelDate", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskCancelDate", TextLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("CancelDateFormat", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"{DateTime.Today.AddDays(10):dd-MM-yyyy}").ConfigureAwait(true);
            reply.Text.Should().Be(GetTextLabelValue($"FinePolicyMVSError{mvsErrorCode}", TextLabelGroupName));
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Unhappy,
                    It.IsAny<CancellationToken>()), Times.Once());
        }

        [Fact]
        public async Task ProductFineCalculation_with_VerifiedCustomer_NewFinePolicyWithOneProduct_WrongCancelDate()
        {
            var sessionManager = GetSessionsManager(4);
            //Arrange
            SwapMockedServices(sessionManager, 4, validCancelDate: false);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Gas")
                .Replace("{enddate}", $"{endContractDate:dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyFine", TextLabelGroupName));
            reply = await testClient.SendActivityAsync(GetTextLabelValue("CalculateFine", TextLabelGroupName)).ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FineDependsOnCancelDate", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskCancelDate", TextLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("CancelDateFormat", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"{DateTime.Today.AddDays(100):dd-MM-yyyy}").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("InvalidCancelDate", TextLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"));
            reply = await testClient.SendActivityAsync($"{DateTime.Today.AddDays(100):dd-MM-yyyy}").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("InvalidCancelDate", TextLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"));
            reply = await testClient.SendActivityAsync($"{DateTime.Today.AddDays(100):dd-MM-yyyy}").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("ThreeTimesWrongCancelDate", TextLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"));
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Unhappy,
                    It.IsAny<CancellationToken>()), Times.Once());
        }

        [Fact]
        public async Task ProductFineCalculation_with_VerifiedCustomer_NewFinePolicyWithTwoProducts_And_OTContract()
        {
            var sessionManager = GetSessionsManager(9);
            //Arrange
            SwapMockedServices(sessionManager, 9);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{enddate}", $"{endContractDate:dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyFine", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("OldFinePolicyOTContract", TextLabelGroupName)
                .Replace("{product}", "Gas"));
            reply = await testClient.SendActivityAsync(GetTextLabelValue("CalculateFine", TextLabelGroupName)).ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FineDependsOnCancelDate", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskCancelDate", TextLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("CancelDateFormat", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"{DateTime.Today.AddDays(10):dd-MM-yyyy}").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyTariffElectricity", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{tariff}", $"0.37")
                .Replace("{referenceProduct}", "Eneco HollandseWind & Zon 3 jaar")
                .Replace("{referenceTariff}", $"0.11")
                .Replace("{tariffDifference}", $"0.26"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyRemainingUsagesElectricity", TextLabelGroupName)
                .Replace("{enddate}", $"{cancelDate:dd-MM-yyyy}")
                .Replace("{usagesSJV}", $"{1096}")
                .Replace("{usagesSJI}", $"{1894}")
                .Replace("{totalUsages}", $"{3222}")
                .Replace("{remaingUsages}", $"{-798}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyFine", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{tariffDifference}", $"0.26")
                .Replace("{denotationType}", $"kWh")
                .Replace("{remaingUsages}", $"{-798}")
                .Replace("{fine}", $"50.00"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyTotalFine", TextLabelGroupName)
                .Replace("{fine}", $"50.00")
                .Replace("{fineWithVat}", $"60.50"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FineDisclaimer", TextLabelGroupName)
                .Replace("&ast;", "*"));
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Success,
                    It.IsAny<CancellationToken>()), Times.Once());
        }

        [Fact]
        public async Task ProductFineCalculation_with_VerifiedCustomer_NewFinePolicyWithTwoProducts_ChooseOneProduct()
        {
            var sessionManager = GetSessionsManager(5);
            //Arrange
            SwapMockedServices(sessionManager, 5);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{enddate}", $"{endContractDate:dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Gas")
                .Replace("{enddate}", $"{endContractDate:dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyFine", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskProductCalculateFine", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"Electricity").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FineDependsOnCancelDate", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskCancelDate", TextLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("CancelDateFormat", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"{DateTime.Today.AddDays(10):dd-MM-yyyy}").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyTariffElectricity", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{tariff}", $"0.37")
                .Replace("{referenceProduct}", "Eneco HollandseWind & Zon 3 jaar")
                .Replace("{referenceTariff}", $"0.11")
                .Replace("{tariffDifference}", $"0.26"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyRemainingUsagesElectricity", TextLabelGroupName)
                .Replace("{enddate}", $"{cancelDate:dd-MM-yyyy}")
                .Replace("{usagesSJV}", $"{1096}")
                .Replace("{usagesSJI}", $"{1894}")
                .Replace("{totalUsages}", $"{3222}")
                .Replace("{remaingUsages}", $"{-798}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyFine", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{tariffDifference}", $"0.26")
                .Replace("{denotationType}", $"kWh")
                .Replace("{remaingUsages}", $"{-798}")
                .Replace("{fine}", $"50.00"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyTotalFine", TextLabelGroupName)
                .Replace("{fine}", $"50.00")
                .Replace("{fineWithVat}", $"60.50"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FineDisclaimer", TextLabelGroupName)
                .Replace("&ast;", "*"));
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Success,
                    It.IsAny<CancellationToken>()), Times.Once());
        }

        [Fact]
        public async Task ProductFineCalculation_with_VerifiedCustomer_NewFinePolicyWithTwoProducts_ContractEndDateWithInRangeCancelDate()
        {
            var sessionManager = GetSessionsManager(10);
            //Arrange
            SwapMockedServices(sessionManager, 10);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{enddate}", $"{DateTime.Today.AddDays(16):dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Gas")
                .Replace("{enddate}", $"{DateTime.Today.AddDays(16):dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyFine", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskProductCalculateFine", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"beide").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FineDependsOnCancelDate", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskCancelDate", TextLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("CancelDateFormat", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"{DateTime.Today.AddDays(10):dd-MM-yyyy}").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("ContractEndDateWithInRangeCancelDate", TextLabelGroupName));

            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Unhappy,
                    It.IsAny<CancellationToken>()), Times.Once());
        }

        [Fact]
        public async Task ProductFineCalculation_with_VerifiedCustomer_NewFinePolicyWithTwoProducts_ContractEndDateAfterCancelDate()
        {
            var sessionManager = GetSessionsManager(10);
            //Arrange
            SwapMockedServices(sessionManager, 10);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{enddate}", $"{DateTime.Today.AddDays(16):dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Gas")
                .Replace("{enddate}", $"{DateTime.Today.AddDays(16):dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyFine", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskProductCalculateFine", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"beide").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FineDependsOnCancelDate", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskCancelDate", TextLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("CancelDateFormat", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"{DateTime.Today.AddDays(17):dd-MM-yyyy}").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("ContractEndDateAfterCancelDate", TextLabelGroupName));

            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Unhappy,
                    It.IsAny<CancellationToken>()), Times.Once());
        }

        [Fact]
        public async Task ProductFineCalculation_with_VerifiedCustomer_NewFinePolicyWithOneProduct_ElectricityWithoutRedelivery()
        {
            var sessionManager = GetSessionsManager(12);
            //Arrange
            SwapMockedServices(sessionManager, 12);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{enddate}", $"{endContractDate:dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyFine", TextLabelGroupName));
            reply = await testClient.SendActivityAsync(GetTextLabelValue("CalculateFine", TextLabelGroupName)).ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FineDependsOnCancelDate", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskCancelDate", TextLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("CancelDateFormat", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"{DateTime.Today.AddDays(10):dd-MM-yyyy}").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyTariffElectricity", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{tariff}", $"0.37")
                .Replace("{referenceProduct}", "Eneco HollandseWind & Zon 3 jaar")
                .Replace("{referenceTariff}", $"0.11")
                .Replace("{tariffDifference}", $"0.26"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyRemainingUsagesElectricityWithoutRedelivery", TextLabelGroupName)
                .Replace("{enddate}", $"{cancelDate:dd-MM-yyyy}")
                .Replace("{usages}", $"{2126}")
                .Replace("{totalUsages}", $"{3222}")
                .Replace("{remaingUsages}", $"{1096}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyFine", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{tariffDifference}", $"0.26")
                .Replace("{denotationType}", $"kWh")
                .Replace("{remaingUsages}", $"{1096}")
                .Replace("{fine}", $"50.00"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyTotalFine", TextLabelGroupName)
                .Replace("{fine}", $"50.00")
                .Replace("{fineWithVat}", $"60.50"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FineDisclaimer", TextLabelGroupName)
                .Replace("&ast;", "*"));
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Success,
                    It.IsAny<CancellationToken>()), Times.Once());
        }

        [Fact]
        public async Task ProductFineCalculation_with_VerifiedCustomer_NewFinePolicyWithTwoProducts_ChooseOneProduct_NoFine()
        {
            var sessionManager = GetSessionsManager(13);
            //Arrange
            SwapMockedServices(sessionManager, 13);
            DialogTestClient testClient = SetupMainDialog();

            //Act & Assert
            var reply = await testClient.SendActivityAsync(DialogCommands._PRODUCT_FINE_CALCULATION).ConfigureAwait(true);

            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Electricity")
                .Replace("{enddate}", $"{endContractDate:dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyProductEndDate", TextLabelGroupName)
                .Replace("{product}", "Gas")
                .Replace("{enddate}", $"{endContractDate:dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("NewFinePolicyFine", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskProductCalculateFine", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"Electricity").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FineDependsOnCancelDate", TextLabelGroupName));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("AskCancelDate", TextLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("CancelDateFormat", TextLabelGroupName));
            reply = await testClient.SendActivityAsync($"{DateTime.Today.AddDays(10):dd-MM-yyyy}").ConfigureAwait(true);
            reply.Text.Should().Contain(GetTextLabelValue("FinePolicyNoFine", TextLabelGroupName)
                .Replace("{product}", "Electricity"));
            reply = testClient.GetNextReply();
            reply.Text.Should().Contain(GetTextLabelValue("FineDisclaimer", TextLabelGroupName)
                .Replace("&ast;", "*"));
            sessionManager.Verify(mock =>
                mock.SendEndOfTransactionActivity(
                    It.IsAny<ITurnContext>(),
                    It.IsAny<string>(), It.IsAny<string>(),
                    TransactionStatus.Success,
                    It.IsAny<CancellationToken>()), Times.Once());
        }


        private void SwapMockedServices(Mock<ISessionManager> sessionManagerMock, long customerId, string mvsErrorCode = null, bool validCancelDate = true)
        {
            Thread.CurrentThread.CurrentCulture = new CultureInfo("nl-NL");
            var customersRepositoryMock = new Mock<IDcCustomersRepository>();
            var productsRepositoryMock = new Mock<IDcProductsRepository>();
            var userAccountRepMock = new Mock<IDcUserAccountsRepository>();
            var fineServiceMock = new Mock<IProductFineCalculationService>();
            var storageServiceMock = new Mock<IStorageService>();

            TextLabelGroupName = "Bot_ProductFineCalculationDialog";
            GetAllTextLabelsFromExport();
            storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
                .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));

            customersRepositoryMock.SetupCustomerTestData(customerId);
            storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
                .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));
            sessionManagerMock.Setup(x => x.GetDialogData(It.IsAny<ITurnContext>()))
                .ReturnsAsync(new DialogData
                {
                    Customer = new CustomerData
                    {
                        Label = Label.Eneco
                    },
                    Verification = new VerificationData
                    {
                        CustomerId = customerId,
                        PostalCode = "3025CB",
                        HouseNumber = 9,
                        IsFound = true,
                        IsVerified = true,
                        MfaSuccessful = true,
                        VerificationCommunicated = false
                    },
                    SelectedAccount = new AccountData
                    {
                        AccountId = 1
                    },
                    ActiveAccounts = new List<AccountData>
                    {
                    new()
                    {
                        AccountId = 1
                    }
                    }
                });

            customersRepositoryMock.Setup(x => x.GetAgreements(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListAgreement
                {
                    Data = new List<Agreement>
                    {
                        new Agreement
                        {
                            IsActive  = true,
                            Products = new List<Domain.Models.Agreements.Product>
                            {
                                new Domain.Models.Agreements.Product
                                {
                                    ProductType = ProductType.Electricity,
                                    IsActive = true,
                                    ProductOffering = new ProductOffering
                                    {
                                        IsIndefiniteDuration = customerId != 1
                                    }
                                }
                            },
                            ProductUsages = new List<ProductUsage>
                            {
                                new ProductUsage
                                {
                                    Quantity = 1465,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.ElectricityLow,
                                    DenotationType = DenotationType.Kwh
                                },
                                new ProductUsage
                                {
                                    Quantity = 1757,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.ElectricityHigh,
                                    DenotationType = DenotationType.Kwh
                                }
                            }
                        },
                        new Agreement
                        {
                            IsActive  = true,
                            Products = new List<Domain.Models.Agreements.Product>
                            {
                                new Domain.Models.Agreements.Product
                                {
                                    ProductType = ProductType.Gas,
                                    IsActive = true,
                                    ProductOffering = new ProductOffering
                                    {
                                        IsIndefiniteDuration = customerId != 1
                                    }
                                }
                            },
                            ProductUsages = new List<ProductUsage>
                            {
                                new ProductUsage
                                {
                                    Quantity = 402,
                                    QuantityType = QuantityType.Estimated,
                                    CounterType = CounterType.Gas,
                                    DenotationType = DenotationType.M3
                                }
                            }
                        }
                    }
                }
            });


            //no E/G
            productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), 1, It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Warmth,
                            },
                            Description = "Warmth",
                        }
                    }
                }
            });

            //OT contracts
            productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), 2, It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Electricity",
                            IsActive = true
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Gas",
                            IsActive = true
                        }
                    }
                }
            });

            //Old fine policy contract

            productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), 3, It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Electricity",
                            StartDate = new DateTime(2021, 1,1),
                            EndDateContract = DateTime.Today.AddMonths(3),
                            Id = 1,
                            IsActive = true
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Gas",
                            StartDate = new DateTime(2021, 1,1),
                            EndDateContract = DateTime.Today.AddMonths(3),
                            Id = 2,
                            IsActive = true
                        }
                    }
                }
            });

            //New fine policy contract 1 product
            productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), 4, It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Gas",
                            StartDate = new DateTime(2021, 6,1),
                            EndDateContract = DateTime.Today.AddMonths(3),
                            Id = 4,
                            IsActive = true
                        }
                    }
                }
            });

            //New fine policy contract 2 products
            productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), 5, It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Electricity",
                            StartDate = new DateTime(2021, 6,1),
                            EndDateContract = DateTime.Today.AddMonths(3),
                            Id = 3,
                            IsActive = true
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Gas",
                            StartDate = new DateTime(2021, 6,1),
                            EndDateContract = DateTime.Today.AddMonths(3),
                            Id = 4,
                            IsActive = true
                        }
                    }
                }
            });

            //New fine policy contract only product gets mvs error back from fine calculation
            productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), 6, It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Electricity",
                            StartDate = new DateTime(2021, 6,1),
                            EndDateContract = DateTime.Today.AddMonths(3),
                            Id = 5,
                            IsActive = true
                        }
                    }
                }
            });

            //New fine policy contract 1 product where the fine is 0.0
            productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), 7, It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Gas",
                            StartDate = new DateTime(2021, 6,1),
                            EndDateContract = DateTime.Today.AddMonths(3),
                            Id = 6,
                            IsActive = true
                        }
                    }
                }
            });

            //old finy policy with one OT contract
            productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), 8, It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Electricity",
                            StartDate = new DateTime(2021, 1,1),
                            EndDateContract = DateTime.Today.AddMonths(3),
                            Id = 3,
                            IsActive = true
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Gas",
                            StartDate = new DateTime(2021, 1,1),
                            Id = 4,
                            IsActive = true
                        }
                    }
                }
            });

            //New fine policy contract 2 products with one OT contract
            productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), 9, It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Electricity",
                            StartDate = new DateTime(2021, 6,1),
                            EndDateContract = DateTime.Today.AddMonths(3),
                            Id = 3,
                            IsActive = true
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Gas",
                            StartDate = new DateTime(2021, 6,1),
                            EndDateContract = null,
                            Id = 4,
                            IsActive = true
                        }
                    }
                }
            });

            //New fine policy contract 2 products with contract endate with in 7 days (10 days of today of selected cancel date + 6 day extra) or after cancel date (17 days of today of selected cancel date)
            productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), 10, It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Electricity",
                            StartDate = new DateTime(2021, 6,1),
                            EndDateContract = DateTime.Today.AddDays(16),
                            Id = 3,
                            IsActive = true
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Gas",
                            StartDate = new DateTime(2021, 6,1),
                            EndDateContract = DateTime.Today.AddDays(16),
                            Id = 4,
                            IsActive = true
                        }
                    }
                }
            });

            //products within 14 days bedenktijd
            productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), 11, It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Electricity",
                            StartDate = DateTime.Today.AddDays(12),
                            EndDateContract = DateTime.Today.AddDays(16),
                            Id = 3,
                            IsActive = true
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Gas",
                            StartDate = DateTime.Today.AddDays(12),
                            EndDateContract = DateTime.Today.AddDays(16),
                            Id = 4,
                            IsActive = true
                        }
                    }
                }
            });

            //product new fine policy without SJI/redelivery
            productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), 12, It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Electricity",
                            StartDate = new DateTime(2021, 6,1),
                            EndDateContract = DateTime.Today.AddMonths(3),
                            Id = 8,
                            IsActive = true
                        }
                    }
                }
            });

            //New fine policy contract 2 products
            productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), 13, It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Electricity",
                            StartDate = new DateTime(2021, 6,1),
                            EndDateContract = DateTime.Today.AddMonths(3),
                            Id = 9,
                            IsActive = true
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Gas",
                            StartDate = new DateTime(2021, 6,1),
                            EndDateContract = DateTime.Today.AddMonths(3),
                            Id = 10,
                            IsActive = true
                        }
                    }
                }
            });

            productsRepositoryMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<Label>(), It.Is<RequestDataProductCancelRequestModel>(x => x.Data.SubscriptionId == 1)))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataProductSwitchResponseModel
                    {
                        Data = new ProductSwitchResponseModel
                        {
                            FineAmount = 50,
                            NewFinePolicy = false
                        }
                    }
                });

            fineServiceMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<DialogData>(), It.Is<ProductCancelRequestModel>(x => x.SubscriptionId == 1)))
                .ReturnsAsync(new ProductFineCalculationModel
                {
                    FineAmount = 50,
                    NewFinePolicy = false,
                    ProductId = 1
                });

            productsRepositoryMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<Label>(), It.Is<RequestDataProductCancelRequestModel>(x => x.Data.SubscriptionId == 2)))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataProductSwitchResponseModel
                    {
                        Data = new ProductSwitchResponseModel
                        {
                            FineAmount = 50,
                            NewFinePolicy = false
                        }
                    }
                });
            fineServiceMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<DialogData>(), It.Is<ProductCancelRequestModel>(x => x.SubscriptionId == 2)))
                .ReturnsAsync(new ProductFineCalculationModel
                {
                    FineAmount = 50,
                    NewFinePolicy = false,
                    ProductId = 2
                });

            productsRepositoryMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<Label>(), It.Is<RequestDataProductCancelRequestModel>(x => x.Data.SubscriptionId == 3)))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataProductSwitchResponseModel
                    {
                        Data = new ProductSwitchResponseModel
                        {
                            FineAmount = 50,
                            FineAmountVat = 0.21,
                            FineAmountInclVat = 60.50,
                            NewFinePolicy = true,
                            FineAmountDeterminationDate = cancelDate,
                            ReferenceProductOMS = "Eneco HollandseWind & Zon 3 jaar",
                            DeliveryTariff = 0.37,
                            ReferenceTariff = 0.11,
                            AverageWeightedDeliveryTariff = 0.37,
                            AverageWeightedReferenceTariff = 0.11,
                            RemainingAmount = -798,
                            RemainingDays = 526,
                            RemainingSJV = 1096,
                            RemainingSJI = 1894
                        }
                    }
                });

            fineServiceMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<DialogData>(), It.Is<ProductCancelRequestModel>(x => x.SubscriptionId == 3)))
                .ReturnsAsync(new ProductFineCalculationModel
                {
                    FineAmount = 50,
                    FineAmountVat = 0.21,
                    FineAmountInclVat = 60.50,
                    NewFinePolicy = true,
                    ProductId = 3,
                    FineAmountDeterminationDate = cancelDate,
                    ReferenceProductOMS = "Eneco HollandseWind & Zon 3 jaar",
                    DeliveryTariff = 0.37,
                    ReferenceTariff = 0.11,
                    AverageWeightedDeliveryTariff = 0.37,
                    AverageWeightedReferenceTariff = 0.11,
                    RemainingAmount = -798,
                    RemainingDays = 526,
                    RemainingSJV = 1096,
                    RemainingSJI = 1894
                });

            productsRepositoryMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<Label>(), It.Is<RequestDataProductCancelRequestModel>(x => x.Data.SubscriptionId == 4)))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataProductSwitchResponseModel
                    {
                        Data = new ProductSwitchResponseModel
                        {
                            FineAmount = 50,
                            FineAmountVat = 0.21,
                            FineAmountInclVat = 60.50,
                            NewFinePolicy = true,
                            FineAmountDeterminationDate = cancelDate,
                            ReferenceProductOMS = "Eneco Gas 3 jaar",
                            DeliveryTariff = 1.61,
                            ReferenceTariff = 0.47,
                            AverageWeightedDeliveryTariff = 1.61,
                            AverageWeightedReferenceTariff = 0.47,
                            RemainingAmount = 283,
                            RemainingDays = 526,
                            RemainingSJV = 283
                        }
                    }
                });

            fineServiceMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<DialogData>(), It.Is<ProductCancelRequestModel>(x => x.SubscriptionId == 4)))
                .ReturnsAsync(new ProductFineCalculationModel
                {
                    FineAmount = 50,
                    FineAmountVat = 0.21,
                    FineAmountInclVat = 60.50,
                    NewFinePolicy = true,
                    FineAmountDeterminationDate = cancelDate,
                    ReferenceProductOMS = "Eneco Gas 3 jaar",
                    DeliveryTariff = 1.61,
                    ReferenceTariff = 0.47,
                    AverageWeightedDeliveryTariff = 1.61,
                    AverageWeightedReferenceTariff = 0.47,
                    RemainingAmount = 283,
                    RemainingDays = 526,
                    RemainingSJV = 283,
                    ProductId = 4
                });

            productsRepositoryMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<Label>(), It.Is<RequestDataProductCancelRequestModel>(x => x.Data.SubscriptionId == 5)))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataProductSwitchResponseModel
                    {
                        Data = new ProductSwitchResponseModel
                        {
                            MvsErrors = new List<MvsError>
                            {
                                new MvsError
                                {
                                    Code = $"MVS-{mvsErrorCode}",
                                    Details = "MVS fout"
                                }
                            },
                            NewFinePolicy = true
                        }
                    }
                });

            fineServiceMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<DialogData>(), It.Is<ProductCancelRequestModel>(x => x.SubscriptionId == 5)))
                .ReturnsAsync(new ProductFineCalculationModel
                {
                    MVSErrors = new List<MvsError>
                    {
                        new MvsError
                        {
                            Code = $"MVS-{mvsErrorCode}",
                            Details = "MVS fout"
                        }
                    },
                    NewFinePolicy = true,
                    ProductId = 5
                });

            productsRepositoryMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<Label>(), It.Is<RequestDataProductCancelRequestModel>(x => x.Data.SubscriptionId == 6)))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataProductSwitchResponseModel
                    {
                        Data = new ProductSwitchResponseModel
                        {
                            FineAmount = 0,
                            FineAmountVat = 0.21,
                            FineAmountInclVat = 0,
                            NewFinePolicy = true,
                            FineAmountDeterminationDate = cancelDate,
                            ReferenceProductOMS = "Eneco Gas 3 jaar",
                            DeliveryTariff = 1.61,
                            ReferenceTariff = 0.47,
                            AverageWeightedDeliveryTariff = 1.61,
                            AverageWeightedReferenceTariff = 0.47,
                            RemainingAmount = 283,
                            RemainingDays = 526,
                            RemainingSJV = 283
                        }
                    }
                });

            fineServiceMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<DialogData>(), It.Is<ProductCancelRequestModel>(x => x.SubscriptionId == 6)))
                .ReturnsAsync(new ProductFineCalculationModel
                {
                    FineAmount = 0,
                    FineAmountVat = 0.21,
                    FineAmountInclVat = 0,
                    FineAmountDeterminationDate = cancelDate,
                    ReferenceProductOMS = "Eneco Gas 3 jaar",
                    DeliveryTariff = 1.61,
                    ReferenceTariff = 0.47,
                    AverageWeightedDeliveryTariff = 1.61,
                    AverageWeightedReferenceTariff = 0.47,
                    RemainingAmount = 283,
                    RemainingDays = 526,
                    RemainingSJV = 283,
                    NewFinePolicy = true,
                    ProductId = 6
                });

            productsRepositoryMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<Label>(), It.Is<RequestDataProductCancelRequestModel>(x => x.Data.SubscriptionId == 8)))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataProductSwitchResponseModel
                    {
                        Data = new ProductSwitchResponseModel
                        {
                            FineAmount = 50,
                            FineAmountVat = 0.21,
                            FineAmountInclVat = 60.50,
                            NewFinePolicy = true,
                            FineAmountDeterminationDate = cancelDate,
                            ReferenceProductOMS = "Eneco HollandseWind & Zon 3 jaar",
                            DeliveryTariff = 0.37,
                            ReferenceTariff = 0.11,
                            AverageWeightedDeliveryTariff = 0.37,
                            AverageWeightedReferenceTariff = 0.11,
                            RemainingAmount = 1096,
                            RemainingDays = 526,
                            RemainingSJV = 1096,
                        }
                    }
                });

            fineServiceMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<DialogData>(), It.Is<ProductCancelRequestModel>(x => x.SubscriptionId == 8)))
                .ReturnsAsync(new ProductFineCalculationModel
                {
                    FineAmount = 50,
                    FineAmountVat = 0.21,
                    FineAmountInclVat = 60.50,
                    NewFinePolicy = true,
                    ProductId = 8,
                    FineAmountDeterminationDate = cancelDate,
                    ReferenceProductOMS = "Eneco HollandseWind & Zon 3 jaar",
                    DeliveryTariff = 0.37,
                    ReferenceTariff = 0.11,
                    AverageWeightedDeliveryTariff = 0.37,
                    AverageWeightedReferenceTariff = 0.11,
                    RemainingAmount = 1096,
                    RemainingDays = 526,
                    RemainingSJV = 1096,
                });


            productsRepositoryMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<Label>(), It.Is<RequestDataProductCancelRequestModel>(x => x.Data.SubscriptionId == 9)))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataProductSwitchResponseModel
                    {
                        Data = new ProductSwitchResponseModel
                        {
                            FineAmount = 0,
                            FineAmountVat = 0,
                            FineAmountInclVat = 0,
                            NewFinePolicy = true,
                            FineAmountDeterminationDate = cancelDate,
                            ReferenceProductOMS = "Eneco HollandseWind & Zon 3 jaar",
                            DeliveryTariff = 0.11,
                            ReferenceTariff = 0.37,
                            AverageWeightedDeliveryTariff = 0.11,
                            AverageWeightedReferenceTariff = 0.37,
                            RemainingAmount = 1096,
                            RemainingDays = 526,
                            RemainingSJV = 1096,
                        }
                    }
                });

            fineServiceMock.Setup(x => x.CalculateFineCancelledProduct(It.IsAny<DialogData>(), It.Is<ProductCancelRequestModel>(x => x.SubscriptionId == 9)))
                .ReturnsAsync(new ProductFineCalculationModel
                {
                    FineAmount = 0,
                    FineAmountVat = 0,
                    FineAmountInclVat = 0,
                    NewFinePolicy = true,
                    ProductId = 9,
                    FineAmountDeterminationDate = cancelDate,
                    ReferenceProductOMS = "Eneco HollandseWind & Zon 3 jaar",
                    DeliveryTariff = 0.11,
                    ReferenceTariff = 0.37,
                    AverageWeightedDeliveryTariff = 0.11,
                    AverageWeightedReferenceTariff = 0.37,
                    RemainingAmount = 1096,
                    RemainingDays = 526,
                    RemainingSJV = 1096,
                });


            var loggingService = new Mock<ILoggingService>();
            var validator = new Mock<IDialogValidators>();
            validator.Setup(x => x.CustomerDialogValidator).Returns(new CustomerDialogValidator(sessionManagerMock.Object));
            validator.Setup(x => x.FinancialsDialogValidator).Returns(new FinancialsDialogValidator(sessionManagerMock.Object));
            validator.Setup(x => x.ProductsDialogValidator).Returns(new ProductsDialogValidator(sessionManagerMock.Object));
            if (validCancelDate)
            {
                validator
                .Setup(x => x.ProductsDialogValidator.FineCancelDateValidator(It.IsAny<PromptValidatorContext<string>>(), It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult(validCancelDate));
            }

            _services.SwapTransient(provider => validator.Object);

            _services.SwapTransient(provider => validator.Object);
            _services.SwapTransient(provider => loggingService.Object);
            _services.SwapTransient(provider => customersRepositoryMock.Object);
            _services.SwapTransient(provider => productsRepositoryMock.Object);
            _services.SwapTransient(provider => userAccountRepMock.Object);
            _services.SwapTransient(provider => sessionManagerMock.Object);
            _services.SwapTransient(provider => storageServiceMock.Object);
            _services.SwapTransient(provider => fineServiceMock.Object);
        }

        private static Mock<ISessionManager> GetSessionsManager(long customerId, Label label = Label.Eneco)
        {
            var sessionManagerMock = new Mock<ISessionManager>();
            sessionManagerMock.Setup(x =>
                    x.GetDialogData(It.IsAny<ITurnContext>()))
                .ReturnsAsync(new DialogData
                {
                    Customer = new CustomerData
                    {
                        Label = label
                    },
                    Verification = new VerificationData
                    {
                        CustomerId = customerId,
                        CustomerIdVerified = true
                    }
                });

            return sessionManagerMock;
        }
    }
}
