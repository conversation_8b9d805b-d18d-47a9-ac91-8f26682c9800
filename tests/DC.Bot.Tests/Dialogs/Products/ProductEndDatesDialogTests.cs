﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Domain.Models.Products;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using FluentAssertions;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Products;

[Collection("Sequential")]
public class ProductEndDatesDialogTests : TestBase
{
    [Fact]
    public async Task GetEndDate_with_VerifiedCustomer_WithoutContract()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATES);
        testClient.GetNextReply().Text.Should().Contain(GetTextLabelValue("NoProducts", TextLabelGroupName));
    }

    [Fact]
    public async Task GetEndDate_with_VerifiedCustomer_WithContract()
    {
        //Arrange
        SwapMockedServices(2);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATES);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Should().Contain("Product A");
        endDateText.Should().Contain(GetTextLabelValue("ProductIndefinite", TextLabelGroupName));
    }

    [Fact]
    public async Task GetEndDate_with_VerifiedCustomer_WithInActiveAccount()
    {
        //Arrange
        SwapMockedServices(2, false);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATES);
        testClient.GetNextReply().Text.Should().Contain(GetTextLabelValue("CouldNotFindActiveAccount", "Bot_GeneralTextLabels"));
    }

    [Fact]
    public async Task GetEndDate_with_VerifiedCustomer_WithMulitpleAccount_WithInActiveAccount()
    {
        //Arrange
        SwapMockedServices(3, false, true);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._PRODUCTENDDATES);

        Assert.StartsWith("Je hebt op dit moment geen contract bij ons. Ik kan je daarom helaas geen einddatum geven.", testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetNextYearNoteDate_with_UnverifiedCustomer()
    {
        //Arrange
        SwapMockedServices(1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithUnverifiedCustomer(DialogCommands._PRODUCTENDDATES);
        Assert.Contains("Ik kan helaas je gegevens niet ophalen.", testClient.GetNextReply().Text);
    }

    private void SwapMockedServices(long customerId, bool activeAccount = true, bool multiAccount = false)
    {
        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        var productsRepositoryMock = new Mock<IDcProductsRepository>();
        var userAccountRepMock = new Mock<IDcUserAccountsRepository>();
        var storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_ProductEndDatesDialog";
        GetAllTextLabelsFromExport();

        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));

        customersRepositoryMock.SetupCustomerTestData(customerId, activeAccount: activeAccount, multiAccount: multiAccount);

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 2), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity
                            },
                            Description = "Product A",
                            EndDate = DateTime.Now
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas
                            },
                            Description = "Product B",
                            EndDate = DateTime.Now,
                            PriceDeterminationDate = DateTime.Now.AddMonths(-6),
                            Indefinite = true,
                            Duration = 12
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.StukjeZon
                            },
                            Description = "StukjeZon"
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 1), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = null
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 3), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = null
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 4), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = null
                }
            });

        var validator = new Mock<IDialogValidators>();
        validator.SetupDialogValidatorWithSessionManager(customerId);
        _services.SwapTransient(provider => validator.Object);

        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => productsRepositoryMock.Object);
        _services.SwapTransient(provider => userAccountRepMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }
}
