﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Customers.Client.Models;
using DC.Domain.Exceptions.ResponseModels;
using DC.Domain.Models.Customers;
using DC.Domain.Models.Products;
using DC.Domain.Models.Products.ServiceAgreements;
using DC.Domain.Models.Products.ServiceOrders;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.SAPI.Interfaces;
using DC.Telemetry.Models;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using AddressModel = DC.Customers.Client.Models.AddressModel;
using ErrorResponse = DC.Domain.Exceptions.ResponseModels.ErrorResponse;
using ProductModel = DC.Domain.Models.Products.ProductModel;
using ProductTypeModel = DC.Domain.Models.Products.ProductTypeModel;
using ResponseDataString = DC.Products.Client.Models.ResponseDataString;
using TransactionStatus = DC.Bot.BusinessLogic.Enumerations.TransactionStatus;

namespace DC.Bot.Tests.Dialogs.Products;

[Collection("Sequential")]
public class CreateServiceOrderDialogTests : TestBase
{
    private const string startCommand = DialogCommands._CREATE_SERVICE_ORDER + "|MalfunctionDescription test service order description";

    [Fact]
    public async Task HappyFlow_WithMultiContracts()
    {
        var sessionManager = GetSessionsManager(1, true);
        SwapMockedServices(sessionManager, 1, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(startCommand + "|MalfunctionDeviceCv-ketel").ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForWhichServiceContractPromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("2-Particuliere Huur abonnement");
        answer.Text.Should().StartWith(GetTextLabelValue("MailfunctionOnCV", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("QuestionKnownMalfunction", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForMalfunctionCodePromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("E064");
        answer.Text.Should().StartWith(GetTextLabelValue("MalfunctionCodeAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrder", TextLabelGroupName));
        var dialogData = await sessionManager.Object.GetDialogData(It.IsAny<ITurnContext>()).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrderPromptText", TextLabelGroupName).Replace("{hardware}", dialogData.ServiceOrder.Hardware));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("CreateServiceOrderAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ServiceOrderCreatedAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("PlanLinkAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("PlanLink", TextLabelGroupName).Replace("{planLink}", "https://www.eneco.nl/"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.SuccessLink,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task HappyFlow_WithCVKetel()
    {
        var sessionManager = GetSessionsManager(3, true);
        SwapMockedServices(sessionManager, 3, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(startCommand + "|MalfunctionDevicecv-ketel").ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("MailfunctionOnCV", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("QuestionKnownMalfunction", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("nee");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrder", TextLabelGroupName));
        var dialogData = await sessionManager.Object.GetDialogData(It.IsAny<ITurnContext>()).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrderPromptText", TextLabelGroupName).Replace("{hardware}", dialogData.ServiceOrder.Hardware));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("CreateServiceOrderAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ServiceOrderCreatedAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("PlanLinkAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("PlanLink", TextLabelGroupName).Replace("{planLink}", "https://www.eneco.nl/"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.SuccessLink,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task HappyFlow_WithCVKetel_WithMalfunctionCode()
    {
        var sessionManager = GetSessionsManager(3, true);
        SwapMockedServices(sessionManager, 3, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(startCommand + "|MalfunctionDevicecv-ketel").ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("MailfunctionOnCV", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("QuestionKnownMalfunction", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForMalfunctionCodePromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("E064");
        answer.Text.Should().StartWith(GetTextLabelValue("MalfunctionCodeAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrder", TextLabelGroupName));
        var dialogData = await sessionManager.Object.GetDialogData(It.IsAny<ITurnContext>()).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrderPromptText", TextLabelGroupName).Replace("{hardware}", dialogData.ServiceOrder.Hardware));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("CreateServiceOrderAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ServiceOrderCreatedAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("PlanLinkAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("PlanLink", TextLabelGroupName).Replace("{planLink}", "https://www.eneco.nl/"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.SuccessLink,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task HappyFlow_WithCVKetel_WithMalfunctionCode_Urgent()
    {
        var sessionManager = GetSessionsManager(3, true);
        SwapMockedServices(sessionManager, 3, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(startCommand + "|MalfunctionDevicecv-ketel|MalfunctionUrgencyUrgent").ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("MailfunctionOnCV", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("QuestionKnownMalfunction", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForMalfunctionCodePromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("E064");
        answer.Text.Should().StartWith(GetTextLabelValue("MalfunctionCodeAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrder", TextLabelGroupName));
        var dialogData = await sessionManager.Object.GetDialogData(It.IsAny<ITurnContext>()).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrderPromptText", TextLabelGroupName).Replace("{hardware}", dialogData.ServiceOrder.Hardware));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("CreateServiceOrderAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ServiceOrderCreatedAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("PlanLinkAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("PlanLink", TextLabelGroupName).Replace("{planLink}", "https://www.eneco.nl/"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.SuccessLink,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task UnHappyFlow_WithCVKetel_WithMalfunctionCode()
    {
        var sessionManager = GetSessionsManager(3, true);
        SwapMockedServices(sessionManager, 3, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(startCommand + "|MalfunctionDeviceCvketel").ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("MailfunctionOnCV", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("QuestionKnownMalfunction", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForMalfunctionCodePromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("E064");
        answer.Text.Should().StartWith(GetTextLabelValue("MalfunctionCodeAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrder", TextLabelGroupName));
        var dialogData = await sessionManager.Object.GetDialogData(It.IsAny<ITurnContext>()).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrderPromptText", TextLabelGroupName).Replace("{hardware}", dialogData.ServiceOrder.Hardware));

        answer = await testClient.SendActivityAsync("nee");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.MBFChat,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task HappyFlow_WithBoiler()
    {
        var sessionManager = GetSessionsManager(4, true);
        SwapMockedServices(sessionManager, 4, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(startCommand + "|MalfunctionDeviceBoiler").ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("MailfunctionOnBoiler", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrder", TextLabelGroupName));
        var dialogData = await sessionManager.Object.GetDialogData(It.IsAny<ITurnContext>()).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrderPromptText", TextLabelGroupName).Replace("{hardware}", dialogData.ServiceOrder.Hardware));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("CreateServiceOrderAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ServiceOrderCreatedAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("PlanLinkAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("PlanLink", TextLabelGroupName).Replace("{planLink}", "https://www.eneco.nl/"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.SuccessLink,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task HappyFlow_WithGeiser()
    {
        var sessionManager = GetSessionsManager(5, true);
        SwapMockedServices(sessionManager, 5, 1);

        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(startCommand + "|MalfunctionDeviceGeiser").ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("MailfunctionOnGeyser", TextLabelGroupName));

        var answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrder", TextLabelGroupName));
        var dialogData = await sessionManager.Object.GetDialogData(It.IsAny<ITurnContext>()).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrderPromptText", TextLabelGroupName).Replace("{hardware}", dialogData.ServiceOrder.Hardware));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("CreateServiceOrderAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ServiceOrderCreatedAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("PlanLinkAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("PlanLink", TextLabelGroupName).Replace("{planLink}", "https://www.eneco.nl/"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.SuccessLink,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task UnHappyFlow_WithBoiler()
    {
        var sessionManager = GetSessionsManager(4, true);
        SwapMockedServices(sessionManager, 4, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(startCommand + "|MalfunctionDeviceBoiler").ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("MailfunctionOnBoiler", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrder", TextLabelGroupName));
        var dialogData = await sessionManager.Object.GetDialogData(It.IsAny<ITurnContext>()).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrderPromptText", TextLabelGroupName).Replace("{hardware}", dialogData.ServiceOrder.Hardware));

        answer = await testClient.SendActivityAsync("nee");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.MBFChat,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task UnHappyFlow_WithGeiser()
    {
        var sessionManager = GetSessionsManager(5, true);
        SwapMockedServices(sessionManager, 5, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(startCommand + "|MalfunctionDeviceGeiser").ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("MailfunctionOnGeyser", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrder", TextLabelGroupName));
        var dialogData = await sessionManager.Object.GetDialogData(It.IsAny<ITurnContext>()).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrderPromptText", TextLabelGroupName).Replace("{hardware}", dialogData.ServiceOrder.Hardware));

        answer = await testClient.SendActivityAsync("nee");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.MBFChat,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task UnHappyFlow_SelfServiceIsNotAllowed()
    {
        var sessionManager = GetSessionsManager(6, true);
        SwapMockedServices(sessionManager, 6, 1);

        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(startCommand).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("SelfServiceNotAllowed", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ContactForAppointment", TextLabelGroupName));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Unhappy,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task UnHappyFlow_NoContracts()
    {
        var sessionManager = GetSessionsManager(2, true);
        SwapMockedServices(sessionManager, 2, 1);

        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(startCommand).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("SelfServiceNotAllowed", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ContactForAppointment", TextLabelGroupName));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Unhappy,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task UnHappyFlow_MuliServiceObjects()
    {
        var sessionManager = GetSessionsManager(7, true);
        SwapMockedServices(sessionManager, 7, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(startCommand + "|MalfunctionDeviceCvketel").ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForWhichServiceContractPromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("2-Particuliere Huur abonnement");
        answer.Text.Should().StartWith(GetTextLabelValue("MailfunctionOnCV", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("QuestionKnownMalfunction", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForMalfunctionCodePromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("E064");
        answer.Text.Should().StartWith(GetTextLabelValue("MalfunctionCodeAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrder", TextLabelGroupName));
        var dialogData = await sessionManager.Object.GetDialogData(It.IsAny<ITurnContext>()).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrderPromptText", TextLabelGroupName).Replace("{hardware}", dialogData.ServiceOrder.Hardware)); answer = await testClient.SendActivityAsync("nee");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.MBFChat,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task ErrorFlow_ErrorOnCreateServiceOrder()
    {
        var sessionManager = GetSessionsManager(8, true);
        SwapMockedServices(sessionManager, 8, 1);

        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(startCommand + "|MalfunctionDevicecv-ketel").ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("MailfunctionOnCV", TextLabelGroupName));

        var answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("QuestionKnownMalfunction", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForMalfunctionCodePromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("E064");
        answer.Text.Should().StartWith(GetTextLabelValue("MalfunctionCodeAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrder", TextLabelGroupName));
        var dialogData = await sessionManager.Object.GetDialogData(It.IsAny<ITurnContext>()).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrderPromptText", TextLabelGroupName).Replace("{hardware}", dialogData.ServiceOrder.Hardware));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("CreateServiceOrderAnswer", TextLabelGroupName));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.UnhappyPhoneNumber,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task ErrorFlow_WithConfiguredPhoneNumber_ErrorOnCreateServiceOrder()
    {
        var sessionManager = GetSessionsManager(11, true);
        SwapMockedServices(sessionManager, 11, 1);

        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(startCommand + "|MalfunctionDevicecv-ketel").ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("MailfunctionOnCV", TextLabelGroupName));

        var answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("QuestionKnownMalfunction", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForMalfunctionCodePromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("E064");
        answer.Text.Should().StartWith(GetTextLabelValue("MalfunctionCodeAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrder", TextLabelGroupName));
        var dialogData = await sessionManager.Object.GetDialogData(It.IsAny<ITurnContext>()).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrderPromptText", TextLabelGroupName).Replace("{hardware}", dialogData.ServiceOrder.Hardware));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("CreateServiceOrderAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("SomethingWentWrong", "Bot_GeneralTextLabels"));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("TryAgianSomethingWentWrong", "Bot_GeneralTextLabels"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.PermanentFailure,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task UnHappyFlow_OpenServiceOrder()
    {
        var sessionManager = GetSessionsManager(9, true);
        SwapMockedServices(sessionManager, 9, 1);

        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(startCommand).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("SelfServiceNotAllowed", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ContactForAppointment", TextLabelGroupName));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Unhappy,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Theory]
    [InlineData(ServiceOrderKind.FirstMaintenance, 0)]
    [InlineData(ServiceOrderKind.Repairs, 10)]
    [InlineData(ServiceOrderKind.Maintenance, 10)]
    public async Task HappyFlow_OpenServiceOrder(ServiceOrderKind serviceOrderKind, int daysInThePast)
    {
        var sessionManager = GetSessionsManager(9, true);
        SwapMockedServices(sessionManager, 9, 1, true, DateTime.Today.AddDays(-daysInThePast), serviceOrderKind);

        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(startCommand).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("MailfunctionOnBoiler", TextLabelGroupName));
    }


    [Fact]
    public async Task HappyFlow_ActiveAccount_IsNotSet_WithCVKetel()
    {
        var sessionManager = GetSessionsManager(3, true);
        SwapMockedServices(sessionManager, 3, 1, false);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(startCommand + "|MalfunctionDevicecv-ketel").ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("MailfunctionOnCV", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("QuestionKnownMalfunction", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("nee");
        answer.Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrder", TextLabelGroupName));
        var dialogData = await sessionManager.Object.GetDialogData(It.IsAny<ITurnContext>()).ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("AskForCreateServiceOrderPromptText", TextLabelGroupName).Replace("{hardware}", dialogData.ServiceOrder.Hardware));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("CreateServiceOrderAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ServiceOrderCreatedAnswer", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("PlanLinkAvailable", TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("PlanLink", TextLabelGroupName).Replace("{planLink}", "https://www.eneco.nl/"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.SuccessLink,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task TemporaryFailureFlow_NoActiveAccount()
    {
        var sessionManager = GetSessionsManager(3, true);
        SwapMockedServices(sessionManager, 3, 0);

        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(startCommand + "|MalfunctionDevicecv-ketel").ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("CouldNotFindData", "Bot_GeneralTextLabels"));
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("ContactUs", "Bot_GeneralTextLabels"));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.TemporaryFailure,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    private void SwapMockedServices(Mock<ISessionManager> sessionManagerMock, long customerId, int accountId = 1, bool activeAccount = true, DateTime? lastServiceOrder = null, ServiceOrderKind serviceOrderKind = ServiceOrderKind.Repairs)
    {
        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        customersRepositoryMock.SetupCustomerTestData(customerId, accountId, activeAccount);

        var productsRepoMock = new Mock<IDcProductsRepository>();
        var saMock = new Mock<IApigeeServiceAgreementsV3Repository>();
        var soMock = new Mock<IApigeeServiceOrdersV3Repository>();
        var storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_CreateServiceOrderDialog";
        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));
        if (accountId > 0)
        {
            customersRepositoryMock.Setup(x => x.GetCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new System.Net.Http.HttpResponseMessage
                    {
                        StatusCode = HttpStatusCode.OK
                    },
                    Body = new ResponseDataCustomerModel
                    {
                        Data = new CustomerModel
                        {
                            Id = (int)customerId,
                            CustomerType = CustomerType.Person,
                            Person = new PersonModel
                            {
                                Initials = "Paul",
                                Surname = "Testman",
                                Gender = Domain.Models.Customers.Gender.Male
                            },
                            Accounts = accountId > 0 ? new List<CustomerAccountModel>
                            {
                                new CustomerAccountModel
                                {
                                    CustomerId = customerId,
                                    Active = true,
                                    Id = accountId,
                                    Address = new AddressModel
                                    {
                                        PostalCode = "3025CB",
                                        HouseNumber = 10,
                                        HouseNumberSuffix = "A"
                                    }
                                }
                            } : new List<CustomerAccountModel>()
                        }
                    }
                });
        }

        productsRepoMock.Setup(x => x.CreateServiceOrder(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<RequestDataServiceOrderRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK
                },
                Body = new ResponseDataString
                {
                    Data = "ASOR001619087"
                }
            });

        productsRepoMock.Setup(x => x.CreateServiceOrder(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 2 || x == 8 || x == 11), It.IsAny<int>(), It.IsAny<RequestDataServiceOrderRequestModel>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest
                },
                Body = new ErrorResponse
                {
                    Errors = new List<ErrorModel>
                    {
                        new ErrorModel {
                            Code = "dcp|7dff0c3e-147c-46bb-b536-7848948e8d90",
                            Details = "Details",
                            Type = "FunctionalException"
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetServiceOrderPlanLink(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<string>(), It.IsAny<PlanLinkType?>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataString
                {
                    Data = "https://www.eneco.nl/"
                }
            });

        productsRepoMock.Setup(x => x.GetHardwareProductSpecifications(It.IsAny<Label>(), It.IsAny<BotChannel>(), null))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataIListHardwareProductSpecification
                {
                    Data = new List<HardwareProductSpecification> {
                        new HardwareProductSpecification
                        {
                            Group = new HardwareGroupDescription
                            {
                                Code = "5210-012",
                                Description = GroupDescription.CvKetel
                            },
                            Id = "OB1300087"
                        },
                        new HardwareProductSpecification
                        {
                            Group = new HardwareGroupDescription
                            {
                                Code = "5210-013",
                                Description = GroupDescription.EBoiler
                            },
                            Id = "OB1300081"
                        },
                        new HardwareProductSpecification
                        {
                            Group = new HardwareGroupDescription
                            {
                                Code = "5210-014",
                                Description = GroupDescription.Geiser
                            },
                            Id = "OB1300081"
                        },
                        new HardwareProductSpecification
                        {
                            Group = new HardwareGroupDescription
                            {
                                Code = "5210-015",
                                Description = GroupDescription.OnbekendOverig
                            },
                            Id = "STOB999999"
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>

            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Category = ProductCategory.Rent,
                                Name = ProductType.CVKetel,
                            },
                            Description = "test1",
                            IsActive = true,
                            AgreementId = 1
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Category = ProductCategory.Rent,
                                Name = ProductType.CVKetel
                            },
                            Description = "Particuliere Huur abonnement",
                            IsActive = true,
                            AgreementId = 3
                        },
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetServiceAgreementsForAgreementId(It.IsAny<Label>(), It.IsAny<BotChannel>(), 1, It.IsAny<bool?>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListServiceAgreement
                {
                    Data = new List<ServiceAgreement>
                    {
                        new ServiceAgreement
                        {
                            Number = "SCON012345",

                            Items = new List<ServiceAgreementItem>
                            {
                                new ServiceAgreementItem
                                {
                                    Service = "Particuliere Huur abonnement",
                                    Resource = new ServiceResource {
                                        Number = "OB1300087",
                                        Name = "Combiketel onbekend",
                                        BrandCode = "COMBIKET",
                                        BrandName = "Combiketel",
                                        ModelCode = "ONBEKEND",
                                        ModelName = "onbekend",
                                        CategoryNumber = "5210-012",
                                        CategoryName = "Gaswand combiketel"
                                    },
                                    Customer = new ServiceAgreementCustomer
                                    {
                                        CustomerId = 1,
                                        AccountId = 1,
                                        AgreementId = 1
                                    },
                                    IsSelfServiceAllowed = true
                                }
                            }
                        },
                        new ServiceAgreement
                        {
                            Number = "SCON012346",
                            Items = new List<ServiceAgreementItem>
                            {
                                new ServiceAgreementItem
                                {
                                    Service = "Particulier S&O abonnement",
                                    Resource = new ServiceResource {
                                        Number = "OB1300087",
                                        Name = "Combiketel onbekend",
                                        BrandCode = "COMBIKET",
                                        BrandName = "Combiketel",
                                        ModelCode = "ONBEKEND",
                                        ModelName = "onbekend",
                                        CategoryNumber = "5210-012",
                                        CategoryName = "Gaswand combiketel"
                                    },
                                    Customer = new ServiceAgreementCustomer
                                    {
                                        CustomerId = 1,
                                        AccountId = 1,
                                        AgreementId = 2
                                    },
                                    IsSelfServiceAllowed = true
                                }
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetServiceAgreementsForAgreementId(It.IsAny<Label>(), It.IsAny<BotChannel>(), 2, It.IsAny<bool?>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListServiceAgreement
                {
                    Data = new List<ServiceAgreement>()
                }
            });

        productsRepoMock.Setup(x => x.GetServiceAgreementsForAgreementId(It.IsAny<Label>(), It.IsAny<BotChannel>(), 3, It.IsAny<bool?>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListServiceAgreement
                {
                    Data = new List<ServiceAgreement>
                    {
                        new ServiceAgreement
                        {
                            Number = "SCON012345",
                            Items = new List<ServiceAgreementItem>
                            {
                                new ServiceAgreementItem
                                {
                                    Resource = new ServiceResource {
                                        Number = "OB1300087",
                                        Name = "Combiketel onbekend",
                                        BrandCode = "COMBIKET",
                                        BrandName = "Combiketel",
                                        ModelCode = "ONBEKEND",
                                        ModelName = "onbekend",
                                        CategoryNumber = "5210-012",
                                        CategoryName = "Gaswand combiketel"
                                    },
                                    Customer = new ServiceAgreementCustomer
                                    {
                                        CustomerId = 3,
                                        AccountId = 1,
                                        AgreementId = 1
                                    },
                                    IsSelfServiceAllowed = true
                                }
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetServiceAgreementsForAgreementId(It.IsAny<Label>(), It.IsAny<BotChannel>(), 4, It.IsAny<bool?>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListServiceAgreement
                {
                    Data = new List<ServiceAgreement>
                    {
                        new ServiceAgreement
                        {
                            Number = "SCON012345",
                            Items = new List<ServiceAgreementItem>
                            {
                                new ServiceAgreementItem
                                {
                                    Resource = new ServiceResource {
                                        Number = "OB1300081",
                                        Name = "Combiketel onbekend",
                                        BrandCode = "COMBIKET",
                                        BrandName = "Combiketel",
                                        ModelCode = "ONBEKEND",
                                        ModelName = "onbekend",
                                        CategoryNumber = "5210-013",
                                        CategoryName = "Boiler elektrisch"
                                    },
                                    Customer = new ServiceAgreementCustomer
                                    {
                                        CustomerId = 4,
                                        AccountId = 1,
                                        AgreementId = 1
                                    },
                                    IsSelfServiceAllowed = true
                                }
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetServiceAgreementsForAgreementId(It.IsAny<Label>(), It.IsAny<BotChannel>(), 5, It.IsAny<bool?>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListServiceAgreement
                {
                    Data = new List<ServiceAgreement>
                    {
                        new ServiceAgreement
                        {
                            Number = "SCON012345",
                            Items = new List<ServiceAgreementItem>
                            {
                                new ServiceAgreementItem
                                {
                                    Resource = new ServiceResource {
                                        Number = "OB1300089",
                                        Name = "Combiketel onbekend",
                                        BrandCode = "COMBIKET",
                                        BrandName = "Combiketel",
                                        ModelCode = "ONBEKEND",
                                        ModelName = "onbekend",
                                        CategoryNumber = "5210-014",
                                        CategoryName = "Geiser"
                                    },
                                    Customer = new ServiceAgreementCustomer
                                    {
                                        CustomerId = 1,
                                        AccountId = 1,
                                        AgreementId = 1
                                    },
                                    IsSelfServiceAllowed = true
                                }
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetServiceAgreementsForAgreementId(It.IsAny<Label>(), It.IsAny<BotChannel>(), 6, It.IsAny<bool?>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListServiceAgreement
                {
                    Data = new List<ServiceAgreement>()
                }
            });

        productsRepoMock.Setup(x => x.GetServiceAgreementsForAgreementId(It.IsAny<Label>(), It.IsAny<BotChannel>(), 7, It.IsAny<bool?>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListServiceAgreement
                {
                    Data = new List<ServiceAgreement>
                    {
                        new ServiceAgreement
                        {
                            Number = "SCON012345",

                            Items = new List<ServiceAgreementItem>
                            {
                                new ServiceAgreementItem
                                {
                                    Service = "Particuliere Huur abonnement",
                                    Resource = new ServiceResource {
                                        Number = "OB1300087",
                                        Name = "Combiketel onbekend",
                                        BrandCode = "COMBIKET",
                                        BrandName = "Combiketel",
                                        ModelCode = "ONBEKEND",
                                        ModelName = "onbekend",
                                        CategoryNumber = "5210-012",
                                        CategoryName = "Gaswand combiketel"
                                    },
                                    Customer = new ServiceAgreementCustomer
                                    {
                                        CustomerId = 7,
                                        AccountId = 1,
                                        AgreementId = 1
                                    },
                                    IsSelfServiceAllowed = true
                                }
                            }
                        },
                        new ServiceAgreement
                        {
                            Number = "SCON012346",
                            Items = new List<ServiceAgreementItem>
                            {
                                new ServiceAgreementItem
                                {
                                    Service = "Particulier S&O abonnement",
                                    Resource = new ServiceResource {
                                        Number = "OB1300087",
                                        Name = "Combiketel onbekend",
                                        BrandCode = "COMBIKET",
                                        BrandName = "Combiketel",
                                        ModelCode = "ONBEKEND",
                                        ModelName = "onbekend",
                                        CategoryNumber = "5210-012",
                                        CategoryName = "Gaswand combiketel"
                                    },
                                    Customer = new ServiceAgreementCustomer
                                    {
                                        CustomerId = 7,
                                        AccountId = 1,
                                        AgreementId = 2
                                    },
                                    IsSelfServiceAllowed = true
                                }
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetServiceAgreementsForAgreementId(It.IsAny<Label>(), It.IsAny<BotChannel>(), 8, It.IsAny<bool?>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListServiceAgreement
                {
                    Data = new List<ServiceAgreement>
                    {
                        new ServiceAgreement
                        {
                            Number = "SCON012345",
                            Items = new List<ServiceAgreementItem>
                            {
                                new ServiceAgreementItem
                                {
                                    Resource = new ServiceResource {
                                        Number = "OB1300087",
                                        Name = "Combiketel onbekend",
                                        BrandCode = "COMBIKET",
                                        BrandName = "Combiketel",
                                        ModelCode = "ONBEKEND",
                                        ModelName = "onbekend",
                                        CategoryNumber = "5210-013",
                                        CategoryName = "Gaswand combiketel"
                                    },
                                    Customer = new ServiceAgreementCustomer
                                    {
                                        CustomerId = 8,
                                        AccountId = 1,
                                        AgreementId = 1
                                    },
                                    IsSelfServiceAllowed = true
                                }
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetServiceAgreementsForAgreementId(It.IsAny<Label>(), It.IsAny<BotChannel>(), 9, It.IsAny<bool?>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListServiceAgreement
                {
                    Data = new List<ServiceAgreement>
                    {
                        new ServiceAgreement
                        {
                            Number = "ASCON00333692",
                            Items = new List<ServiceAgreementItem>
                            {
                                new ServiceAgreementItem
                                {
                                    Resource = new ServiceResource {
                                        Number = "OB1300087",
                                        Name = "Combiketel onbekend",
                                        BrandCode = "COMBIKET",
                                        BrandName = "Combiketel",
                                        ModelCode = "ONBEKEND",
                                        ModelName = "onbekend",
                                        CategoryNumber = "5210-013",
                                        CategoryName = "Gaswand combiketel"
                                    },
                                    Customer = new ServiceAgreementCustomer
                                    {
                                        CustomerId = 9,
                                        AccountId = 1,
                                        AgreementId = 1
                                    },
                                    IsSelfServiceAllowed = true
                                }
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetServiceAgreementsForAgreementId(It.IsAny<Label>(), It.IsAny<BotChannel>(), 10, It.IsAny<bool?>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListServiceAgreement
                {
                    Data = new List<ServiceAgreement>
                    {
                        new ServiceAgreement
                        {
                            Number = "SCON012345",
                            Items = new List<ServiceAgreementItem>
                            {
                                new ServiceAgreementItem
                                {
                                    Resource = new ServiceResource {
                                        Number = "OB1300081",
                                        Name = "Combiketel onbekend",
                                        BrandCode = "COMBIKET",
                                        BrandName = "Combiketel",
                                        ModelCode = "ONBEKEND",
                                        ModelName = "onbekend",
                                        CategoryNumber = "6210-013",
                                        CategoryName = "ONBEKEND"
                                    },
                                    Customer = new ServiceAgreementCustomer
                                    {
                                        CustomerId = 10,
                                        AccountId = 1,
                                        AgreementId = 1
                                    },
                                    IsSelfServiceAllowed = true
                                }
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetServiceOrdersFromServiceAgreement(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<string>(x => x != "ASCON00333692"), It.IsAny<ServiceOrderStatus?>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListServiceOrder
                {
                    Data = new List<ServiceOrder>()
                }
            });

        productsRepoMock.Setup(x => x.GetServiceOrdersFromServiceAgreement(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<string>(x => x == "ASCON00333692"), It.IsAny<ServiceOrderStatus?>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListServiceOrder
                {
                    Data = new List<ServiceOrder>
                    {
                        new ServiceOrder
                        {
                            OrderDate = lastServiceOrder.HasValue ? lastServiceOrder.Value : DateTime.Today.AddDays(-1),
                            Description = "Reperaties BPM",
                            Status = ServiceOrderStatus.Open,
                            SubStatusCode = "90",
                            SubStatusDescription = "administratief gereed",
                            Type = serviceOrderKind,
                            Items = new List<ServiceOrderItem>
                            {
                                new ServiceOrderItem
                                {
                                    Contact = new ServiceOrderItemContact
                                    {
                                        Name = "John",
                                        EmailAddress = "<EMAIL>",
                                        MobilePhoneNumber = "0612345678",
                                        PhoneNumber = "1234567890"
                                    },
                                    Appointment = new ServiceOrderItemAppointment
                                    {
                                        WindowStartDateTime = DateTime.Today,
                                        StartDateTime = DateTime.Today.AddDays(1),
                                        EndDateTime = DateTime.Today.AddDays(2),
                                        WindowEndDateTime = DateTime.Today.AddDays(3)
                                    },
                                    Service = new ServiceOrderItemService
                                    {
                                        Name = "Entreebeurt",
                                        Resource = new ServiceResource
                                        {
                                            BrandCode = "b1",
                                            BrandName = "b2",
                                            CategoryName = "c1",
                                            CategoryNumber = "c2",
                                            ModelCode = "m1",
                                            ModelName = "Model",
                                            Name = "name",
                                            Number = "123",
                                            SerialNumber = "321",
                                            YearOfManufacture = 2008
                                        }
                                    },
                                    PlanActions = new List<ServiceOrderPlanAction>
                                    {
                                        new ServiceOrderPlanAction
                                        {
                                            Channel = "channel",
                                            Description = "descript",
                                            ExecutedOnDate = DateTime.Today.AddDays(1),
                                            Id = "1",
                                            Initiator = ServiceOrderPlanActionInitiator.Agent,
                                            Type = ServiceOrderPlanActionType.Invite,
                                            PlanLink = new ServiceOrderPlanActionLink
                                            {
                                                Url = "http://google.com",
                                                ValidUntilDate = new DateTime(2021, 12, 1)
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetServiceOrdersFromServiceAgreement(It.IsAny<Label>(), It.IsAny<BotChannel>(), "SCON123456", ServiceOrderStatus.Open))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new System.Net.Http.HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListServiceOrder
                {
                    Data = new List<ServiceOrder>
                    {
                        new ServiceOrder
                        {
                            Number = "12345",
                            Status = ServiceOrderStatus.Open
                        }
                    }
                }
            });

        // Used to test error flow with configured phone number
        if (customerId == 11)
        {
            customersRepositoryMock
                .Setup(mock => mock.GetCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), 11))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataCustomerModel
                    {
                        Data = new CustomerModel
                        {
                            Id = 11,
                            CustomerType = CustomerType.Person,
                            Person = new PersonModel
                            {
                                Initials = "Paul",
                                Surname = "Testman",
                                Gender = Gender.Male
                            },
                            Contact = new ContactModel { PhoneNumber = "**********", },
                            Accounts = new List<CustomerAccountModel>
                            {
                                new()
                                {
                                    CustomerId = 11,
                                    Active = true,
                                    Id = accountId,
                                    Address = new AddressModel
                                    {
                                        PostalCode = "3025CB",
                                        HouseNumber = 10,
                                        HouseNumberSuffix = "A"
                                    }
                                }
                            }
                        }
                    }
                });
        }

        var validator = new Mock<IDialogValidators>();
        var loggingService = new Mock<ILoggingService>();
        validator.SetupDialogValidatorWithSessionManager(customerId);

        _services.SwapTransient(provider => loggingService.Object);
        _services.SwapTransient(provider => validator.Object);
        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => productsRepoMock.Object);
        _services.SwapTransient(provider => saMock.Object);
        _services.SwapTransient(provider => soMock.Object);
        _services.SwapTransient(provider => sessionManagerMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }

    private static Mock<ISessionManager> GetSessionsManager(long customerId, bool customerIdVerified = false, bool isUrgent = false)
    {
        List<ProductModel> products = new();
        ProductModel product = new();

        switch (customerId)
        {
            case 1://wrong product
                products = new List<ProductModel>
                {
                    new ProductModel
                    {
                        AgreementId = 1,
                        Type = new ProductTypeModel
                        {
                            Category = ProductCategory.Service,
                            MarketingCode = "OB123449",
                            Name = ProductType.CVKetel,
                            RateCode = "OB123449"
                        }
                    },
                    new ProductModel
                    {
                        AgreementId = 1,
                        Type = new ProductTypeModel
                        {
                            Category = ProductCategory.Service,
                            MarketingCode = "OB123449",
                            Name = ProductType.CVKetel,
                            RateCode = "OB123449"
                        }
                    }
                };
                break;
            case 3://CV-ketel
            case 8 or 11://error with order creation
                product = new ProductModel
                {
                    AgreementId = 3,
                    Type = new ProductTypeModel
                    {
                        Category = ProductCategory.Service,
                        MarketingCode = "OB123443",
                        Name = ProductType.CVKetel,
                        RateCode = "OB123443"
                    }
                };
                break;
            case 4://Boiler
                product = new ProductModel
                {
                    AgreementId = 4,
                    Type = new ProductTypeModel
                    {
                        Category = ProductCategory.Service,
                        MarketingCode = "OB123443",
                        Name = ProductType.Huurapparaat,
                        RateCode = "OB123443"
                    }
                };
                break;
            case 5://Geiser
                product = new ProductModel
                {
                    AgreementId = 5,
                    Type = new ProductTypeModel
                    {
                        Category = ProductCategory.Service,
                        MarketingCode = "OB123445",
                        Name = ProductType.Huurapparaat,
                        RateCode = "OB123445"
                    }
                };
                break;
            case 7://multiple objects
                product = new ProductModel
                {
                    AgreementId = 7,
                    Type = new ProductTypeModel
                    {
                        Category = ProductCategory.Service,
                        MarketingCode = "OB123447",
                        Name = ProductType.Huurapparaat,
                        RateCode = "OB123447"
                    }
                };
                break;
            case 9://open order
                product = new ProductModel
                {
                    AgreementId = 9,
                    Type = new ProductTypeModel
                    {
                        Category = ProductCategory.Service,
                        MarketingCode = "OB123449",
                        Name = ProductType.Huurapparaat,
                        RateCode = "OB123449"
                    }
                };
                break;
            case 10://wrong product
                product = new ProductModel
                {
                    AgreementId = 10,
                    Type = new ProductTypeModel
                    {
                        Category = ProductCategory.Service,
                        MarketingCode = "OB123450",
                        Name = ProductType.Huurapparaat,
                        RateCode = "OB123450"
                    }
                };
                break;
            default:
                product = new ProductModel();
                break;
        }


        var sessionManagerMock = new Mock<ISessionManager>();
        sessionManagerMock.Setup(x =>
                x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Customer = new CustomerData
                {
                    Label = Label.Eneco
                },
                Verification = new VerificationData
                {
                    CustomerId = customerId,
                    CustomerIdVerified = customerIdVerified
                },
                ServiceOrder = new ServiceOrderData
                {
                    Description = "Storing 2",
                    IsUrgent = isUrgent,
                    ChosenProduct = customerId == 7 || customerId == 1 ? null : product,
                    Products = customerId == 1 ? products : new List<ProductModel>
                    {
                        product
                    }
                }
            });

        return sessionManagerMock;
    }
}
