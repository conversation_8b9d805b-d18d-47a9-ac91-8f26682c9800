﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Domain.Models.Products;
using DC.Domain.Models.Products.ProductRates;
using DC.Domain.Models.Usages;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using ISessionManager = DC.Bot.BusinessLogic.Interfaces.ISessionManager;

namespace DC.Bot.Tests.Dialogs.Products;

[Collection("Sequential")]
public class ProductRatesDialogTests : TestBase
{
    private readonly Mock<IDcCustomersRepository> _customersRepositoryMock;
    private readonly Mock<IDcProductsRepository> _productsRepo;
    private readonly Mock<IStorageService> _storageServiceMock;

    public ProductRatesDialogTests()
    {
        _customersRepositoryMock = new Mock<IDcCustomersRepository>();
        _productsRepo = new Mock<IDcProductsRepository>();
        _storageServiceMock = new Mock<IStorageService>();
    }

    [Fact]
    public async Task GetProductRates_with_VerifiedCustomer_returns_correct_reply()
    {
        var sessionManager = GetSessionsManager(1);
        //Arrange
        SwapMockedServices(sessionManager, 1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._RATES);

        Assert.StartsWith(GetTextLabelValue("NoProductsFound", TextLabelGroupName), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetProductRates_with_VerifiedCustomer_returns_correct_reply2()
    {
        var sessionManager = GetSessionsManager(2);
        //Arrange
        SwapMockedServices(sessionManager, 2);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._RATES);

        Assert.StartsWith(GetTextLabelValue("NextTariffsFound", TextLabelGroupName), testClient.GetNextReply().Text);

        var productText = testClient.GetNextReply().Text;
        Assert.Contains("Item A", productText);
        Assert.Contains(GetTextLabelValue("DoubleTariff", TextLabelGroupName).Replace("{doubleTariff}", "Nee"), productText);
    }

    [Fact]
    public async Task GetProductRates_with_VerifiedCustomer_WithMulitpleAccount_WithInActiveAccount()
    {
        var sessionManager = GetSessionsManager(3);
        //Arrange
        SwapMockedServices(sessionManager, 3, false, true);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._RATES);

        Assert.StartsWith(GetTextLabelValue("NextTariffsFound", TextLabelGroupName), testClient.GetNextReply().Text);

        var productText = testClient.GetNextReply().Text;
        Assert.Contains("Item A", productText);
        Assert.Contains(GetTextLabelValue("DoubleTariff", TextLabelGroupName).Replace("{doubleTariff}", "Nee"), productText);
    }

    [Fact]
    public async Task GetProductRates_with_VerifiedCustomer_WithRedelivery()
    {
        var sessionManager = GetSessionsManager(7);
        //Arrange
        SwapMockedServices(sessionManager, 7, false, true);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._RATES);

        Assert.StartsWith(GetTextLabelValue("NextTariffsFound", TextLabelGroupName), testClient.GetNextReply().Text);

        var productText = testClient.GetNextReply().Text;
        Assert.Contains("Electricity", productText);
        Assert.Contains($"Normaaltarief: €0.31888 per kWh", productText);
        Assert.Contains($"Terugleververgoeding: €0.09 per kWh", productText);
        Assert.Contains($"Vaste leveringskosten: €7.75 per maand", productText);
        Assert.Contains($"Netbeheerkosten: €17.06 per maand", productText);
        Assert.Contains(GetTextLabelValue("DoubleTariff", TextLabelGroupName).Replace("{doubleTariff}", "Nee"), productText);
    }

    [Fact]
    public async Task GetProductRates_with_VerifiedCustomer_withNoneMeasuredProducts()
    {
        var sessionManager = GetSessionsManager(4);
        //Arrange
        SwapMockedServices(sessionManager, 4);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._RATES);
        Assert.StartsWith(GetTextLabelValue("NextTariffsFound", TextLabelGroupName), testClient.GetNextReply().Text);

        var productText = testClient.GetNextReply().Text;
        Assert.Contains("Toon", productText);
        Assert.Contains($"Toon jaar: €1.25 per jaar", productText);
    }

    [Fact]
    public async Task GetProductRates_Oxxio_with_VerifiedCustomer()
    {
        var sessionManager = GetSessionsManager(5);
        //Arrange
        SwapMockedServices(sessionManager, 5, label: Label.Oxxio);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._RATES + "-LabelOxxio");
        Assert.StartsWith(GetTextLabelValue("NextTariffsFound", TextLabelGroupName), testClient.GetNextReply().Text);

        var productText = testClient.GetNextReply().Text;
        Assert.Contains("Gas", productText);
        Assert.Contains($"Vaste leveringskosten: €7.75 per maand", productText);
        Assert.Contains($"Netbeheerkosten: €17.06 per maand", productText);
    }

    [Fact]
    public async Task GetProductRates_with_UnverifiedCustomer()
    {
        var sessionManager = GetSessionsManager(1);
        //Arrange
        SwapMockedServices(sessionManager, 1);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithUnverifiedCustomer(DialogCommands._RATES);
        Assert.Contains(GetTextLabelValue("CouldNotFindData", "Bot_GeneralTextLabels"), testClient.GetNextReply().Text);
    }

    [Fact]
    public async Task GetProductRates_with_VerifiedCustomer_returns_DynamicPricing()
    {
        var sessionManager = GetSessionsManager(6, hasDynamicPricing: true);
        //Arrange
        SwapMockedServices(sessionManager, 6, hasDynamicPricing: true);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._RATES);
        sessionManager.Verify(mock =>
           mock.SendEndOfTransactionActivity(
               It.IsAny<ITurnContext>(),
               It.IsAny<string>(), It.IsAny<string>(),
               TransactionStatus.UnhappyDynamicPricing,
               It.IsAny<CancellationToken>()), Times.Once());
    }

    private void SwapMockedServices(Mock<ISessionManager> sessionManagerMock, long customerId, bool activeAccount = true, bool multiAccount = false, Label label = Label.Eneco, bool hasDynamicPricing = false)
    {
        
        TextLabelGroupName = "Bot_ProductRatesDialog";
        GetAllTextLabelsFromExport();
        _storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(new List<Storage.Client.Models.TextLabelModel>
            {

            }));
        _customersRepositoryMock.SetupCustomerTestData(
            customerId: customerId,
            activeAccount: activeAccount,
            multiAccount: multiAccount,
            nextChargeDate: (customerId == 1 ? DateTime.Now : null),
            hasDynamicPricing: hasDynamicPricing);

        _productsRepo.Setup(x =>
                x.GetProductRates(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 1), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataProductRates
                {
                    Data = new ProductRates
                    {
                        Rates = null
                    }
                }
            });

        _productsRepo.Setup(x =>
                x.GetProductRates(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 2), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataProductRates
                {
                    Data = new ProductRates
                    {
                        Rates = new List<ProductRate>
                        {
                            new ProductRate
                            {
                                Name = "Item A",
                                MonthlyCosts = new VatModel { VatAmount = 1 },
                                MonthlyCostsFixed = new VatModel { VatAmount = 2 },
                                MonthlyCostsVariable = new VatModel { VatAmount = 3 },
                                YearlyCosts = new VatModel { VatAmount = 4 },
                                YearlyCostsFixed = new VatModel { VatAmount = 5 },
                                YearlyCostsVariable = new VatModel { VatAmount = 6 },
                                ProductType = ProductType.Electricity,
                                IsDoubleTariff = false,
                                ProductRateDetails = new List<ProductRateDetail>
                                {
                                    new ProductRateDetail
                                    {
                                        ByPeriod = ByPeriod.D
                                    }
                                }
                            }
                        }
                    }
                }
            });

        _productsRepo.Setup(x =>
                x.GetProductRates(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 3), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataProductRates
                {
                    Data = new ProductRates
                    {
                        Rates = new List<ProductRate>
                        {
                            new ProductRate
                            {
                                Name = "Item A",
                                MonthlyCosts = new VatModel { VatAmount = 1 },
                                MonthlyCostsFixed = new VatModel { VatAmount = 2 },
                                MonthlyCostsVariable = new VatModel { VatAmount = 3 },
                                YearlyCosts = new VatModel { VatAmount = 4 },
                                YearlyCostsFixed = new VatModel { VatAmount = 5 },
                                YearlyCostsVariable = new VatModel { VatAmount = 6 },
                                ProductType = ProductType.Electricity,
                                IsDoubleTariff = false,
                                ProductRateDetails = new List<ProductRateDetail>
                                {
                                    new ProductRateDetail
                                    {
                                        Type = ProductRateDetailType.Tariff,
                                        ByPeriod = ByPeriod.D
                                    }
                                }
                            }
                        }
                    }
                }
            });

        _productsRepo.Setup(x =>
                x.GetProductRates(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 7), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataProductRates
                {
                    Data = new ProductRates
                    {
                        Rates = new List<ProductRate>
                        {
                            new ProductRate
                            {
                                Name = "Electricity",
                                MonthlyCosts = new VatModel { VatAmount = 1 },
                                MonthlyCostsFixed = new VatModel { VatAmount = 2 },
                                MonthlyCostsVariable = new VatModel { VatAmount = 3 },
                                YearlyCosts = new VatModel { VatAmount = 4 },
                                YearlyCostsFixed = new VatModel { VatAmount = 5 },
                                YearlyCostsVariable = new VatModel { VatAmount = 6 },
                                ProductType = ProductType.Electricity,
                                IsDoubleTariff = false,
                                ProductRateDetails = new List<ProductRateDetail>
                                {
                                    new ProductRateDetail()
                                    {
                                        Type = ProductRateDetailType.Tariff,
                                        Description = "Normaaltarief",
                                        VATIncluded = 0.31888M,
                                        ByPeriod = null,
                                        IsVariable = true,
                                        DenotationType = DenotationType.Kwh
                                    },
                                    new ProductRateDetail()
                                    {
                                        Type = ProductRateDetailType.RedeliveryTariff,
                                        Description = "Terugleververgoeding",
                                        VATIncluded = -0.11M,
                                        VATExcluded = -0.09222M,
                                        ByPeriod = null,
                                        IsVariable = true,
                                        DenotationType = DenotationType.Kwh
                                    },
                                    new ProductRateDetail()
                                    {
                                        Type = ProductRateDetailType.StandingCharge,
                                        Description = "Vaste leveringskosten",
                                        VATIncluded = 92.99M,
                                        ByPeriod = ByPeriod.Y,
                                        IsVariable = false,
                                    },
                                    new ProductRateDetail()
                                    {
                                        Type = ProductRateDetailType.DeliveryCosts,
                                        Description = "Netbeheerkosten",
                                        VATIncluded = 204.71M,
                                        ByPeriod = ByPeriod.Y,
                                        IsVariable = false,
                                    }
                                }
                            },
                        }
                    }
                }
            });

        _productsRepo.Setup(x => x.GetProductRates(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == 4), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataProductRates
                {
                    Data = new ProductRates
                    {
                        Rates = new List<ProductRate> {
                            new ProductRate {
                                Name = "Stukje zon",
                                ProductType = ProductType.StukjeZon,
                                ProductRateDetails = new List<ProductRateDetail>() {
                                    new ProductRateDetail()
                                    {
                                        Type = ProductRateDetailType.StukjeZon,
                                        Description = "Stukje zon",
                                        VATIncluded = -1.25M,
                                        ByPeriod = ByPeriod.M,
                                        IsVariable = false
                                    }
                                }
                            },
                            new ProductRate
                            {
                                Name = "Toon",
                                ProductType = ProductType.ToonService,
                                ProductRateDetails = new List<ProductRateDetail>
                                {
                                    new ProductRateDetail()
                                    {
                                        Type = ProductRateDetailType.ProjectDiscount,
                                        Description = "Toon jaar",
                                        VATIncluded = 1.25M,
                                        ByPeriod = ByPeriod.Y,
                                        IsVariable = false
                                    },                                    }
                            },
                            new ProductRate
                            {
                                Name = "Toon",
                                ProductType = ProductType.ToonService,
                                ProductRateDetails = new List<ProductRateDetail>
                                {
                                    new ProductRateDetail()
                                    {
                                        Type = ProductRateDetailType.ProjectDiscount,
                                        Description = "Toon week",
                                        VATIncluded = 1.25M,
                                        ByPeriod = ByPeriod.W,
                                        IsVariable = false
                                    }
                                }
                            },
                            new ProductRate
                            {
                                Name = "Toon",
                                ProductType = ProductType.ToonService,
                                ProductRateDetails = new List<ProductRateDetail>
                                {
                                    new ProductRateDetail()
                                    {
                                        Type = ProductRateDetailType.ProjectDiscount,
                                        Description = "Toon dag",
                                        VATIncluded = 1.25M,
                                        ByPeriod = ByPeriod.D,
                                        IsVariable = false
                                    }
                                }
                            },
                            new ProductRate
                            {
                                Name = "Toon",
                                ProductType = ProductType.ToonService,
                                ProductRateDetails = new List<ProductRateDetail>
                                {
                                    new ProductRateDetail()
                                    {
                                        Type = ProductRateDetailType.ProjectDiscount,
                                        Description = "Toon uur",
                                        VATIncluded = 1.25M,
                                        ByPeriod = ByPeriod.H,
                                        IsVariable = false
                                    }
                                }
                            }
                        }
                    }
                }
            });

        _productsRepo.Setup(x => x.GetProductRates(It.Is<Label>(x => x == Label.Oxxio), It.IsAny<BotChannel>(), It.Is<long>(x => x == 5), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataProductRates
                {
                    Data = new ProductRates
                    {
                        Rates = new List<ProductRate> {
                            new ProductRate {
                                Name = "Gas",
                                ProductType = ProductType.Gas,
                                ProductRateDetails = new List<ProductRateDetail>() {
                                    new ProductRateDetail()
                                    {
                                        Type = ProductRateDetailType.Tariff,
                                        Description = "Normaaltarief",
                                        VATIncluded = 0.92888M,
                                        ByPeriod = null,
                                        IsVariable = true,
                                        DenotationType = DenotationType.M3
                                    },
                                    new ProductRateDetail()
                                    {
                                        Type = ProductRateDetailType.StandingCharge,
                                        Description = "Vaste leveringskosten",
                                        VATIncluded = 92.99M,
                                        ByPeriod = ByPeriod.Y,
                                        IsVariable = false,
                                    },
                                    new ProductRateDetail()
                                    {
                                        Type = ProductRateDetailType.DeliveryCosts,
                                        Description = "Netbeheerkosten",
                                        VATIncluded = 204.71M,
                                        ByPeriod = ByPeriod.Y,
                                        IsVariable = false,
                                    }
                                }
                            }
                        }
                    }
                }
            });


        var validator = new Mock<IDialogValidators>();
        validator.SetupDialogValidatorWithSessionManager(customerId, sessionManagerMock: sessionManagerMock);
        _services.SwapTransient(provider => validator.Object);

        _services.SwapTransient(provider => _customersRepositoryMock.Object);
        _services.SwapTransient(provider => _productsRepo.Object);
        _services.SwapTransient(provider => _storageServiceMock.Object);
        _services.SwapTransient(provider => sessionManagerMock.Object);
    }

    private static Mock<BusinessLogic.Interfaces.ISessionManager> GetSessionsManager(long customerId, Label label = Label.Eneco, bool hasDynamicPricing = false)
    {
        var sessionManagerMock = new Mock<ISessionManager>();
        sessionManagerMock.Setup(x =>
                x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Customer = new CustomerData
                {
                    Label = label
                },
                Verification = new VerificationData
                {
                    CustomerId = customerId,
                    CustomerIdVerified = true
                },
                SelectedAccount = new AccountData
                {
                    AccountId = 1,
                    HasDynamicPricing = hasDynamicPricing
                }
            });

        return sessionManagerMock;
    }
}
