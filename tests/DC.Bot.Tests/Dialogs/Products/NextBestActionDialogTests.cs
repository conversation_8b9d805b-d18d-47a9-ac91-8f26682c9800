﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Dialogs.Financials.Validators;
using DC.Bot.BusinessLogic.Dialogs.Products.Validators;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Domain.Models.NextBestAction;
using DC.Financials.Client.Models;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Storage.Client.Models;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using TransactionStatus = DC.Bot.BusinessLogic.Enumerations.TransactionStatus;

namespace DC.Bot.Tests.Dialogs.Products;

[Collection("Sequential")]
public class NextBestActionDialogTests : TestBase
{
    /// <summary>
    /// Full test on the whole logic using choices
    /// </summary>
    /// <param name="answer"></param>
    /// <param name="expectedActionType"></param>
    /// <param name="available"></param>
    /// <param name="expectedDialog"></param>
    /// <param name="actionTypes"></param>
    /// <returns></returns>
    [Theory]
    // Continue answer + NBA available
    [InlineData(true, "BankAccountNumber", true, DialogAction.ChangeIBAN, "BankAccountNumber")]
    [InlineData(true, "CustomerPhoneNumber", true, DialogAction.ChangePhoneNumber, "CustomerPhoneNumber")]
    [InlineData(true, "CustomerEmailAddress", true, DialogAction.ChangeEmail, "CustomerEmailAddress")]
    [InlineData(true, "CustomerPhoneNumber", true, DialogAction.ChangePhoneNumber, "CustomerPhoneNumber", "BankAccountNumber", "CustomerEmailAddress")]
    [InlineData(true, "BankAccountNumber", true, DialogAction.ChangeIBAN, "BankAccountNumber", "CustomerPhoneNumber", "CustomerEmailAddress")]
    [InlineData(true, "CustomerEmailAddress", true, DialogAction.ChangeEmail, "CustomerEmailAddress", "BankAccountNumber", "CustomerPhoneNumber")]
    [InlineData(true, "Isma", true, DialogAction.IsmaMandate, "Isma")]
    // Continue answer + no (compatible) NBA available
    [InlineData(true, null, false, null, "Afco")]
    [InlineData(true, null, false, null)]
    // Dismiss answer + NBA available
    [InlineData(false, "BankAccountNumber", true, DialogAction.ChangeIBAN, "BankAccountNumber")]
    [InlineData(false, "CustomerPhoneNumber", true, DialogAction.ChangePhoneNumber, "CustomerPhoneNumber")]
    [InlineData(false, "CustomerEmailAddress", true, DialogAction.ChangeEmail, "CustomerEmailAddress")]
    [InlineData(false, "CustomerPhoneNumber", true, DialogAction.ChangePhoneNumber, "CustomerPhoneNumber", "BankAccountNumber", "CustomerEmailAddress")]
    [InlineData(false, "BankAccountNumber", true, DialogAction.ChangeIBAN, "BankAccountNumber", "CustomerPhoneNumber", "CustomerEmailAddress")]
    [InlineData(false, "CustomerEmailAddress", true, DialogAction.ChangeEmail, "CustomerEmailAddress", "BankAccountNumber", "CustomerPhoneNumber")]
    // Dismiss answer + no (compatible) NBA available
    [InlineData(false, null, false, null, "Afco")]
    [InlineData(false, null, false, null)]
    // Ignore answer + NBA available
    [InlineData(null, "BankAccountNumber", true, DialogAction.ChangeIBAN, "BankAccountNumber")]
    [InlineData(null, "CustomerPhoneNumber", true, DialogAction.ChangePhoneNumber, "CustomerPhoneNumber")]
    [InlineData(null, "CustomerEmailAddress", true, DialogAction.ChangeEmail, "CustomerEmailAddress")]
    [InlineData(null, "CustomerPhoneNumber", true, DialogAction.ChangePhoneNumber, "CustomerPhoneNumber", "BankAccountNumber", "CustomerEmailAddress")]
    [InlineData(null, "BankAccountNumber", true, DialogAction.ChangeIBAN, "BankAccountNumber", "CustomerPhoneNumber", "CustomerEmailAddress")]
    [InlineData(null, "CustomerEmailAddress", true, DialogAction.ChangeEmail, "CustomerEmailAddress", "BankAccountNumber", "CustomerPhoneNumber")]
    // Ignore answer + no (compatible) NBA available
    [InlineData(null, null, false, null, "Afco")]
    [InlineData(null, null, false, null)]
    public async Task DialogTest_Choice(bool? answer, string expectedActionType, bool available, DialogAction? expectedDialog, params string[] actionTypes)
    {
        var nba = PrepareActions(actionTypes);
        var sessionManager = GetSessionManager(nba);
        SwapMockedServices(sessionManager, nba, out var nbaServiceMock);
        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._NEXTBESTACTION).ConfigureAwait(true);
        if (!available)
        {
            sessionManager.Verify(m => m.SendEndOfTransactionActivity(It.IsAny<ITurnContext>(), It.IsAny<string>(), It.IsAny<string>(), TransactionStatus.NextBestActionNotAvailable, It.IsAny<CancellationToken>()), Times.Once);
            return;
        }
        var config = expectedActionType.GetNextBestActionConfiguration(nba, TextLabelModels);
        nbaServiceMock.Verify(m => m.AddFeedback(It.IsAny<DialogData>(), expectedActionType, FeedbackStatus.Viewed), Times.Once);
        testClient.GetNextReply().Text.Should().StartWith(config.PromptText);

        if (answer.HasValue)
        {
            if (answer.Value)
            {
                var answerActivity = await testClient.SendActivityAsync(config.ContinueChoiceText).ConfigureAwait(true);
                answerActivity.Text.Should().StartWith(config.ContinueFollowUpText);
                nbaServiceMock.Verify(m => m.AddFeedback(It.IsAny<DialogData>(), expectedActionType, FeedbackStatus.Dismissed), Times.Never);
                nbaServiceMock.Verify(m => m.AddFeedback(It.IsAny<DialogData>(), expectedActionType, FeedbackStatus.Success), Times.Once);
                sessionManager.Verify(m => m.SetCurrentDialogAction(It.IsAny<ITurnContext>(), expectedDialog.Value), Times.AtLeastOnce);
                sessionManager.Verify(m => m.SendEndOfTransactionActivity(It.IsAny<ITurnContext>(), It.IsAny<string>(), It.IsAny<string>(), TransactionStatus.NextBestActionDismissed, It.IsAny<CancellationToken>()), Times.Never);
                sessionManager.Verify(m => m.SendEndOfTransactionActivity(It.IsAny<ITurnContext>(), It.IsAny<string>(), It.IsAny<string>(), TransactionStatus.NextBestActionIgnored, It.IsAny<CancellationToken>()), Times.Never);
            }
            else
            {
                await testClient.SendActivityAsync("Herinner mij niet opnieuw").ConfigureAwait(true);
                nbaServiceMock.Verify(m => m.AddFeedback(It.IsAny<DialogData>(), expectedActionType, FeedbackStatus.Success), Times.Never);
                nbaServiceMock.Verify(m => m.AddFeedback(It.IsAny<DialogData>(), expectedActionType, FeedbackStatus.Dismissed), Times.Once);
                sessionManager.Verify(m => m.SetCurrentDialogAction(It.IsAny<ITurnContext>(), expectedDialog.Value), Times.Never);
                sessionManager.Verify(m => m.SendEndOfTransactionActivity(It.IsAny<ITurnContext>(), It.IsAny<string>(), It.IsAny<string>(), TransactionStatus.NextBestActionDismissed, It.IsAny<CancellationToken>()), Times.Once);
                sessionManager.Verify(m => m.SendEndOfTransactionActivity(It.IsAny<ITurnContext>(), It.IsAny<string>(), It.IsAny<string>(), TransactionStatus.NextBestActionIgnored, It.IsAny<CancellationToken>()), Times.Never);
            }
        }
        else
        {
            await testClient.SendActivityAsync("Niet nu").ConfigureAwait(true);
            nbaServiceMock.Verify(m => m.AddFeedback(It.IsAny<DialogData>(), expectedActionType, FeedbackStatus.Success), Times.Never);
            nbaServiceMock.Verify(m => m.AddFeedback(It.IsAny<DialogData>(), expectedActionType, FeedbackStatus.Dismissed), Times.Never);
            sessionManager.Verify(m => m.SetCurrentDialogAction(It.IsAny<ITurnContext>(), expectedDialog.Value), Times.Never);
            sessionManager.Verify(m => m.SendEndOfTransactionActivity(It.IsAny<ITurnContext>(), It.IsAny<string>(), It.IsAny<string>(), TransactionStatus.NextBestActionDismissed, It.IsAny<CancellationToken>()), Times.Never);
            sessionManager.Verify(m => m.SendEndOfTransactionActivity(It.IsAny<ITurnContext>(), It.IsAny<string>(), It.IsAny<string>(), TransactionStatus.NextBestActionIgnored, It.IsAny<CancellationToken>()), Times.Once);
        }
    }

    /// <summary>
    /// Short text based test on the synonyms. Does not test the whole logic like above.
    /// </summary>
    /// <param name="answer"></param>
    /// <returns></returns>
    [Theory]
    [InlineData("BankAccountNumberChoiceText")]
    [InlineData("DismissNbaChoice")]
    [InlineData("IgnoreNbaChoice")]
    [InlineData("Onbegrepen")]
    public async Task DialogTest_Text(string answer)
    {
        var nba = PrepareActions(["BankAccountNumber"]);
        var sessionManager = GetSessionManager(nba);
        SwapMockedServices(sessionManager, nba, out _);
        DialogTestClient testClient = SetupMainDialog();
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._NEXTBESTACTION).ConfigureAwait(true);

        var config = "BankAccountNumber".GetNextBestActionConfiguration(nba, TextLabelModels);
        testClient.GetNextReply().Text.Should().StartWith(config.PromptText);

        var answerText = GetTextLabelValue(answer, TextLabelGroupName) ?? answer;
        var answerActivity = await testClient.SendActivityAsync(answerText).ConfigureAwait(true);
        if (answerText == GetTextLabelValue("BankAccountNumberChoiceText", TextLabelGroupName))
            answerActivity.Text.Should().StartWith(GetTextLabelValue("BankAccountNumberFollowUpText", TextLabelGroupName));
        else if (answerText == GetTextLabelValue("DismissNbaChoice", TextLabelGroupName))
            answerActivity?.Text.Should().BeNull();
        else if (answerText == GetTextLabelValue("IgnoreNbaChoice", TextLabelGroupName))
            answerActivity?.Text.Should().BeNull();
        else
            answerActivity.Text.Should().StartWith(config.PromptText);
    }

    private static NextBestAction PrepareActions(string[] actionTypes)
    {
        List<Tuple<double, string>> actions = new();
        double score = 100.0;
        foreach (var actionType in actionTypes)
        {
            actions.Add(new Tuple<double, string>(score, actionType));
            score -= 10.0;
        }
        return new NextBestAction
        {
            CustomerId = 123,
            ContextId = "ctx1",
            Actions = actions.Select(a =>
            {
                return new ActionModel
                {
                    AccountId = 1,
                    ActionType = a.Item2,
                    ActionId = 1,
                    Channel = Channel.Chatbot,
                    ServingPointId = 4,
                    TreatmentVariationId = 2,
                    Score = a.Item1
                };
            }).ToList()
        };
    }

    private static Mock<ISessionManager> GetSessionManager(NextBestAction nba)
    {
        var sessionManagerMock = new Mock<ISessionManager>();
        sessionManagerMock.Setup(x =>
                x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Customer = new CustomerData
                {
                    Label = Label.Eneco
                },
                Verification = new VerificationData
                {
                    CustomerId = 123,
                    CustomerIdVerified = true,
                },
                NextBestAction = new NextBestActionData
                {
                    Data = nba
                }
            });

        return sessionManagerMock;
    }

    private void SwapMockedServices(Mock<ISessionManager> sessionManagerMock, NextBestAction nba, out Mock<INextBestActionService> nbaServiceMock)
    {
        var loggingService = new Mock<ILoggingService>();
        nbaServiceMock = new Mock<INextBestActionService>();

        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        var usagesRepositoryMock = new Mock<IDcUsagesRepository>();
        var storageServiceMock = new Mock<IStorageService>();

        customersRepositoryMock.SetupCustomerTestData(123, 1);

        var validator = new Mock<IDialogValidators>();
        validator.Setup(x => x.CustomerDialogValidator)
            .Returns(new CustomerDialogValidator(sessionManagerMock.Object));
        validator.Setup(x => x.FinancialsDialogValidator)
            .Returns(new FinancialsDialogValidator(sessionManagerMock.Object));
        validator.Setup(x => x.ProductsDialogValidator)
            .Returns(new ProductsDialogValidator(sessionManagerMock.Object));

        nbaServiceMock.Setup(m => m.GetNextBestActions(It.IsAny<DialogData>(), It.IsAny<bool>()))
            .ReturnsAsync(nba);

        var finServiceMock = new Mock<IFinancialsService>();
        finServiceMock.Setup(m => m.GetFinancialsPreferences(It.IsAny<DialogData>(), It.IsAny<int>()))
            .ReturnsAsync(new FinancialPreferences
            {
                BankAccount = new BankAccount
                {
                    Number = "*****************"
                }
            });

        TextLabelGroupName = "Bot_NextBestActionDialog";
        GetAllTextLabelsFromExport();

        var storageRepoMock = new Mock<IDcStorageRepository>();
        storageRepoMock.Setup(x => x.GetTextlabels(It.IsAny<Label>(), It.IsAny<RequestDataTextLabelRequest>(), It.IsAny<bool>()))
        .ReturnsAsync(
            new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataIListTextLabelModel
                {
                    Data = TextLabelModels?.ToList()
                }
            });

        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), It.IsAny<List<string>>(), It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));

        usagesRepositoryMock.Setup(m => m.GetSmartMeterInterruption(It.IsAny<Label>(), It.IsAny<Telemetry.Models.BotChannel>(), It.IsAny<long>(), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Body = new DC.Usages.Client.Models.ResponseDataSmartMeterInterruptionModel
                {
                    Data = new DC.Usages.Client.Models.SmartMeterInterruptionModel
                    {
                        HasSmartMeter = true,
                        SmartMeterHasInterruption = true,
                        InterruptionCode = "code"
                    }
                },
                Response = new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK
                }
            });

        usagesRepositoryMock.Setup(m => m.GetMandate(It.IsAny<Label>(), It.IsAny<Telemetry.Models.BotChannel>(), It.IsAny<long>(), It.IsAny<int>(), DC.Usages.Client.Models.ServiceProductType.Isma))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Body = new DC.Usages.Client.Models.ResponseDataListServiceProductVersionModel
                {
                    Data = new List<DC.Usages.Client.Models.ServiceProductVersionModel>
                    {
                        new DC.Usages.Client.Models.ServiceProductVersionModel
                        {
                            IsActive = true,
                            IsAvailable = true,
                            ProductType = DC.Usages.Client.Models.ServiceProductType.Isma
                        }
                    }
                },
                Response = new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK
                }
            });

        var localNbaServiceMock = nbaServiceMock;

        _services.SwapTransient(provider => loggingService.Object);
        _services.SwapTransient(provider => sessionManagerMock.Object);
        _services.SwapTransient(provider => localNbaServiceMock.Object);
        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => validator.Object);
        _services.SwapTransient(provider => finServiceMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
        _services.SwapTransient(provider => storageRepoMock.Object);
        _services.SwapTransient(provider => usagesRepositoryMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }
}
