﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Dialogs.Financials.Validators;
using DC.Bot.BusinessLogic.Dialogs.Products.Validators;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Domain.Models.Products;
using DC.Domain.Models.Products.ProductRates;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Microsoft.Rest;
using Moq;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using DiscontinueProductType = DC.Domain.Models.Products.DiscontinueProductType;
using TransactionStatus = DC.Bot.BusinessLogic.Enumerations.TransactionStatus;

namespace DC.Bot.Tests.Dialogs.Products;

[Collection("Sequential")]
public class CancelServiceContractTests : TestBase
{
    [Fact]
    public async Task HappyFlow()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 1, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._DISCONTINUE_SERVICE_CONTRACT)
            .ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("FoundContract", TextLabelGroupName)
            .Replace("{productname}", "KetelComfort"));
        testClient.GetNextReply().Text.Should()
            .StartWith(GetTextLabelValue("DiscontinuePromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("DiscontinueReasonPromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("Beter aanbod");
        answer.Text.Should().StartWith(GetTextLabelValue("DiscontinueContract", TextLabelGroupName));
        testClient.GetNextReply().Text.Should()
            .StartWith(GetTextLabelValue("DiscontinueContractEnds", TextLabelGroupName));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Success,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Theory]
    [InlineData(DiscontinueDisallowReason.NoActiveProduct, "NoActiveProduct")]
    [InlineData(DiscontinueDisallowReason.CancelledAlready, "CancelledAlready")]
    [InlineData(DiscontinueDisallowReason.MoreThanOneMatchedProduct,
        "MoreThanOneMatchedProduct")]
    public async Task IntakeFailedResults(DiscontinueDisallowReason reason, string key)
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 2, 1, reason);

        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._DISCONTINUE_SERVICE_CONTRACT)
            .ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue(key, TextLabelGroupName));
        testClient.GetNextReply().Text.Should().StartWith(
            reason == DiscontinueDisallowReason.MoreThanOneMatchedProduct
                ? GetTextLabelValue("MoreThanOneMatchedProductContactUs", TextLabelGroupName)
                : GetTextLabelValue("ChooseContactIfIncorrect", TextLabelGroupName));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                reason == DiscontinueDisallowReason.MoreThanOneMatchedProduct
                    ? TransactionStatus.MBFChat
                    : TransactionStatus.Unhappy,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task IntakeFailedStillInContractPeriod()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 2, 1, DiscontinueDisallowReason.MinimumContractPeriod);

        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._DISCONTINUE_SERVICE_CONTRACT)
            .ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("FoundContract", TextLabelGroupName)
            .Replace("{productname}", "KetelComfort"));
        testClient.GetNextReply().Text.Should()
            .StartWith(GetTextLabelValue("MinimumContractPeriodHasStartDate", TextLabelGroupName)
            .Replace("{startDate}", "31-12-2021"));
        testClient.GetNextReply().Text.Should()
            .StartWith(GetTextLabelValue("MinimumContractPeriodWantToDiscontinue", TextLabelGroupName));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Unhappy,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task WontCancelAnyway()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 1, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._DISCONTINUE_SERVICE_CONTRACT)
            .ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("FoundContract", TextLabelGroupName)
            .Replace("{productname}", "KetelComfort"));
        testClient.GetNextReply().Text.Should()
            .StartWith(GetTextLabelValue("DiscontinuePromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("nee");
        answer.Text.Should().StartWith(GetTextLabelValue("WantsToCancel", TextLabelGroupName));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Success,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task CancelDueToMovingOutGivesMBFChat()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 1, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._DISCONTINUE_SERVICE_CONTRACT)
            .ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("FoundContract", TextLabelGroupName)
            .Replace("{productname}", "KetelComfort"));
        testClient.GetNextReply().Text.Should()
            .StartWith(GetTextLabelValue("DiscontinuePromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("DiscontinueReasonPromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("Verhuizen");
        answer.Text.Should().StartWith(GetTextLabelValue("ProductDiscontinueReasonMoveOut", TextLabelGroupName));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.MBFChat,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task BadRequestOnDisContinueConfirm()
    {
        var sessionManager = GetSessionsManager(1);
        SwapMockedServices(sessionManager, 3, 1);

        DialogTestClient testClient = SetupMainDialog();
        IMessageActivity answer;

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._DISCONTINUE_SERVICE_CONTRACT)
            .ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("FoundContract", TextLabelGroupName)
            .Replace("{productname}", "KetelComfort"));
        testClient.GetNextReply().Text.Should()
            .StartWith(GetTextLabelValue("DiscontinuePromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("ja");
        answer.Text.Should().StartWith(GetTextLabelValue("DiscontinueReasonPromptText", TextLabelGroupName));

        answer = await testClient.SendActivityAsync("Beter aanbod");
        answer.Text.Should().StartWith(GetTextLabelValue("DiscontinueContractNotAllowed", TextLabelGroupName));

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.PermanentFailure,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task HasNoCustomerId()
    {
        var sessionManager = GetSessionsManager(null);
        SwapMockedServices(sessionManager, 0, 0);

        DialogTestClient testClient = SetupMainDialog();

        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._DISCONTINUE_SERVICE_CONTRACT)
            .ConfigureAwait(true);
        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("CouldNotFindData", "Bot_GeneralTextLabels"));
    }

    private void SwapMockedServices(Mock<ISessionManager> sessionManagerMock, long customerId, int accountId,
        DiscontinueDisallowReason reason = DiscontinueDisallowReason.MinimumContractPeriod)
    {
        var customersRepositoryMock = new Mock<IDcCustomersRepository>();
        customersRepositoryMock.SetupCustomerTestData(customerId, accountId);

        var productsRepoMock = new Mock<IDcProductsRepository>();
        var storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_DiscontinueServiceContractDialog";
        GetAllTextLabelsFromExport();
        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(TextLabelModels?.ToList()));

        productsRepoMock.Setup(x => x.GetProductRates(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataProductRates
                {
                    Data = new ProductRates
                    {
                        Rates = new List<ProductRate>
                        {
                            new ProductRate
                            {
                                AgreementId = 2,
                                ProductType = ProductType.Toon,
                                ProductRateCode = "T2001"
                            }
                        }
                    }
                }
            });

        productsRepoMock.Setup(x => x.GetProductDiscontinueIntake(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 1 || x == 3),
                DiscontinueProductType.ServiceContract))
            .ReturnsAsync(GetResponse(HttpStatusCode.Accepted, GetIntakeOkResult()));

        productsRepoMock.Setup(x => x.GetProductDiscontinueIntake(
                Label.Eneco, It.IsAny<BotChannel>(), It.Is<long>(x => x == 2),
                DiscontinueProductType.ServiceContract))
            .ReturnsAsync(GetResponse(HttpStatusCode.Accepted, GetIntakeFailedResult(reason)));

        if (customerId is 1)
            productsRepoMock.Setup(x => x.PutProductOrderV2(
                    Label.Eneco, It.IsAny<RequestDataProductOrderV2Model>()))
                .ReturnsAsync(GetResponse(HttpStatusCode.Accepted, GetConfirmOkResult()));

        if (customerId is 2 or 3)
            productsRepoMock.Setup(x => x.PutProductOrderV2(
                    Label.Eneco, It.IsAny<RequestDataProductOrderV2Model>()))
                .ReturnsAsync(GetResponse(HttpStatusCode.BadRequest, GetConfirmErrorResponse()));

        var validator = new Mock<IDialogValidators>();
        var loggingService = new Mock<ILoggingService>();
        validator.Setup(x => x.CustomerDialogValidator)
            .Returns(new CustomerDialogValidator(sessionManagerMock.Object));
        validator.Setup(x => x.FinancialsDialogValidator)
            .Returns(new FinancialsDialogValidator(sessionManagerMock.Object));
        validator.Setup(x => x.ProductsDialogValidator)
            .Returns(new ProductsDialogValidator(sessionManagerMock.Object));

        _services.SwapTransient(provider => loggingService.Object);
        _services.SwapTransient(provider => validator.Object);
        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => productsRepoMock.Object);
        _services.SwapTransient(provider => sessionManagerMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }

    private static Mock<ISessionManager> GetSessionsManager(long? customerId)
    {
        var sessionManagerMock = new Mock<ISessionManager>();
        sessionManagerMock.Setup(x =>
                x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Customer = new CustomerData
                {
                    Label = Label.Eneco
                },
                Verification = new VerificationData
                {
                    CustomerId = customerId,
                    CustomerIdVerified = customerId.HasValue
                }
            });

        return sessionManagerMock;
    }

    private static HttpOperationResponse<object> GetResponse(HttpStatusCode statuscode, object body)
    {
        return new HttpOperationResponse<object>
        {
            Response = new System.Net.Http.HttpResponseMessage
            {
                StatusCode = statuscode
            },
            Body = body
        };
    }

    private static ResponseDataDiscontinueIntake GetIntakeOkResult()
    {
        return new ResponseDataDiscontinueIntake
        {
            Data = new DiscontinueIntake
            {
                Allowed = true,
                ProductName = "KetelComfort",
                Accounts = new List<DiscontinueAddress>()
                {
                    new DiscontinueAddress
                    {
                        AccountId = 1,
                        AgreementId = 2,
                        HouseNumber = 3,
                        PostalCode = "1010AA"
                    }
                },
                Reasons = new List<Reason>
                {
                    new Reason
                    {
                        Text = "Beter aanbod",
                        Code = "GDN",
                        Explanation = true
                    },
                    new Reason
                    {
                        Text = "Verhuizen",
                        Code = "VER",
                        Explanation = true
                    }
                }
            }
        };
    }

    private static ResponseDataDiscontinueIntake GetIntakeFailedResult(DiscontinueDisallowReason reason)
    {
        return new ResponseDataDiscontinueIntake
        {
            Data = new DiscontinueIntake
            {
                Allowed = false,
                ProductName = "KetelComfort",
                DiscontinueDisallowReason = reason,
                Accounts = new List<DiscontinueAddress>()
                {
                    new DiscontinueAddress
                    {
                        AccountId = 1,
                        AgreementId = 2,
                        HouseNumber = 3,
                        PostalCode = "1010AA",
                        StartDate = new System.DateTime(2020, 12, 31)
                    }
                }
            }
        };
    }

    private static ResponseDataString GetConfirmOkResult()
    {
        return new ResponseDataString
        {
            Data = ""
        };
    }

    private static Domain.Exceptions.ResponseModels.ErrorResponse GetConfirmErrorResponse()
    {
        return new Domain.Exceptions.ResponseModels.ErrorResponse
        {
            Errors = new List<Domain.Exceptions.ResponseModels.ErrorModel>
            {
                new Domain.Exceptions.ResponseModels.ErrorModel
                {
                    Code = "123",
                    Details = "BadRequest",
                    Type = "FunctionalException"
                }
            }
        };
    }
}
