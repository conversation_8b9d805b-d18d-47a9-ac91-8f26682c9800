﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Bot.Tests.Dialogs.Extensions;
using DC.Bot.Tests.Extensions;
using DC.Domain.Exceptions;
using DC.Domain.Models.Products;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using FluentAssertions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Microsoft.Rest;
using Moq;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Products;

[Collection("Sequential")]
public class ZonOpDakDialogTests : TestBase
{
    private const int _User_UnhappyFlow = 666;
    private const int _User_CouldNotFindCustomer = 1;
    private const int _User_DefiniteContract_After_1_June = 16;
    private const int _User_DefiniteContract_Before_1_June = 20;
    private const int _User_DefiniteContract_DynamicPricing = 16;
    private const int _User_DefiniteContract_IndefiniteContract = 17;
    private const int _User_DefiniteContract_ModelContract = 15;
    private const int _User_DefiniteContract_MultipleAccounts = 10;
    private const int _User_DefiniteContract_NoElectricity = 14;
    private const int _User_DefiniteContract_WithInActiveAccount = 2;
    private const int _User_DefiniteContract_WithoutContract = 1;

    private readonly Mock<IDcCustomersRepository> customersRepositoryMock;
    private readonly Mock<IDcProductsRepository> productsRepositoryMock;
    private readonly Mock<IDcUserAccountsRepository> userAccountRepMock;
    private readonly Mock<IStorageService> storageServiceMock;

    public ZonOpDakDialogTests()
    {
        customersRepositoryMock = new Mock<IDcCustomersRepository>();
        productsRepositoryMock = new Mock<IDcProductsRepository>();
        userAccountRepMock = new Mock<IDcUserAccountsRepository>();
        storageServiceMock = new Mock<IStorageService>();

        TextLabelGroupName = "Bot_ZonOpDakDialog";
    }

    [Fact]
    public async Task ZonOpDak_UnhappyFlow()
    {
        var sessionManager = GetSessionsManager(_User_UnhappyFlow);

        //Arrange
        SwapMockedServices(sessionManager, _User_UnhappyFlow);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ZON_OP_DAK);

        var errorText = testClient.GetNextReply().Text;
        errorText.Should().StartWith(GetTextLabelValue("SomethingWentWrong", "Bot_GeneralTextLabels"));
    }

    [Fact]
    public async Task ZonOpDak_with_CouldNotFindCustomer()
    {
        var sessionManager = GetSessionsManager(_User_CouldNotFindCustomer);
        //Arrange
        SwapMockedServices(sessionManager, _User_CouldNotFindCustomer);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithCouldNotFindCustomer(DialogCommands._ZON_OP_DAK);
        var result = GetTextLabelValue("CouldNotFindData", "Bot_GeneralTextLabels");
        string replyNext = testClient.GetNextReply()?.Text;
        Assert.Contains(result, replyNext);
    }

    [Fact]
    public async Task ZonOpDak_with_VerifiedCustomer_DefiniteContract_After_1_June()
    {
        var sessionManager = GetSessionsManager(_User_DefiniteContract_After_1_June);
        //Arrange
        SwapMockedServices(sessionManager, _User_DefiniteContract_After_1_June);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ZON_OP_DAK);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains($"Einddatum: {DateTime.Today.AddDays(100):dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ZonOpDakElectricityDefiniteAfter1June,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task ZonOpDak_with_VerifiedCustomer_DefiniteContract_Before_1_June()
    {
        var sessionManager = GetSessionsManager(_User_DefiniteContract_Before_1_June);
        //Arrange
        SwapMockedServices(sessionManager, _User_DefiniteContract_Before_1_June);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ZON_OP_DAK);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains($"Einddatum: {DateTime.Now:dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ZonOpDakElectricityDefiniteBefore1June,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task ZonOpDak_with_VerifiedCustomer_DynamicPricing()
    {
        var sessionManager = GetSessionsManager(_User_DefiniteContract_DynamicPricing);
        //Arrange
        SwapMockedServices(sessionManager, _User_DefiniteContract_DynamicPricing, hasDynamicPricing: true);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ZON_OP_DAK);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains($"Einddatum: {DateTime.Today.AddDays(100):dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ZonOpDakElectricityDynamicPricing,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task ZonOpDak_with_VerifiedCustomer_IndefiniteContract()
    {
        var sessionManager = GetSessionsManager(_User_DefiniteContract_IndefiniteContract);
        //Arrange
        SwapMockedServices(sessionManager, _User_DefiniteContract_IndefiniteContract);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ZON_OP_DAK);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains($"Einddatum: {DateTime.Now:dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ZonOpDakElectricityIndefinite,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task ZonOpDak_with_VerifiedCustomer_ModelContract()
    {
        var sessionManager = GetSessionsManager(_User_DefiniteContract_ModelContract);
        //Arrange
        SwapMockedServices(sessionManager, _User_DefiniteContract_ModelContract);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ZON_OP_DAK);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains($"Einddatum: {DateTime.Today.AddDays(100):dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ZonOpDakElectricityTemplateContract,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task ZonOpDak_with_VerifiedCustomer_MultipleAccounts()
    {
        var sessionManager = GetSessionsManager(_User_DefiniteContract_MultipleAccounts);
        //Arrange
        SwapMockedServices(sessionManager, _User_DefiniteContract_MultipleAccounts, multiAccountZonOpDak: true);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ZON_OP_DAK);

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.Success,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task ZonOpDak_with_VerifiedCustomer_NoElectricity()
    {
        var sessionManager = GetSessionsManager(_User_DefiniteContract_NoElectricity);
        //Arrange
        SwapMockedServices(sessionManager, _User_DefiniteContract_NoElectricity);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ZON_OP_DAK);

        var endDateText = testClient.GetNextReply().Text;

        endDateText.Contains("Product A");
        endDateText.Contains($"Einddatum: {DateTime.Today.AddDays(100):dd-MM-yyyy} ");

        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ZonOpDakNoElectricity,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    [Fact]
    public async Task ZonOpDak_with_VerifiedCustomer_WithInActiveAccount()
    {
        var sessionManager = GetSessionsManager(_User_DefiniteContract_WithInActiveAccount);
        //Arrange
        SwapMockedServices(sessionManager, _User_DefiniteContract_WithInActiveAccount, false);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ZON_OP_DAK);

        testClient.GetNextReply().Text.Should().StartWith(GetTextLabelValue("CouldNotFindActiveAccount", "Bot_GeneralTextLabels"));
    }

    [Fact]
    public async Task ZonOpDak_with_VerifiedCustomer_WithoutContract()
    {
        var sessionManager = GetSessionsManager(_User_DefiniteContract_WithoutContract);
        //Arrange
        SwapMockedServices(sessionManager, _User_DefiniteContract_WithoutContract);
        DialogTestClient testClient = SetupMainDialog();

        //Act & Assert
        await testClient.StartDialogWithVerifiedCustomer(DialogCommands._ZON_OP_DAK);
        sessionManager.Verify(mock =>
            mock.SendEndOfTransactionActivity(
                It.IsAny<ITurnContext>(),
                It.IsAny<string>(), It.IsAny<string>(),
                TransactionStatus.ZonOpDakNoElectricity,
                It.IsAny<CancellationToken>()), Times.Once());
    }

    private void SwapMockedServices(Mock<ISessionManager> sessionManagerMock, long customerId, bool activeAccount = true, bool multiAccountZonOpDak = false, bool hasDynamicPricing = false)
    {
        

        storageServiceMock.Setup(x => x.GetTextLabels(It.IsAny<Label>(), It.IsAny<string>(), null, It.IsAny<bool>()))
            .ReturnsAsync(new TextLabelCollection(new List<Storage.Client.Models.TextLabelModel>
            {

            }));
        customersRepositoryMock.SetupCustomerTestData(customerId: customerId, activeAccount: activeAccount, multiAccountZonOpDak: multiAccountZonOpDak, hasDynamicPricing: hasDynamicPricing);

        //If multiple account needs to be more than 3: new business rule


        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == _User_UnhappyFlow), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .Throws(new TechnicalException(new Guid("********-8e7a-4a4e-902d-681f9063c1ff"), "Error, customer 666 is evil"));

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == _User_DefiniteContract_Before_1_June), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = null,
                            StartDate = new DateTime(2024, 5, 20).AddMonths(-1),
                            Duration = 1,
                            EndDateContract = new DateTime(2024, 5, 20), // Before 1st June
                            Indefinite = false
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Product B",
                            StartDate = new DateTime(2024, DateTime.Today.Month, 1).AddMonths(-1),
                            EndDatePrice = null,
                            PriceDeterminationDate = null,
                            EndDateContract = new DateTime(2024, DateTime.Today.Month, 1).AddMonths(1),
                            Duration = 1,
                            Indefinite = false
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.StukjeZon
                            },
                            Description = "StukjeZon"
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == _User_DefiniteContract_IndefiniteContract), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(60),
                            EndDateContract = null,
                            Duration = 1
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == _User_DefiniteContract_After_1_June), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(100),
                            EndDateContract = DateTime.Today.AddDays(100),
                            Duration = 3
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
               x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == _User_DefiniteContract_DynamicPricing), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
           .ReturnsAsync(new HttpOperationResponse<object>
           {
               Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
               Body = new ResponseDataListProductModel
               {
                   Data = new List<ProductModel>
                   {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(100),
                            EndDateContract = DateTime.Today.AddDays(100),
                            Duration = 3
                        }
                   }
               }
           });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == _User_DefiniteContract_ModelContract), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(60),
                            EndDateContract = DateTime.Today.AddDays(60),
                            Duration = 0 //Model Contract duration = 0
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == _User_DefiniteContract_NoElectricity), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Product B",
                            EndDatePrice = DateTime.Today.AddDays(100),
                            EndDateContract = null,
                            PriceDeterminationDate = DateTime.Now.AddMonths(-6),
                            Duration = 12
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == _User_DefiniteContract_MultipleAccounts), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Today.AddDays(60),
                            EndDateContract = null
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Warmth,
                            },
                            Description = "Product B",
                            EndDatePrice = DateTime.Today.AddDays(60),
                            EndDateContract = null,
                            Duration = 12
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == _User_DefiniteContract_WithInActiveAccount), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<ProductModel>
                    {
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Electricity,
                            },
                            Description = "Product A",
                            EndDatePrice = DateTime.Now
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.Gas,
                            },
                            Description = "Product B",
                            EndDatePrice = DateTime.Now,
                            PriceDeterminationDate = DateTime.Now.AddMonths(-6),
                            EndDateContract = DateTime.Now,
                            Duration = 12
                        },
                        new ProductModel
                        {
                            Type = new ProductTypeModel
                            {
                                Name = ProductType.StukjeZon
                            },
                            Description = "StukjeZon"
                        }
                    }
                }
            });

        productsRepositoryMock.Setup(x =>
                x.GetCustomerAccountProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<long>(x => x == _User_CouldNotFindCustomer || x == _User_DefiniteContract_WithoutContract), It.IsAny<int>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = null
                }
            });

        var validator = new Mock<IDialogValidators>();
        var loggingService = new Mock<ILoggingService>();
        validator.SetupDialogValidatorWithSessionManager(customerId);
        _services.SwapTransient(provider => validator.Object);

        _services.SwapTransient(provider => loggingService.Object);
        _services.SwapTransient(provider => customersRepositoryMock.Object);
        _services.SwapTransient(provider => productsRepositoryMock.Object);
        _services.SwapTransient(provider => userAccountRepMock.Object);
        _services.SwapTransient(provider => sessionManagerMock.Object);
        _services.SwapTransient(provider => storageServiceMock.Object);
    }

    private static Mock<ISessionManager> GetSessionsManager(long customerId, Label label = Label.Eneco)
    {
        var sessionManagerMock = new Mock<ISessionManager>();
        sessionManagerMock.Setup(x =>
                x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Customer = new CustomerData
                {
                    Label = label
                },
                Verification = new VerificationData
                {
                    CustomerId = customerId,
                    CustomerIdVerified = true
                }
            });

        return sessionManagerMock;
    }
}
