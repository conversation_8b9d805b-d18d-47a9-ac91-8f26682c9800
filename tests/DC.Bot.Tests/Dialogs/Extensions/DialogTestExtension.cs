﻿using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Dialogs.Financials.Validators;
using DC.Bot.BusinessLogic.Dialogs.Products.Validators;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.Customers.Client.Models;
using DC.Domain.Models.Accounts;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.Customers;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using DC.Utilities.Environments;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Testing;
using Microsoft.Bot.Schema;
using Microsoft.Rest;
using Moq;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;
using AddressModel = DC.Customers.Client.Models.AddressModel;
using Gender = DC.Domain.Models.Customers.Gender;

namespace DC.Bot.Tests.Dialogs.Extensions;

public static class DialogTestExtension
{
    public static PromptOptions GetDialogPromptOptions(this DialogTestClient testClient)
    {
        try
        {
            var stack = testClient.DialogContext?.Child?.Stack?
                .Find(x => x.Id == testClient.DialogContext?.Child?.ActiveDialog?.Id);
            var state = (DialogState)stack?.State?.First().Value;
            var instance = state?.DialogStack?[0];

            return (PromptOptions)instance?.State.First().Value;
        }
        catch (Exception)
        {
            return null;
        }
    }

    public static async Task StartDialogWithVerifiedCustomer(this DialogTestClient testClient, string startCommand)
    {
        var reply = await testClient.SendActivityAsync(startCommand).ConfigureAwait(true);
        Assert.Equal("Daarvoor moet ik je eerst even een paar vragen stellen om te weten wie je bent.", reply.Text);
        Assert.Equal("Wat is je postcode?", testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("3025CB").ConfigureAwait(true);
        Assert.Equal("En wat is je huisnummer, zonder toevoeging?", reply.Text);

        reply = await testClient.SendActivityAsync("9").ConfigureAwait(true);
        if (DigitalCoreEnvironment.IsProduction())
        {
            Activity activity = GetMfaSuccessActivity();
            reply = await testClient.SendActivityAsync<Activity>(activity: activity, cancellationToken: default).ConfigureAwait(true);
        }

        Assert.Equal("Ik heb je gegevens gevonden. Goedendag Paul Testman.", reply.Text);
    }

    public static async Task StartDialogUnverifiedCustomer(this DialogTestClient testClient, string startCommand)
    {
        var reply = await testClient.SendActivityAsync(startCommand).ConfigureAwait(true);
        Assert.Equal("Daarvoor moet ik je eerst even een paar vragen stellen om te weten wie je bent.", reply.Text);
        Assert.Equal("Wat is je postcode?", testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("3025CB").ConfigureAwait(true);
        Assert.Equal("En wat is je huisnummer, zonder toevoeging?", reply.Text);

        reply = await testClient.SendActivityAsync("8").ConfigureAwait(true);
        Assert.Equal("Ik heb je helaas niet gevonden op dit adres. Probeer het opnieuw met het adres dat op je contract staat.", reply.Text);
        Assert.Equal("Wat is je postcode?", testClient.GetNextReply().Text);

        reply = await testClient.SendActivityAsync("3025CB").ConfigureAwait(true);
        Assert.Equal("En wat is je huisnummer, zonder toevoeging?", reply.Text);

        reply = await testClient.SendActivityAsync("8").ConfigureAwait(true);
        Assert.Equal("Ik heb je helaas niet gevonden op dit adres. Probeer het opnieuw met het adres dat op je contract staat.", reply.Text);
    }

    public static async Task StartDialogWithUnverifiedCustomer(this DialogTestClient testClient, string startCommand)
    {
        var reply = await testClient.SendActivityAsync(startCommand).ConfigureAwait(true);
        Assert.Equal("Daarvoor moet ik je eerst even een paar vragen stellen om te weten wie je bent.", reply.Text);
        Assert.Equal("Wat is je postcode?", testClient.GetNextReply()?.Text);

        reply = await testClient.SendActivityAsync("3025CB").ConfigureAwait(true);
        Assert.Equal("En wat is je huisnummer, zonder toevoeging?", reply.Text);

        reply = await testClient.SendActivityAsync("10").ConfigureAwait(true);

        Assert.Equal("Ik heb je gegevens gevonden. Goedendag Paul Testman.", reply.Text);
    }

    public static async Task StartDialogWithCouldNotFindCustomer(this DialogTestClient testClient, string startCommand)
    {
        var reply = await testClient.SendActivityAsync(startCommand).ConfigureAwait(true);
        Assert.Equal("Daarvoor moet ik je eerst even een paar vragen stellen om te weten wie je bent.", reply.Text);
        Assert.Equal("Wat is je postcode?", testClient.GetNextReply()?.Text);

        reply = await testClient.SendActivityAsync("3025CB").ConfigureAwait(true);
        Assert.Equal("En wat is je huisnummer, zonder toevoeging?", reply.Text);

        reply = await testClient.SendActivityAsync("10").ConfigureAwait(true);
        Assert.Equal("Ik heb je gegevens gevonden. Goedendag Paul Testman.", reply.Text);
    }

    public static void SetupCustomerTestData(this Mock<IDcCustomersRepository> customersRepositoryMock,
        long customerId = 1,
        int accountId = 2,
        bool activeAccount = true,
        bool multiAccount = false,
        DateTime? nextChargeDate = null,
        int houseNumber = 9,
        bool returnsError = false,
        string email = "",
        Label label = Label.Eneco,
        bool hasDynamicPricing = false,
        bool multiAccountZonOpDak = false)
    {
        var productsRepositoryMock = new Mock<IDcProductsRepository>();

        customersRepositoryMock.Setup(x =>
                x.VerifyCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<RequestDataCustomerVerification>(x => x.Data.Addresses.First().HouseNumber == 9)))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataCustomerVerificationResponse
                {
                    Data = new CustomerVerificationResponse(customerId,
                    ValidationStatus.Match,
                    ValidationStatus.Match,
                    ValidationStatus.Match,
                    ValidationStatus.Match,
                    ValidationStatus.Match,
                    true,
                    true,
                    false)
                    {

                    }
                }
            });

        customersRepositoryMock.Setup(x =>
                x.VerifyCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<RequestDataCustomerVerification>(x => x.Data.Addresses.First().HouseNumber == 8)))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.NoContent }
            });

        customersRepositoryMock.Setup(x =>
                x.VerifyCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.Is<RequestDataCustomerVerification>(x => x.Data.Addresses.First().HouseNumber == 10)))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataCustomerVerificationResponse
                {
                    Data = new CustomerVerificationResponse(0,
                    ValidationStatus.Match,
                    ValidationStatus.Match,
                    ValidationStatus.Match,
                    ValidationStatus.Match,
                    ValidationStatus.Match,
                    true,
                    true,
                    false)
                    {

                    }
                }
            });
        customersRepositoryMock.Setup(x => x.GetAgreements(
            It.IsAny<Label>(),
            It.IsAny<BotChannel>(),
            It.IsAny<long>(),
            It.IsAny<int>(),
            It.IsAny<bool?>(),
            It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Body = new ResponseDataListAgreement
                {
                    Data = new List<Agreement> {
                        new Agreement
                        {
                            AccountId = 1,
                            IsActive = true,
                            Products = new List<Domain.Models.Agreements.Product>
                            {
                                new Domain.Models.Agreements.Product {
                                    AccountId= 1,
                                    AgreementId = 1,
                                    ProductType = Domain.Models.Products.ProductType.Electricity,
                                    IsActive = true,
                                }
                            },
                            Connection = new Connection
                            {
                                ConnectionPointEan = "ean",
                                AccountId = 1,
                                GridOperator = new DistributionNetworkOperator
                                {
                                    Name = "netbeheerder"
                                }
                            }
                        }
                    }
                },
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                }
            });

        customersRepositoryMock.Setup(x => x.GetCustomerAgreements(
            It.IsAny<Label>(),
            It.IsAny<BotChannel>(),
            It.IsAny<long>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Body = new ResponseDataListAgreement
                {
                    Data = new List<Agreement> {
                        new Agreement
                        {
                            AccountId = 1,
                            IsActive = true,
                            Products = new List<Domain.Models.Agreements.Product>
                            {
                                new Domain.Models.Agreements.Product {
                                    AccountId= 1,
                                    AgreementId = 1,
                                    ProductType = Domain.Models.Products.ProductType.Electricity,
                                    IsActive = true,
                                }
                            },
                            Connection = new Connection
                            {
                                ConnectionPointEan = "ean",
                                AccountId = 1,
                                GridOperator = new DistributionNetworkOperator
                                {
                                    Name = "netbeheerder"
                                }
                            }
                        }
                    }
                },
                Response = new System.Net.Http.HttpResponseMessage
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                }
            });
        if (multiAccount)
        {
            customersRepositoryMock.Setup(x =>
                    x.GetCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataCustomerModel
                    {
                        Data = new CustomerModel
                        {
                            Id = (int)customerId,
                            Person = new PersonModel
                            {
                                Initials = "Paul",
                                Surname = "Testman",
                                Gender = Gender.Male
                            },
                            Contact = new ContactModel
                            {
                                EmailAddress = email
                            },
                            CustomerType = CustomerType.Person,
                            Accounts = new List<CustomerAccountModel>
                                {
                                    new CustomerAccountModel
                                    {
                                        Active = activeAccount,
                                        Id = accountId,
                                        NextChargeDate = nextChargeDate,
                                        Address = new AddressModel
                                        {
                                            PostalCode = "3025CB",
                                            HouseNumber = 9,
                                            Street = "Straat",
                                            City = "Roffa"
                                        },
                                        Bankaccount = new BankaccountModel
                                        {
                                            Bank = "ABN Amro",
                                            Country = "Nederland",
                                            Number = "******************",
                                            Holder = "P Testman",
                                            IsStandard = true
                                        },
                                        CustomerProfileType = CustomerProfileType.NonActive,
                                        HasRedelivery = false,
                                        CanInsertReadings = false,
                                        HasDynamicPricing = hasDynamicPricing
                                    },
                                    new CustomerAccountModel
                                    {
                                        Active = true,
                                        HasDynamicPricing = hasDynamicPricing,
                                        Id = accountId,
                                        NextChargeDate = nextChargeDate,
                                        Address = new AddressModel
                                        {
                                            PostalCode = "3025CB",
                                            HouseNumber = 9,
                                            Street = "Straat",
                                            City = "Roffa"
                                        },
                                        Bankaccount = new BankaccountModel
                                        {
                                            Bank = "ABN Amro",
                                            Country = "Nederland",
                                            Number = "******************",
                                            Holder = "P Testman",
                                            IsStandard = true
                                        }
                                    }
                                }
                        }
                    }
                }
                );
        }
        else if (multiAccountZonOpDak) //Needs more than 3 accounts
        {
            customersRepositoryMock.Setup(x =>
                    x.GetCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataCustomerModel
                    {
                        Data = new CustomerModel
                        {
                            Id = (int)customerId,
                            Person = new PersonModel
                            {
                                Initials = "Paul",
                                Surname = "Testman",
                                Gender = Gender.Male
                            },
                            Contact = new ContactModel
                            {
                                EmailAddress = email
                            },
                            CustomerType = CustomerType.Person,
                            Accounts = new List<CustomerAccountModel>
                                {
                                    new CustomerAccountModel
                                    {
                                        Active = activeAccount,
                                        Id = accountId,
                                        NextChargeDate = nextChargeDate,
                                        Address = new AddressModel
                                        {
                                            PostalCode = "3025CB",
                                            HouseNumber = 9,
                                            Street = "Straat",
                                            City = "Roffa"
                                        },
                                        Bankaccount = new BankaccountModel
                                        {
                                            Bank = "ABN Amro",
                                            Country = "Nederland",
                                            Number = "******************",
                                            Holder = "P Testman",
                                            IsStandard = true
                                        },
                                        CustomerProfileType = CustomerProfileType.NonActive,
                                        HasRedelivery = false,
                                        CanInsertReadings = false,
                                        HasDynamicPricing = hasDynamicPricing
                                    },
                                    new CustomerAccountModel
                                    {
                                        Active = true,
                                        HasDynamicPricing = hasDynamicPricing,
                                        Id = accountId,
                                        NextChargeDate = nextChargeDate,
                                        Address = new AddressModel
                                        {
                                            PostalCode = "3025CB",
                                            HouseNumber = 9,
                                            Street = "Straat",
                                            City = "Roffa"
                                        },
                                        Bankaccount = new BankaccountModel
                                        {
                                            Bank = "ABN Amro",
                                            Country = "Nederland",
                                            Number = "******************",
                                            Holder = "P Testman",
                                            IsStandard = true
                                        }
                                    },
                                    new CustomerAccountModel
                                    {
                                        Active = true,
                                        HasDynamicPricing = hasDynamicPricing,
                                        Id = accountId,
                                        NextChargeDate = nextChargeDate,
                                        Address = new AddressModel
                                        {
                                            PostalCode = "3025CB",
                                            HouseNumber = 9,
                                            Street = "Straat",
                                            City = "Roffa"
                                        },
                                        Bankaccount = new BankaccountModel
                                        {
                                            Bank = "ABN Amro",
                                            Country = "Nederland",
                                            Number = "******************",
                                            Holder = "P Testman",
                                            IsStandard = true
                                        }
                                    },
                                    new CustomerAccountModel
                                    {
                                        Active = true,
                                        HasDynamicPricing = hasDynamicPricing,
                                        Id = accountId,
                                        NextChargeDate = nextChargeDate,
                                        Address = new AddressModel
                                        {
                                            PostalCode = "3025CB",
                                            HouseNumber = 9,
                                            Street = "Straat",
                                            City = "Roffa"
                                        },
                                        Bankaccount = new BankaccountModel
                                        {
                                            Bank = "ABN Amro",
                                            Country = "Nederland",
                                            Number = "******************",
                                            Holder = "P Testman",
                                            IsStandard = true
                                        }
                                    }
                                }
                        }
                    }
                }
                );
        }
        else
        {
            customersRepositoryMock.Setup(x =>
                    x.GetCustomer(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                    Body = new ResponseDataCustomerModel
                    {
                        Data = new CustomerModel
                        {
                            Id = (int)customerId,
                            Person = new PersonModel
                            {
                                Initials = "Paul",
                                Surname = "Testman",
                                Gender = Gender.Male
                            },
                            Contact = new ContactModel
                            {
                                EmailAddress = email
                            },
                            CustomerType = CustomerType.Person,
                            Accounts = new List<CustomerAccountModel>
                                {
                                    new CustomerAccountModel
                                    {
                                        Active = activeAccount,
                                        HasDynamicPricing = hasDynamicPricing,
                                        Id = accountId,
                                        NextChargeDate = nextChargeDate,
                                        Address = new AddressModel
                                        {
                                            PostalCode = "3025CB",
                                            HouseNumber = 9,
                                            Street = "Straat",
                                            City = "Roffa"
                                        },
                                        Bankaccount = new BankaccountModel
                                        {
                                            Bank = "ABN Amro",
                                            Country = "Nederland",
                                            Number = "******************",
                                            Holder = "P Testman",
                                            IsStandard = true
                                        }
                                    }
                                }
                        }
                    }
                }
                );
        }

        customersRepositoryMock.Setup(x =>
                x.GetCustomerV2(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataCustomer
                {
                    Data = new Customer
                    {
                        Id = (int)customerId,
                        Person = new Person
                        {
                            Initials = "Paul",
                            Surname = "Testman",
                            Gender = Gender.Male
                        },
                        Contact = new Domain.Models.Customers.Contact
                        {
                            EmailAddress = email
                        },
                        CustomerType = CustomerType.Person
                    }
                }
            });

        productsRepositoryMock.Setup(x => x.GetCustomerProducts(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<bool?>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataListProductModel
                {
                    Data = new List<Domain.Models.Products.ProductModel>()
                }
            });

        customersRepositoryMock.Setup(x => x.GetPaymentPlan(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>(), It.IsAny<int>()))
            .ReturnsAsync(new HttpOperationResponse<object>
            {
                Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
                Body = new ResponseDataPaymentPlan
                {
                    Data = new PaymentPlan
                    {
                        NextBillingCycle = new BillingCycle
                        {
                            ChargeDate = nextChargeDate
                        }
                    }
                }
            });

        customersRepositoryMock.Setup(x => x.GetCustomerOrders(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>()))
           .ReturnsAsync(new HttpOperationResponse<object>
           {
               Response = new HttpResponseMessage { StatusCode = HttpStatusCode.OK },
               Body = new ResponseDataListOrderResponseModel
               {
                   Data = new List<OrderResponseModel>()
                   {

                   }
               }
           });

        if (returnsError)
        {
            customersRepositoryMock.Setup(x =>
                    x.GetCustomerV2(It.IsAny<Label>(), It.IsAny<BotChannel>(), It.IsAny<long>()))
                .ReturnsAsync(new HttpOperationResponse<object>
                {
                    Response = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest }
                });
        }
    }

    public static void SetupDialogValidatorWithSessionManager(
        this Mock<IDialogValidators> validatorMock,
        long customerId,
        Label label = Label.Eneco,
        Mock<ISessionManager> sessionManagerMock = null)
    {
        var sessionMock = sessionManagerMock ?? new Mock<ISessionManager>();
        sessionMock.Setup(x =>
                x.GetDialogData(It.IsAny<ITurnContext>()))
            .ReturnsAsync(new DialogData
            {
                Customer = new CustomerData
                {
                    Label = label
                },
                Verification = new VerificationData
                {
                    CustomerId = customerId
                },
                SelectedAccount = new AccountData
                {
                    AccountId = 1
                }
            });

        validatorMock.Setup(x => x.CustomerDialogValidator).Returns(new CustomerDialogValidator(sessionMock.Object));
        validatorMock.Setup(x => x.FinancialsDialogValidator).Returns(new FinancialsDialogValidator(sessionMock.Object));
        validatorMock.Setup(x => x.ProductsDialogValidator).Returns(new ProductsDialogValidator(sessionMock.Object));
    }

    public static Activity GetMfaSuccessActivity()
    {
        MfaSuccessActivityEntity value = new()
        {
            Text = "123456",
            Success = true,
            Status = "success"
        };

        Activity activity = new()
        {
            Text = "mfa result",
            Type = "message",
            Entities = new List<Entity>()
            {
                new()
                {
                    Properties = JObject.FromObject(new ValueEntity<MfaSuccessActivityEntity>("mfa", value)),
                    Type = "mfa"
                }
            }
        };
        return activity;
    }
}