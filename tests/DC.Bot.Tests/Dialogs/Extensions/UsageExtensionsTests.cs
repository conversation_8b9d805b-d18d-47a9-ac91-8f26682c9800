﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Usages.Client.Models;
using FluentAssertions;
using Xunit;

namespace DC.Bot.Tests.Dialogs.Extensions;

public class UsageExtensionsTests
{
    [Theory]
    [InlineData(1, "Kwh", false)]
    [InlineData(1, "Kwh", true)]
    [InlineData(1, "", true)]
    [InlineData(1, "Kwh", true, true)]
    public void GetDescriptionByMonth_WorksAsExpected(int month, string measureUnit, bool doubleTariff, bool usageItemIsNull = false)
    {
        UsageItem usage = null;
        if (!usageItemIsNull)
            usage = new UsageItem { Low = 50, High = 100, IsDoubleTariff = doubleTariff };

        var result = usage.GetDescriptionByMonth(month, measureUnit);
        if (usageItemIsNull)
            result.Should().BeEmpty();
        else
        {
            result.Should().NotBeEmpty();
            if (!string.IsNullOrWhiteSpace(measureUnit))
                result.Should().Contain(measureUnit);

            if (doubleTariff)
            {
                result.Should().Contain("dal");
                result.Should().Contain("/ normaal");
            }
        }
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("<EMAIL>")]
    public void ToRequestDataReportRequestModel_WorksAsExpected(string email)
    {
        var result = email.ToRequestDataReportRequestModel();

        result.Should().NotBeNull();
        result.Data.Email.Should().Be(email);
    }
}