[{"$id": "1", "id": "f6725519-c3ce-4ac5-abb5-41f798801e9c", "key": "AskCustomerId", "url": null, "filter": "", "language": "Dutch", "value": "Wat is je klantnummer? Je vindt deze onder andere in de app, onze e-mails en op je jaarnota.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "2", "id": "dd85191e-4b06-4558-b53b-417f84aa15a6", "key": "AskDateOfBirth", "url": null, "filter": "", "language": "Dutch", "value": "Welke geboortedatum is bij ons bekend (DD-MM-JJJJ)?", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "3", "id": "f75dc139-4482-4d9f-a9c7-2970312a27e4", "key": "AskHouseNumber", "url": null, "filter": "", "language": "Dutch", "value": "En wat is je huisnummer, zonder toevoeging?", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "4", "id": "cf29d43f-229a-4ef2-94bf-e8f756c3ae1c", "key": "AskIbanDigits", "url": null, "filter": "", "language": "Dutch", "value": "<PERSON><PERSON>, en wat zijn de laatste 3 cijfers van het IBAN-rekeningnummer dat bij ons bekend is?", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "5", "id": "39689c18-a64e-45e3-8302-ad5fc37f2508", "key": "AskPostalCode", "url": null, "filter": "", "language": "Dutch", "value": "Wat is je postcode?", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "6", "id": "18714bfe-680b-49e1-a3e4-f33287bcee7b", "key": "CustomerIdNotFound", "url": null, "filter": "", "language": "Dutch", "value": "<PERSON>k kan helaas geen klantnummer vinden bij dit adres, daardoor kan ik je niet verder helpen.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "7", "id": "67c848fa-d803-4abb-a894-10e0fd0d0d1f", "key": "DataFoundWelcomeMessage", "url": null, "filter": "", "language": "Dutch", "value": "Ik heb je gegevens gevonden. Goedendag {customerName}.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "8", "id": "aa07aafc-618c-44a1-b9b7-e3f0a64d60d9", "key": "DataNotFound", "url": null, "filter": "", "language": "Dutch", "value": "Deze gegevens kan ik helaas niet vinden.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "9", "id": "973f9ade-c245-4a30-aaa9-f9d8015d98ed", "key": "HaveToAskValidationQuestions", "url": null, "filter": "", "language": "Dutch", "value": "Daarvoor moet ik je eerst even een paar vragen stellen om te weten wie je bent.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "10", "id": "8950a839-6897-4a07-a459-654f05416bae", "key": "InputNotValid", "url": null, "filter": "", "language": "Dutch", "value": "Deze invoer herken ik niet.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "11", "id": "dcabbdfc-a55a-46b1-91b0-7de8b89b494d", "key": "MfaCodeIncorrect", "url": null, "filter": "", "language": "Dutch", "value": "Dat is niet de juiste code.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "12", "id": "7889b1df-015f-475a-80a7-7b55ee02f8c0", "key": "MfaPromptMessage", "url": null, "filter": "", "language": "Dutch", "value": "Om je gegevens beter te beveiligen, ontvang je tot slot per SMS een code om mee in te loggen. Deze code is 5 minuten geldig.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "13", "id": "ae3635e1-fb19-4ff2-8cb2-39988b4444b7", "key": "ValidationError", "url": null, "filter": "", "language": "Dutch", "value": "{validationErrorReason} Daardoor kan ik je gegevens niet vinden.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "14", "id": "6e11adf2-2b44-44ff-9a87-3e09e992e5ee", "key": "ValidationErrorCustomerId", "url": null, "filter": "", "language": "Dutch", "value": "Dit is helaas niet een klantnummer dat ik herken.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "15", "id": "f8633d30-feff-4aa7-b69c-95d82af895a6", "key": "ValidationErrorCustomerIdRetry", "url": null, "filter": "", "language": "Dutch", "value": "<PERSON>t klantnummer herken ik helaas niet. Je klantnummer bestaat uit maximaal 10 cijfers. Probeer het opnieuw.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "16", "id": "447cef89-7218-4080-9208-536e414c3863", "key": "ValidationErrorDateOfBirth", "url": null, "filter": "", "language": "Dutch", "value": "Dit is helaas niet een geboortedatum die ik herken.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "17", "id": "f7e07270-70f8-4e0f-874c-916a4fcb6daa", "key": "ValidationErrorDateOfBirthRetryPartOne", "url": null, "filter": "", "language": "Dutch", "value": "Deze geboortedatum herken ik helaas niet.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "18", "id": "979e8027-c471-4f25-9282-5b22cfe99e8a", "key": "ValidationErrorDateOfBirthRetryPartTwo", "url": null, "filter": "", "language": "Dutch", "value": "<PERSON><PERSON><PERSON> het opnieuw, bijvoorbeeld DD-MM-JJJJ", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "19", "id": "dfdf2a78-0fae-4bb2-8d19-7a1f1b773a29", "key": "ValidationErrorHouseNumber", "url": null, "filter": "", "language": "Dutch", "value": "Dit is helaas niet een huisnummer dat ik herken.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "20", "id": "dc814d91-4551-46a7-adf6-63d398144b40", "key": "ValidationErrorHouseNumberRetry", "url": null, "filter": "", "language": "Dutch", "value": "Dit huisnummer herken ik helaas niet. Probeer het opnieuw.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "21", "id": "53e62d51-ac57-4b2c-bdd6-07f11e61e373", "key": "ValidationErrorIbanDigits", "url": null, "filter": "", "language": "Dutch", "value": "Dit zijn helaas niet de laatste 3 cijfers van het IBAN-rekeningnummer dat bij ons bekend is.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "22", "id": "757d0a14-fa68-479e-9fea-9ffda915969e", "key": "ValidationErrorMailAddressRetry", "url": null, "filter": "", "language": "Dutch", "value": "<PERSON><PERSON> is geen geldig e-mailadres. Probeer het opnieuw.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "23", "id": "f3124648-00a9-424a-9e7c-d53571169e22", "key": "ValidationErrorOtherError", "url": null, "filter": "", "language": "Dutch", "value": "Er is he<PERSON><PERSON> iets mis gegaan.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "24", "id": "e35f6810-1131-4737-934a-2d142a58167b", "key": "ValidationErrorPhoneNumberRetry", "url": null, "filter": "", "language": "Dutch", "value": "<PERSON>t is geen geldig telefoonnummer. Probeer het opnieuw.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "25", "id": "467b58bd-87d2-43d2-9b5d-51bfa1ecc50e", "key": "ValidationErrorPostalCode", "url": null, "filter": "", "language": "Dutch", "value": "Dit is hela<PERSON> niet een postcode die ik herken.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "26", "id": "51e17435-8ee1-455f-bf9e-c65fd7834bbb", "key": "ValidationErrorPostalCodeRetry", "url": null, "filter": "", "language": "Dutch", "value": "Deze postcode herken ik helaas niet. Probeer het opnieuw, bijvoorbeeld: 1234AB.", "type": "text", "groupName": "Bot_CustomerVerificationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}]