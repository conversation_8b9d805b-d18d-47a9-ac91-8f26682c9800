[{"$id": "1", "id": "3bfbdc8f-1a2f-4730-afcd-d14d265438e4", "key": "AskCancelDate", "url": null, "filter": "", "language": "Dutch", "value": "Welke beëindigingsdatum wil je gebruiken voor de berekening? Kies een datum tussen vandaag en {maxCancelDate}.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "2", "id": "62535c90-5ac0-4d48-a481-f1100dfb30a7", "key": "AskProductCalculateFine", "url": null, "filter": "", "language": "Dutch", "value": "Voor welk contract wil je dat ik je opzegboete bereken?", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "3", "id": "6fbcc592-5ff0-4e66-83c0-0f078fcc340f", "key": "CalculateFine", "url": null, "filter": "", "language": "Dutch", "value": "Opzegboete berekenen", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "4", "id": "975c7924-4e94-4125-945a-b42d38e268ed", "key": "CancelDateFormat", "url": null, "filter": "", "language": "Dutch", "value": "Vul in als \"DD-MM-JJJJ\"", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "5", "id": "a2d4109e-ced6-4072-95ec-4b7ad14d48ff", "key": "ContractEndDateAfterCancelDate", "url": null, "filter": "", "language": "Dutch", "value": "De gekozen beëindigingsdatum valt op of na de einddatum van je contract. \n\nJe krijgt geen boete als je je contract op deze datum beëindigt.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "6", "id": "9672874b-2335-485a-9b7c-a892a3be452d", "key": "ContractEndDateWithInRangeCancelDate", "url": null, "filter": "", "language": "Dutch", "value": "De gekozen beëindigingsdatum valt binnen 7 dagen voor de einddatum van je contract. \n\nJe krijgt geen boete als je je contract op deze datum beëindigt.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "7", "id": "f1fbbc06-af89-4231-9200-3623f52f60a3", "key": "ContractReconsiderationPeriod", "url": null, "filter": "", "language": "Dutch", "value": "*Je contract bevindt zich binnen de 14 dagen bedenktijd.*\n\n*Deze periode gaat in op de dag dat je je contractbevestiging hebt ontvangen.*\n\n*In deze periode kan je kosteloos opzeggen en ontvang je geen opzegboete.*", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "8", "id": "550f11a0-2720-4a1b-88a9-55c43f7cbd49", "key": "FineDependsOnCancelDate", "url": null, "filter": "", "language": "Dutch", "value": "De berekening van de opzegboete hangt af van de datum waarop je het contract wil beëindigen. We berekenen de boete zonder overheidsheffingen en btw.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "9", "id": "ba6ce575-bf86-4f6c-b20e-ee438e9133ea", "key": "FineDisclaimer", "url": null, "filter": "", "language": "Dutch", "value": "*Deze opzegboete is een schatting. Hieraan kunnen geen rechten worden ontleend.*\n\n*Als je opzegt ontvang je de eindnota met je definitieve opzegboete. De hoogte van de boete is af<PERSON><PERSON><PERSON><PERSON> van het tariefverschil op de datum van opzegging en je daadwerkelijke verbruik.*", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "10", "id": "ac6e113f-0885-4b97-bff7-5f2aebe803ff", "key": "FinePolicyFine", "url": null, "filter": "", "language": "Dutch", "value": "Het verwachte resterende verbruik van {remaingUsages} {denotationType} vermenigvuldigen we met het tariefverschil van € {tariffDifference}.\n\nDe opzegboete voor {product}:\n**€ {fine} exclusief btw.***", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "11", "id": "c6a7cd73-2844-4f09-bcd8-c908bb7c46db", "key": "FinePolicyMVSError", "url": null, "filter": "", "language": "Dutch", "value": "Sorry, het is niet gelukt je opzegboete te berekenen.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "12", "id": "952d395d-a27f-4b01-a393-5dfda3fcb448", "key": "FinePolicyMVSError23391", "url": null, "filter": "", "language": "Dutch", "value": "Sorry, het is niet gelukt je opzegboete te berekenen.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "13", "id": "450cc5d2-b96d-45c2-b8ac-06bedf16442f", "key": "FinePolicyMVSError23392", "url": null, "filter": "", "language": "Dutch", "value": "Sorry, het is niet gelukt je opzegboete te berekenen.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "14", "id": "a0140590-0235-4abc-bf9f-13098c5981f7", "key": "FinePolicyMVSError23393", "url": null, "filter": "", "language": "Dutch", "value": "Sorry, het is niet gelukt je opzegboete te berekenen.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "15", "id": "bfe5d545-04c8-4207-98e1-78f4b37a1236", "key": "FinePolicyMVSError23394", "url": null, "filter": "", "language": "Dutch", "value": "Sorry, het is niet gelukt je opzegboete te berekenen.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "16", "id": "7373a374-aebe-4d7a-b8b0-a73741822251", "key": "FinePolicyMVSError23395", "url": null, "filter": "", "language": "Dutch", "value": "Sorry, het is niet gelukt je opzegboete te berekenen.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "17", "id": "3938956c-01ff-4410-bb1f-8b34b3420fdd", "key": "FinePolicyNoFine", "url": null, "filter": "", "language": "Dutch", "value": "Voor {product} is het referentiet<PERSON><PERSON> van een soortgelijk product gelijk of hoger.\n\n**Hierdoor verwachten we dat je geen boete krijgt.***", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "18", "id": "b231ff40-31b9-467d-beda-3ce4f12febc4", "key": "FinePolicyRemainingUsagesElectricity", "url": null, "filter": "", "language": "Dutch", "value": "Het verschil tussen je geschatte verbruik tijdens je contract en wat je verbruikt tot {enddate} noemen we het verwachte resterende verbruik.\n\nJe geschatte afname van stroom is {usagesSJV} kWh. Je verwachte teruglevering van stroom is {usagesSJI} kWh.\n\n**Je verwachte resterende verbruik: {remaingUsages} kWh.**", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "19", "id": "14f7605d-2619-4e3f-bf07-c482878116b5", "key": "FinePolicyRemainingUsagesElectricityWithoutRedelivery", "url": null, "filter": "", "language": "Dutch", "value": "Het verschil tussen je geschatte verbruik tijdens je contract en wat je verbruikt tot {enddate} noemen we het verwachte resterende verbruik.\n\n **Je verwachte resterende verbruik: {remaingUsages} kWh.**", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "20", "id": "0d36e1dd-3fa8-4057-ab70-712dd2856718", "key": "FinePolicyRemainingUsagesGas", "url": null, "filter": "", "language": "Dutch", "value": "Het verschil tussen je geschatte verbruik tijdens je contract en wat je verbruikt tot {enddate} noemen we het verwachte resterende verbruik.\n\n **Je verwachte resterende verbruik: {remaingUsages} m³.**", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "21", "id": "b645df25-7e80-4be8-996b-135d50c9120f", "key": "FinePolicyTariffElectricity", "url": null, "filter": "", "language": "Dutch", "value": "Voor {product} betaal je  €{tariff} per kWh. Als je normaal en daltarief hebt, is dit een gemiddeld tarief.\n\n Het referentietarief van {referenceProduct} is op dit moment €{referenceTariff} per kWh.\n \n **Het tariefverschil: €{tariffDifference}.**", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "22", "id": "fe95a0c5-6ecb-4ef8-9459-8bf324a00732", "key": "FinePolicyTariffGas", "url": null, "filter": "", "language": "Dutch", "value": "Voor {product} betaal je €{tariff} per m³.\n\n Het referentietarief van {referenceProduct} is op dit moment €{referenceTariff} per m³.\n \n **Het tariefverschil: €{tariffDifference}.**", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "23", "id": "5b8b6c3d-4903-4423-8248-714ec6aa1b72", "key": "FinePolicyTotalFine", "url": null, "filter": "", "language": "Dutch", "value": "De totale geschatte opzegboete bij het vroegtijdig opzeggen tegen de door jou gevraagde datum:\n**€{fine}.***\n\n**Inclusief 21% btw is dit €{fineWithVat}.***", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "24", "id": "d50cfdad-18ba-48eb-8ae9-3166357f4d5b", "key": "InvalidCancelDate", "url": null, "filter": "", "language": "Dutch", "value": "De datum die je hebt hebt ingevuld klopt niet of is geen datum tussen vandaag en {maxCancelDate}.\n\nProbeer het opnieuw.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "25", "id": "6c25a372-6897-42e2-b4ad-bbf2adcd2ba9", "key": "NewFinePolicyFine", "url": null, "filter": "", "language": "Dutch", "value": "Je krijgt een boete als je dit contract opzegt vóór die datum.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "26", "id": "66e3d24c-c286-427b-be4e-7ee0d96d8590", "key": "NewFinePolicyProductEndDate", "url": null, "filter": "", "language": "Dutch", "value": "Je {product} contract loopt tot: {enddate}.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "27", "id": "d1504b57-6c9c-4936-b0c2-cfa9c913304d", "key": "NoEGContract", "url": null, "filter": "", "language": "Dutch", "value": "Ik heb geen geschikte contracten gevonden die in aanmerking komen voor een opzegboete.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "28", "id": "ac44759a-dcc9-410d-8cc0-30a3ff09d8e7", "key": "OldFinePolicyOTContract", "url": null, "filter": "", "language": "Dutch", "value": "Je contract: {product} is een contract voor onbepaalde tijd. Je krijgt geen boete als je dit contract beëindigt.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "29", "id": "23989f0a-3826-4225-85f7-eb61d9c1d8d6", "key": "OldFinePolicyProductFine", "url": null, "filter": "", "language": "Dutch", "value": "Je {product} contract loopt tot: {enddate}. Als je dit contract opzegt vóór die datum, dan krijg je een boete van: € {fine}.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "30", "id": "fd0755f7-f68f-4bca-81c2-f6e76720b99c", "key": "OldFinePolicyTotalFine", "url": null, "filter": "", "language": "Dutch", "value": "De totale boete: € {totalFine}.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "31", "id": "42392aad-3a7c-4e13-9faa-57ce45a2d80d", "key": "OptionBothProducts", "url": null, "filter": "", "language": "Dutch", "value": "Beide/ alles", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "32", "id": "affef730-ffb6-420e-823b-1c0c1437621f", "key": "OptionProduct", "url": null, "filter": "", "language": "Dutch", "value": "{product}", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "33", "id": "17e9093a-9c1f-482c-b339-32fc3a8f1ebf", "key": "OTContract", "url": null, "filter": "", "language": "Dutch", "value": "Je contracten hebben een looptijd voor onbepaalde tijd. Je krijgt geen boete als je deze beëindigt.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "34", "id": "c4a4a0c2-c8f7-48c8-8f3d-9b97cc606d90", "key": "ThreeTimesWrongCancelDate", "url": null, "filter": "", "language": "Dutch", "value": "Je hebt 3 keer een verkeerde datum ingevuld.", "type": "text", "groupName": "Bot_ProductFineCalculationDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}]