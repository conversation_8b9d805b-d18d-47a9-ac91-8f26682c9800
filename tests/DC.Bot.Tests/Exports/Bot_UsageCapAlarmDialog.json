[{"$id": "1", "id": "9050dc3d-b0a5-4844-881e-c3d4d0d9999d", "key": "GenericMvsError", "url": null, "filter": "", "language": "Dutch", "value": "Sorry, voor jou is het niet mogelijk om de verbruiksplafondmelding in te stellen.", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "2", "id": "7f16b93b-5609-47d1-b485-e5a7faf3c451", "key": "HasSolarPanelsError", "url": null, "filter": "", "language": "Dutch", "value": "Het prijsplafondinzicht houdt geen rekening met je zonnepanelen. Je teruglevering wordt pas verrekend op de jaarnota.\n\nDaarom kan ik de verbruiksplafondmelding voor jou niet instellen.", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "3", "id": "4d7220d0-b227-4482-b322-41b4820c462e", "key": "HighUsagesError", "url": null, "filter": "", "language": "Dutch", "value": "Je bent een grootverbruiker. Daarom is het voor jou het niet mogelijk om de verbruiksplafondmelding in te stellen.", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "4", "id": "b383158e-5819-4f13-a3ae-9ae0a300b498", "key": "NoLeaveOff", "url": null, "filter": "", "language": "Dutch", "value": "<PERSON><PERSON>, laat uit", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "5", "id": "19df60a6-67e5-4dd4-a532-b9825a50b3ea", "key": "NoLeaveOn", "url": null, "filter": "", "language": "Dutch", "value": "<PERSON><PERSON>, laat aan", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "6", "id": "da4d2e95-6682-4fb6-b598-85ff8c4c5145", "key": "NoP4MandateError", "url": null, "filter": "", "language": "Dutch", "value": "Je hebt nog geen toestemming gegeven om je slimme meter uit te lezen. Dat is eerst nodig om je de verbruiksplafondmelding te kunnen geven.", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "7", "id": "d2bb54be-6307-4cdc-954d-4933d37b5874", "key": "NoRelevantAgreementsError", "url": null, "filter": "", "language": "Dutch", "value": "Je hebt geen contract dat in aanmerking komt voor de verbruiksplafondmelding.", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "8", "id": "0c97bd93-88a2-4a70-8e62-2e10f97ef2e5", "key": "NoResidentialPurposeError", "url": null, "filter": "", "language": "Dutch", "value": "Je adres heeft geen verblijfsfunctie. Daarom is het niet mogelijk om de verbruiksplafondmelding in te stellen.", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "9", "id": "f66d22b4-3855-4064-8bed-4cc96826d4f3", "key": "NoResidentialPurposeForProduct", "url": null, "filter": "", "language": "Dutch", "value": "Let op! Je adres heeft geen verblijfsfunctie. Daarom krijg je alleen een seintje voor {product}.", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "10", "id": "75487c3e-2627-4a05-b75a-6abedf7ab7d2", "key": "NoSmartMeterError", "url": null, "filter": "", "language": "Dutch", "value": "Je hebt geen slimme meters. Daarom is het voor jou niet mogelijk om de verbruiksplafondmelding in te stellen.", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "11", "id": "81b7d70b-19a7-4e0e-9ec8-d7a5df62fef9", "key": "NoSmartMeterForProduct", "url": null, "filter": "", "language": "Dutch", "value": "Let op! Je hebt geen slimme meters voor {product}. <PERSON><PERSON><PERSON> krijg je daar geen melding voor.", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "12", "id": "9d752ab3-ddca-4be8-b8cb-5f8734c4d0d0", "key": "OffWantToTurnOn", "url": null, "filter": "", "language": "Dutch", "value": "De verbruiksplafondmelding staat op dit moment bij jou **uit**. Wil je deze aanzetten?", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "13", "id": "0d9bea36-7ebe-4613-b6ea-3f442042921e", "key": "OnWantToTurnOff", "url": null, "filter": "", "language": "Dutch", "value": "De verbruiksplafondmelding staat op dit moment bij jou **aan**. Wil je deze u<PERSON>etten?", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "14", "id": "f92aec99-945e-49e7-8849-dd2eb68105f2", "key": "OopsSomethingWentWrong", "url": null, "filter": "", "language": "Dutch", "value": "Oeps! Er is helaas iets misgegaan.", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "15", "id": "319fa803-ade7-4300-9826-1b9647184019", "key": "SolarPanelsNoEAlarm", "url": null, "filter": "", "language": "Dutch", "value": "Let op! We kunnen geen melding geven voor 80% van je stroomverbruik, dit komt door je zonnepanelen.\nDaarom krijg je alleen een melding voor {product}.", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "16", "id": "c750299c-3dbb-4e8d-bc8c-c64b90951d24", "key": "TurnedOffAlarm", "url": null, "filter": "", "language": "Dutch", "value": "Oké. **De verbruiksplafondmelding staat vanaf nu uit.** Laat het mij weten als je het seintje weer aan wil zetten.", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "17", "id": "d63f7b63-be10-4d7f-9d6c-a91fb83b9dd8", "key": "TurnedOnAlarm", "url": null, "filter": "", "language": "Dutch", "value": "Top. **De verbruiksplafondmelding staat aan.** Je krijgt een seintje als je rond de 80% van het verbruiksplafond zit.", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "18", "id": "c1cc5b0d-69c7-45f5-96e5-568aeb7068f4", "key": "YesTurnOff", "url": null, "filter": "", "language": "Dutch", "value": "J<PERSON>, zet uit", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}, {"$id": "19", "id": "e8de9a8a-aa4b-4567-819e-522b159faddf", "key": "YesTurnOn", "url": null, "filter": "", "language": "Dutch", "value": "Ja, zet aan", "type": "text", "groupName": "Bot_UsageCapAlarmDialog", "groupDescription": null, "createdAtUtc": "0001-01-01T00:00:00", "updatedAtUtc": null, "removedAtUtc": null, "channel": "unknown", "isMarkDownText": false}]