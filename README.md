# XAPI Bot

## About the Bot Experience API (XAPI Bot)
The XAPI Bot is the entry point of the Chat channel within the EnecoGroup landscape. Clients will connect to this XAPI through the Microsoft Botframework Directline protocol (https://docs.microsoft.com/en-us/azure/bot-service/rest-api/bot-framework-rest-direct-line-3-0-concepts?view=azure-bot-service-4.0).
The only current client is Conversationals, they facilitate the software around the chat channel in the Eneco landscape. Their framework called Seamly connects to our XAPI when they need to do a certain _transaction_: one or multiple dialogs within a conversation where a connection with the EnecoGroup back-end is needed.
One example is asking for the current (monthly) advance payment amount, which consists of 2 dialogs: one to verify the customer with some personal details, and one to tell him this amount. 

## Local Development
### Prerequisites: 
* Local checkout of this repository
* Installation of Microsoft Botframework Emulator: https://github.com/microsoft/BotFramework-Emulator
* Installation of ngrok
* Read access to our Acceptance keyvault

### How to run locally: 
Start the emulator, configure the ngrok location on your PC. 
Create a DevelopmentLocal appsettings file by copying the Acceptance appsettings and replacing the "azureKeyvault" settings with their real value from the Acceptance keyvault.
Start the solution, copy the localhost address where the Swagger pops up.
Start a new bot connection in the emulator, leading to the localhost addres just copied, suffixed by /api/messages. 
Start a transaction with (for example) $advancepaymentamount

## Azure
In order to run this solution on Azure, some additional things need to be configured, apart from the usual Web App in our other XAPIs/PAPIs.
* WebSockets must be enabled
* MicrosoftAppId and MicrosoftAppPassword must be added to Application Settings
* An additional Web App Bot resource needs to be deployed
* On multiple instance environments, such as Acceptance and Production, a (Blob) storage account needs to be deployed
Correct ARMs are already configured in this repository, in the /infra folder. 
Additional reading about App service registration of the Bot: https://docs.microsoft.com/en-us/azure/bot-service/bot-service-resources-bot-framework-faq?view=azure-bot-service-3.0#app-registration
Additional reading about setting up CI/CD: https://www.bartvanuden.com/2018/12/13/bot-framework-v4-continuous-deployment-part-2-arm-template-and-azure-devops-release-pipeline/

### Keyvault secret names
* BotFrameworkAzureBlobConnectionString (appsettings - AzureBlob:ConnectionString)
* BotFrameworkXapiBotClientId (Application Settings in Azure - MicrosoftAppId)
* BotFrameworkXapiBotClientSecret (Application Settings in Azure - MicrosoftAppSecret)
* DigitalCore-ServiceAccount-ClientId-XapiBot (appsettings - DigitalCore:ServiceAccount:ClientId)
* DigitalCore-ServiceAccount-ClientSecret-XapiBot (appsettings - DigitalCore:ServiceAccount:ClientSecret)

### App Registrations Bot Service

* appreg-digitalchat-app-t  ca27fefd-4d7e-437e-939c-62ba20c4a9ab

* appreg-digitalchat-app-a  1f4bcd21-173f-4627-bf88-736f4d27378e

* appreg-digitalchat-app-p  b5295653-39ac-470a-a392-12f5a9853c1a

### App Registrations XAPI / APIM
* appreg-digitalcore-apim-xapi-bot-t  ae1d522c-dd48-4790-94bc-5cfe62ac38f0

* appreg-digitalcore-apim-xapi-bot-a  6d00f01a-030d-4165-9833-0989fc2db5b4

* appreg-digitalcore-apim-xapi-bot-p  735dc07c-e229-4938-876c-cd685adf470f

