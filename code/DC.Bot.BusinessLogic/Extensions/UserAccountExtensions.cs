﻿using DC.Bot.BusinessLogic.Models;
using DC.Domain.Exceptions;
using DC.UserAccounts.Client.Models;
using System;

namespace DC.Bot.BusinessLogic.Extensions;

public static class UserAccountsExtensions
{
    public static RequestDataValidationRequest ToRequestDataValidationRequest(this DialogData dialogData)
    {
        var verification = dialogData.Verification;
        if (verification == null || string.IsNullOrWhiteSpace(verification.PostalCode) || !verification.HouseNumber.HasValue)
            throw new ValidationException(new Guid("0488468b-789a-47c1-86b5-b39a0c9cfad5"), $"{nameof(UserAccountsExtensions)}.{nameof(ToRequestDataValidationRequest)} cannot map because the {nameof(DialogData)}. Verification property is invalid.");

        return new RequestDataValidationRequest
        {
            Data = new ValidationRequest
            {
                Address = new Address
                {
                    PostalCode = verification.PostalCode,
                    HouseNumber = verification.HouseNumber.HasValue ? verification.HouseNumber.Value : 0
                } 
            }
        };
    }
}