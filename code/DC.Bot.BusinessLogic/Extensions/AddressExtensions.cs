﻿using DC.Utilities.Formatters;
using System.Linq;

namespace DC.Bot.BusinessLogic.Extensions;

public static class AddressExtensions
{
    public static string CityNameToFirstUpperLetter(this string city)
    {
        // 's-gravenhage, 's-Hertogenbosch
        if (city.StartsWith("'s-", System.StringComparison.CurrentCultureIgnoreCase))
        {
            return string.Concat("'s-", city.ToLowerInvariant().Replace("'s-", string.Empty).FirstLetterToUpper());
        }

        var parts = city.Split(' ');
        // Den <PERSON>, Den Bosch, Den Dolder
        if (parts.Length == 2)
        {
            return string.Concat(parts[0].ToLowerInvariant().FirstLetterToUpper() + " ",
                parts[1].ToLowerInvariant().FirstLetterToUpper());
        }

        return city.ToLowerInvariant().FirstLetterToUpper();
    }
}