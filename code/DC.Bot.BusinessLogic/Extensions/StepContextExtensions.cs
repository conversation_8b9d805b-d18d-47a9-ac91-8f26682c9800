﻿using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using System;
using System.Linq;

namespace DC.Bot.BusinessLogic.Extensions;

public static class StepContextExtensions
{
    public static bool IsChoiceSelected(this WaterfallStepContext stepContext, string selectedChoice)
    {
        return stepContext.Result is FoundChoice choice && (choice.Value.Contains(selectedChoice, StringComparison.InvariantCultureIgnoreCase) || choice.Synonym.Contains(selectedChoice, StringComparison.InvariantCultureIgnoreCase)) ||
               stepContext.Result is string textAnswer && textAnswer.Contains(selectedChoice, StringComparison.InvariantCultureIgnoreCase);
    }

    public static bool SetNextStepIndex(this WaterfallStepContext stepContext, DialogSet dialogs, string stepName)
    {
        var index = dialogs.GetDialogs().Select((s) => s.Id).ToList().IndexOf(stepName);

        if (index == -1)
            return false;

        stepContext.ActiveDialog.State["stepIndex"] = index - 1;
        return true;
    }
}