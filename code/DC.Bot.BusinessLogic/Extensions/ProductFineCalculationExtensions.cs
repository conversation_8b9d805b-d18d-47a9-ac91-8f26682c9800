﻿using DC.Domain.Models.Agreements;
using System.Collections.Generic;
using System.Linq;

namespace DC.Bot.BusinessLogic.Extensions
{
    public static class ProductFineCalculationExtensions
    {
        /// <summary>
        /// Totals the product usages.
        /// </summary>
        /// <param name="productUsages">The product usages.</param>
        /// <returns>A double.</returns>
        public static double TotalProductUsages(this List<ProductUsage> productUsages)
        {
            if (productUsages?.Exists(x => x.CounterType is Domain.Models.Usages.CounterType.ElectricityRedeliveryLow or Domain.Models.Usages.CounterType.ElectricityRedeliveryHigh) == true)
            {
                var delivery = productUsages
                    .Where(x => x.CounterType is Domain.Models.Usages.CounterType.ElectricityLow or Domain.Models.Usages.CounterType.ElectricityHigh)
                    .Sum(x => x.Quantity) ?? 0;
                var redelivery = productUsages
                .Where(x => x.CounterType is Domain.Models.Usages.CounterType.ElectricityLow or Domain.Models.Usages.CounterType.ElectricityHigh)
                .Sum(x => x.Quantity) ?? 0;
                return delivery - redelivery;
            }
            else
                return productUsages.Sum(x => x.Quantity) ?? 0;
        }

        /// <summary>
        /// Totals the delivery product usages.
        /// </summary>
        /// <param name="productUsages">The product usages.</param>
        /// <returns>A double.</returns>
        public static double TotalDeliveryProductUsages(this List<ProductUsage> productUsages)
        {
            return productUsages
                    .Where(x => x.CounterType is Domain.Models.Usages.CounterType.ElectricityLow or Domain.Models.Usages.CounterType.ElectricityHigh)
                    .Sum(x => x.Quantity) ?? 0;
        }

        /// <summary>
        /// Totals the redelivery product usages.
        /// </summary>
        /// <param name="productUsages">The product usages.</param>
        /// <returns>A double.</returns>
        public static double TotalRedeliveryProductUsages(this List<ProductUsage> productUsages)
        {
            return productUsages
                .Where(x => x.CounterType is Domain.Models.Usages.CounterType.ElectricityLow or Domain.Models.Usages.CounterType.ElectricityHigh)
                .Sum(x => x.Quantity) ?? 0;
        }

        /// <summary>
        /// Excepteds the product usages until cancel date.
        /// </summary>
        /// <param name="totalUsages">The total usages.</param>
        /// <param name="remainingSJV">The remaining sjv.</param>
        /// <param name="remainingSJI">The remaining sji.</param>
        /// <returns>A double.</returns>
        public static double ExceptedProductUsagesUntilCancelDate(this double totalUsages, double remainingSJV, double remainingSJI = 0) =>
            totalUsages - (remainingSJV - remainingSJI);
    }
}
