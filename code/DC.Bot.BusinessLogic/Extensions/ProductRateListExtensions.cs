﻿using DC.Domain.Models.Products.ProductRates;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DC.Bot.BusinessLogic.Extensions;

public static class ProductRateListExtensions
{
    /// <summary>
    /// In case we have multiple stukjezon productrates in the list, get the details from them and output the details.
    /// </summary>
    public static string GetStukjeZonSummary(this IList<ProductRate> productRates, string text)
    {
        productRates = productRates.Where(pr => pr.IsStukjeZonProductType()).ToList();
        string output = string.Empty;
        if (productRates.Any())
        {
            var amountStukjezonProductRates = productRates.Count;
            var stukjeZonProductRateDetail = productRates[0].ProductRateDetails.FirstOrDefault();
            // Return the yearly discount. A single productRateDetail represents the monthly discount. That is why we multiply the amount stukjezonDiscountValue by 12 calculate the yearly discount.
            output = text.Replace("{numberOfStukjeZon}", $"{amountStukjezonProductRates}").Replace("{priceStukjeZon}",
                $"{Math.Abs(Math.Round(amountStukjezonProductRates * (stukjeZonProductRateDetail?.VATIncluded ?? 0) * 12, 2))}");
        }

        return output;
    }

    public static IList<ProductRate> GetMeasuredProductRates(this IList<ProductRate> productRates)
    {
        return productRates.Where(pr => pr.IsMeasuredProductType()).ToList();
    }

    public static IList<ProductRate> GetNonMeasuredProductRates(this IList<ProductRate> productRates)
    {
        return productRates.Where(pr => pr.IsNonMeasuredProductType()).ToList();
    }
}