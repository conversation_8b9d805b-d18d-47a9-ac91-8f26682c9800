﻿using DC.Bot.BusinessLogic.Models;
using DC.Customers.Client.Models;
using DC.Domain.Models.Customers;
using DC.Domain.Models.General;
using DC.Domain.Models.Products;
using DC.Products.Client.Models;
using DC.Utilities.Formatters;
using System;
using System.Collections.Generic;
using Action = DC.Domain.Models.Products.OrdersV2.Action;
using AddressModel = DC.Customers.Client.Models.AddressModel;

namespace DC.Bot.BusinessLogic.Extensions
{
    public static class ProductOrderExtensions
    {
        public static RequestDataServiceOrderRequestModel ToServiceOrderRequestModel(this DialogData dialogData)
        {
            return new RequestDataServiceOrderRequestModel
            {
                Data = new ServiceOrderRequestModel
                {
                    Type = dialogData.ServiceOrder.Type,
                    Description = dialogData.ServiceOrder.Description.ShortenString(50, "Storing"),
                    Comments = dialogData.ServiceOrder.Comments,
                    MalfunctionCode = dialogData.ServiceOrder.MalfunctionCode,
                    ResourceNumber = dialogData.ServiceOrder.ResourceNumber
                }
            };
        }

        /// <summary>
        /// Removal order for Toon. For removals MPC is not relevant.
        /// </summary>
        /// <param name="dialogData"></param>
        /// <param name="productType"></param>
        /// <param name="reason"></param>
        /// <param name="customer"></param>
        /// <returns></returns>
        public static RequestDataProductOrderV2Model ToProductRemovalOrder(this DialogData dialogData, ProductType productType, ProductDiscontinueReason? reason, CustomerModel customer)
        {
            if (dialogData?.DiscontinueContractData == null || customer == null)
            {
                return null;
            }

            var account = dialogData.GetAccount(customer);

            return new RequestDataProductOrderV2Model
            {
                Data = new ProductOrderV2Model
                {
                    SalesChannel = SalesChannel.ChatBot,
                    ReferenceId = Guid.NewGuid().ToString(),
                    ProcessingType = OrderProcessingType.DirectProcessing,
                    ProductOffers = new List<ProductOfferingV2Model>
                {
                    new ProductOfferingV2Model
                    {
                        Options = new Dictionary<string, string> {{"quantity", "1"}},
                        ProductType = productType,
                        ActionReason = ActionReason.Discontinue,
                        Action = Action.Remove,
                        DiscontinueReason = reason,
                        StartDate = DateTime.Today.AddDays(30),
                        AgreementId = dialogData.DiscontinueContractData?.AgreementId,
                        MarketingProductCode = dialogData.DiscontinueContractData?.MarketingProductCode
                    }
                },
                    DeliveryAddress = account.Address.ToAddress(),
                    Customer = new CustomerV2Model
                    {
                        Id = (int)dialogData.Verification.CustomerId,
                        IsNew = false,
                        Contact = new ContactV2Model
                        {
                            EmailAddress = dialogData.Customer.EmailAddress,
                            Address = GetContactAddress(customer, account)
                        },
                        Person = customer.Person.ToPerson(),
                        Organisation = customer.Organisation.ToOrganisation()
                    }
                }
            };
        }

        private static Address GetContactAddress(CustomerModel customer, CustomerAccountModel account)
        {
            if (customer.Contact.Address != null
                && customer.Contact.Address.PostalCode != null
                && customer.Contact.Address.HouseNumber != null)
            {
                return customer.Contact.Address.ToAddress();
            }

            return account.Address.ToAddress();
        }

        private static Address ToAddress(this AddressModel address)
        {
            if (address == null) return null;

            return new Address
            {
                Street = address.Street,
                PostalCode = address.PostalCode,
                HouseNumber = address.HouseNumber,
                HouseNumberSuffix = address.HouseNumberSuffix
            };
        }

        private static PersonV2Model ToPerson(this PersonModel person)
        {
            if (person == null) return null;

            return new PersonV2Model
            {
                Initials = person.Initials,
                Surname = person.Surname,
                SurnamePreposition = person.SurnamePreposition
            };
        }

        private static OrganisationV2Model ToOrganisation(this OrganisationModel organisation)
        {
            if (organisation == null) return null;

            return new OrganisationV2Model
            {
                ChamberOfCommerceNumber = organisation.ChamberOfCommerceNumber,
                Name = organisation.Name
            };
        }
    }
}
