﻿using AngleSharp.Dom;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Models;
using DC.Customers.Client.Models;
using DC.Domain.Models.Accounts;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.Customers;
using DC.Domain.Models.Extensions;
using DC.Domain.Models.Products;
using DC.Utilities.Formatters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;
using Customer = DC.Domain.Models.Customers.Customer;

[assembly: InternalsVisibleTo("DC.Bot.Tests")]
namespace DC.Bot.BusinessLogic.Extensions;

public static class CustomerExtensions
{
    public static string GetName(this CustomerModel customer)
    {
        if (customer.CustomerType == CustomerType.Person)
            return (string.IsNullOrWhiteSpace(customer.Person?.SurnamePreposition)
                ? $"{customer.Person?.Initials} {customer.Person?.Surname}"
                : $"{customer.Person?.Initials} {customer.Person?.SurnamePreposition} {customer.Person?.Surname}").Trim();

        return $"{customer.GetFirstname()} {customer.GetSurname()}".Trim();
    }

    public static string GetFirstname(this CustomerModel customer)
    {
        if (customer.CustomerType != CustomerType.Person)
            return string.Empty;
        return !string.IsNullOrWhiteSpace(customer.Person.Name) ? customer.Person.Name : customer.Person.Initials;
    }

    public static string GetSurname(this CustomerModel customer)
    {
        if (customer.CustomerType != CustomerType.Person)
            return customer.Organisation.Name;
        return $"{customer.Person.SurnamePreposition} {customer.Person.Surname}".Trim();
    }

    public static Gender GetGender(this CustomerModel customer)
    {
        if (customer.CustomerType != CustomerType.Person)
            return Gender.Unknown;
        return customer.Person.Gender;
    }

    public static List<string> GetPhoneNumbers(this CustomerModel customer)
    {
        List<string> phoneNumbers = new List<string>();

        if (!string.IsNullOrEmpty(customer.Contact?.MobilePhoneNumber))
        {
            phoneNumbers.Add(customer.Contact.MobilePhoneNumber);
        }

        if (!string.IsNullOrEmpty(customer.Contact?.PhoneNumber))
        {
            phoneNumbers.Add(customer.Contact.PhoneNumber);
        }

        return phoneNumbers;
    }

    public static CustomerAccountModel GetAccount(this DialogData dialogData, CustomerModel customer)
    {
        return customer.Accounts?.FirstOrDefault(x =>
            x.Active == true
            && x.Address.PostalCode == dialogData.Verification.PostalCode
            && x.Address.HouseNumber == dialogData.Verification.HouseNumber);
    }

    public static RequestDataCustomerVerification ToCustomerVerificationRequest(this DialogData dialogData)
    {
        return new RequestDataCustomerVerification
        {
            Data = new CustomerVerification
            {
                CustomerId = dialogData.Verification.CustomerId,
                DatesOfBirth = dialogData.Verification.DateOfBirth.HasValue ? new List<DateTime?> { dialogData.Verification.DateOfBirth } : null,
                Addresses = !string.IsNullOrWhiteSpace(dialogData.Verification.PostalCode) && dialogData.Verification.HouseNumber.HasValue ?
                    new List<AddressModel> { new AddressModel { PostalCode = dialogData.Verification.PostalCode, HouseNumber = dialogData.Verification.HouseNumber.Value } } : null,
                IbanSuffixes = !string.IsNullOrWhiteSpace(dialogData.Verification.IbanSuffix) ? new List<string> { dialogData.Verification.IbanSuffix } : null,
                PhoneNumber = !string.IsNullOrWhiteSpace(dialogData.Verification.PhoneNumber) ? dialogData.Verification.PhoneNumber : null,
                IncludeOrganisations = true
            }
        };
    }

    public static ContactData ToContact(this Customer customer)
    {
        return new ContactData
        {
            PhoneNumbers = customer.Contact?.PhoneNumbers != null ? customer.Contact?.PhoneNumbers?.Where(x => x != null).ToList() : new List<string>()
        };
    }

    public static bool IsZzp(this Customer customer)
    {
        if (customer.CustomerType != CustomerType.Organisation || string.IsNullOrEmpty(customer.CustomerSegment)) return false;
        var parts = customer.CustomerSegment.ToLower().Split(" ");
        return parts.Contains("zzp");
    }

    /// <summary>
    /// Returns the personalisation info given a customer, product collection and payment plan collection
    /// </summary>
    /// <param name="customerModel"></param>
    /// <param name="products"></param>
    /// <param name="paymentPlans"></param>
    /// <param name="agreements"></param>
    /// <param name="meterMalfunction"></param>
    /// <returns></returns>
    public static PersonalisationInfo ToPersonalisationInfo(this CustomerModel customerModel, IList<Domain.Models.Products.ProductModel> products, IList<PaymentPlan> paymentPlans, IList<Agreement> agreements, IList<OrderResponseModel> orders, bool meterMalfunction)
    {
        // get active product types
        var activeProductTypes = products?.Where(p => p.IsActive && p.Type?.Name != null)?.Select(p => p.Type.Name).ToList() ?? new List<ProductType>();

        var hasWarmth = activeProductTypes.Exists(p => p is ProductType.Warmth or ProductType.WarmthEkv);
        var hasReadableSmartWarmthMeter = customerModel.HasReadableSmartMeter(ProductType.Warmth);
        var gridOperatorName = agreements?.GetGridOperatorName();

        //Orders: Get Electricity & Gas Consideration dates to identify cooling off periods
        DateTime? considerationDateElectricity = orders?.FirstOrDefault(x => x.OrderItemsProductTypes.Contains(ProductType.Electricity))?.ConsiderationEndDate;
        DateTime? considerationDateGas = orders?.FirstOrDefault(x => x.OrderItemsProductTypes.Contains(ProductType.Gas))?.ConsiderationEndDate;

        return new PersonalisationInfo
        {
            // customer
            CustomerId = customerModel.Id,
            FirstnameLegacy = customerModel.GetName(),
            Firstname = customerModel.GetFirstname(),
            Surname = customerModel.GetSurname(),
            Gender = customerModel.GetGender(),
            Email = customerModel.Contact?.EmailAddress,
            IsOrganisation = customerModel.CustomerType == CustomerType.Organisation,
            DateOfBirthToday = customerModel.Person?.DateOfBirth != null
                && customerModel.Person.DateOfBirth > DateTime.MinValue
                && customerModel.Person.DateOfBirth != new DateTime(1900, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                && customerModel.Person.DateOfBirth.Value.Month == DateTime.Today.Month
                && customerModel.Person.DateOfBirth.Value.Day == DateTime.Today.Day,
            // products
            HasElectricity = activeProductTypes.Exists(p => p == ProductType.Electricity),
            HasGas = activeProductTypes.Exists(p => p == ProductType.Gas),
            HasToon = activeProductTypes.Exists(p => p == ProductType.ToonService),
            HasWarmth = hasWarmth,
            HasTapwater = activeProductTypes.Exists(p => p == ProductType.Tapwater),
            ElectricityIsIndefinite = products?.Any(p => p.IsActive && p.Type?.Name == ProductType.Electricity && p.Indefinite == true) == true,
            GasIsIndefinite = products?.Any(p => p.IsActive && p.Type?.Name == ProductType.Gas && p.Indefinite == true) == true,
            ElectricityDynamicPricing = agreements.HasDynamicPricing(ProductType.Electricity),
            GasDynamicPricing = agreements.HasDynamicPricing(ProductType.Gas),
            AfcoElectricity = agreements?.ToList().CheckEndContract90Days(ProductType.Electricity) == true,
            AfcoGas = agreements?.ToList().CheckEndContract90Days(ProductType.Gas) == true,
            // meters
            HasRedelivery = customerModel.Accounts?.Any(a => a.Active == true && a.HasRedelivery) == true,
            HasReadableSmartElectricityMeter = customerModel.HasReadableSmartMeter(ProductType.Electricity),
            HasReadableSmartGasMeter = customerModel.HasReadableSmartMeter(ProductType.Gas),
            HasReadableSmartWarmthMeter = hasReadableSmartWarmthMeter == null && hasWarmth ? false : hasReadableSmartWarmthMeter,
            MeterMalfunction = meterMalfunction,
            MeterDataRequest = customerModel.Accounts?.Any(a => a.CanInsertReadings) == true,
            Netbeheerder = gridOperatorName,
            NetbeheerderNames = gridOperatorName.GetGridGridOperatorNames(),
            HasActiveMandate = customerModel.HasActiveMandate(),
            // billing
            HasNextChargeDate = paymentPlans?.Any(p => p.NextBillingCycle != null && p.NextBillingCycle.ChargeDate.HasValue) == true,
            InDebtCollection = paymentPlans.DetermineDebtCollection(),
            NextChargeDate = paymentPlans?.Where(p => p.NextBillingCycle != null && p.NextBillingCycle.ChargeDate.HasValue && p.NextBillingCycle.ChargeDate > DateTime.Now).Min(x => x.NextBillingCycle.ChargeDate)?.ToString("dd-MM-yyyy"),
            RateChangeIn30Days = products?.Any(p => p?.IsActive == true && p.EndDatePrice.HasValue && p.EndDatePrice <= DateTime.Now.AddDays(30)) == true,
            //Cooling Off: only possible to determinate for Electricity & Gas. Not possible for Cv, Boiler or Heatpump
            CoolingOffElectricity = considerationDateElectricity >= DateTime.Now,
            CoolingOffGas = considerationDateGas >= DateTime.Now
        };
    }

    /// <summary>
    /// Returns true if the customer(account)'s meters of a specified producttype are smart, false if they are conventional, null if no meters of the producttype exist 
    /// </summary>
    /// <param name="customerModel"></param>
    /// <param name="productType"></param>
    /// <param name="accountId"></param>
    /// <returns></returns>
    public static bool? HasReadableSmartMeter(this CustomerModel customerModel, ProductType productType, int? accountId = null)
    {
        // get all meters of the customer or of a specific customer account
        var meters = customerModel.Accounts?.Where(a => a.MeterDetails != null && (!accountId.HasValue || accountId.Value == a.Id)).SelectMany(a => a.MeterDetails).ToList() ?? new List<MeterDetail>();

        // if any meters with the product type, return if any of these meters are smart
        if (meters.Exists(m => m.ProductType.Equals(productType.ToString(), StringComparison.OrdinalIgnoreCase)))
            return meters.Exists(m => m.ProductType.Equals(productType.ToString(), StringComparison.OrdinalIgnoreCase) && m.IsSmartMeter == true && m.IsSmartMeterReadingAllowed == true);

        // no meters found of the specified producttype, return null
        return null;
    }

    /// <summary>
    /// Have the active mandate.
    /// </summary>
    /// <param name="customerModel">The customer model.</param>
    /// <returns>A bool? .</returns>
    public static bool? HasActiveMandate(this CustomerModel customerModel)
    {
        var activeAccounts = customerModel.Accounts?.Where(a => a.Active == true);
        if (activeAccounts?.Any() != true || !customerModel.HasSmartMeters())
            return null;

        return activeAccounts.Any(a => a.MeterDetails?.Any(m => m.IsSmartMeter == true && m.IsSmartMeterReadingAllowed == true) == true);
    }

    /// <summary>
    /// Gets the grid operator name.
    /// </summary>
    /// <param name="agreements">The agreements.</param>
    /// <returns>A string.</returns>
    public static string GetGridOperatorName(this IList<Agreement> agreements)
    {
        if (agreements?.Any() != true)
            return null;

        return agreements
            .Where(a => a.IsActive && !string.IsNullOrEmpty(a.Connection?.GridOperator?.Name))
            .OrderBy(a => a?.Products?.FirstOrDefault()?.ProductType)
            .FirstOrDefault()?.Connection?.GridOperator?.Name;
    }

    /// <summary>
    /// Have the smart meter.
    /// </summary>
    /// <param name="customerModel">The customer model.</param>
    /// <param name="accountId">The account id.</param>
    /// <returns>A bool.</returns>
    public static bool HasOnlySmartMetersAndActiveMandate(this CustomerModel customerModel, int accountId)
    {
        // get all meters of the customer or of a specific customer account
        var meters = customerModel.Accounts?.Where(a => a.MeterDetails != null && accountId == a.Id).SelectMany(a => a.MeterDetails).ToList() ?? new List<MeterDetail>();

        return meters.TrueForAll(m => m.IsSmartMeter == true && m.IsSmartMeterReadingAllowed == true);
    }

    /// <summary>
    /// Are the smart meter.
    /// </summary>
    /// <param name="customerModel">The customer model.</param>
    /// <returns>A bool.</returns>
    public static bool HasSmartMeters(this CustomerModel customerModel)
    {
        // get all meters of the customer or of a specific customer account
        var meters = customerModel.Accounts?.Where(a => a.MeterDetails != null).SelectMany(a => a.MeterDetails).ToList() ?? new List<MeterDetail>();

        return meters.Exists(m => m.IsSmartMeter == true);
    }

    /// <summary>
    /// Returns true if any of the passed payment plans are in a reminder state
    /// </summary>
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Critical Code Smell", "SYSLIB1045: Use 'GeneratedRegexAttribute' to generate the regular expression implementation at compile-time.", Justification = "To refactor later on.")]
    public static bool DetermineDebtCollection(this IList<PaymentPlan> paymentPlans)
    {
        // no payment plan means no payment reminder
        if (paymentPlans?.Any() != true)
            return false;
        // select all different states
        var states = paymentPlans.Select(p => p.DebtCollection?.ProcessStep?.State).Distinct().ToList();
        // one null state means no payment reminder
        if (states.Count == 1 && string.IsNullOrWhiteSpace(states.FirstOrDefault()))
            return false;
        // check HR41A + HR41B
        if (states.Exists(s => s?.StartsWith("HR41A", StringComparison.OrdinalIgnoreCase) == true || states.Exists(s => s?.StartsWith("HR41B", StringComparison.OrdinalIgnoreCase) == true)))
            return true;
        // check HR1 to HR40
        for (int i = 1; i <= 40; i++)
        {
            if (states.Exists(s => !string.IsNullOrWhiteSpace(s) && s.StartsWith($"HR", StringComparison.OrdinalIgnoreCase) && Regex.Match(s, "\\d+", RegexOptions.None, TimeSpan.FromSeconds(2)).Value == $"{i}"))
                return true;
        }
        // any other state means no payment reminder
        return false;
    }

    /// <summary>
    /// Are the new customer.
    /// </summary>
    /// <param name="customerModel">The customer model.</param>
    /// <returns>A bool.</returns>
    public static bool IsNewCustomer(this CustomerModel customerModel)
    {
        var startDate = customerModel.Accounts?.FirstOrDefault(a => a.Active == true)?.StartDate;
        return startDate.HasValue && startDate.Value > DateTime.Today.AddYears(-1);
    }

    /// <summary>
    /// Gets the netbeheer name from list.
    /// </summary>
    /// <param name="gridoperatorName">The gridoperator name.</param>
    /// <returns>A PersonalisationGridGridOperator.</returns>
    internal static string GetGridGridOperatorNames(this string gridoperatorName)
    {
        //when gridoperator name is empty/null in the agreements return Unknown
        PersonalisationGridGridOperator gridoperator = PersonalisationGridGridOperator.Unknown;
        if (string.IsNullOrWhiteSpace(gridoperatorName))
            return gridoperator.ToString();

        //parse gridoperator name from agreement(s) when not existing in Enum return Other
        return gridoperatorName.FromEnumMemberValue<PersonalisationGridGridOperator>(false).GetValueOrDefault(PersonalisationGridGridOperator.Other).ToString();
    }

    /// <summary>
    /// Have the dynamic pricing.
    /// </summary>
    /// <param name="agreements">The agreements.</param>
    /// <param name="productType">The product type.</param>
    /// <returns>A bool.</returns>
    public static bool HasDynamicPricing(this IList<Agreement> agreements, ProductType? productType) =>
        agreements?.Any(x => x.IsActive
        && x.Products?.Any(x => x.IsActive
        && x.IsDynamicPricing
        && (productType is null || x.ProductType == productType.Value)) == true) == true;
}