﻿using DC.Bot.BusinessLogic.Models;
using DC.Products.Client.Models;
using System.Linq;

namespace DC.Bot.BusinessLogic.Extensions;

public static class KetelComfortExtensions
{
    public static KetelComfortServiceModel ToKetelComfortMaintenanceAppointment(this ResponseDataKetelComfortProductDetails model)
    {
        if (model?.Data?.Service == null)
            return null;

        return new KetelComfortServiceModel
        {
            PreviousAppointment = model.Data.Service.PreviousAppointments != null && model.Data.Service.PreviousAppointments.Any()
                ? model.Data.Service?.PreviousAppointments.LastOrDefault()?.AppointmentDate : null,
            UpcomingAppointment = model.Data.Service.UpcomingAppointment?.ToUpcomingAppointment()
        };
    }

    private static KetelComfortMaintenanceAppointmentModel ToUpcomingAppointment(this MaintenanceAppointment model)
    {
        if (model == null)
            return null;

        return new KetelComfortMaintenanceAppointmentModel
        {
            AppointmentId = model.AppointmentId,
            AppointmentDate = model.AppointmentDate,
            AppointmentStartTime = model.AppointmentWindow?.Start,
            AppointmentEndTime = model.AppointmentWindow?.End,
            PlanWindowStartTime = model.PlanWindow?.Start,
            PlanWindowEndTime = model.PlanWindow?.End,
            Type = model.Type
        };
    }
}