﻿using DC.Bot.BusinessLogic.Models.Request;
using DC.Domain.Models.Bot;
using DC.Storage.Client.Models;
using System;
using System.Linq;

namespace DC.Bot.BusinessLogic.Extensions;

public static class StorageExtensions
{
    public static RequestDataBotConversationRequest ToStorageModel(this Models.Request.Conversation request)
    {
        return new RequestDataBotConversationRequest
        {
            Data = new BotConversationRequest
            {
                CustomerId = !string.IsNullOrWhiteSpace(request.CustomerId) && long.TryParse(request.CustomerId, out var customerId) ? customerId : null,
                ExternalSessionId = request.SessionId,
                TransactionName = request.TransactionName,
                Messages = request.History?.Select(h => h.ToStorageModel()).ToList()
            }
        };
    }

    public static BotMessageRequest ToStorageModel(this Message request)
    {
        var milliseconds = request.OccurredAt / 1000;
        return new BotMessageRequest
        {
            Message = request.Body,
            Source = request.FromClient ? MessageSource.Client : MessageSource.Bot,
            Type = request.Type,
            Timestamp = DateTimeOffset.FromUnixTimeMilliseconds(milliseconds).UtcDateTime
        };
    }
}