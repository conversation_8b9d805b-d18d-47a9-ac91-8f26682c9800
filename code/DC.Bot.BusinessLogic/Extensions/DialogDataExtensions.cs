﻿using DC.Bot.BusinessLogic.Models;
using DC.Customers.Client.Models;
using DC.Financials.Client.Models;
using DC.Products.Client.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace DC.Bot.BusinessLogic.Extensions;

public static class DialogDataExtensions
{
    public static bool IsVerified(this DialogData dialogData)
    {
        return dialogData?.Verification?.IsVerified == true && dialogData.Verification.CustomerId.GetValueOrDefault(0) > 0;
    }

    public static bool IsCustomerIdVerified(this DialogData dialogData)
    {
        return dialogData?.Verification?.CustomerIdVerified == true && dialogData.Verification.CustomerId.GetValueOrDefault(0) > 0;
    }

    public static bool HasCustomerId(this DialogData dialogData)
    {
        return dialogData?.Verification?.CustomerId.GetValueOrDefault(0) > 0;
    }

    public static bool HasLabel(this DialogData dialogData)
    {
        return dialogData?.Customer?.Label != DC.Repositories.Base.Enumerations.Label.Default;
    }

    public static bool HasActiveAccount(this DialogData dialogData)
    {
        return dialogData?.SelectedAccount?.AccountId > 0;
    }

    public static CustomerAccountModel GetActiveAccountFromCustomerModel(this DialogData dialogData, CustomerModel customerModel)
    {
        return customerModel?.Accounts?.FirstOrDefault(a => a.Active == true && a.Id == dialogData?.SelectedAccount?.AccountId);
    }

    public static void SetAllActiveAccountsFromCustomerModel(this DialogData dialogData, CustomerModel customerModel)
    {
        var activeAccountsData = new List<AccountData>();

        if (customerModel?.Accounts?.Any() ?? false)
        {
            foreach (var account in customerModel.Accounts.Where(account =>
                                                                    account != null
                                                                    && account.Active == true
                                                                    && account.Address != null) )
            {
                activeAccountsData.Add(new AccountData
                {
                    AccountId = account?.Id ?? 0,
                    BankAccountNumber = account?.Bankaccount?.Number,
                    PostalCode = account?.Address?.PostalCode,
                    HouseNumber = account?.Address?.HouseNumber,
                    HouseNumberSuffix = account?.Address?.HouseNumberSuffix,
                    Street = account?.Address?.Street,
                    City = account?.Address?.City,
                    HasDynamicPricing = account?.HasDynamicPricing ?? false
                });
            }
        }

        dialogData.ActiveAccounts = activeAccountsData;
    }

    public static void SetSelectedAccountFromDialogData(this DialogData dialogData, int accountId)
    {
        var activeAccount = dialogData?.ActiveAccounts?.Find(a => a.AccountId == accountId);

        if (dialogData != null)
        {
            dialogData.SelectedAccount = new()
            {
                AccountId = activeAccount?.AccountId ?? 0,
                BankAccountNumber = activeAccount?.BankAccountNumber,
                PostalCode = activeAccount?.PostalCode,
                HouseNumber = activeAccount?.HouseNumber,
                HouseNumberSuffix = activeAccount?.HouseNumberSuffix,
                Street = activeAccount?.Street,
                City = activeAccount?.City,
                HasDynamicPricing = activeAccount?.HasDynamicPricing ?? false
            };
        }
    }

    public static void MapAdvancePaymentAdvice(this DialogData dialogData, AdvancePaymentAdviceV2Response advice)
    {
        if (advice == null)
            return;

        dialogData.AdvancePayment ??= new AdvancePaymentData();
        dialogData.AdvancePayment.Advice = advice.Advice;
        dialogData.AdvancePayment.AdvancePaymentAdviceStatus = advice.AdviceStatus;
        dialogData.AdvancePayment.Minimum = advice.Limits.MinimumRange;
        dialogData.AdvancePayment.Maximum = advice.Limits.MaximumRange;
    }

    public static void MapAdvancePaymentModel(this DialogData dialogData, AdvancePayment info)
    {
        if (info == null)
            return;

        dialogData.AdvancePayment ??= new AdvancePaymentData();
        dialogData.AdvancePayment.AdvancePaymentAmount = info.Amount;
        dialogData.AdvancePayment.PaymentDayOfMonth = info.Preferences.PaymentDayOfMonth;
        dialogData.AdvancePayment.PaymentWithGiroCard = info.PaymentWithGiroCard;
        dialogData.AdvancePayment.IsRecurringPaymentWithPreferedPaymentDay = info.IsRecurringPaymentWithPreferedPaymentDay;
    }

    public static void MapDiscontinueIntakeModel(this DialogData dialogData, DiscontinueIntake intake)
    {
        if (intake == null)
            return;

        dialogData.DiscontinueContractData.Reasons = intake.Reasons ?? new List<Reason>();
        dialogData.DiscontinueContractData.Allowed = intake.Allowed;
        dialogData.DiscontinueContractData.AgreementId = intake.Accounts.FirstOrDefault(x => x.AccountId == dialogData.SelectedAccount.AccountId)?.AgreementId;
        dialogData.DiscontinueContractData.DiscontinueDisallowReason = intake.DiscontinueDisallowReason;
        dialogData.DiscontinueContractData.MarketingProductCode = intake.MarketingProductCode;
    }

    public static void CheckApprovedPhoneNumbers(this DialogData dialogData)
    {
        if (dialogData?.PhoneNumber?.ApprovedPhoneNumbers?.Any() != true ) return;
        
        foreach (var phoneNumber in dialogData.PhoneNumber?.ApprovedPhoneNumbers?.ToList() ?? new List<string>())
        {
            if (!string.IsNullOrEmpty(phoneNumber) && !Regex.IsMatch(phoneNumber, 
                @"(^\+[0-9]{2}|^\+[0-9]{2}\(0\)|^\(\+[0-9]{2}\)\(0\)|^00[0-9]{2}|^0)([0-9]{9}$|6\-[0-9]{8}$|[0-9]{2}\-[0-9]{7}$|[0-9]{3}\-[0-9]{6}$)",
                RegexOptions.None, TimeSpan.FromSeconds(2)))
                dialogData.PhoneNumber?.ApprovedPhoneNumbers.Remove(phoneNumber);
        }
    }

    public static bool HasTextLabels(this DialogData dialogData, string textLabelGroupName)
    {
        return dialogData?.TextLabels?.TextLabels?.Exists(t => t.GroupName == textLabelGroupName) == true;
    }
}