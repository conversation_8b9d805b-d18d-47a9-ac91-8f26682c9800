﻿using DC.Domain.Models.Products;

namespace DC.Bot.BusinessLogic.Extensions;

public static class DenotationTypeExtensions
{
    /// <summary>
    /// Parse the DenotationType enum to a userfriendly string.
    /// </summary>
    /// <param name="denotationType"></param>
    /// <returns></returns>
    public static string ParseDenotationType(this DenotationType denotationType)
    {
        return denotationType switch
        {
            DenotationType.Kwh => "kWh",
            DenotationType.M3 => "m³",
            DenotationType.GJ => "GJ",
            _ => null
        };
    }
}