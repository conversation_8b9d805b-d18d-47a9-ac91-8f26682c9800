﻿using DC.Domain.Models.Financials;

namespace DC.Bot.BusinessLogic.Extensions
{
    public static class AdvancePaymentExtensions
    {
        public static bool AdviceIsNotAvailable(this AdvancePaymentAdviceStatus status)
        {
            return status is
            AdvancePaymentAdviceStatus.SmartAndDumbMeter or
            AdvancePaymentAdviceStatus.ActiveDebtCollection or
            AdvancePaymentAdviceStatus.MeterChanged or
            AdvancePaymentAdviceStatus.NotAvailable or
            AdvancePaymentAdviceStatus.NoActiveMandate or
            AdvancePaymentAdviceStatus.MeterInError or
            AdvancePaymentAdviceStatus.AdviceTooLow or
            AdvancePaymentAdviceStatus.AdviceTooHigh or
            AdvancePaymentAdviceStatus.NoMeters or
            AdvancePaymentAdviceStatus.PaymentAmountIncorrect or
            AdvancePaymentAdviceStatus.PaymentAmountTooLow or
            AdvancePaymentAdviceStatus.PaymentAmountNotFilled or
            AdvancePaymentAdviceStatus.PaymentAmountTooLowToInvoice or
            AdvancePaymentAdviceStatus.PaymentMethodShouldBeDirectDebit or
            AdvancePaymentAdviceStatus.PaymentMethodShouldNotBeDirectDebit or
            AdvancePaymentAdviceStatus.YearlyInvoiceEstimatedOnMeterReadings or
            AdvancePaymentAdviceStatus.IbanMissingOrIncorrect;
        }
    }
}
