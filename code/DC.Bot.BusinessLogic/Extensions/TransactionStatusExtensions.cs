﻿using DC.Domain.Models.Financials;
using TransactionStatus = DC.Bot.BusinessLogic.Enumerations.TransactionStatus;

namespace DC.Bot.BusinessLogic.Extensions;

public static class TransactionStatusExtensions
{
    public static TransactionStatus GetAdvancePaymentAdviceTransactionStatus(this TransactionStatus transactionStatus, AdvancePaymentAdviceStatus? adviceStatus)
    {
        if (!adviceStatus.HasValue)
            return transactionStatus;

        return adviceStatus switch
        {
            null or AdvancePaymentAdviceStatus.Default => transactionStatus,
            AdvancePaymentAdviceStatus.Ok => TransactionStatus.Success,
            AdvancePaymentAdviceStatus.NewCustomer => TransactionStatus.UnhappyNewCustomer,
            AdvancePaymentAdviceStatus.HasWarmth => TransactionStatus.UnhappyWarmth,
            AdvancePaymentAdviceStatus.HasRedelivery => TransactionStatus.UnhappyRedelivery,
            AdvancePaymentAdviceStatus.YearNoteTooClose => TransactionStatus.UnhappyCloseToYearNote,
            AdvancePaymentAdviceStatus.UpdateAdvice or
            AdvancePaymentAdviceStatus.AdviceTooOld => TransactionStatus.UnhappyMeterreadings,
            AdvancePaymentAdviceStatus.AdviceFromLastYear or
            AdvancePaymentAdviceStatus.NoAdviceYet or
            AdvancePaymentAdviceStatus.SmartAndDumbMeter or
            AdvancePaymentAdviceStatus.ActiveDebtCollection or
            AdvancePaymentAdviceStatus.MeterChanged or
            AdvancePaymentAdviceStatus.NotAvailable or
            AdvancePaymentAdviceStatus.NoActiveMandate or
            AdvancePaymentAdviceStatus.MeterInError or
            AdvancePaymentAdviceStatus.AdviceTooLow or
            AdvancePaymentAdviceStatus.AdviceTooHigh or
            AdvancePaymentAdviceStatus.NoMeters or
            AdvancePaymentAdviceStatus.PaymentAmountIncorrect or
            AdvancePaymentAdviceStatus.PaymentAmountTooLow or
            AdvancePaymentAdviceStatus.PaymentAmountNotFilled or
            AdvancePaymentAdviceStatus.PaymentAmountTooLowToInvoice or
            AdvancePaymentAdviceStatus.PaymentMethodShouldBeDirectDebit or
            AdvancePaymentAdviceStatus.PaymentMethodShouldNotBeDirectDebit or
            AdvancePaymentAdviceStatus.YearlyInvoiceEstimatedOnMeterReadings or
            AdvancePaymentAdviceStatus.IbanMissingOrIncorrect or
            _
            => TransactionStatus.Unhappy
        };
    }
}