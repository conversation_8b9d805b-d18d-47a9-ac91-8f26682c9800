﻿using System.Text;

namespace DC.Bot.BusinessLogic.Extensions;

public static class StringBuilderExtensions
{
    public static StringBuilder AppendLineWithMarkdown(this StringBuilder stringbuilder, string value)
    {
        return stringbuilder.Append($"{value.Trim()}  \n ");
    }
    public static StringBuilder AppendBoldLineWithMarkdown(this StringBuilder stringbuilder, string value)
    {
        return AppendLineWithMarkdown(stringbuilder, $"**{value.Trim()}**");
    }
    public static StringBuilder AppendItalicLineWithMarkdown(this StringBuilder stringbuilder, string value)
    {
        return AppendLineWithMarkdown(stringbuilder, $"*{value.Trim()}*");
    }
}