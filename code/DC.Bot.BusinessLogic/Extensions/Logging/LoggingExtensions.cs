﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Models;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Schema;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
namespace DC.Bot.BusinessLogic.Extensions.Logging;

public static class LoggingExtensions
{
    /// <summary>
    /// Creates a key-value object to pass to the logging scopes. This will appear in AI as customDimensions.
    /// </summary>
    /// <param name="activity"></param>
    /// <returns></returns>
    public static Dictionary<string, object> CreateLoggingScope(this Activity activity, ConversationData conversationData = null)
    {
        var dictionary = new Dictionary<string, object>();
        dictionary.TryAdd(LoggingTags.ConversationId, activity.Conversation?.Id);
        dictionary.TryAdd(LoggingTags.Action, conversationData?.CurrentDialogAction.ToString() ?? "N/A");
        dictionary.TryAdd(LoggingTags.FromId, activity.From?.Id);
        dictionary.TryAdd(LoggingTags.FromName, activity.From?.Name);
        dictionary.TryAdd(LoggingTags.RecipientId, activity.Recipient?.Id);
        dictionary.TryAdd(LoggingTags.RecipientName, activity.Recipient?.Name);
        dictionary.TryAdd(LoggingTags.Name, activity.Name);
        dictionary.TryAdd(LoggingTags.Type, activity.Type);
        dictionary.TryAdd(LoggingTags.Text, activity.Text);

        if (activity.Value is string valueString)
        {
            dictionary.TryAdd(LoggingTags.Value, valueString);
        }
        else if (activity.Value is JToken jtoken)
        {
            foreach (var child in jtoken.Children<JProperty>())
                dictionary.TryAdd($"{LoggingTags.Value}-{child.Name}", child.Value?.ToString());
        }
        else if (activity.Value == null)
        {
            dictionary.TryAdd(LoggingTags.Value, null);
        }
        else
        {
            dictionary.TryAdd(LoggingTags.Value, $"{activity.Value.GetType()} Object");
        }


        return dictionary;
    }

    /// <summary>
    /// Creates a custom event properties dictionary, based on the session data, DialogAction and success indicator.
    /// </summary>
    /// <param name="dialogData"></param>
    /// <param name="action"></param>
    /// <param name="success"></param>
    /// <returns></returns>
    public static Dictionary<string, string> ToCustomEventProperties(this ConversationData conversationData, string dialog, string dialogStep, TransactionStatus status, ITurnContext turnContext, string message = null, bool completeDialogData = false)
    {
        conversationData.DialogData.TextLabels = new TextLabelCollection(new List<Storage.Client.Models.TextLabelModel>());
        return new Dictionary<string, string>
        {
            { "Channel", conversationData.DialogData.Channel.ToString()},
            { "Label", conversationData.DialogData.Customer.Label.ToString()},
            { "CustomerId", conversationData.DialogData.Verification?.CustomerId?.ToString()},
            { "IsVerified", conversationData.DialogData.Verification?.IsVerified?.ToString()},
            { "Action", conversationData.CurrentDialogAction.ToString()},
            { "TransactionStatus", status.ToString()},
            { "Dialog", dialog },
            { "DialogStep",dialogStep },
            { "Success", (status == TransactionStatus.Success || status == TransactionStatus.SuccessLink || status == TransactionStatus.SuccessAdviceAlMostKnown).ToString()},
            { "MalfunctionUrgency", conversationData.DialogData.ServiceOrder?.IsUrgent.ToString()},
            { "MalfunctionDevice", conversationData.DialogData.ServiceOrder?.ProductType?.ToString()},
            { "MalfunctionDescription", conversationData.DialogData.ServiceOrder?.Description},
            { "AdvancePaymentAdviceStatus", conversationData.DialogData.AdvancePayment?.AdvancePaymentAdviceStatus.ToString()},
            { "Message", message},
            { "DialogData", (completeDialogData ? JsonConvert.SerializeObject(conversationData.DialogData) : "")},
            { LoggingTags.ConversationId, turnContext?.Activity?.Conversation?.Id}
        };
    }
}