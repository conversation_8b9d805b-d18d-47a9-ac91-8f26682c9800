﻿using DC.Domain.Models.Products;
using DC.Domain.Models.Products.ProductRates;
using DC.Domain.Models.Usages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace DC.Bot.BusinessLogic.Extensions;

public static class ProductRateExtensions
{
    private static readonly List<ProductType> MeasuredProductTypes = new()
    {
        ProductType.Electricity,
        ProductType.Gas,
        ProductType.Warmth,
        ProductType.Tapwater,
        ProductType.WarmthEkv
    };

    private static readonly List<ProductType> NonMeasuredProductTypes = new()
    {
        ProductType.WarmtewinnerService,
        ProductType.BronWarmte,
        ProductType.OxxioPro,
        ProductType.KetelComfort,
        ProductType.WarmteWisselaar,
        ProductType.Huurapparaat,
        ProductType.ServiceDelgas,
        ProductType.ServiceCVKetel,
        ProductType.ServiceGevas,
        ProductType.ServiceEnergielease,
        ProductType.ServiceMetapart,
        ProductType.Toon,
        ProductType.ToonService
    };

    private static readonly List<ProductType> IsStukjeZonProductTypes = new()
    {
        ProductType.StukjeZon
    };

    private static readonly List<ProductRateDetailType> TariffTypes = new()
    {
        ProductRateDetailType.Tariff,
        ProductRateDetailType.LowTariff,
        ProductRateDetailType.TariffColdWater,
        ProductRateDetailType.TariffWarmWater
    };

    private static readonly List<ProductRateDetailType> RedeliveryTariffTypes = new()
    {
        ProductRateDetailType.RedeliveryTariff,
        ProductRateDetailType.LowRedeliveryTariff
    };

    public static bool IsStukjeZonProductType(this ProductRate productRate)
    {
        return IsStukjeZonProductTypes.Contains(productRate.ProductType) && productRate.ProductRateDetails.Count > 0;
    }
    public static bool IsMeasuredProductType(this ProductRate productRate)
    {
        return MeasuredProductTypes.Contains(productRate.ProductType) && productRate.ProductRateDetails.Count > 0;
    }
    public static bool IsNonMeasuredProductType(this ProductRate productRate)
    {
        return NonMeasuredProductTypes.Contains(productRate.ProductType) && productRate.ProductRateDetails.Count > 0;
    }

    /// <summary>
    /// Query all tariff productRateDetailType and order it by desc so the high tariff is always listed first. 
    /// </summary>
    /// <param name="productRate"></param>
    /// <returns>A string with all the data</returns>
    public static string GetProductRateDetailTariffs(this ProductRate productRate)
    {
        var stringBuilder = new StringBuilder();
        var tariffs = productRate.ProductRateDetails.Where(r => TariffTypes.Contains(r.Type)).OrderByDescending(t => t.VATIncluded);
        foreach (ProductRateDetail productRateDetail in tariffs)
        {
            stringBuilder.AppendLineWithMarkdown($"{productRateDetail.Description}: €{productRateDetail.VATIncluded} per {productRateDetail.DenotationType.GetValueOrDefault().ParseDenotationType()}");
        }
        return stringBuilder.ToString();
    }

    /// <summary>
    /// Get the RedeliveryCostTariff 
    /// </summary>
    public static string GetProductRateDetailRedeliveryCostTariffs(this ProductRate productRate)
    {
        var stringBuilder = new StringBuilder();
        var tariffs = productRate.ProductRateDetails.Where(r => r.Type == ProductRateDetailType.RedeliveryCostTariff).OrderByDescending(t => t.VATIncluded);
        foreach (ProductRateDetail productRateDetail in tariffs)
        {
            stringBuilder.AppendLineWithMarkdown($"{productRateDetail.Description}: €{Math.Abs(Math.Round(productRateDetail.VATIncluded, 3, MidpointRounding.ToZero))} per {productRateDetail.DenotationType.GetValueOrDefault().ParseDenotationType()}");
        }
        return stringBuilder.ToString();
    }

    /// <summary>
    /// Query all Redelivery Tariff and order it by desc so the high tariff is always listed first. 
    /// </summary>
    public static string GetProductRateDetailRedeliveryTariffs(this ProductRate productRate)
    {
        var stringBuilder = new StringBuilder();
        var tariffs = productRate.ProductRateDetails.Where(r => RedeliveryTariffTypes.Contains(r.Type)).OrderByDescending(t => t.VATExcluded);
        foreach (ProductRateDetail productRateDetail in tariffs)
        {
            stringBuilder.AppendLineWithMarkdown($"{productRateDetail.Description}: €{Math.Abs(Math.Round(productRateDetail.VATExcluded, 2, MidpointRounding.ToZero))} per {productRateDetail.DenotationType.GetValueOrDefault().ParseDenotationType()}");
        }
        return stringBuilder.ToString();
    }

    /// <summary>
    /// Gets the product rate detail costs by period.
    /// </summary>
    /// <param name="productRate">The product rate.</param>
    /// <param name="byPeriod">The by period.</param>
    /// <returns></returns>
    public static string GetProductRateDetailCostsByPeriod(this ProductRate productRate, ByPeriod byPeriod)
    {
        var stringBuilder = new StringBuilder();
        var tariffs = GetAllProductRateDetailByPeriod(productRate, byPeriod);
        foreach (ProductRateDetail productRateDetail in tariffs)
        {
            stringBuilder.AppendLineWithMarkdown($"{productRateDetail.Description}: €{productRateDetail.VATIncluded} per {byPeriod.ByPeriodName()}");
        }
        return stringBuilder.ToString();
    }

    /// <summary>
    /// Gets the product rate detail yearly costs calculated to monthly.
    /// </summary>
    /// <param name="productRate">The product rate.</param>
    /// <returns></returns>
    public static string GetProductRateDetailYearlyCostsCalculatedToMonthly(this ProductRate productRate)
    {
        var stringBuilder = new StringBuilder();
        var tariffs = GetAllProductRateDetailByPeriod(productRate, ByPeriod.Y);
        foreach (ProductRateDetail productRateDetail in tariffs)
        {
            stringBuilder.AppendLineWithMarkdown($"{productRateDetail.Description}: €{Math.Round((productRateDetail.VATIncluded / 12), 2, MidpointRounding.ToEven)} per {ByPeriod.M.ByPeriodName()}");
        }
        return stringBuilder.ToString();
    }
    /// <summary>
    /// Return IsDoubleTariff in the output in case of producttype electricity
    /// </summary>
    /// <param name="productRate"></param>
    /// <returns>A string with all the data</returns>
    public static string HasDoubleTariffIndicator(this ProductRate productRate, string text)
    {
        if (productRate.ProductType == ProductType.Electricity)
        {
            string doubletariff = "Nee";
            if (productRate.IsDoubleTariff.Value)
            {
                doubletariff = "Ja";
            }
            text = text.Replace("{doubleTariff}", doubletariff);
            return text;
        }
        return string.Empty;
    }

    /// <summary>
    /// Gets the non variable product rate details.
    /// </summary>
    /// <param name="productRate">The product rate.</param>
    /// <returns></returns>
    public static string GetNonVariableProductRateDetails(this ProductRate productRate)
    {
        var stringBuilder = new StringBuilder();
        string period = null;
        if (productRate.ProductType == ProductType.BronWarmte)
        {
            foreach (ProductRateDetail productRateDetail in productRate.ProductRateDetails)
            {
                if (productRateDetail?.ByPeriod.HasValue == true)
                    period = productRateDetail.ByPeriod.Value.ByPeriodNameNoneMeausered();
                else
                    period = "eenmalig";

                stringBuilder.AppendLineWithMarkdown($"{productRateDetail?.Description}: €{Math.Round(productRateDetail?.VATIncluded ?? 0, 2)} {period}");
            }
            return stringBuilder.ToString();
        }
        else
        {
            var productRateDetail = productRate.ProductRateDetails.Find(d => !d.IsVariable);

            if (productRateDetail?.ByPeriod.HasValue == true)
                period = productRateDetail.ByPeriod.Value.ByPeriodNameNoneMeausered();
            else
                period = "eenmalig";

            stringBuilder.AppendLineWithMarkdown($"{productRateDetail?.Description}: €{Math.Round(productRateDetail?.VATIncluded ?? 0, 2)} {period}");
        }



        return stringBuilder.ToString();
    }

    /// <summary>
    /// Gets all product rate detail by period.
    /// </summary>
    /// <param name="productRate">The product rate.</param>
    /// <param name="byPeriod">The by period.</param>
    /// <returns></returns>
    private static List<ProductRateDetail> GetAllProductRateDetailByPeriod(this ProductRate productRate, ByPeriod byPeriod)
    {
        return productRate.ProductRateDetails.Where(d => d.ByPeriod == byPeriod && d.Type != ProductRateDetailType.TaxDiscount).OrderBy(t => t.VATIncluded).ToList();
    }

    /// <summary>
    /// Bies the name of the period.
    /// </summary>
    /// <param name="byPeriod">The by period.</param>
    /// <returns></returns>
    private static string ByPeriodName(this ByPeriod byPeriod)
    {
        return byPeriod switch
        {
            ByPeriod.Y => "jaar",
            ByPeriod.M => "maand",
            _ => throw new NotImplementedException()
        };
    }

    private static string ByPeriodNameNoneMeausered(this ByPeriod byPeriod)
    {
        return byPeriod switch
        {
            ByPeriod.D => "per dag",
            ByPeriod.M => "per maand",
            ByPeriod.Y => "per jaar",
            _ => "eenmalig"
        };
    }
}