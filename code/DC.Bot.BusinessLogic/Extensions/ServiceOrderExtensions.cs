﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Products.Client.Models;

namespace DC.Bot.BusinessLogic.Extensions;

public static class ServiceOrderExtensions
{
    public static ServiceOrderProductType ToServiceOrderProductType(this GroupDescription groupDescription)
    {
        return groupDescription switch
        {
            GroupDescription.EBoiler => ServiceOrderProductType.Boiler,
            GroupDescription.Warmtepompboiler => ServiceOrderProductType.Boiler,
            GroupDescription.Ventilatorgeiser => ServiceOrderProductType.Gei<PERSON>,
            GroupDescription.Geiser => ServiceOrderProductType.Gei<PERSON>,
            GroupDescription.CvKetel => ServiceOrderProductType.CVKetel,
            GroupDescription.CvHaardofstaandecvKetel => ServiceOrderProductType.CVKetel,
            GroupDescription.Voorzetbrander => ServiceOrderProductType.CVKetel,
            _ => ServiceOrderProductType.Unknown
        };
    }

    public static ServiceOrderProductType? ToServiceOrderProductType(this string hardwareType)
    {
        return hardwareType.ToLower() switch
        {
            "boiler" => ServiceOrderProductType.Boiler,
            "geiser" => ServiceOrderProductType.Geiser,
            "cv-ketel" => ServiceOrderProductType.CVKetel,
            "cvketel" => ServiceOrderProductType.CVKetel,
            _ => null
        };
    }

    public static bool ToServiceOrderUrgent(this string urgent)
    {
        return urgent switch
        {
            string a when !string.IsNullOrWhiteSpace(a) && a.Equals("urgent", System.StringComparison.CurrentCultureIgnoreCase) => true,
            _ => false
        };
    }
}