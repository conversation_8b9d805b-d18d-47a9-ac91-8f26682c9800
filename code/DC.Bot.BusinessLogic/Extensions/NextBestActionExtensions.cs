﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs.Customers;
using DC.Bot.BusinessLogic.Dialogs.Customers.PhoneNumberDialogs;
using DC.Bot.BusinessLogic.Dialogs.Usages;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.NextBestAction;
using DC.Domain.Models.NextBestAction;
using DC.Products.Client.Models;
using DC.Storage.Client.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DC.Bot.BusinessLogic.Extensions;

public static class NextBestActionExtensions
{
    private static readonly string _textLabelGroupName = "Bot_NextBestActionDialog";

    /// <summary>
    /// Returns the NextBestAction object filtered by only the Chatbot supported actions
    /// </summary>
    /// <param name="nextBestAction"></param>
    /// <returns></returns>
    public static NextBestAction FilterSupportedActions(this NextBestAction nextBestAction, List<TextLabelModel> textLabels = null)
    {
        nextBestAction.Actions = nextBestAction.Actions?.Where(a => a.ActionType.GetNextBestActionConfiguration(nextBestAction, textLabels).IsSupported).ToList() ?? [];
        return nextBestAction;
    }

    /// <summary>
    /// Returns a configuration object with a flag wether the action type is supported, and if so the Followup dialog name and the prompt text.
    /// </summary>
    /// <param name="actionType"></param>
    /// <returns></returns>
    public static NextBestActionConfiguration GetNextBestActionConfiguration(this string actionType, NextBestAction nextBestAction, List<TextLabelModel> textLabels = null)
    {
        var action = nextBestAction.Actions?.FirstOrDefault();
        return actionType switch
        {
            "BankAccountNumber" => new NextBestActionConfiguration
            {
                IsSupported = true,
                FollowUpDialogName = nameof(ChangeIbanDialog),
                PromptText = textLabels?.GetTreatementVariantTextLabel(action, "BankAccountNumberPromptText", _textLabelGroupName)?.Value,
                ContinueChoiceText = textLabels?.GetTreatementVariantTextLabel(action, "BankAccountNumberChoiceText", _textLabelGroupName)?.Value,
                ContinueFollowUpText = textLabels?.GetTreatementVariantTextLabel(action, "BankAccountNumberFollowUpText", _textLabelGroupName)?.Value,
            },
            "CustomerPhoneNumber" => new NextBestActionConfiguration
            {
                IsSupported = true,
                FollowUpDialogName = nameof(ChangePhoneNumberDialog),
                PromptText = textLabels?.GetTreatementVariantTextLabel(action, "CustomerPhoneNumberPromptText", _textLabelGroupName)?.Value,
                ContinueChoiceText = textLabels?.GetTreatementVariantTextLabel(action, "CustomerPhoneNumberChoiceText", _textLabelGroupName)?.Value,
                ContinueFollowUpText = textLabels?.GetTreatementVariantTextLabel(action, "CustomerPhoneNumberFollowUpText", _textLabelGroupName)?.Value
            },
            "CustomerEmailAddress" => new NextBestActionConfiguration
            {
                IsSupported = true,
                FollowUpDialogName = nameof(ChangeEmailDialog),
                PromptText = textLabels?.GetTreatementVariantTextLabel(action, "CustomerEmailAddressPromptText", _textLabelGroupName)?.Value,
                ContinueChoiceText = textLabels?.GetTreatementVariantTextLabel(action, "CustomerEmailAddressChoiceText", _textLabelGroupName)?.Value,
                ContinueFollowUpText = textLabels?.GetTreatementVariantTextLabel(action, "CustomerEmailAddressFollowUpText", _textLabelGroupName)?.Value
            },
            "Isma" => new NextBestActionConfiguration
            {
                IsSupported = true,
                FollowUpDialogName = nameof(IsmaMandateDialog),
                PromptText = textLabels?.GetTreatementVariantTextLabel(action, "IsmaPromptText", _textLabelGroupName)?.Value,
                ContinueChoiceText = textLabels?.GetTreatementVariantTextLabel(action, "IsmaChoiceText", _textLabelGroupName)?.Value,
                ContinueFollowUpText = textLabels?.GetTreatementVariantTextLabel(action, "IsmaFollowUpText", _textLabelGroupName)?.Value
                ?.Replace("{GedragscodeLeveranciersSlimmeMeters}", DialogContent.GedragscodeLeveranciersSlimmeMeters)
            },
            _ => new NextBestActionConfiguration
            {
                IsSupported = false,
                FollowUpDialogName = null,
                PromptText = null,
                ContinueChoiceText = null
            },
        };
    }

    /// <summary>
    /// Gets the treatement variant text label.
    /// </summary>
    /// <param name="textLabels">The text labels.</param>
    /// <param name="action">The action.</param>
    /// <param name="key">The key.</param>
    /// <param name="groupName">The group name.</param>
    /// <returns>A TextLabelModel.</returns>
    public static TextLabelModel GetTreatementVariantTextLabel(this List<TextLabelModel> textLabels, ActionModel action, string key, string groupName)
    {
        var textlabel = textLabels?.Find(x => x.Key == key && x.GroupName == groupName && !string.IsNullOrEmpty(x.Filter) && x.Filter == $"TreatmentVariationId_{action?.TreatmentVariationId ?? 0}");
        return textlabel ?? textLabels?.Find(x => x.Key == key && x.GroupName == groupName && string.IsNullOrEmpty(x.Filter));
    }

    /// <summary>
    /// Returns the object to be included in the CustomerInfo event.
    /// </summary>
    /// <param name="nbaResponse"></param>
    /// <returns></returns>
    public static NextBestActionInfo ToNextBestActionInfo(this NextBestAction nbaResponse)
    {
        var type = nbaResponse?.FilterSupportedActions()?.Actions?.OrderByDescending(a => a.Score).FirstOrDefault()?.ActionType;
        return new NextBestActionInfo
        {
            Available = !string.IsNullOrWhiteSpace(type),
            Type = type
        };
    }

    /// <summary>
    /// Enriches a ActionFeedbackData model with the available data.
    /// This is part of a mutation request model that triggers a NBA conversation in the PAPI layer.
    /// </summary>
    public static ActionFeedbackData EnrichActionFeedbackData(this ActionFeedbackData model, string actionType, DialogData dialogData, FeedbackStatus feedbackStatus = FeedbackStatus.Unknown)
    {
        if ((dialogData.NextBestAction?.CurrentTransactionIsNba) != true)
        {
            return null;
        }
        var nbaData = dialogData.NextBestAction.Data;
        var action = nbaData?.Actions?.OrderBy(a => a.Score).FirstOrDefault(a => a.ActionType == actionType);
        if (action == null)
        {
            return null;
        }

        model.AccountId = (dialogData.SelectedAccount?.AccountId ?? 0) > 0 ? dialogData.SelectedAccount.AccountId : null;
        model.ActionId = action.ActionId;
        model.Channel = Channel.Chatbot;
        model.ContextId = nbaData.ContextId;
        model.ServingPointId = action.ServingPointId;
        model.TimeStampActionReacted = DateTime.Now;
        model.VariationId = action.TreatmentVariationId;
        model.Status = feedbackStatus;
        return model;
    }
}
