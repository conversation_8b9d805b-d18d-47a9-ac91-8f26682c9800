﻿using DC.Bot.BusinessLogic.Constants;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.Products;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DC.Bot.BusinessLogic.Extensions
{
    public static class ProductModelExtensions
    {
        /// <summary>
        /// Get End Date String
        /// </summary>
        public static string GetEndDateString(this ProductModel productModel)
        {
            if (productModel.EndDateContract.HasValue)
            {
                return $"Einddatum: {productModel.EndDateContract.Value:dd-MM-yyyy}";
            }
            return "Contract: onbepaalde tijd";
        }

        /// <summary>
        /// Called when [warmth elements].
        /// </summary>
        public static bool OnlyWarmthElements(this IList<ProductModel> products)
        {
            return products?.All(p => p.Type?.Name == ProductType.Warmth ||
            p.Type?.Name == ProductType.WarmthEkv ||
            p.Type?.Name == ProductType.Tapwater ||
            p.Type?.Name == ProductType.Cooling ||
            p.Type?.Name == ProductType.WarmteWisselaar ||
            p.Type?.Name == ProductType.WarmthProducts) == true;
        }

        /// <summary>
        /// Called when [electricity].
        /// </summary>
        public static bool OnlyElectricity(this IList<ProductModel> products)
        {
            return products?.All(p => p.Type?.Name == ProductType.Electricity ||
            p.Type?.Name == ProductType.Happypower) == true;
        }

        /// <summary>
        /// Any electricity product
        /// </summary>
        public static bool AnyElectricity(this IList<ProductModel> products)
        {
            return products?.Any(p => p.Type?.Name == ProductType.Electricity ||
            p.Type?.Name == ProductType.Happypower) == true;
        }


        /// <summary>
        /// Called when [warmth elements and electricity].
        /// </summary>
        public static bool OnlyWarmthElementsAndElectricity(this IList<ProductModel> products)
        {
            return products?.Any(p => p.Type?.Name == ProductType.Warmth ||
            p.Type?.Name == ProductType.WarmthEkv ||
            p.Type?.Name == ProductType.Tapwater ||
            p.Type?.Name == ProductType.WarmteWisselaar ||
            p.Type?.Name == ProductType.Cooling ||
            p.Type?.Name == ProductType.WarmthProducts) == true &&
            products.Any(p => p.Type?.Name == ProductType.Electricity);
        }

        /// <summary>
        /// Status = Success 
        /// Check if it has both E & G contracts where at least 
        ///   - 1 is indefinite (meaning a combo Indefinite/Definite) OR 1 or more are model contracts
        /// </summary>
        public static bool IsCombinationOfElectricityGasIndefiniteAndModelContract(this IList<ProductModel> products)
        {
            int countElectricity = products?.Count(p => p.Type?.Name == ProductType.Electricity) ?? 0;

            int countGas = products?.Count(p => p.Type?.Name == ProductType.Gas) ?? 0;

            //Is any EG contract indefinite?
            int countEGIndefinite = products?.Count(p => (p.Type?.Name == ProductType.Gas || p.Type?.Name == ProductType.Electricity) && p.Indefinite == true) ?? 0;

            //Is any EG a model contract?
            int countModelContract = CountModelContracts(products);

            return countElectricity > 0 && countGas > 0 && (countEGIndefinite == 1 || countModelContract > 0);
        }

        /// <summary>
        /// Identifying a Model contract is not a straight forward thing, was discussed to use MPC codes, but in test cases was identified
        /// Model contracts that were not falling in the given MPC codes, so was decided to use the "modelcontract" in the description 
        /// which is understandably not the most elegant code solution.
        /// </summary>
        private static int CountModelContracts(IList<ProductModel> products)
        {
            int countContracts = products?.Count(p => ModelContractsMarketingTypes.ContainsCode(p.Type?.MarketingCode)) ?? 0;

            return countContracts;
        }

        /// <summary>
        /// Called when [gas].
        /// </summary>
        public static bool OnlyGas(this IList<ProductModel> products)
        {
            return products?.All(p => p.Type?.Name == ProductType.Gas) == true;
        }


        /// <summary>
        /// Gets the main e g products.
        /// </summary>
        public static List<ProductModel> GetMainEGProducts(this IList<ProductModel> products)
        {
            return products?.Where(p => p.Type?.Name == ProductType.Gas ||
                                        p.Type?.Name == ProductType.Electricity).ToList();
        }

        /// <summary>
        /// Have the contract end date.
        /// </summary>
        public static bool HasContractEndDate(this IList<ProductModel> products)
        {
            return products?.Any(p => p.IsActive && p.EndDateContract.HasValue) == true;
        }

        /// <summary>
        /// Contracts the in reconsideration period.
        /// </summary>
        public static bool ContractInReconsiderationPeriod(this IList<ProductModel> products)
        {
            return products?.Any(p => p.IsActive && p.StartDate.HasValue && p.StartDate.Value >= DateTime.Today && p.StartDate.Value <= DateTime.Today.AddDays(14)) == true;
        }

        /// <summary>
        /// Get Main EGW Products - using ProductMode
        /// </summary>
        public static List<ProductModel> GetMainEGWProducts(this IList<ProductModel> products)
        {
            return products?.Where(p => p.Type?.Name == ProductType.Gas ||
                                        p.Type?.Name == ProductType.Electricity ||
                                        p.Type?.Name == ProductType.Warmth ||
                                        p.Type?.Name == ProductType.WarmthEkv ||
                                        p.Type?.Name == ProductType.Tapwater).ToList();
        }

        /// <summary>
        /// Get Main EGW Products - using Agreements
        /// </summary>
        public static List<Agreement> GetMainEGWProducts(this IList<Agreement> agreements)
        {
            return agreements?.Where(a => a.IsActive && a.Products?.Any(p => p.IsActive &&
                                        (p.ProductType == ProductType.Gas ||
                                        p.ProductType == ProductType.Electricity ||
                                        p.ProductType == ProductType.Warmth ||
                                        p.ProductType == ProductType.WarmthEkv ||
                                        p.ProductType == ProductType.Tapwater)) == true).ToList();
        }

        /// <summary>
        /// check if contract duration is less than the given duration based on the startdate and duration
        /// </summary>
        public static bool ContractDurationIsLessThan(this ProductModel product, int duration)
        {
            if (product.StartDate.HasValue && product.EndDateContract.HasValue)
            {
                var endDateContract = product.StartDate.Value.AddMonths(product.Duration);
                var endDateDuration = DateTime.Today.AddDays(duration);
                return endDateContract <= endDateDuration;
            }
            else
            {
                return false;
            }
        }
    }
}