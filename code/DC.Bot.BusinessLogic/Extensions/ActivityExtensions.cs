﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Models;
using DC.Repositories.Base.Enumerations;
using DC.Security.Encryption;
using DC.Telemetry.Models;
using Microsoft.Bot.Schema;
using Microsoft.Extensions.Configuration;
using MimeTypes;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using Activity = Microsoft.Bot.Schema.Activity;

namespace DC.Bot.BusinessLogic.Extensions;

public static class ActivityExtensions
{
    /// <summary>
    /// Gets the tranactions start value from a startOfTransaction event activity
    /// Also parses the text command for L/D/T/A environments for test purposes
    /// </summary>
    /// <param name="activity"></param>
    /// <returns></returns>
#pragma warning disable S3776 // Cognitive Complexity of methods should not be too high
    public static TransactionStartValue GetTransactionStartValue(this IActivity activity)
#pragma warning restore S3776 // Cognitive Complexity of methods should not be too high
    {
        if (activity.IsStartOfTransactionActivity(out var eventActivity))
        {
            // default label is Eneco
            Label label = Label.Eneco;

            if (eventActivity.Value is TransactionStartValue transactionStartValue)
                return transactionStartValue;

            // try to parse the label from the value object
            dynamic eventValue = eventActivity.Value;
            if (Enum.TryParse(eventValue.label?.ToString(), true, out Label labelParsed) && labelParsed != Label.Default)
            {
                label = labelParsed;
            }

            // default channel is Web
            BotChannel channel = BotChannel.Web;
            if (Enum.TryParse(eventValue.channel?.ToString(), true, out BotChannel channelParsed))
            {
                channel = channelParsed;
            }

            // return as TransactionStartValue model
            return new TransactionStartValue
            {
                Label = label,
                StartCommand = eventValue?.startCommand?.ToString() ?? string.Empty,
                Channel = channel,
                Authorization = eventValue?.authorization?.ToString() ?? string.Empty,
                Hardware = eventValue?.malfunctionDevice?.ToString() ?? string.Empty,
                Urgent = eventValue?.malfunctionUrgency?.ToString() ?? string.Empty,
                Description = eventValue?.malfunctionDescription?.ToString() ?? string.Empty,
                IsNba = eventValue?.isNba ?? false,
                CustomerId = eventValue?.customerId
            };
        }


#pragma warning disable S1135 // Intentional todo
        // TODO ? : restrict production for regular message start events
#pragma warning restore S1135 // Intentional todo

        var messageActivity = activity.AsMessageActivity();
        if (messageActivity != null)
        {

            Label? providedLabel = null;
            // if optional label param is passed, parse it to a Label enum
            if (messageActivity.Text.Contains("-Label"))
            {
                var split = messageActivity.Text.Split("-Label");
                var labelText = split[1].Trim().Split("-")[0].Trim();
                if (Enum.TryParse(labelText, true, out Label parsed))
                    providedLabel = parsed;
            }

            //Default = web
            BotChannel providedChannel = BotChannel.Web;
            if (messageActivity.Text.Contains("-Channel"))
            {
                var split = messageActivity.Text.Split("-Channel");
                var channelText = split[1].Trim().Split("-")[0].Trim();
                if (Enum.TryParse(channelText, true, out BotChannel parsed))
                    providedChannel = parsed;
            }
            var authorization = string.Empty;
            if (messageActivity.Text.Contains("-Authorization"))
            {
                var split = messageActivity.Text.Split("-Authorization");
                authorization = split[1].Trim().Split("-")[0].Trim();
            }

            // Because a variable may contain a dash - (ie: cv-ketel) split on |
            var urgent = string.Empty;
            if (messageActivity.Text.Contains("|MalfunctionUrgency"))
            {
                var split = messageActivity.Text.Split("|MalfunctionUrgency");
                urgent = split[1].Trim().Split("|")[0].Trim();
            }

            var hardware = string.Empty;
            if (messageActivity.Text.Contains("|MalfunctionDevice"))
            {
                var split = messageActivity.Text.Split("|MalfunctionDevice");
                hardware = split[1].Trim().Split("|")[0].Trim();
            }

            var description = string.Empty;
            if (messageActivity.Text.Contains("|MalfunctionDescription"))
            {
                var split = messageActivity.Text.Split("|MalfunctionDescription");
                description = split[1].Trim().Split("|")[0].Trim();
            }

            // start command will be the text value. Remove any characters after '-' for filtering out additional parameters
            var startCommand = messageActivity.Text.Split('-')[0];

            // return TransactionStartValue with Eneco as default label
            return new TransactionStartValue
            {
                Label = providedLabel ?? Label.Eneco,
                StartCommand = startCommand,
                Channel = providedChannel,
                Authorization = authorization,
                Description = description,
                Hardware = hardware,
                Urgent = urgent
            };
        }
        return null;
    }

    public static bool IsStartOfTransactionActivity(this IActivity activity, out IEventActivity eventActivity)
    {
        return activity.IsEventActivityWithName("startOfTransaction", out eventActivity);
    }

    private static bool IsEventActivityWithName(this IActivity activity, string name, out IEventActivity eventActivity)
    {
        eventActivity = activity.AsEventActivity();
        return eventActivity != null && eventActivity.Name == name;
    }

    public static IEventActivity CreateEncryptedEvent<T>(this T objectToEncrypt, IConfiguration configuration, bool encrypted = true)
    {
        var typeName = typeof(T).Name;
        var encryptionKey = configuration["AesEncryption:Key"];
        var json = JsonConvert.SerializeObject(objectToEncrypt);
        var eventActivity = Activity.CreateEventActivity();
        eventActivity.Name = char.ToLowerInvariant(typeName[0]) + typeName[1..];
        eventActivity.Value = encrypted ? AesEncryption.Encrypt(json, encryptionKey) : json;
        return eventActivity;
    }

    public static IMessageActivity CreateCustomMessageAttachment<T>(this T objectToSend, string type = null)
    {
        if (type == null)
            type = typeof(T).Name;
        var customMessageAttachmentValue = new CustomMessageAttachmentValue<T>
        {
            Data = objectToSend,
            Type = type
        };
        var messageActivity = Activity.CreateMessageActivity();
        messageActivity.Text = $"{type}data";
        messageActivity.Attachments = new List<Attachment>() { new Attachment() { Content = customMessageAttachmentValue, ContentType = "application/vnd.seamly.custom-message" } };
        return messageActivity;
    }

    public static IMessageActivity CreateMessageWithExpectedAttachment(string message, int maxSize = 4194304, params string[] allowedMimeTypes)
    {
        var messageActivity = Activity.CreateMessageActivity();
        messageActivity.Text = message;
        if (allowedMimeTypes == null || allowedMimeTypes.Length == 0)
        {
            // default is word(doc, docx), pdf, png, jpeg/jpg
            allowedMimeTypes = new[]
            {
                "application/pdf",
                "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "image/png",
                "image/jpeg"
            };
        }
        messageActivity.Entities.Add(new Entity
        {
            Properties = JObject.FromObject(new EntityProperties<ExpectedEntryEntity>("expectedEntry", new ExpectedEntryEntity
            {
                Type = ExpectedEntryType.Upload,
                MaxSize = maxSize,
                AllowedMimeTypes = allowedMimeTypes
            }))
        });
        return messageActivity;
    }

    public static string ToFilename(this DialogData userdata, string prefix, string contentType)
    {
        var extension = MimeTypeMap.GetExtension(contentType, false);
        return $"{prefix}_{userdata.Verification.CustomerId}-{userdata.SelectedAccount.AccountId}{extension}";
    }
}