﻿using DC.Bot.BusinessLogic.Models;
using Microsoft.Bot.Builder.Dialogs;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Extensions;

public static class PromptValidatorContextExtensions
{
    public static async Task<bool> ReachedMaxAmountAttempts(
        this PromptValidatorContext<string> promptContext, 
        DialogData dialogData,
        CancellationToken cancellationToken,
        int maxAttempts = 3,
        params string[] retryPrompText)
    {
        if (promptContext.AttemptCount >= maxAttempts)
        {
            dialogData.Verification.TooManyAttempts = true;
            return true;
        }

        for(int i = 0; i < retryPrompText.Length; i++)
        {
            await promptContext.Context.SendActivityAsync(retryPrompText[i], cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        return false;
    }
}