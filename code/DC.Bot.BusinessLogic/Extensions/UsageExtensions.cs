﻿using DC.Domain.Models.Agreements;
using DC.Domain.Models.Products;
using DC.Usages.Client.Models;
using DC.Utilities.Formatters;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace DC.Bot.BusinessLogic.Extensions;

public static class UsageExtensions
{
    public static string GetDescriptionByMonth(this UsageItem usage, int month, string measureUnit)
    {
        if (usage != null)
        {
            var fullMonth = new DateTime(DateTime.Today.Year, month, 1, 0, 0, 0, DateTimeKind.Utc).ToString("MMMM", new CultureInfo("NL-nl"));
            if (usage.IsDoubleTariff && usage.Low > 0)
            {
                return $"{fullMonth}: dal {Math.Round(usage.Low, 2).ToString("0.##").Replace(".", ",")} {measureUnit} / normaal {Math.Round(usage.High, 2).ToString("0.#####").Replace(".", ",")} {measureUnit}";
            }

            return $"{fullMonth}: {Math.Round(usage.High, 2).ToString("0.##").Replace(".", ",")} {measureUnit}";
        }
        return string.Empty;
    }

    public static string GetProductName(this ProductType productType)
    {
        return productType switch
        {
            ProductType.Electricity => "Stroom",
            ProductType.Gas => "Gas",
            ProductType.Warmth => "Warmte",
            ProductType.Tapwater => "Warm tapwater",
            ProductType.Cooling => "Koude",
            _ => null
        };
    }

    public static string GetDescriptionByProduct(this List<ProductUsage> usages, ProductType productType)
    {
        if (usages.HasDoubleRate())
        {
            var low = usages.First(i => i.CounterType == Domain.Models.Usages.CounterType.ElectricityLow);
            var high = usages.First(i => i.CounterType == Domain.Models.Usages.CounterType.ElectricityHigh);
            return $"{productType.GetProductName()}: normaal {high.Quantity} {high.DenotationType.GetDenotationTypeName()} / dal {low.Quantity} {low.DenotationType.GetDenotationTypeName()}";
        }

        var usage = usages.FirstOrDefault();
        return $"{productType.GetProductName()}: {usage?.Quantity} {usage?.DenotationType.GetDenotationTypeName()}";
    }

    public static string GetDescriptionByProductTotal(this List<ProductUsage> usages, ProductType productType)
    {
        return $"{productType.GetProductName()} totaal: {usages.Sum(u => u.Quantity)} {usages[0].DenotationType.GetDenotationTypeName()}";
    }

    public static bool HasDoubleRate(this List<ProductUsage> usages) =>
        usages?.Count > 1 && usages.Count(i => i.CounterType == Domain.Models.Usages.CounterType.ElectricityLow || i.CounterType == Domain.Models.Usages.CounterType.ElectricityHigh) == 2;

    public static string GetDenotationTypeName(this DenotationType denotationType)
    {
        return denotationType switch
        {
            DenotationType.M3 => "m³",
            _ => denotationType.ToEnumMemberValue()
        };
    }
    public static RequestDataReportRequestModel ToRequestDataReportRequestModel(this string email)
    {
        return new RequestDataReportRequestModel
        {
            Data = new ReportRequestModel
            {
                Email = email
            }
        };
    }
}