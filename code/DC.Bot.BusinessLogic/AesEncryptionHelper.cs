﻿using DC.Security.Encryption;
using Newtonsoft.Json;

namespace DC.Bot.BusinessLogic;
public static class AesEncryptionHelper
{
    public static string Encrypt<T>(T value, string encryptionKey, string aesIv)
    {
        var deserialised = JsonConvert.SerializeObject(value);
        return AesEncryption.Encrypt(deserialised, encryptionKey, aesIv);
    }

    public static T Decrypt<T>(string value, string encryptionKey)
    {
        var stringValue = AesEncryption.Decrypt(value, encryptionKey);
        return JsonConvert.DeserializeObject<T>(stringValue);
    }
}
