﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.BusinessLogic.Base;
using DC.Customers.Client.Models;
using DC.Domain.Exceptions;
using DC.Domain.Exceptions.Helpers;
using DC.Domain.Exceptions.ResponseModels;
using DC.Domain.Models.Accounts;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.Customers;
using DC.Domain.Models.NextBestAction;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using DC.Usages.Client.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using CustomerMutation = DC.Customers.Client.Models.CustomerMutation;

namespace DC.Bot.BusinessLogic;

public class CustomersService : BaseService, ICustomersService
{
    private readonly IDcCustomersRepository _customersRepository;
    private readonly IProductsService _productsService;
    private readonly IDcUsagesRepository _usagesRepository;
    private readonly string _currentDomain;

    /// <summary>
    /// Constructor
    /// </summary>
    public CustomersService(
        ILoggerFactory loggerFactory,
        IConfiguration configuration,
        IDcCustomersRepository customersRepository,
        IProductsService productsService,
        IDcUsagesRepository usagesRepository) : base(loggerFactory, configuration)
    {
        _customersRepository = customersRepository;
        _productsService = productsService;
        _usagesRepository = usagesRepository;

        _currentDomain = "https://www.acc.LABEL.nl";

        if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == Environments.Production)
        {
            _currentDomain = "https://www.LABEL.nl";
        }
    }

    /// <summary>
    /// Get Customer (Customer PAPI)
    /// </summary>
    public async Task<CustomerModel> GetCustomer(DialogData dialogData)
    {
        var apiCall = _customersRepository.GetCustomer(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0));
        var onSuccess = new Func<HttpOperationResponse<object>, CustomerModel>(httpResponse => ((ResponseDataCustomerModel)httpResponse.Body).Data);
        return await ExecuteDcCall<CustomerModel, ErrorResponse>(apiCall, onSuccess, new Guid("5e6bd2ff-ab44-41d5-806e-56d4ef85f8f8"), nameof(GetCustomer)).ConfigureAwait(false);
    }

    /// <summary>
    /// Get Customer (Customer PAPI)
    /// </summary>
    public async Task<PaymentPlan> GetPaymentPlan(DialogData dialogData)
    {
        var apiCall = _customersRepository.GetPaymentPlan(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), dialogData.SelectedAccount.AccountId);
        var onSuccess = new Func<HttpOperationResponse<object>, PaymentPlan>(httpResponse => ((ResponseDataPaymentPlan)httpResponse.Body).Data);
        return await ExecuteDcCall<PaymentPlan, ErrorResponse>(apiCall, onSuccess, new Guid("3e3a0ea2-6dbb-4ce1-964e-7d9a8af1c8fa"), nameof(GetPaymentPlan)).ConfigureAwait(false);
    }

    /// <summary>
    /// If verified with token, enrich the dialogdata object with all the information from the back-end.
    /// </summary>
    public void EnrichDialogData(DialogData dialogData, CustomerModel customer, NextBestAction nextBestActionData)
    {
        if (dialogData.Verification.IsVerifiedWithToken)
        {
            dialogData.Customer.Name = customer.GetName();
            dialogData.Customer.Firstname = customer.GetFirstname();
            dialogData.Customer.Surname = customer.GetSurname();
            dialogData.Customer.Gender = customer.GetGender();
            dialogData.Customer.EmailAddress = customer.Contact?.EmailAddress;
            dialogData.NextBestAction.Data = nextBestActionData;
            dialogData.Customer.CurrentDomain = _currentDomain.Replace("LABEL", dialogData.Customer.Label.ToString().ToLowerInvariant());
        }
    }

    /// <summary>
    /// Get CustomerV2 (Customer PAPI)
    /// </summary>
    public async Task<Customer> GetCustomerV2(Label label, BotChannel botChannel, long customerId)
    {
        var apiCall = _customersRepository.GetCustomerV2(label, botChannel, customerId);
        var onSuccess = new Func<HttpOperationResponse<object>, Customer>(httpResponse => ((ResponseDataCustomer)httpResponse.Body).Data);
        return await ExecuteDcCall<Customer, ErrorResponse>(apiCall, onSuccess, new Guid("d6c111a0-e1fe-41cb-815c-ca450f05bd86"), nameof(GetCustomerV2)).ConfigureAwait(false);
    }

    /// <summary>
    /// Verify Customer (Customer PAPI - public).
    /// Returns the dialog data in which the customer verification details have been updated
    /// </summary>
    public async Task<DialogData> VerifyCustomer(DialogData dialogData)
    {
        // map request
        var request = dialogData.ToCustomerVerificationRequest();
        try
        {
            // not using ExecuteDcCall because response code 428 is not a real error, just an intermediate status
            var httpResponse = await _customersRepository.VerifyCustomer(dialogData.Customer.Label, dialogData.Channel, request).ConfigureAwait(false);
            if (httpResponse.Response.StatusCode is HttpStatusCode.OK or HttpStatusCode.PreconditionRequired)
            {
                var responseBody = ((ResponseDataCustomerVerificationResponse)httpResponse.Body).Data;
                dialogData.Verification.IsFound = true;
                dialogData.Verification.IsPossiblyOrganisation = responseBody.IncludesOrganisations;

                // verified is 200, not yet fully verified is 428
                if (httpResponse.Response.StatusCode == HttpStatusCode.OK)
                {
                    dialogData.Verification.IsVerified = true;
                    dialogData.Verification.CustomerId = responseBody.CustomerId;
                }
                else
                {
                    dialogData.Verification.IsVerified = false;
                }

                //Set Customer current domain:
                if (string.IsNullOrEmpty(dialogData.Customer.CurrentDomain))
                {
                    dialogData.Customer.CurrentDomain = _currentDomain.Replace("LABEL", dialogData.Customer.Label.ToString().ToLowerInvariant());
                }

                return dialogData;
            }

            if (httpResponse.Response.StatusCode == HttpStatusCode.NoContent)
            {
                if (request.Data.IbanSuffixes == null && request.Data.CustomerId == null && request.Data.DatesOfBirth == null && request.Data.PhoneNumber == null)
                {
                    dialogData.Verification.IsFound = false;
                    dialogData.Verification.IsVerified = false;
                    return dialogData;
                }

                // retry with only address to check for real 204 or 428
                var retryWithOnlyAddress = new DialogData
                {
                    Verification = new VerificationData
                    {
                        PostalCode = dialogData.Verification.PostalCode,
                        HouseNumber = dialogData.Verification.HouseNumber,
                        HouseNumberSuffix = dialogData.Verification.HouseNumberSuffix,
                    },
                    Customer = new CustomerData
                    {
                        Label = dialogData.Customer.Label
                    }
                };
                return await VerifyCustomer(retryWithOnlyAddress).ConfigureAwait(false);
            }

            if (httpResponse.Response.StatusCode == HttpStatusCode.NotFound)
            {
                dialogData.Verification.IsFound = false;
                dialogData.Verification.IsVerified = false;
                return dialogData;
            }
            // error handling, expected error structure

            if (httpResponse.Body is ErrorResponse errorResponse)
                throw DigitalCoreExceptionHelper.RecreateExceptionFromDerivedErrorResponse(errorResponse);
            // error handling, unexpected error structure
            throw new TechnicalException(new Guid("5afe2592-d24e-4ff4-a645-71257459adef"), $"{nameof(VerifyCustomer)} threw an expected statuscode {(int)httpResponse.Response.StatusCode} with unexpected message {await httpResponse.Response.Content.ReadAsStringAsync().ConfigureAwait(false)}");
        }
        // error handling, unexpected status code
        catch (HttpOperationException hox)
        {
            throw new TechnicalException(new Guid("5ec80dc0-edb8-492e-83ad-9a4596c3892c"), $"{nameof(VerifyCustomer)} threw an un-expected statuscode {(int)hox.Response.StatusCode} with message {hox.Response.Content}");
        }
    }

    /// <summary>
    /// GetAgreements
    /// </summary>
    public async Task<IList<Agreement>> GetAgreements(DialogData dialogData, bool? onlyActive = null, bool includeLastYearProductUsage = false, int? accountId = null)
    {
        int accountIdValue = accountId ?? dialogData.SelectedAccount?.AccountId ?? 0 ;

        var apiCall = _customersRepository.GetAgreements(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), accountIdValue, onlyActive, includeLastYearProductUsage);
        var onSuccess = new Func<HttpOperationResponse<object>, IList<Agreement>>(httpResponse => ((ResponseDataListAgreement)httpResponse.Body).Data);
        return await ExecuteDcCall<IList<Agreement>, ErrorResponse>(apiCall, onSuccess, new Guid("e86609ad-d097-4535-8493-18d7f963ced7"), nameof(GetAgreements)).ConfigureAwait(false);
    }

    /// <summary>
    /// GetCustomerAgreements
    /// </summary>
    public async Task<IList<Agreement>> GetCustomerAgreements(DialogData dialogData)
    {
        var apiCall = _customersRepository.GetCustomerAgreements(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0));
        var onSuccess = new Func<HttpOperationResponse<object>, IList<Agreement>>(httpResponse => ((ResponseDataListAgreement)httpResponse.Body).Data);
        return await ExecuteDcCall<IList<Agreement>, ErrorResponse>(apiCall, onSuccess, new Guid("058c645d-172c-44c4-b8d6-66fe0d03af10"), nameof(GetAgreements)).ConfigureAwait(false);
    }

    /// <summary>
    /// GetCustomerOrders
    /// </summary>
    public async Task<IList<OrderResponseModel>> GetCustomerOrders(DialogData dialogData)
    {
        var apiCall = _customersRepository.GetCustomerOrders(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0));
        var onSuccess = new Func<HttpOperationResponse<object>, IList<OrderResponseModel>>(httpResponse => ((ResponseDataListOrderResponseModel)httpResponse.Body).Data);
        return await ExecuteDcCall<IList<OrderResponseModel>, ErrorResponse>(apiCall, onSuccess, new Guid("6C2F76FA-E174-48ED-BD1B-DF4C6F4DD2D8"), nameof(GetCustomerOrders)).ConfigureAwait(false);
    }

    /// <summary>
    /// PatchCustomerProfile
    /// </summary>
    public async Task<ChangeCustomerProfileResponse> PatchCustomerProfile(DialogData dialogData)
    {
        if (dialogData.Contact == null || dialogData.Verification?.CustomerId.GetValueOrDefault(0) == 0)
        {
            throw new DataQualityException(new Guid("b84918f5-50b6-48cc-9478-5159a08f1edf"), $"{nameof(PatchCustomerProfile)} Missing UserData");
        }

        var mutation = new CustomerMutation
        {
            MobilePhoneNumber = dialogData.PhoneNumber.ApprovedPhoneNumbers.Count >= 1 ? dialogData.PhoneNumber.ApprovedPhoneNumbers[0] : null,
            PhoneNumber = dialogData.PhoneNumber.ApprovedPhoneNumbers.Count >= 2 ? dialogData.PhoneNumber.ApprovedPhoneNumbers[1] : null,
            SendEmail = true,
            ActionFeedbackData = new ActionFeedbackData()
        };
        mutation.ActionFeedbackData.EnrichActionFeedbackData("CustomerPhoneNumber", dialogData);

        var result = await _customersRepository.UpdateProfile(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), new RequestDataCustomerMutation
        {
            Data = mutation
        }).ConfigureAwait(false);


        // Everything in outside the 2xx range is treated as an error.
        if ((int)result.Response.StatusCode / 100 != 2)
        {
            var error = $"{nameof(PatchCustomerProfile)} threw an un-expected statuscode {(int)result.Response.StatusCode} with message {await result.Response.Content.ReadAsStringAsync().ConfigureAwait(false)}";
            _logger.LogError(error);
            throw new TechnicalException(new Guid("e9341b47-8685-4b53-9acd-583d8af69835"), error);
        }

        return ((ResponseDataChangeCustomerProfileResponse)result.Body).Data;
    }


    /// <summary>
    /// UpdateEmailAddress
    /// </summary>
    public async Task<ChangeCustomerProfileResponse> UpdateEmailAddress(DialogData dialogData, string email)
    {
        if (!dialogData.Verification.IsVerified.HasValue || !dialogData.Verification.IsVerified.Value)
            return null;

        RequestDataCustomerMutation requestData = new()
        {
            Data = new CustomerMutation
            {
                Email = email,
                SendEmail = true,
                ActionFeedbackData = new Domain.Models.NextBestAction.ActionFeedbackData()
            }
        };

        requestData.Data.ActionFeedbackData.EnrichActionFeedbackData("CustomerEmailAddress", dialogData);

        var apiCall = _customersRepository.UpdateProfile(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), requestData);
        var onSuccess = new Func<HttpOperationResponse<object>, ChangeCustomerProfileResponse>(httpResponse =>
        {
            var data = ((ResponseDataChangeCustomerProfileResponse)httpResponse.Body).Data;
            return data;
        });

        return await ExecuteDcCall<ChangeCustomerProfileResponse, ErrorResponse>(apiCall, onSuccess, new Guid("905ae56f-1ceb-496a-8158-fa998063d0e2"), nameof(UpdateEmailAddress)).ConfigureAwait(false);
    }

    /// <summary>
    /// Gets the PersonalisationInfo object by requesting the customers' products and payment plan(s).
    /// </summary>
    public async Task<PersonalisationInfo> GetPersonalisationInfo(DialogData dialogData, CustomerModel customerModel)
    {
        // select active accounts
        var activeAccountIds = customerModel?.Accounts?.Where(a => a.Active ?? false).Select(a => a.Id);
        // parallel payment plan + products 
        var ppTaskCollection = activeAccountIds?.Select(a => GetPaymentPlan(dialogData, a));
        PaymentPlan[] paymentPlans = null;
        if (ppTaskCollection != null)
        {
            var ppTask = Task.WhenAll(ppTaskCollection);
            paymentPlans = await ppTask.ConfigureAwait(false);
        }
        var productTask = _productsService.GetCustomerProducts(dialogData);
        var agreementsTask = GetCustomerAgreements(dialogData);
        var meterMalfunctionTask = GetSmartMeterMalfunction(dialogData, activeAccountIds);
        var ordersTask = GetCustomerOrders(dialogData);

        await Task.WhenAll(productTask, agreementsTask, meterMalfunctionTask, ordersTask).ConfigureAwait(false);

        var products = await productTask.ConfigureAwait(false);
        var agreements = await agreementsTask.ConfigureAwait(false);
        var meterMalfunction = await meterMalfunctionTask.ConfigureAwait(false);
        var orders = await ordersTask.ConfigureAwait(false);

        // generate the personalisation info
        return customerModel.ToPersonalisationInfo(products, paymentPlans?.ToList(), agreements?.ToList(), orders?.ToList(), meterMalfunction);
    }

    /// <summary>
    /// Get payment plan by account (Customer PAPI)
    /// </summary>
    private async Task<PaymentPlan> GetPaymentPlan(DialogData dialogData, int accountId)
    {
        var apiCall = _customersRepository.GetPaymentPlan(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), accountId);
        var onSuccess = new Func<HttpOperationResponse<object>, PaymentPlan>(httpResponse => ((ResponseDataPaymentPlan)httpResponse.Body).Data);
        return await ExecuteDcCall<PaymentPlan, ErrorResponse>(apiCall, onSuccess, new Guid("********-20ae-4dbe-8a23-1579f4043155"), nameof(GetPaymentPlan)).ConfigureAwait(false);
    }

    /// <summary>
    /// Returns true if the customer has smart meters and at least one of them has a malfunction.
    /// This couldn't be implemented in the UsagesService because of circular dependencies and this is currently only used in the personalisation info.
    /// </summary>
    private async Task<bool> GetSmartMeterMalfunction(DialogData dialogData, IEnumerable<int> accountIds)
    {
        if ((accountIds?.Any()) != true)
        {
            // no meter of any account id has a malfunction
            return false;
        }

        foreach (var accountId in accountIds.Where(x => x > 0))
        {
            try
            {
                var apiCall = _usagesRepository.GetSmartMeterInterruption(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.Value, accountId);
                var onSuccess = new Func<HttpOperationResponse<object>, bool>(httpResponse =>
                {
                    var data = ((ResponseDataSmartMeterInterruptionModel)httpResponse.Body).Data;
                    return data.HasSmartMeter && data.SmartMeterHasInterruption;
                });
                var result = await ExecuteDcCall<bool, ErrorResponse>(apiCall, onSuccess, new Guid("0109718e-47a1-45af-bb93-f5834fa118fa"), nameof(GetSmartMeterMalfunction)).ConfigureAwait(false);
                // continue with the account id loop when outcome is false.
                if (result)
                    return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"{nameof(GetSmartMeterMalfunction)} failed.");
                // continue with the account id loop
            }
        }

        // no meter of any account id has a malfunction
        return false;
    }
}