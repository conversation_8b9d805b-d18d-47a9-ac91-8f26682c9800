﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Schema;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

/// <summary>
/// 
/// </summary>
namespace DC.Bot.BusinessLogic.Bots
{
    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class EnecoBot<T> : ActivityHandler where T : Dialog
    {
        /// <summary>
        /// 
        /// </summary>
        protected readonly Dialog _dialog;
        protected readonly BotState _conversationState;
        protected readonly BotState _userState;
        protected readonly ILogger<EnecoBot<T>> _logger;
        protected readonly ISessionManager _sessionManager;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="conversationState"></param>
        /// <param name="userState"></param>
        /// <param name="dialog"></param>
        /// <param name="loggerFactory"></param>
        public EnecoBot(
            ConversationState conversationState,
            UserState userState,
            T dialog,
            ILoggerFactory loggerFactory,
            ISessionManager sessionManager)
        {
            _conversationState = conversationState;
            _userState = userState;
            _dialog = dialog;
            _logger = loggerFactory.CreateLogger<EnecoBot<T>>();
            _sessionManager = sessionManager;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="turnContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public override async Task OnTurnAsync(ITurnContext turnContext, CancellationToken cancellationToken = default)
        {
            await base.OnTurnAsync(turnContext, cancellationToken).ConfigureAwait(false);

            // Save any state changes that might have occured during the turn.
            await _conversationState.SaveChangesAsync(turnContext, true, cancellationToken).ConfigureAwait(false);
            await _userState.SaveChangesAsync(turnContext, true, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="turnContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected override async Task OnMessageActivityAsync(ITurnContext<IMessageActivity> turnContext, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Running dialog with Message Activity.");
            turnContext.Activity.Text ??= string.Empty;

            // Run the Dialog with the new message Activity.
            await _dialog.RunAsync(turnContext, _conversationState.CreateProperty<DialogState>(nameof(DialogState)), cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="turnContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected override async Task OnEventActivityAsync(ITurnContext<IEventActivity> turnContext, CancellationToken cancellationToken)
        {
            // StartOfTransaction event: Run the Dialog with the new message Activity.
            if (turnContext.Activity.IsStartOfTransactionActivity(out _))
            {
                _logger.LogInformation("Running dialog with Event Activity.");
                await _dialog.RunAsync(turnContext, _conversationState.CreateProperty<DialogState>(nameof(DialogState)), cancellationToken).ConfigureAwait(false);
            }
            // CancelConversation event: Close the conversation
            else if (turnContext.Activity.Name == "closeConversation")
            {
                _logger.LogInformation("Closing conversation with Event Activity.");

                await _conversationState.DeleteAsync(turnContext, cancellationToken).ConfigureAwait(false);
                await _userState.DeleteAsync(turnContext, cancellationToken).ConfigureAwait(false);
                var endOf = Activity.CreateEndOfConversationActivity();
                endOf.Code = EndOfConversationCodes.UserCancelled;
                await turnContext.SendActivityAsync(endOf, cancellationToken).ConfigureAwait(false);
            }
            // On all other events just follow the baseclass implementation
            else
            {
                await base.OnEventActivityAsync(turnContext, cancellationToken).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="membersAdded"></param>
        /// <param name="turnContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
        protected override async Task OnMembersAddedAsync(IList<ChannelAccount> membersAdded, ITurnContext<IConversationUpdateActivity> turnContext, CancellationToken cancellationToken)
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously
        {
            _logger.LogInformation("New members were added: {@members}", membersAdded);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="membersRemoved"></param>
        /// <param name="turnContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
        protected override async Task OnMembersRemovedAsync(IList<ChannelAccount> membersRemoved, ITurnContext<IConversationUpdateActivity> turnContext, CancellationToken cancellationToken)
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously

        {
            _logger.LogInformation("Members were removed: {@members}", membersRemoved);
        }
    }
}
