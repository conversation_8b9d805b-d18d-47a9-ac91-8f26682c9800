﻿using AutoMapper;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.FinePolicy;
using DC.Bot.Repositories.Interfaces;
using DC.BusinessLogic.Base;
using DC.Products.Client.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic
{
    public class ProductFineCalculationService : BaseService, IProductFineCalculationService
    {
        private readonly IDcProductsRepository _productsRepository;
        private readonly IMapper _mapper;

        /// <summary>
        /// Constructor
        /// </summary>
        public ProductFineCalculationService(
            ILoggerFactory loggerFactory,
            IConfiguration configuration,
            IMapper mapper,
            IDcProductsRepository productsRepository) : base(loggerFactory, configuration)
        {
            _logger = loggerFactory.CreateLogger(GetType().Name);
            _mapper = mapper;
            _productsRepository = productsRepository;
        }

        /// <summary>
        /// Calculates the fine cancelled product.
        /// </summary>
        public async Task<ProductFineCalculationModel> CalculateFineCancelledProduct(DialogData dialogData, ProductCancelRequestModel request)
        {
            var response = await _productsRepository.CalculateFineCancelledProduct(dialogData.Customer.Label, new RequestDataProductCancelRequestModel { Data = request }).ConfigureAwait(false);
            if (response.Response.IsSuccessStatusCode)
            {
                _logger.LogInformation($"fine response: {JsonConvert.SerializeObject(((ResponseDataProductSwitchResponseModel)response.Body)?.Data)}");
                var result = _mapper.Map<ProductFineCalculationModel>(((ResponseDataProductSwitchResponseModel)response.Body)?.Data);
                result.ProductId = request.SubscriptionId;
                _logger.LogInformation($"fine model: {JsonConvert.SerializeObject(result)}");
                return result;
            }

            return new ProductFineCalculationModel();
        }

        /// <summary>
        /// Calculates the fine switched product.
        /// </summary>
        public async Task<ProductFineCalculationModel> CalculateFineSwitchedProduct(DialogData dialogData, ProductSwitchRequestModel request)
        {
            var response = await _productsRepository.CalculateFineSwitchedProduct(dialogData.Customer.Label, new RequestDataProductSwitchRequestModel { Data = request }).ConfigureAwait(false);
            if (response.Response.IsSuccessStatusCode)
            {
                var result = _mapper.Map<ProductFineCalculationModel>(((ResponseDataProductSwitchResponseModel)response.Body)?.Data);
                result.ProductId = request.SubscriptionId;
                return result;
            }

            return new ProductFineCalculationModel();
        }
    }
}
