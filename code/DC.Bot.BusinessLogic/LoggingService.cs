﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using Microsoft.Bot.Builder;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic
{
    public class LoggingService : ILoggingService
    {
        private readonly IStatePropertyAccessor<ConversationData> _conversationDataAccessor;
        private readonly ILogger _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public LoggingService(IStatePropertyAccessor<ConversationData> conversationDataAccessor, ILoggerFactory loggerFactory)
        {
            _conversationDataAccessor = conversationDataAccessor;
            _logger = loggerFactory.CreateLogger(GetType().Name);
        }

        private async Task<ConversationData> GetConversationData(ITurnContext context)
        {
            return await _conversationDataAccessor.GetAsync(context, () =>
            {
                return new ConversationData
                {
                    DialogData = new DialogData(),
                    CurrentDialogAction = DialogAction.None
                };
            }).ConfigureAwait(false);
        }

        /// <summary>
        /// LogException
        /// </summary>
        public void LogException(Exception ex, ITurnContext turnContext, string method, string message = null)
        {
            var conversationTask = Task.Run(async () => await GetConversationData(turnContext)).ConfigureAwait(false);
            var conversationData = conversationTask.GetAwaiter().GetResult();

            using (_logger.BeginScope(new Dictionary<string, object>()
                {
                    { LoggingTags.ConversationId, $"{turnContext?.Activity?.Conversation?.Id}"},
                    { LoggingTags.Action, $"{conversationData?.CurrentDialogAction}"},
                    { LoggingTags.LastUserInput, $"{turnContext?.Activity?.Text}"}
                }))
            {
                var logString = !string.IsNullOrWhiteSpace(message)
                    ? $"{method} [{message}] -> {ex.Message}"
                    : $"{method} -> {ex.Message}";

                _logger.LogError(ex, logString);
            }
        }

        /// <summary>
        /// LogInformation
        /// </summary>
        public void LogInformation(ITurnContext turnContext, string method, string message = null)
        {
            var conversationTask = Task.Run(async () => await GetConversationData(turnContext)).ConfigureAwait(false);
            var conversationData = conversationTask.GetAwaiter().GetResult();

            using (_logger.BeginScope(new Dictionary<string, object>()
                {
                    { LoggingTags.ConversationId, $"{turnContext?.Activity?.Conversation?.Id}"},
                    { LoggingTags.Action, $"{conversationData?.CurrentDialogAction}"},
                    { LoggingTags.LastUserInput, $"{turnContext?.Activity?.Text}"}
                }))
            {
                var logString = !string.IsNullOrWhiteSpace(message)
                    ? $"{method} [{message}]"
                    : $"{method}";

                _logger.LogInformation(logString);
            }
        }

        public void LogWarning(ITurnContext turnContext, string method, string message = null)
        {
            var conversationTask = Task.Run(async () => await GetConversationData(turnContext)).ConfigureAwait(false);
            var conversationData = conversationTask.GetAwaiter().GetResult();

            using (_logger.BeginScope(new Dictionary<string, object>()
                {
                    { LoggingTags.ConversationId, $"{turnContext?.Activity?.Conversation?.Id}"},
                    { LoggingTags.Action, $"{conversationData?.CurrentDialogAction}"},
                    { LoggingTags.LastUserInput, $"{turnContext?.Activity?.Text}"}
                }))
            {
                var logString = !string.IsNullOrWhiteSpace(message)
                    ? $"{method} [{message}]"
                    : $"{method}";

                _logger.LogWarning(logString);
            }
        }
    }
}
