﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Customers.Client.Models;
using DC.Customers.Client.Repository;
using DC.Domain.Models.Customers;
using DC.Repositories.Base.Enumerations;

namespace DC.Bot.BusinessLogic;

public class ContactPreferencesService : IContactPreferencesService
{
    private readonly IDcCustomersRepository _customersRepository;

    public ContactPreferencesService(IDcCustomersRepository customersRepository)
    {
        _customersRepository = customersRepository;
    }

    public async Task<EmailContactPreferences> GetEmailContactPreferences(
        Label label,
        long customerId)
    {
        var contactPreferences = await _customersRepository.GetPreferencesContactForCustomer(label, customerId);

        return GetContactPreferences(contactPreferences);
    }

    public async Task ChangeContactPreference(
        Label label,
        long customerId,
        EmailContactPreferences preferences)
    {
        var currentPreferences = await _customersRepository.GetPreferencesContactForCustomer(label, customerId);

        var updatedPreferences = currentPreferences
            .Select(currentPreference => UpdateEmailPreference(currentPreference, preferences))
            .ToList();

        var foo = await _customersRepository.PatchPreferencesContactForCustomer(
            label, customerId, new RequestDataIListContactPreference
            {
                Data = updatedPreferences
            });
    }

    private static EmailContactPreferences GetContactPreferences(List<ContactPreference> contactPreferences) =>
        new(
            contactPreferences.Where(c => c.Preference is ContactPreferenceType.MerByEmail).All(c => c.Value)
            && contactPreferences.Where(c => c.Preference is ContactPreferenceType.MerOnlineOnly).All(c => !c.Value),
            contactPreferences.Any(c => c.Preference is ContactPreferenceType.InvoicesByEmail) && contactPreferences.Where(c => c.Preference is ContactPreferenceType.InvoicesByEmail).All(c => c.Value));

    /// <summary>
    /// If customer has non-email preference, and an update is requested, that means that we enable the email preference
    /// </summary>
    private static ContactPreference UpdateEmailPreference(
        ContactPreference contactPreference,
        EmailContactPreferences preferences)
    {
        if (contactPreference.Preference is ContactPreferenceType.InvoicesByEmail
            && !preferences.IsInvoicePreferenceEmail
            || contactPreference.Preference is ContactPreferenceType.MerByEmail
            && !preferences.IsInvoicePreferenceEmail)
        {
            return new ContactPreference
            {
                ActionFeedbackData = contactPreference.ActionFeedbackData,
                KeyType = contactPreference.KeyType,
                Preference = contactPreference.Preference,
                Value = true
            };
        }

        return contactPreference;
    }
}

public sealed record EmailContactPreferences(
    bool IsMonthlyEnergyReportPreferenceEmail,
    bool IsInvoicePreferenceEmail);
