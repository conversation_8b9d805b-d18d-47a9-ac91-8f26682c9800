﻿

using AutoMapper;
using DC.Bot.BusinessLogic.Models.FinePolicy;

namespace DC.Bot.BusinessLogic.Configuration
{
    public class ProductFineCalculationProfile : Profile
    {
        public ProductFineCalculationProfile()
        {
            #region ResponseModels
            CreateMap<Products.Client.Models.ProductSwitchResponseModel, ProductFineCalculationModel>()
            .ForMember(dest => dest.ProductId, opt => opt.Ignore());
            #endregion
        }
    }
}