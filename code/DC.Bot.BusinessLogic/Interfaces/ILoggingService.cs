﻿using Microsoft.Bot.Builder;
using System;

namespace DC.Bot.BusinessLogic.Interfaces
{
    public interface ILoggingService
    {
        void LogException(Exception ex, ITurnContext turnContext, string method, string message = null);
        void LogInformation(ITurnContext turnContext, string method, string message = null);
        void LogWarning(ITurnContext turnContext, string method, string message);
    }
}
