﻿using DC.Bot.BusinessLogic.Models;
using DC.Customers.Client.Models;
using DC.Domain.Models.Accounts;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.Customers;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Interfaces
{
    public interface ICustomersService
    {
        Task<CustomerModel> GetCustomer(DialogData dialogData);

        Task<PaymentPlan> GetPaymentPlan(DialogData dialogData);

        void EnrichDialogData(DialogData dialogData, CustomerModel customer, NextBestAction nextBestActionData);

        Task<Customer> GetCustomerV2(Label label, BotChannel botChannel, long customerId);

        Task<DialogData> VerifyCustomer(DialogData dialogData);

        Task<ChangeCustomerProfileResponse> UpdateEmailAddress(DialogData dialogData, string email);

        Task<ChangeCustomerProfileResponse> PatchCustomerProfile(DialogData dialogData);

        Task<PersonalisationInfo> GetPersonalisationInfo(DialogData dialogData, CustomerModel customerModel);

        Task<IList<Agreement>> GetAgreements(DialogData dialogData, bool? onlyActive = null, bool includeLastYearProductUsage = false, int? accountId = null);

        Task<IList<Agreement>> GetCustomerAgreements(DialogData dialogData);
    }
}
