﻿using DC.Bot.BusinessLogic.Dialogs.Customers;
using DC.Bot.BusinessLogic.Dialogs.Customers.PhoneNumberDialogs;

namespace DC.Bot.BusinessLogic.Interfaces.DialogContainers
{
    public interface ICustomersDialogContainer
    {
        CustomerAccountsDialog CustomerAccountsDialog { get; }

        YearnoteDateDialog YearnoteDateDialog { get; }

        RelocateDateDialog RelocateDateDialog { get; }

        ChangeEmailDialog ChangeEmailDialog { get; }

        ChangeIbanDialog ChangeIbanDialog { get; }

        ChangePhoneNumberDialog ChangePhoneNumberDialog { get; }

        ChangeContactPreferencesDialog ChangeContactPreferencesDialog { get; }
    }
}
