﻿using DC.Bot.BusinessLogic.Dialogs.Products;

namespace DC.Bot.BusinessLogic.Interfaces.DialogContainers
{
    public interface IProductsDialogContainer
    {
        ProductRatesDialog ProductRatesDialog { get; }
        ProductEndDatesDialog ProductEndDatesDialog { get; }
        ProductEndDatesAdviceDialog ProductEndDatesAdviceDialog { get; }

        DiscontinueToonDialog PersonalDiscontinueToonDialog { get; }
        DiscontinueServiceContractDialog PersonalDiscontinueServiceContractDialog { get; }
        KetelComfortAppointmentDialog KetelComfortAppointmentDialog { get; }
        CreateServiceOrderDialog CreateServiceOrderDialog { get; }
        NextBestActionDialog NextBestActionDialog { get; }
        ProductFineCalculationDialog ProductFineCalculationDialog { get; }
        ZonOpDakDialog ZonOpDakDialog { get; }
    }
}
