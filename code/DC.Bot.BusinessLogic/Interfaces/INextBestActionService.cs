﻿using DC.Bot.BusinessLogic.Models;
using DC.Domain.Models.NextBestAction;
using DC.Products.Client.Models;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Interfaces
{
    public interface INextBestActionService
    {
        Task<NextBestAction> GetNextBestActions(DialogData dialogData, bool filterSupported = true);

        Task AddFeedback(DialogData dialogData, string actionType, FeedbackStatus status);
    }
}
