﻿using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.Financial;
using DC.Customers.Client.Models;
using DC.Domain.Models.Financials;
using DC.Financials.Client.Models;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Interfaces
{
    public interface IFinancialsService
    {
        Task<int> GetAdvancePaymentAmount(DialogData dialogData, int accountId);

        Task<FinancialPreferences> GetFinancialsPreferences(DialogData dialogData, int accountId);

        Task<PaymentArrangementModel> GetPaymentArrangement(DialogData dialogData, int accountId);

        Task<AdvancePayment> GetAdvancePaymentAmountAndDayOfPayment(DialogData dialogData, CustomerAccountModel account);

        Task<AdvancePaymentAdviceStatus> UpdatePaymentDayOfMonth(DialogData dialogData, int paymentDayOfMonth);

        Task<AdvancePaymentAdviceStatus> UpdatePaymentToDirectDebit(DialogData dialogData, string bankAccountNumber, int paymentDayOfMonth);

        Task<AdvancedPaymentAdviceExtraStatus> UpdateAdvancePaymentAmount(DialogData dialogData, int amount);

        Task<AdvancePaymentAdviceStatus> UpdateBankAccount(DialogData dialogData, string bankAccount);

        Task<AdvancePaymentAdviceV2Response> GetAdvancePaymentAdviceV2(DialogData dialogData);
    }
}
