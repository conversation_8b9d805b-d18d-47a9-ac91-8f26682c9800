﻿using DC.Bot.BusinessLogic.Models;
using DC.Domain.Models.Products;
using DC.Domain.Models.Products.ProductRates;
using DC.Domain.Models.Products.ServiceAgreements;
using DC.Domain.Models.Products.ServiceOrders;
using DC.Products.Client.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using DiscontinueProductType = DC.Domain.Models.Products.DiscontinueProductType;
using PlanLinkType = DC.Products.Client.Models.PlanLinkType;

namespace DC.Bot.BusinessLogic.Interfaces
{
    public interface IProductsService
    {
        Task<IList<ProductRate>> GetProductRates(DialogData dialogData, int accountId);

        Task<bool> CancelProductContract(DialogData dialogData, ProductType productType, ProductDiscontinueReason reason);

        Task<DiscontinueIntake> GetDiscontinueIntake(DialogData dialogData, DiscontinueProductType discontinueProductType);

        Task<KetelComfortServiceModel> GetKetelComfortProductDetails(DialogData dialogData);

        Task<List<HardwareProductSpecification>> GetHardwareProductSpecifications(DialogData dialogData);

        Task<List<ServiceAgreement>> GetServiceAgreementsForAgreementId(DialogData dialogData, int agreementId, bool activeAgreementsWithSelfService = true);

        Task<List<ServiceOrder>> GetServiceOrdersForAgreement(DialogData dialogData, string serviceAgreementId, ServiceOrderStatus? orderStatus = null);

        Task<string> CreateServiceOrder(DialogData dialogData);

        Task<string> GetServiceOrderPlanLink(DialogData dialogData, string orderId, PlanLinkType type);

        Task<IList<ProductModel>> GetCustomerProductsByAccount(DialogData dialogData, int accountId, bool? active = null, bool includeProductRates = false);

        Task<IList<ProductModel>> GetCustomerProducts(DialogData dialogData, bool includeProductRates = false);

        Task<IList<Relocations>> GetCustomerRelocations(DialogData dialogData);
    }
}
