﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.Auth;
using DC.Bot.BusinessLogic.Models.NextBestAction;
using DC.Customers.Client.Models;
using DC.Repositories.Base.Enumerations;
using Microsoft.Bot.Builder;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Interfaces
{
    public interface ISessionManager
    {
        Task<DialogData> GetDialogData(ITurnContext context);

        Task SetCurrentDialogAction(ITurnContext context, DialogAction action);

        Task ResetConversationData(ITurnContext context);

        Task SendEndOfTransactionActivity(ITurnContext context, string dialog, string dialogStep, TransactionStatus status, CancellationToken cancellationToken = default);

        Task LogEvent(ITurnContext context, string dialog, string dialogStep, TransactionStatus status, string message = null, bool completeDialogData = false);

        Task SendCustomerInfoEvent(ITurnContext context, PersonalisationInfo personalisationInfo, NextBestActionInfo nextBestActionInfo);

        Task SendCustomMessageWithAttachment<T>(ITurnContext context, T objectToSend, string type = null);

        Task SendMessageWithExpectedAttachmentSend(ITurnContext context, string message);

        Task HandleAuthorization(ITurnContext context, TransactionStartValue transactionStartValue, CancellationToken cancellationToken = default);

        Task<OktaValidationToken> ValidateOktaToken(Label label, string token);

        Task<(PersonalisationInfo personalisation, NextBestActionInfo nba, CustomerModel customer)> GetSeamlyCustomerInfo(DialogData dialogData);
    }
}
