﻿using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.Request;
using DC.Repositories.Base.Enumerations;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Interfaces
{
    public interface IStorageService
    {
        /// <summary>
        /// GetTextLabels
        /// </summary>
        /// <param name="label"></param>
        /// <param name="textLabelGroupName"></param>
        /// <param name="filters"></param>
        /// <returns></returns>
        Task<TextLabelCollection> GetTextLabels(Label label, string textLabelGroupName, List<string> filters, bool passthroughLanguage = true);

        Task AddConversation(Label label, ConversationWrapper request);

        Task ExportTextLabels(Label label, bool isTest = false);
    }
}
