﻿using DC.Bot.BusinessLogic.Models;
using DC.Usages.Client.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Interfaces
{
    public interface IUsagesService
    {
        Task<UsagesSummaryModel> GetPastHalfYearUsages(DialogData dialogData, int accountId);
        Task<SaveReadingModel> RegisterReportRequest(DialogData dialogData, int accountId);
        Task<ReadingsReportRequest> CanRequestRegisterReportRequest(DialogData dialogData);
        Task<List<ReadingHistoryItem>> GetReadingsHistoryForCustomer(DialogData dialogData);
        Task<GetReadingOutputModel> GetReadingForCustomer(DialogData dialogData);
        Task<List<OutstandingReading>> GetOutstandingReadings(DialogData dialogData);
        Task<ServiceProductVersionModel> GetMandate(DialogData dialogData, ServiceProductType serviceProductType);
        Task<ResponseDataEnableMandateForCustomerResult> EnableMandate(DialogData dialogData, ServiceProductType serviceProductType);
        Task DisableMandate(DialogData dialogData, ServiceProductType serviceProductType);
    }
}
