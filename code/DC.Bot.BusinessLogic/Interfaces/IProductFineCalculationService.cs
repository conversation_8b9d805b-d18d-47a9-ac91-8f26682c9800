﻿using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.FinePolicy;
using DC.Products.Client.Models;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Interfaces
{
    public interface IProductFineCalculationService
    {
        Task<ProductFineCalculationModel> CalculateFineCancelledProduct(DialogData dialogData, ProductCancelRequestModel request);
        Task<ProductFineCalculationModel> CalculateFineSwitchedProduct(DialogData dialogData, ProductSwitchRequestModel request);
    }
}
