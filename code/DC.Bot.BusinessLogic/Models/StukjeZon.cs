﻿using Microsoft.Bot.Builder.Dialogs.Choices;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace DC.Bot.BusinessLogic.Models
{
    public class StukjeZon
    {
        [JsonProperty("currentNumber")]
        public int CurrentNumber { get; set; }

        [JsonProperty("maximum")]
        public int Maximum { get; set; }

        [JsonProperty("price")]
        public decimal Price { get; set; }

        [JsonProperty("yearlyElectricitySaving")]
        public int YearlyElectricitySaving { get; set; }

        [JsonProperty("yearlyAmountSaving")]
        public decimal YearlyAmountSaving { get; set; }

        [JsonProperty("amountToOrder")]
        public int AmountToOrder { get; set; }

        [JsonProperty("choices")]
        public List<Choice> Choices { get; set; } = new List<Choice>();
    }
}