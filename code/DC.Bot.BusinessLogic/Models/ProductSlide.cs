﻿using DC.Bot.BusinessLogic.Enumerations;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace DC.Bot.BusinessLogic.Models
{
    public class ProductSlide
    {
        [JsonConverter(typeof(StringEnumConverter))]
        [JsonProperty("type")]
        public SlideProduct Type { get; set; }

        [JsonProperty("title")]
        public string Title { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("buttonText")]
        public string ButtonText { get; set; }

        [JsonProperty("buttonAction")]
        public string ButtonAction { get; set; }
    }
}
