﻿using DC.Bot.BusinessLogic.Enumerations;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;

namespace DC.Bot.BusinessLogic.Models
{
    /// <summary>
    /// Metadata to contain properties of the expected answer.
    /// </summary>
    public class ExpectedEntryEntity
    {
        [JsonProperty("type")]
        [EnumDataType(typeof(ExpectedEntryType))]
        [JsonConverter(typeof(StringEnumConverter))]
        public ExpectedEntryType Type { get; set; }

        /// <summary>
        /// When Type = Text, else null
        /// In characters
        /// </summary>
        private int? _Limit;
        [JsonProperty("limit", NullValueHandling = NullValueHandling.Ignore)]
        public int? Limit
        {
            get
            {
                return Type == ExpectedEntryType.Text ? _Limit : null;
            }
            set
            {
                _Limit = value;
            }
        }

        /// <summary>
        /// When Type = Upload, else null
        /// In kilobytes
        /// </summary>
        private int? _MaxSize;
        [JsonProperty("max_size", NullValueHandling = NullValueHandling.Ignore)]
        public int? MaxSize
        {
            get
            {
                return Type == ExpectedEntryType.Upload ? _MaxSize : null;
            }
            set
            {
                _MaxSize = value;
            }
        }

        /// <summary>
        /// When Type = Upload, else null
        /// </summary>
        private string[] _AllowedMimeTypes;
        [JsonProperty("allowed_mime_types", NullValueHandling = NullValueHandling.Ignore)]
        public string[] AllowedMimeTypes
        {
            get
            {
                return Type == ExpectedEntryType.Upload ? _AllowedMimeTypes : System.Array.Empty<string>();
            }
            set
            {
                _AllowedMimeTypes = value;
            }
        }
    }
}
