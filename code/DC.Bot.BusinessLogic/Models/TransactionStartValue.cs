﻿using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;

namespace DC.Bot.BusinessLogic.Models
{
    public class TransactionStartValue
    {
        public Label Label { get; set; }

        public string StartCommand { get; set; }

        public BotChannel Channel { get; set; }

        public string Authorization { get; set; }

        public string Urgent { get; set; }

        public string Description { get; set; }

        public string Hardware { get; set; }

        public bool IsNba { get; set; }

        public long? CustomerId { get; set; }
    }
}
