﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace DC.Bot.BusinessLogic.Models.Request
{
    public class Conversation
    {
        [JsonProperty("customer_number")]
        public string CustomerId { get; set; }

        [JsonProperty("transaction_name")]
        public string TransactionName { get; set; }

        [JsonProperty("session_id")]
        public string SessionId { get; set; }

        public IList<Message> History { get; set; }
    }
}
