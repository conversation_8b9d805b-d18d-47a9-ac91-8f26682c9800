﻿using DC.Domain.Models.Customers;
using Newtonsoft.Json;

namespace DC.Bot.BusinessLogic.Models
{
    /// <summary>
    /// Personalisation Info - this class is used for personalization information in Seamly
    /// *IMPORTANT* When adding new properties to this class, Seamly will need to configure them as well.!
    /// </summary>
    public class PersonalisationInfo
    {
        [JsonProperty(nameof(CustomerId))]
        public long CustomerId { get; set; }

        // legacy naming
        [JsonProperty(nameof(Firstname))]
        public string FirstnameLegacy { get; set; }

        [JsonProperty("FirstnameOnly")]
        public string Firstname { get; set; }

        [JsonProperty(nameof(Surname))]
        public string Surname { get; set; }

        [JsonProperty("gender")]
        public Gender Gender { get; set; }

        [JsonProperty("email")]
        public string Email { get; set; }

        [JsonProperty("organisation")]
        public bool IsOrganisation { get; set; }

        [JsonProperty("hasElectricity")]
        public bool HasElectricity { get; set; }

        [JsonProperty("hasGas")]
        public bool HasGas { get; set; }

        [JsonProperty("hasWarmth")]
        public bool HasWarmth { get; set; }

        [JsonProperty("hasTapwater")]
        public bool HasTapwater { get; set; }

        [JsonProperty("hasToon")]
        public bool HasToon { get; set; }

        [JsonProperty("smart_meter_electricity")]
        public bool? HasReadableSmartElectricityMeter { get; set; }

        [JsonProperty("smart_meter_gas")]
        public bool? HasReadableSmartGasMeter { get; set; }

        [JsonProperty("hasReadableSmartWarmthMeter")]
        public bool? HasReadableSmartWarmthMeter { get; set; }

        [JsonProperty("hasRedelivery")]
        public bool HasRedelivery { get; set; }

        [JsonProperty("hasNextChargeDate")]
        public bool HasNextChargeDate { get; set; }

        [JsonProperty("inDebtCollection")]
        public bool InDebtCollection { get; set; }

        [JsonProperty("meterMalfunction")]
        public bool MeterMalfunction { get; set; }

        [JsonProperty("dateOfBirthToday")]
        public bool DateOfBirthToday { get; set; }

        [JsonProperty("electricityIsIndefinite")]
        public bool ElectricityIsIndefinite { get; set; }

        [JsonProperty("gasIsIndefinite")]
        public bool GasIsIndefinite { get; set; }

        [JsonProperty("meterDataRequest")]
        public bool MeterDataRequest { get; set; }

        [JsonProperty("hasActiveMandate")]
        public bool? HasActiveMandate { get; set; }

        [JsonProperty("netbeheerder")]
        public string Netbeheerder { get; set; }

        [JsonProperty("netbeheerderNames")]
        public string NetbeheerderNames { get; set; }

        [JsonProperty("gasDynamicPricing")]
        public bool GasDynamicPricing { get; set; }

        [JsonProperty("dynamic_pricing_electricity")]
        public bool ElectricityDynamicPricing { get; set; }

        [JsonProperty("coolingOffElectricity")]
        public bool CoolingOffElectricity { get; set; }

        [JsonProperty("coolingOffGas")]
        public bool CoolingOffGas { get; set; }

        [JsonProperty("rateChangeIn30Days")]
        public bool RateChangeIn30Days { get; set; }

        [JsonProperty("nextChargeDate")]
        public string NextChargeDate { get; set; }

        [JsonProperty("afcoElectricity")]
        public bool AfcoElectricity { get; set; }

        [JsonProperty("afcoGas")]
        public bool AfcoGas { get; set; }
    }
}
