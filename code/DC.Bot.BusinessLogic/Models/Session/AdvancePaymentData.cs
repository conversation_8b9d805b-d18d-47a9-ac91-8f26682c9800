﻿using DC.Domain.Models.Financials;
using System;

namespace DC.Bot.BusinessLogic.Models;

public class AdvancePaymentData
{
    /// <summary>
    /// Acceptgiro
    /// </summary>
    public bool? PaymentWithGiroCard { get; set; }
    public bool? IsRecurringPaymentWithPreferedPaymentDay { get; set; }

    public int? PaymentDayOfMonth { get; set; }

    public int? AdvancePaymentAmount { get; set; }
    public int? AdvancePaymentCurrentAmount { get; set; }

    public bool? AskToUpdatePaymentDay { get; set; }

    public string BankAccountNumber { get; set; }

    public bool WaitingForAnnualInvoice { get; set; }

    public decimal Advice { get; set; }

    public decimal Minimum { get; set; }

    public decimal Maximum { get; set; }

    public AdvancePaymentAdviceStatus AdvancePaymentAdviceStatus { get; set; }

    public DateTime? NextChargeDate { get; set; }

    public bool ChangeIBANTriggerdAsSeparateStep { get; set; }
    public bool ChangePaymentDayTriggerdAsSeparateStep { get; set; }
    public int AdjustAdvancePaymentAmountAttempts { get; set; } = 0;
}
