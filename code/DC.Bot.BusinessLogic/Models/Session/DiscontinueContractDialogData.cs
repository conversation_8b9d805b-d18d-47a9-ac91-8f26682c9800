﻿using DC.Domain.Models.Products;
using DC.Products.Client.Models;
using System.Collections.Generic;

namespace DC.Bot.BusinessLogic.Models;

public class DiscontinueContractDialogData
{
    public IList<Reason> Reasons { get; set; } = new List<Reason>();
    public bool Allowed { get; set; }
    public int? AgreementId { get; set; }
    public DiscontinueDisallowReason? DiscontinueDisallowReason { get; set; }
    public string MarketingProductCode { get; set; }
}
