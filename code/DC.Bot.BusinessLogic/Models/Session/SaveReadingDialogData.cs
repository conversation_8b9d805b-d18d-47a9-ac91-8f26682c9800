﻿using System.Collections.Generic;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Usages.Client.Models;

namespace DC.Bot.BusinessLogic.Models.Session;

public class SaveReadingDialogData
{
    public bool IsPeriodicReadingAvailable { get; set; }
    public bool IsTariffMigrationReadingAvailable { get; set; }

    public bool CorrectionNotaAvailable { get; set; }

    public bool PeriodReadingInsertedWithin7Days { get; set; }

    public bool InformAboutSmartMeterReadingMandate { get; set; }

    public GetReadingOutputModel ReadingModel { get; set; }

    public int MeterCount { get; set; }

    public List<ReadingHistoryItem> ReadingHistory { get; set; }

    public List<OutstandingReading> OutstandingReadings { get; set; }

    public Dictionary<SaveReadingPersonalChoiceOption, TransactionStatus> TransactionStatusMap { get; set; } = new();
}
