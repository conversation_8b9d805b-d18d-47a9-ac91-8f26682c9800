﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Domain.Models.Products;
using DC.Domain.Models.Products.ServiceAgreements;
using DC.Products.Client.Models;
using System.Collections.Generic;

namespace DC.Bot.BusinessLogic.Models;

public class ServiceOrderData
{
    public List<Domain.Models.Products.ProductModel> Products { get; set; }
    public ProductModel ChosenProduct { get; set; }
    public ServiceAgreement ChosenServiceAgreement { get; set; }
    public ServiceOrderProductType? ProductType { get; set; }
    public ServiceOrderProductType? HardwareType { get; set; }
    public bool IsUrgent { get; set; }
    public ServiceOrderType Type { get; set; }
    public string Description { get; set; }
    public string MalfunctionCode { get; set; }
    public string ResourceNumber { get; set; }
    public ServiceOrderComments Comments { get; set; }
    public string Hardware { get; set; }
    public string Number { get; set; }
}
