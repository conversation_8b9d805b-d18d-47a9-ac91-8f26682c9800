﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Telemetry.Models;
using System.Collections.Generic;
using DC.Bot.BusinessLogic.Models.Session;

namespace DC.Bot.BusinessLogic.Models;

public class DialogData
{
    /// <summary>
    /// Set by Dialogs in return statements so we can keep track of the status on other dialogs.
    /// </summary>
    public TransactionStatus TransactionStatus { get; set; }

    public string Token { get; set; }

    public BotChannel Channel { get; set; }

    public TextLabelCollection TextLabels { get; set; } = new([]);

    public VerificationData Verification { get; set; } = new();

    public CustomerData Customer { get; set; } = new();

    public AccountData SelectedAccount { get; set; } = new();

    public List<AccountData> ActiveAccounts { get; set; } = new();

    public AdvancePaymentData AdvancePayment { get; set; } = new();

    public ContactData Contact { get; set; } = new();

    public PhoneNumberDialogData PhoneNumber { get; set; } = new();

    public SolarProducts PossibleSolarProducts { get; set; } = new();

    public ValidationData ValidationData { get; set; } = new();

    public DiscontinueContractDialogData DiscontinueContractData { get; set; } = new();

    public SaveReadingDialogData SaveReadingDialogData { get; set; } = new();

    public ServiceOrderData ServiceOrder { get; set; } = new();

    public NextBestActionData NextBestAction { get; set; } = new();

    public MandateData MandateData { get; set; } = new();

    public ProductFineCalculationData ProductFineCalculationData { get; set; } = new();

    public string CommandBeginDialog { get; set; }

    public ChangeContactPreferencesData ChangeContactPreferencesData { get; set; } = new();
}
