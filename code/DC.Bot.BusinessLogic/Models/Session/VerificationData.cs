﻿using System;

namespace DC.Bot.BusinessLogic.Models;

public class VerificationData
{
    public int FindUserAttempts { get; set; }

    public bool? IsFound { get; set; }

    /// <summary>
    /// Basic validation based on address and last digits of IBAN or birth date.
    /// </summary>
    public bool? IsVerified { get; set; }

    /// <summary>
    /// Check if customerId was verified against user input.
    /// </summary>
    public bool? CustomerIdVerified { get; set; }

    /// <summary>
    /// True if verified via okta token, false otherwise.
    /// </summary>
    public bool IsVerifiedWithToken { get; set; } = false;

    public bool TooManyAttempts { get; set; }

    public bool? VerificationCommunicated { get; set; }

    public bool? IsPossiblyOrganisation { get; set; }

    public bool MfaSuccessful { get; set; }

    public string PostalCode { get; set; }

    public int? HouseNumber { get; set; }

    public string HouseNumberSuffix { get; set; }

    public bool HouseNumberSuffixIsSet { get; set; }

    public string IbanSuffix { get; set; }

    public long? CustomerId { get; set; }

    public DateTime? DateOfBirth { get; set; }

    public string PhoneNumber { get; set; }
}
