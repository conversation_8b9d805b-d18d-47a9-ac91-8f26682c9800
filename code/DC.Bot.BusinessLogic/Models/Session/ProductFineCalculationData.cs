﻿using DC.Bot.BusinessLogic.Models.FinePolicy;
using DC.Domain.Models.Products;
using System.Collections.Generic;

namespace DC.Bot.BusinessLogic.Models;

public class ProductFineCalculationData
{
    public List<ProductModel> Products { get; set; } = new List<ProductModel>();
    public List<ProductModel> SelectedProducts { get; set; } = new List<ProductModel>();
    public List<ProductFineCalculationModel> Fines { get; set; } = new List<ProductFineCalculationModel>();
}
