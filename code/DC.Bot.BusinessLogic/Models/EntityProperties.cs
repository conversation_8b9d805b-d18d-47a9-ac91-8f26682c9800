﻿using Newtonsoft.Json;

namespace DC.Bot.BusinessLogic.Models;

/// <summary>
/// Template class of an entry in the Activity.Entities property to hold metadata.
/// </summary>
/// <typeparam name="T"></typeparam>
public class EntityProperties<T>
{
    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="type"></param>
    /// <param name="properties"></param>
    public EntityProperties(string type, T properties)
    {
        Type = type;
        Properties = properties;
    }

    /// <summary>
    /// Type of the metadata.
    /// </summary>
    [JsonProperty("type")]
    public string Type { get; set; }

    /// <summary>
    /// Properties of the metadata of type T.
    /// </summary>
    [JsonProperty("properties")]
    public T Properties { get; set; }
}
