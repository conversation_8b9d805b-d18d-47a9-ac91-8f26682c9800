﻿using DC.Storage.Client.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace DC.Bot.BusinessLogic.Models
{
    public class TextLabelCollection
    {
        public List<TextLabelModel> TextLabels { get; }

        public TextLabelCollection(List<TextLabelModel> textLabels)
        {
            TextLabels = textLabels;
        }

        public string GetValue(string textLabelKey, string groupName)
        {
            var text = TextLabels?.Find(x => x.Key == textLabelKey && x.GroupName == groupName)?.Value ?? string.Empty;
            if (text.Contains('\n') || text.Contains('\r') || text.Contains("\r\n"))
                text = text
                    .Replace("\r\n", "  \n  \n ")
                    .Replace("\r", "  \n ")
                    .Replace("\n", "  \n ");
            return text;
        }

        public string GetValue(string textLabelKey, string groupName, object variables)
        {
            var value = GetValue(textLabel<PERSON><PERSON>, groupName);

            if (string.IsNullOrEmpty(value))
                return value;

            Type myType = variables.GetType();
            IList<PropertyInfo> props = new List<PropertyInfo>(myType.GetProperties());

            foreach (PropertyInfo prop in props)
            {
                var propValue = prop.GetValue(variables, null);
                value = value.Replace($"{{{prop.Name}}}", $"{propValue}");
            }

            return value;
        }
    }
}
