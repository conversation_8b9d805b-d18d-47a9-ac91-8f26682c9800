﻿using DC.Products.Client.Models;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace DC.Bot.BusinessLogic.Models.FinePolicy
{
    public class ProductFineCalculationModel
    {
        public double? ProductId { get; set; }
        public double? FineAmountVat { get; set; }
        public System.DateTime? FineAmountDeterminationDate { get; set; }
        public double FineAmount { get; set; }
        public double? FineAmountInclVat { get; set; }
        public double? AverageWeightedDeliveryTariff { get; set; }
        public double? AverageWeightedReferenceTariff { get; set; }
        public double? DeliveryTariff { get; set; }
        public double? DeliveryTariffHigh { get; set; }
        public double? DeliveryTarifffLow { get; set; }
        public string Description { get; set; }
        public string ReferenceProduct { get; set; }
        public string ReferenceProductOMS { get; set; }
        public double? ReferenceTariff { get; set; }
        public double? ReferenceTariffHigh { get; set; }
        public double? ReferenceTariffLow { get; set; }
        public int? RemainingSJI { get; set; }
        public int? RemainingSJIHigh { get; set; }
        public int? RemainingSJILow { get; set; }
        public int? RemainingSJV { get; set; }
        public int? RemainingSJVHigh { get; set; }
        public int? RemainingSJVLow { get; set; }
        public int? RemainingDays { get; set; }
        public int? RemainingAmount { get; set; }

        /// <summary>
        /// return MVS error message
        /// </summary>
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<MvsError> MVSErrors { get; set; }
        public bool NewFinePolicy { get; set; }
    }
}
