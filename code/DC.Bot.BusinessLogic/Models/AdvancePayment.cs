﻿using DC.Customers.Client.Models;
using DC.Financials.Client.Models;

namespace DC.Bot.BusinessLogic.Models
{
    public class AdvancePayment
    {
        public int Amount { get; set; }
        public FinancialPreferences Preferences { get; set; }
        public CustomerAccountModel Account { get; set; }
        public bool IsRecurringPaymentWithPreferedPaymentDay => Preferences != null && Preferences.PaymentDayOfMonth.HasValue && Preferences.PaymentMethodIsDirectDebit == true;
        public bool IsRecurringPaymentWithoutPreferedPaymentDay => Preferences != null && Preferences.PaymentDayOfMonth == null && Preferences.PaymentMethodIsDirectDebit == true;

        //Acceptgiro
        public bool PaymentWithGiroCard => !IsRecurringPaymentWithPreferedPaymentDay && !IsRecurringPaymentWithoutPreferedPaymentDay;
    }
}
