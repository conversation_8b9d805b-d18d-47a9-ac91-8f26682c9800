﻿using Microsoft.Bot.Builder.Dialogs.Choices;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace DC.Bot.BusinessLogic.Models
{
    public class SolarPanels
    {
        [JsonProperty("numberOfSolarPanels")]
        public int NumberOfSolarPanels { get; set; }

        [JsonProperty("investment")]
        public decimal Investment { get; set; }

        [JsonProperty("yearlyElectricitySaving")]
        public int YearlyElectricitySaving { get; set; }

        [JsonProperty("yearlyAmountSaving")]
        public decimal YearlyAmountSaving { get; set; }

        [JsonProperty("paybackTimeInYears")]
        public double PaybackTimeInYears { get; set; }

        [JsonProperty("roofArea")]
        public double RoofArea { get; set; }

        [JsonProperty("choices")]
        public List<Choice> Choices { get; set; } = new List<Choice>();
    }
}