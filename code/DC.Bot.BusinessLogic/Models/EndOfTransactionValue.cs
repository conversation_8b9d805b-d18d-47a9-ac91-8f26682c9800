﻿using DC.Bot.BusinessLogic.Enumerations;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;

namespace DC.Bot.BusinessLogic.Models
{
    public class EndOfTransactionValue
    {
        [EnumDataType(typeof(TransactionStatus))]
        [JsonConverter(typeof(StringEnumConverter))]
        [JsonProperty("status")]
        public TransactionStatus Status { get; set; }

        [JsonProperty("result")]
        [EnumDataType(typeof(DialogAction))]
        [JsonConverter(typeof(StringEnumConverter))]
        public DialogAction Result { get; set; }
    }
}
