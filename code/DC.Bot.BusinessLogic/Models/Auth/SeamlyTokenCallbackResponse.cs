﻿using Newtonsoft.Json;

namespace DC.Bot.BusinessLogic.Models.Auth;
public class SeamlyTokenCallbackResponse
{
    /// <summary>
    /// ExternalId for use in Seamly. Should be unique and encrypted and reproducable
    /// </summary>
    [JsonProperty("external_id")]
    public string ExternalId { get; set; }

    /// <summary>
    /// Customer context variables for use in Seamly. Can be any map/object.
    /// </summary>
    [JsonProperty("variables")]
    public SeamlyContextVariables Variables { get; set; } = new();
}
