﻿using DC.Usages.Client.Models;
using System;
using System.Collections.Generic;

namespace DC.Bot.BusinessLogic.Models
{
    public class UsagesSummaryModel
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public List<UsagesSummaryActual> UsageActuals { get; set; }
        public bool HasSmartMeter { get; set; }
    }

    public class UsagesSummaryActual
    {
        public string Title { get; set; }
        public string MeasureType { get; set; }
        public List<UsagesSummaryUsageItem> UsageItems { get; set; }
    }

    public class UsagesSummaryUsageItem
    {
        public UsagesSummaryUsageItem(int month, UsageItem item)
        {
            UsageItem = item;
            Month = month;
        }

        public int Month { get; set; }
        public UsageItem UsageItem { get; set; }
    }
}