﻿using System.Collections.Generic;

namespace DC.Bot.BusinessLogic.Models
{
    public class SolarProducts
    {
        public SolarPanels SolarPanels { get; set; }

        public StukjeZon StukjeZon { get; set; }

        public bool HasProductWithIndefiniteDuration { get; set; }

        public List<ProductSlide> Slides { get; set; } = new List<ProductSlide>();

        public int AmountAttemptsSlideSelection { get; set; }

        public bool DataAlreadyFetched { get; set; }
    }
}