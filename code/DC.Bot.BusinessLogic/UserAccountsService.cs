﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.BusinessLogic.Base;
using DC.Domain.Exceptions.ResponseModels;
using DC.UserAccounts.Client.Models;
using DC.Utilities.Formatters;
using DC.Utilities.Validators;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using System;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic
{
    public class UserAccountsService : BaseService, IUserAccountsService
    {
        private readonly IDcUserAccountsRepository _userAccountsRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        public UserAccountsService(
            ILoggerFactory loggerFactory,
            IConfiguration configuration,
            IDcUserAccountsRepository userAccountsRepository) : base(loggerFactory, configuration)
        {
            _userAccountsRepository = userAccountsRepository;
        }

        /// <summary>
        /// Get Username (UserAccounts PAPI)
        /// </summary>
        public async Task<UserAccount> GetUserName(DialogData dialogData, int accountId)
        {
            var request = dialogData.ToRequestDataValidationRequest();
            var apiCall = _userAccountsRepository.GetUsername(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), request);
            var onSuccess = new Func<HttpOperationResponse<object>, UserAccount>(httpResponse =>
            {
                var data = ((ResponseDataUserAccount)httpResponse.Body).Data;
                //mask username
                if (!string.IsNullOrEmpty(data?.Username))
                    data.Username = data.Username.IsValidEmail() ? data.Username.MaskEmail() : data.Username.MaskUsername();

                return data;
            });

            return await ExecuteDcCall<UserAccount, ErrorResponse>(apiCall, onSuccess, new Guid("c6033540-187d-4566-87d6-8cc495390369"), nameof(GetUserName)).ConfigureAwait(false);
        }
    }
}
