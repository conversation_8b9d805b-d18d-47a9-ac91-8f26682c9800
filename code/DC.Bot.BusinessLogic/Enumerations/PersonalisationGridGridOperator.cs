﻿using System.Runtime.Serialization;

namespace DC.Bot.BusinessLogic.Enumerations
{
    /// <summary>
    /// The personalisation list of grid operators 
    /// use own const list because of small differences in enum name/value in the GridOperator Enum and the extra option Other.
    /// </summary>
    public enum PersonalisationGridGridOperator
    {
        /// <summary>
        /// Unknown grid operator 
        /// </summary>
        [EnumMember(Value = "Unknown")]
        Unknown,
        /// <summary>
        /// Other grid operator 
        /// </summary>
        [EnumMember(Value = "Other")]
        Other,
        /// <summary>
        /// grid operator Stedin Netbeheer B.V.
        /// </summary>
        [EnumMember(Value = "Stedin Netbeheer B.V.")]
        Stedin,
        /// <summary>
        /// grid operator Endinet B.V.
        /// </summary>
        [EnumMember(Value = "Endinet B.V.")]
        Endinet,
        /// <summary>
        /// grid operator Coteq Netbeheer B.V.
        /// </summary>
        [EnumMember(Value = "Coteq Netbeheer B.V.")]
        Coteq,
        /// <summary>
        /// grid operator N.V. RENDO
        /// </summary>
        [EnumMember(Value = "N.V. RENDO")]
        Rendo,
        /// <summary>
        /// grid operator Westland Infra Netbeheer B.V.
        /// </summary>
        [EnumMember(Value = "Westland Infra Netbeheer B.V.")]
        WestlandInfraNet,
        /// <summary>
        /// grid operator Enexis B.V.
        /// </summary>
        [EnumMember(Value = "Enexis B.V.")]
        Enexis,
        /// <summary>
        /// grid operator Liander N.V.
        /// </summary>
        [EnumMember(Value = "Liander N.V.")]
        Liander,
        /// <summary>
        /// grid operator Delta N.V.
        /// </summary>
        [EnumMember(Value = "Delta N.V.")]
        Delta,
        /// <summary>
        /// grid operator Cogas Netbeheer B.V.
        /// </summary>
        [EnumMember(Value = "Cogas")]
        Cogas,
    }
}
