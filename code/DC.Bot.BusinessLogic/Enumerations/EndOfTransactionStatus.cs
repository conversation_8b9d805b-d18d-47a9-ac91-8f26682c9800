﻿using System.Runtime.Serialization;

namespace DC.Bot.BusinessLogic.Enumerations
{
    public enum TransactionStatus
    {
        [EnumMember(Value = "error")]
        Error,
        [EnumMember(Value = "success")]
        Success,
        [EnumMember(Value = "temporary_failure")]
        TemporaryFailure,
        [EnumMember(Value = "permanent_failure")]
        PermanentFailure,
        [EnumMember(Value = "invalid_start_event")]
        InvalidStartEvent,
        [EnumMember(Value = "refund_daily_service")]
        RefundDailyService,
        [EnumMember(Value = "refund_daily_commercieel")]
        RefundCommercieel,
        [EnumMember(Value = "mbfchat")]
        MBFChat,
        [EnumMember(Value = "unhappy")]
        Unhappy,
        [EnumMember(Value = "appointment")]
        Appointment,
        [EnumMember(Value = "product_advice_no_products")]
        ProductAdviceNoProducts,
        [EnumMember(Value = "product_advice_indefinite_in_60_days")]
        ProductAdviceIndefiniteIn60Days,
        [EnumMember(Value = "product_advice_indefinite_after_60_days")]
        ProductAdviceIndefiniteAfter60Days,
        [EnumMember(Value = "product_advice_definite_in_60_days")]
        ProductAdviceDefiniteIn60Days,
        [EnumMember(Value = "product_advice_definite_after_60_days")]
        ProductAdviceDefiniteAfter60Days,
        [EnumMember(Value = "product_advice_indefinite_in_90_days")]
        ProductAdviceIndefiniteIn90Days,
        [EnumMember(Value = "product_advice_indefinite_after_90_days")]
        ProductAdviceIndefiniteAfter90Days,
        [EnumMember(Value = "product_advice_definite_in_90_days")]
        ProductAdviceDefiniteIn90Days,
        [EnumMember(Value = "product_advice_definite_after_90_days")]
        ProductAdviceDefiniteAfter90Days,
        [EnumMember(Value = "product_advice_warmth")]
        ProductAdviceWarmth,
        [EnumMember(Value = "product_advice_indefinite_elec_and_warmth_in_90_days")]
        ProductAdviceIndefiniteElecAndWarmthIn90Days,
        [EnumMember(Value = "product_advice_indefinite_elec_and_warmth_after_90_days")]
        ProductAdviceIndefiniteElecAndWarmthAfter90Days,
        [EnumMember(Value = "product_advice_definite_elec_and_warmth_in_90_day")]
        ProductAdviceDefiniteElecAndWarmthIn90Days,
        [EnumMember(Value = "product_advice_definite_elec_and_warmth_after_90_day")]
        ProductAdviceDefiniteElecAndWarmthAfter90Days,
        [EnumMember(Value = "success_link")]
        SuccessLink,
        [EnumMember(Value = "nba_not_available")]
        NextBestActionNotAvailable,
        [EnumMember(Value = "nba_dismissed")]
        NextBestActionDismissed,
        [EnumMember(Value = "nba_ignored")]
        NextBestActionIgnored,
        [EnumMember(Value = "unhappy_range")]
        UnhappyRange,
        [EnumMember(Value = "success_advicealmostknown")]
        SuccessAdviceAlMostKnown,
        [EnumMember(Value = "unhappy_newcustomer")]
        UnhappyNewCustomer,
        [EnumMember(Value = "unhappy_closetoyearnote")]
        UnhappyCloseToYearNote,
        [EnumMember(Value = "unhappy_meterreadings")]
        UnhappyMeterreadings,
        [EnumMember(Value = "unhappy_warmth")]
        UnhappyWarmth,
        [EnumMember(Value = "unhappy_redelivery")]
        UnhappyRedelivery,
        [EnumMember(Value = "unhappy_warmthandredelivery")]
        UnhappyWarmthAndRedelivery,
        [EnumMember(Value = "success_mandate")]
        SuccessMandate,
        [EnumMember(Value = "success_calculated")]
        SuccessCalculated,
        [EnumMember(Value = "unhappy_p4")]
        UnhappyP4,
        [EnumMember(Value = "unhappy_conventional")]
        UnhappyConventional,
        [EnumMember(Value = "unhappy_dynamicpricing")]
        UnhappyDynamicPricing,
        [EnumMember(Value = "product_advice_dynamic_pricing")]
        ProductAdviceDynamicPricing,
        [EnumMember(Value = "unhappy_phonenumber")]
        UnhappyPhoneNumber,
        [EnumMember(Value = "success_user_authenticated")]
        SuccessUserAuthenticated,
        [EnumMember(Value = "zonopdak_electricity_indefinite")]
        ZonOpDakElectricityIndefinite,
        [EnumMember(Value = "zonopdak_electricity_definite_before_1_june")]
        ZonOpDakElectricityDefiniteBefore1June,
        [EnumMember(Value = "zonopdak_electricity_definite_after_1_june")]
        ZonOpDakElectricityDefiniteAfter1June,
        [EnumMember(Value = "zonopdak_electricity_dynamic_pricing")]
        ZonOpDakElectricityDynamicPricing,
        [EnumMember(Value = "zonopdak_electricity_template_contract")]
        ZonOpDakElectricityTemplateContract,
        [EnumMember(Value = "zonopdak_electricity_no_electricity")]
        ZonOpDakNoElectricity,
        [EnumMember(Value = "no_active_energycontract")]
        NoActiveEnergyContract,
        [EnumMember(Value = "unhappy_active_debt")]
        UnhappyActiveDebt,
        [EnumMember(Value = "unhappy_amount_too_low")]
        UnhappyAmountTooLow,
        [EnumMember(Value = "unhappy_estimated_mr")]
        UnhappyEstimatedMr,
        [EnumMember(Value = "unhappy_nextchargedate_unknown")]
        UnhappyNextChargeDateUnknown,
        [EnumMember(Value = "unhappy_payment_amount_not_determined")]
        UnhappyPaymentAmountNotDetermined,
        [EnumMember(Value = "unhappy_tariff_structure")]
        UnhappyTariffStructure,
        [EnumMember(Value = "unhappy_false_amount")]
        UnhappyFalseAmount,
        [EnumMember(Value = "unhappy_minimum_amount")]
        UnhappyMinimumAmount,
        [EnumMember(Value = "unhappy_incorrect_amount")]
        UnhappyIncorrectAmount,
        [EnumMember(Value = "unhappy_3_strikes")]
        Unhappy3Strikes,
        [EnumMember(Value = "unhappy_nocustomernumber")]
        UnhappyNoCustomerNumber,
        [EnumMember(Value = "unhappy_tariff_structure_correction")]
        UnhappyTariffStructureCorrection,
        [EnumMember(Value = "unhappy_no_agreement")]
        UnhappyNoAgreement,
        [EnumMember(Value = "unhappy_multiple_agreements")]
        UnhappyMultipleAgreements,
        [EnumMember(Value = "error_nodatafound")]
        ErrorNoDataFound,
        [EnumMember(Value = "error_query")]
        ErrorQuery,
        [EnumMember(Value = "error_exception")]
        ErrorException,
        [EnumMember(Value = "success_lower_than_nine_euros")]
        SuccessLowerThanNineEuros,
        [EnumMember(Value = "unhappy_outstanding_debt")]
        UnhappyOutstandingDebt,
        [EnumMember(Value = "mbfchat_savereading_per_smn")]
        MBFChatSaveReadingPerSmn,
        [EnumMember(Value = "success_savereading_per_smn_skip")]
        SuccessSaveReadingPerSmnSkip,
        [EnumMember(Value = "mbfchat_savereading_tme_smn")]
        MBFChatSaveReadingTmeSmn,
        [EnumMember(Value = "success_savereading_tme_smn_skip")]
        SuccessSaveReadingTmeSmnSkip,
        [EnumMember(Value = "success_sm_nosavereading")]
        SuccessSmNoSaveReading,
        [EnumMember(Value = "mbfchat_readingadded_correction")]
        MBFChatReadingAddedCorrection,
        [EnumMember(Value = "success_readingadded_skip")]
        SuccessReadingAddedSkip,
        [EnumMember(Value = "no_reading_permission_tme_skip")]
        NoReadingPermissionTmeSkip,
        [EnumMember(Value = "no_reading_permission_skip")]
        NoReadingPermissionSkip,
        [EnumMember(Value = "success_savereading_skip")]
        SuccessSaveReadingSkip,
        [EnumMember(Value = "success_savereading_tme_skip")]
        SuccessSaveReadingTmeSkip,
        [EnumMember(Value = "success_noperiodicreading_skip")]
        SuccessNoPeriodicReadingSkip,
        [EnumMember(Value = "success_readingadded_link")]
        SuccessReadingAddedLink,
        [EnumMember(Value = "no_reading_permission_tme_link")]
        NoReadingPermissionTmeLink,
        [EnumMember(Value = "no_reading_permission_link")]
        NoReadingPermissionLink,
        [EnumMember(Value = "success_savereading_link")]
        SuccessSaveReadingLink,
        [EnumMember(Value = "success_savereading_tme_link")]
        SuccessSaveReadingTmeLink,
        [EnumMember(Value = "success_noperiodicreading_link")]
        SuccessNoPeriodicReadingLink,
        [EnumMember(Value = "unhappy_nocontract")]
        UnhappyNoContract,
        [EnumMember(Value = "unhappy_ekv_warmth")]
        UnhappyEkvWarmth,
        [EnumMember(Value = "unhappy_eco_warmth")]
        UnhappyEcoWarmth,
        [EnumMember(Value = "error_locationid")]
        ErrorLocationID,
        [EnumMember(Value = "success_already_email")]
        SuccessAlreadyEmail,
        [EnumMember(Value = "success_changed_all")]
        SuccessChangedAll,
        [EnumMember(Value = "success_changed_mer")]
        SuccessChangedMer,
        [EnumMember(Value = "success_changed_nota")]
        SuccessChangedNota,
        [EnumMember(Value = "success_no_change")]
        SuccessNoChange
    }
}
