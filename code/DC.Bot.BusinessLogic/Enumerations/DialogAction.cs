﻿using System.Runtime.Serialization;

namespace DC.Bot.BusinessLogic.Enumerations
{
    public enum DialogAction
    {
        None = 0,

        [EnumMember(Value = "customer_verification")]
        CustomerVerification = 10,

        CustomerIdVerification = 11,

        CustomerAccountSelection = 15,

        [EnumMember(Value = "get_advance_payment_amount")]
        GetAdvancePaymentAmount = 20,

        [EnumMember(Value = "get_advance_payment_day")]
        GetAdvancePaymentDay = 21,

        NonDirectDebit = 22,

        [EnumMember(Value = "get_next_yearnote_date")]
        GetNextYearnoteDate = 30,

        [EnumMember(Value = "get_product_rates")]
        GetProductRates = 40,

        [EnumMember(Value = "get_username")]
        GetUserName = 60,

        [EnumMember(Value = "get_product_enddates")]
        GetProductEndDates = 70,

        [EnumMember(Value = "product_enddates_advice")]
        GetProductEndDatesAdvice = 75,

        [EnumMember(Value = "get_payment_arrangement")]
        GetPaymentArrangement = 80,

        GetRelocationDate = 90,

        [EnumMember(Value = "get_product_usages")]
        GetProductUsages = 100,

        SolarProductsPersonal = 110,

        SolarProductsPublic = 120,

        AnonymousAddress = 130,

        StukjeZonProductsPersonal = 150,

        SunCampaignPersonal = 160,

        SunCampaignPublic = 170,

        [EnumMember(Value = "change_phonenumber")]
        ChangePhoneNumber = 180,

        [EnumMember(Value = "discontinue_toon")]
        DiscontinueToon = 190,

        DiscontinueServiceContract = 200,

        KetelComfortAppointment = 210,

        [EnumMember(Value = "change_email")]
        ChangeEmail = 220,

        [EnumMember(Value = "change_iban")]
        ChangeIBAN = 230,

        [EnumMember(Value = "readings_report_request")]
        ReadingsReportRequest = 240,

        [EnumMember(Value = "create_serviceorder" )]
        CreateServiceOrder = 250,

        [EnumMember(Value = "save_reading_personal")]
        SaveReadingPersonal = 260,

        NextBestAction = 270,

        [EnumMember(Value = "get_advance_payment_advice")]
        GetAdvancePaymentAdvice = 280,

        IsmaMandate = 290,

        UsageCapAlarm = 300,

        [EnumMember(Value = "product_fine_calculation")]
        ProductFineCalculation = 310,

        [EnumMember(Value = "zonopdak")]
        ZonOpDak = 350,

        [EnumMember(Value = "change_contact_preferences")]
        ChangeContactPreferences = 380
    }
}
