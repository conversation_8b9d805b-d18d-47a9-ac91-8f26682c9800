﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.Request;
using DC.Bot.Repositories.Interfaces;
using DC.BusinessLogic.Base;
using DC.Repositories.Base.Enumerations;
using DC.Storage.Client.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using Newtonsoft.Json;

namespace DC.Bot.BusinessLogic;

public class StorageService : BaseService, IStorageService
{
    private readonly IDcStorageRepository _storageRepository;
    private readonly bool _allowTextLabelsExport = false;

    /// <summary>
    /// Constructor
    /// </summary>
    public StorageService(ILoggerFactory loggerFactory, IConfiguration configuration, IDcStorageRepository storageRepository) : base(loggerFactory, configuration)
    {
        _storageRepository = storageRepository;
        _allowTextLabelsExport = _configuration?.GetValue<bool>("AllowTextLabelsExport") ?? false;
    }

    /// <summary>
    /// GetTextLabels
    /// </summary>
    public async Task<TextLabelCollection> GetTextLabels(Label label, string textLabelGroupName, List<string> filters, bool passthroughLanguage = true)
    {
        //if label is Default use Label Eneco as default
        if (label == Label.Default)
            label = Label.Eneco;
        IList<TextLabelModel> result = await ExecuteStorageTextLabelsCall(label, textLabelGroupName, filters, passthroughLanguage);

        return new TextLabelCollection(result?.ToList());
    }

    private async Task<IList<TextLabelModel>> ExecuteStorageTextLabelsCall(Label label, string textLabelGroupName, List<string> filters, bool passthroughLanguage = true)
    {
        // API call task
        var task = _storageRepository.GetTextlabels(label, new RequestDataTextLabelRequest(new TextLabelRequest(textLabelGroupName, filters)), passthroughLanguage);

        // SuccessCode response handling
        var successFunc = new Func<HttpOperationResponse<object>, IList<TextLabelModel>>((httpResponse) => ((ResponseDataIListTextLabelModel)httpResponse.Body)?.Data);

        // Execute the DC call
        return await ExecuteDcCall<IList<TextLabelModel>, Domain.Exceptions.ResponseModels.ErrorResponse>(task, successFunc, new Guid("5fe8fb3f-809a-4a7e-8ad9-1b8052096cae"), nameof(GetTextLabels));
    }

    /// <summary>
    /// Adds a conversation to the database.
    /// </summary>
    public async Task AddConversation(Label label, ConversationWrapper request)
    {
        var storageRequest = request.Payload.ToStorageModel();
        await _storageRepository.AddConversation(label, storageRequest);
    }

    /// <summary>
    /// Retrieves all text labels that are used within the bot and exports them to JSON files that can be used in the unit tests for this solution
    /// </summary>
    public async Task ExportTextLabels(Label label, bool isTest = false)
    {
        if (!_allowTextLabelsExport)
            return;

        CleanUpPreviousTextLabelExport();
        (string key, IList<TextLabelModel> labels)[] result = await GetTextLabelGroups(label);

        var groups = result?.Select(x => new
        {
            Key = x.key,
            Value = x.labels.Select(y =>
            new TextLabelModel
            {
                Id = y.Id,
                Key = y.Key,
                Value = y.Value,
                GroupName = y.GroupName,
                GroupDescription = y.GroupDescription,
                Channel = y.Channel,
                Type = y.Type,
                Language = y.Language,
                Filter = y.Filter,
                RemovedAtUtc = y.RemovedAtUtc,
                UpdatedAtUtc = y.UpdatedAtUtc
            })
        }).ToList();

        foreach (var group in groups ?? [])
        {
            string json = JsonConvert.SerializeObject(group.Value, Formatting.Indented,
            new JsonSerializerSettings { PreserveReferencesHandling = PreserveReferencesHandling.Objects });
            if (!isTest)
                await File.WriteAllTextAsync(Path.Combine(Directory.GetParent(Directory.GetCurrentDirectory()).Parent.FullName, $"tests\\DC.Bot.Tests\\Exports\\{group.Key}.json"), json);
        }
    }

    private async Task<(string, IList<TextLabelModel>)[]> GetTextLabelGroups(Label label)
    {
        List<Task<(string, IList<TextLabelModel>)>> retrieveTextLabelsCalls = new();
        foreach (var group in TextLabelGroups.AllGroupNames)
        {
            retrieveTextLabelsCalls.Add(GetTextLabelsForGroup(group));
        }

        async Task<(string, IList<TextLabelModel>)> GetTextLabelsForGroup(string group)
        {
            return (group, await ExecuteStorageTextLabelsCall(label, group, null, passthroughLanguage: false));
        }

        return await Task.WhenAll(retrieveTextLabelsCalls);
    }

    private static void CleanUpPreviousTextLabelExport()
    {
        var directory = new DirectoryInfo(Path.Combine(Directory.GetParent(Directory.GetCurrentDirectory()).Parent.FullName, $"tests\\DC.Bot.Tests\\Exports"));

        if (!directory.Exists)
        {
            return;
        }

        foreach (FileInfo file in directory.GetFiles()) file.Delete();
        foreach (DirectoryInfo subDirectory in directory.GetDirectories()) subDirectory.Delete(true);
    }

}
