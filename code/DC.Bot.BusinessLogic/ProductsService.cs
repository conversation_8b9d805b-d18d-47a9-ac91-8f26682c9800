﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.BusinessLogic.Base;
using DC.Customers.Client.Models;
using DC.Domain.Exceptions;
using DC.Domain.Exceptions.Helpers;
using DC.Domain.Exceptions.ResponseModels;
using DC.Domain.Models.Products;
using DC.Domain.Models.Products.ProductRates;
using DC.Domain.Models.Products.ServiceAgreements;
using DC.Domain.Models.Products.ServiceOrders;
using DC.Products.Client.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DiscontinueProductType = DC.Domain.Models.Products.DiscontinueProductType;
using PlanLinkType = DC.Products.Client.Models.PlanLinkType;
using ProductModel = DC.Domain.Models.Products.ProductModel;
using Relocations = DC.Products.Client.Models.Relocations;
using ResponseDataListRelocations = DC.Products.Client.Models.ResponseDataListRelocations;
using ResponseDataString = DC.Products.Client.Models.ResponseDataString;

namespace DC.Bot.BusinessLogic;

public class ProductsService : BaseService, IProductsService
{
    private readonly IDcProductsRepository _productsRepository;
    private readonly IDcCustomersRepository _customersRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public ProductsService(
        ILoggerFactory loggerFactory,
        IConfiguration configuration, IDcCustomersRepository customersRepository,
        IDcProductsRepository productsRepository) : base(loggerFactory, configuration)
    {
        _productsRepository = productsRepository;
        _customersRepository = customersRepository;
    }

    /// <summary>
    /// Get product rates (Products PAPI)
    /// </summary>
    public async Task<IList<ProductRate>> GetProductRates(DialogData dialogData, int accountId)
    {
        var apiCall = _productsRepository.GetProductRates(dialogData.Customer.Label, dialogData.Channel,
            dialogData.Verification.CustomerId.GetValueOrDefault(0), accountId);
        var onSuccess = new Func<HttpOperationResponse<object>, IList<ProductRate>>(httpResponse =>
        {
            var data = ((ResponseDataProductRates)httpResponse.Body).Data;
            return data.Rates;
        });

        return await ExecuteDcCall<IList<ProductRate>, ErrorResponse>(apiCall, onSuccess,
            new Guid("5e6bd2ff-ab44-41d5-806e-56d4ef85f8f8"), nameof(GetProductRates)).ConfigureAwait(false);
    }

    /// <summary>
    /// CancelProductContract
    /// </summary>
    public async Task<bool> CancelProductContract(DialogData dialogData, ProductType productType,
        ProductDiscontinueReason reason)
    {
        if (!dialogData.DiscontinueContractData.AgreementId.HasValue)
            throw new TechnicalException(new Guid("dc32297c-a2cc-4c66-9c6b-95f89ec92f64"),
                $"Unable to determine AgreementID in {nameof(CancelProductContract)}");

        var apiCall = await _customersRepository.GetCustomer(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0)).ConfigureAwait(false);
        var customer = ((ResponseDataCustomerModel)apiCall.Body).Data;
        var account = dialogData.GetAccount(customer);

        if (account == null)
            throw new TechnicalException(new Guid("e888570e-bba3-4af4-96bf-770ee12531fe"),
                $"Unable to determine account in {nameof(CancelProductContract)}");

        var httpResponse = await _productsRepository
            .PutProductOrderV2(dialogData.Customer.Label, dialogData.ToProductRemovalOrder(productType, reason, customer))
            .ConfigureAwait(false);

        if (httpResponse.Response.StatusCode == System.Net.HttpStatusCode.Accepted)
        {
            return true;
        }

        if (httpResponse.Body is ErrorResponse errorResponse)
        {
            throw DigitalCoreExceptionHelper.RecreateExceptionFromDerivedErrorResponse(errorResponse);
        }

        throw new TechnicalException(new Guid("21138bda-f941-4324-8850-9c6b87163782"),
            $"{nameof(CancelProductContract)} threw an un-expected statuscode {(int)httpResponse.Response.StatusCode} " +
            $"with message {await httpResponse.Response.Content.ReadAsStringAsync().ConfigureAwait(false)}");
    }

    /// <summary>
    /// Check if a customer is allowed to cancel a product
    /// </summary>
    public async Task<DiscontinueIntake> GetDiscontinueIntake(DialogData dialogData,
        DiscontinueProductType discontinueProductType)
    {
        if (dialogData == null || dialogData.Verification?.CustomerId.GetValueOrDefault(0) == 0)
        {
            throw new DataQualityException(new Guid("797d5e90-b218-40b0-88a7-3bc1c0ce96f1"),
                $"{nameof(GetDiscontinueIntake)} Missing UserData");
        }

        var apiCall = _productsRepository.GetProductDiscontinueIntake(dialogData.Customer.Label, dialogData.Channel,
            dialogData.Verification.CustomerId.GetValueOrDefault(0), discontinueProductType);
        var onSuccess = new Func<HttpOperationResponse<object>, DiscontinueIntake>(httpResponse =>
            ((ResponseDataDiscontinueIntake)httpResponse.Body).Data);
        return await ExecuteDcCall<DiscontinueIntake, ErrorResponse>(apiCall, onSuccess,
            new Guid("2e5a13f4-180b-4969-a384-cbf4ba068956"), nameof(GetDiscontinueIntake)).ConfigureAwait(false);
    }

    /// <summary>
    /// Gets the ketel comfort product details.
    /// </summary>
    public async Task<KetelComfortServiceModel> GetKetelComfortProductDetails(DialogData dialogData)
    {
        var apiCall = _productsRepository.GetKetelComfortProductDetails(dialogData.Customer.Label,
            dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0),
            dialogData.SelectedAccount.AccountId);
        var onSuccess = new Func<HttpOperationResponse<object>, KetelComfortServiceModel>(httpResponse =>
            ((ResponseDataKetelComfortProductDetails)httpResponse?.Body).ToKetelComfortMaintenanceAppointment());

        return await ExecuteDcCall<KetelComfortServiceModel, ErrorResponse>(apiCall, onSuccess,
                new Guid("c1facb43-a69a-4cdb-b14d-dc64eefc46cc"), nameof(GetKetelComfortProductDetails))
            .ConfigureAwait(false);
    }

    /// <summary>
    /// Gets the service agreements for customer account.
    /// </summary>
    public async Task<List<ServiceAgreement>> GetServiceAgreementsForAgreementId(DialogData dialogData, int agreementId,
        bool activeAgreementsWithSelfService = true)
    {
        var apiCall = _productsRepository.GetServiceAgreementsForAgreementId(dialogData.Customer.Label,
            dialogData.Channel, agreementId, activeAgreementsWithSelfService);
        var onSuccess = new Func<HttpOperationResponse<object>, List<ServiceAgreement>>(httpResponse =>
            ((ResponseDataListServiceAgreement)httpResponse?.Body)?.Data?.ToList());

        return await ExecuteDcCall<List<ServiceAgreement>, ErrorResponse>(apiCall, onSuccess,
                new Guid("3fde6c1e-986e-433d-8957-2bb14cee6aad"), nameof(GetServiceAgreementsForAgreementId))
            .ConfigureAwait(false);
    }

    /// <summary>
    /// Gets the service orders for agreement.
    /// </summary>
    public async Task<List<ServiceOrder>> GetServiceOrdersForAgreement(DialogData dialogData,
        string serviceAgreementId, ServiceOrderStatus? orderStatus = null)
    {
        var apiCall = _productsRepository.GetServiceOrdersFromServiceAgreement(dialogData.Customer.Label,
            dialogData.Channel, serviceAgreementId, orderStatus);
        var onSuccess = new Func<HttpOperationResponse<object>, List<ServiceOrder>>(httpResponse =>
            ((ResponseDataListServiceOrder)httpResponse?.Body)?.Data?.ToList());

        return await ExecuteDcCall<List<ServiceOrder>, ErrorResponse>(apiCall, onSuccess,
                new Guid("a542de80-26e7-4c7e-9c83-1846bfe5bf41"), nameof(GetServiceOrdersForAgreement))
            .ConfigureAwait(false);
    }

    /// <summary>
    /// Gets the hardware product specifications.
    /// </summary>
    public async Task<List<HardwareProductSpecification>> GetHardwareProductSpecifications(DialogData dialogData)
    {
        var apiCall =
            _productsRepository.GetHardwareProductSpecifications(dialogData.Customer.Label, dialogData.Channel);
        var onSuccess = new Func<HttpOperationResponse<object>, List<HardwareProductSpecification>>(httpResponse =>
            ((ResponseDataIListHardwareProductSpecification)httpResponse?.Body)?.Data?.ToList());

        return await ExecuteDcCall<List<HardwareProductSpecification>, ErrorResponse>(apiCall, onSuccess,
                new Guid("1c7814df-d8cb-4abb-947d-841ae997c880"), nameof(GetHardwareProductSpecifications))
            .ConfigureAwait(false);
    }

    /// <summary>
    /// Creates the service order.
    /// </summary>
    public async Task<string> CreateServiceOrder(DialogData dialogData)
    {
        var apiCall = _productsRepository.CreateServiceOrder(dialogData.Customer.Label, dialogData.Channel,
            dialogData.Verification.CustomerId.GetValueOrDefault(0), dialogData.SelectedAccount.AccountId,
            dialogData.ToServiceOrderRequestModel());
        var onSuccess =
            new Func<HttpOperationResponse<object>, string>(httpResponse =>
                ((ResponseDataString)httpResponse?.Body)?.Data);

        return await ExecuteDcCall<string, ErrorResponse>(apiCall, onSuccess,
            new Guid("ef4de067-d62a-4309-93d9-e3614af62611"), nameof(CreateServiceOrder)).ConfigureAwait(false);
    }

    /// <summary>
    /// Gets the service order plan link.
    /// </summary>
    public async Task<string> GetServiceOrderPlanLink(DialogData dialogData, string orderId, PlanLinkType type)
    {
        var apiCall = _productsRepository.GetServiceOrderPlanLink(dialogData.Customer.Label, dialogData.Channel,
            orderId, type);
        var onSuccess =
            new Func<HttpOperationResponse<object>, string>(httpResponse =>
                ((ResponseDataString)httpResponse?.Body)?.Data);

        return await ExecuteDcCall<string, ErrorResponse>(apiCall, onSuccess,
                new Guid("********-fdfd-43a6-b80d-c3decf987f8b"), nameof(GetServiceOrderPlanLink))
            .ConfigureAwait(false);
    }

    /// <summary>
    /// Get Customer Products by Account
    /// </summary>
    public async Task<IList<ProductModel>> GetCustomerProductsByAccount(DialogData dialogData, int accountId, bool? active = null, bool includeProductRates = false)
    {
        var apiCall = _productsRepository.GetCustomerAccountProducts(dialogData.Customer.Label, dialogData.Channel,
            dialogData.Verification.CustomerId.GetValueOrDefault(0), accountId, active, includeProductRates);
        var onSuccess = new Func<HttpOperationResponse<object>, IList<ProductModel>>(httpResponse =>
            ((ResponseDataListProductModel)httpResponse.Body).Data);
        return await ExecuteDcCall<IList<ProductModel>, ErrorResponse>(apiCall, onSuccess,
                new Guid("5781be4d-6512-4025-adfb-c870cd41f358"), nameof(GetCustomerProductsByAccount))
            .ConfigureAwait(false);
    }

    /// <summary>
    /// Get customer products of all accounts
    /// </summary>
    public async Task<IList<ProductModel>> GetCustomerProducts(DialogData dialogData, bool includeProductRates = false)
    {
        var apiCall = _productsRepository.GetCustomerProducts(dialogData.Customer.Label, dialogData.Channel,
            dialogData.Verification.CustomerId.GetValueOrDefault(0), includeProductRates: includeProductRates);
        var onSuccess = new Func<HttpOperationResponse<object>, IList<ProductModel>>(httpResponse =>
            ((ResponseDataListProductModel)httpResponse.Body).Data);
        return await ExecuteDcCall<IList<ProductModel>, ErrorResponse>(apiCall, onSuccess,
            new Guid("51e3cc83-08fd-4f42-920d-50fe36d94bf1"), nameof(GetCustomerProducts)).ConfigureAwait(false);
    }


    /// <summary>
    /// Get GetCustomerRelocations (Products PAPI)
    /// </summary>       
    public async Task<IList<Relocations>> GetCustomerRelocations(DialogData dialogData)
    {
        var apiCall = _productsRepository.GetCustomerRelocations(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0));
        var onSuccess = new Func<HttpOperationResponse<object>, IList<Relocations>>(httpResponse => ((ResponseDataListRelocations)httpResponse.Body).Data);
        return await ExecuteDcCall<IList<Relocations>, ErrorResponse>(apiCall, onSuccess, new Guid("22c04306-b921-47df-af3c-3314618c6454"), nameof(GetCustomerRelocations)).ConfigureAwait(false);
    }
}