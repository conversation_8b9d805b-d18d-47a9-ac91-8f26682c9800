﻿using DC.Bot.BusinessLogic.Interfaces.DialogContainers;

namespace DC.Bot.BusinessLogic.Dialogs.Products
{
    public class ProductsDialogContainer : IProductsDialogContainer
    {
        public ProductRatesDialog ProductRatesDialog { get; private set; }
        public ProductEndDatesDialog ProductEndDatesDialog { get; private set; }
        public ProductEndDatesAdviceDialog ProductEndDatesAdviceDialog { get; private set; }

        public KetelComfortAppointmentDialog KetelComfortAppointmentDialog { get; private set; }

        public DiscontinueToonDialog PersonalDiscontinueToonDialog { get; private set; }
        public DiscontinueServiceContractDialog PersonalDiscontinueServiceContractDialog { get; private set; }
        public CreateServiceOrderDialog CreateServiceOrderDialog { get; private set; }
        public NextBestActionDialog NextBestActionDialog { get; private set; }

        public ProductFineCalculationDialog ProductFineCalculationDialog { get; private set; }

        public ZonOpDakDialog ZonOpDakDialog { get; private set; }


        [System.Diagnostics.CodeAnalysis.SuppressMessage("Major Code Smell", "S107:Methods should not have too many parameters", Justification = "This is the setup for now")]
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Major Code Smell", "S107:Methods should not have too many parameters", Justification = "<Pending>")]
        public ProductsDialogContainer(
            ProductRatesDialog productRatesDialog,
            ProductEndDatesDialog productEndDatesDialog,
            ProductEndDatesAdviceDialog productEndDatesAdviceDialog,
            KetelComfortAppointmentDialog ketelComfortAppointmentDialog,
            DiscontinueToonDialog personalDiscontinueToonDialog,
            DiscontinueServiceContractDialog personalDiscontinueServiceContractDialog,
            CreateServiceOrderDialog createServiceOrderDialog,
            NextBestActionDialog nextBestActionDialog,
            ProductFineCalculationDialog productFineCalculationDialog,
            ZonOpDakDialog zonOpDakDialog)
        {
            ProductRatesDialog = productRatesDialog;
            ProductEndDatesDialog = productEndDatesDialog;
            ProductEndDatesAdviceDialog = productEndDatesAdviceDialog;
            PersonalDiscontinueToonDialog = personalDiscontinueToonDialog;
            PersonalDiscontinueServiceContractDialog = personalDiscontinueServiceContractDialog;
            KetelComfortAppointmentDialog = ketelComfortAppointmentDialog;
            CreateServiceOrderDialog = createServiceOrderDialog;
            NextBestActionDialog = nextBestActionDialog;
            ProductFineCalculationDialog = productFineCalculationDialog;
            ZonOpDakDialog = zonOpDakDialog;
        }
    }
}
