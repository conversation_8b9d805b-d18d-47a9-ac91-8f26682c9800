﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Models.NextBestAction;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Products
{
    public class NextBestActionDialog : BaseDialog
    {
        // dependencies
        private readonly INextBestActionService _nextBestActionService;
        private readonly ICustomersService _customersService;

        // choice configuration
        private IList<Choice> nbaChoice { get; set; }

        public NextBestActionDialog(
            ILoggerFactory loggerFactory,
            ISessionManager sessionManager,
            ILoggingService loggingService,
            INextBestActionService nextBestActionService,
            ICustomersService customersService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
                new WaterfallStep[]
                {
                    InitTextLabels,
                    ProposeNextBestActionStep,
                    NextBestActionDecisionStep,
                    TriggerFollowUpDialog
                }));

            AddDialog(new TextPrompt(nameof(InitTextLabels)));
            AddDialog(new TextPrompt(nameof(ProposeNextBestActionStep)));
            AddDialog(new ChoicePrompt(nameof(NextBestActionDecisionStep)));

            InitialDialogId = nameof(WaterfallDialog);

            _nextBestActionService = nextBestActionService;
            _customersService = customersService;
        }

        private async Task<DialogTurnResult> ProposeNextBestActionStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.NextBestAction).ConfigureAwait(false);

            // check if NBA available
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            // if the data is not present (due to a earlier end of transaction reset), re-collect from the endpoint
            if (dialogData.NextBestAction.Data == null)
                dialogData.NextBestAction.Data = await _nextBestActionService.GetNextBestActions(dialogData, true).ConfigureAwait(false);

            var nbaInfo = dialogData.NextBestAction.Data?.ToNextBestActionInfo();

            // NBA is available, go to ProposeNextBestActionStep
            if (nbaInfo?.Available == true)
            {
                // get the nbaConfiguration text belonging to the next best action type
                var actionType = nbaInfo.Type;
                var nbaConfiguration = actionType.GetNextBestActionConfiguration(dialogData.NextBestAction.Data, dialogData.TextLabels.TextLabels);

                // if there is no support for the action type, end the dialog
                if (!nbaConfiguration.IsSupported)
                {
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(NextBestActionDialog), nameof(ProposeNextBestActionStep), TransactionStatus.PermanentFailure, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }

                SetupChoice(actionType, dialogData);
                dialogData.NextBestAction.CurrentTransactionIsNba = true;

                // add VIEWED feedback and...
                await _nextBestActionService.AddFeedback(dialogData, actionType, FeedbackStatus.Viewed).ConfigureAwait(false);
                // ... propose the NBA with choice
                return await stepContext.PromptAsync(nameof(NextBestActionDecisionStep), new PromptOptions
                {
                    Prompt = MessageFactory.Text(nbaConfiguration.PromptText),
                    RetryPrompt = MessageFactory.Text(nbaConfiguration.PromptText),
                    Choices = nbaChoice,
                    Style = ListStyle.SuggestedAction
                }, cancellationToken).ConfigureAwait(false);
            }
            // no NBA available
            else
            {
                // Because there are no more NBA's, send a customerEvent to Conversationals so they will not trigger another NBA request ($nba) because the previous sent event contains available: true.
                var customer = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);
                var personalisationInfoTask = _customersService.GetPersonalisationInfo(dialogData, customer);

                await _sessionManager.SendCustomerInfoEvent(stepContext.Context, await personalisationInfoTask.ConfigureAwait(false), dialogData.NextBestAction.Data.ToNextBestActionInfo()).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(NextBestActionDialog), nameof(ProposeNextBestActionStep), TransactionStatus.NextBestActionNotAvailable, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }
        }

        private async Task<DialogTurnResult> NextBestActionDecisionStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            var actionType = dialogData.NextBestAction.Data.ToNextBestActionInfo().Type;
            var configuration = actionType.GetNextBestActionConfiguration(dialogData.NextBestAction.Data, dialogData.TextLabels.TextLabels);

            // process answer: support choice and textual answers
            var continueWithNba = stepContext.Result is FoundChoice continueChoice && continueChoice.Value.Contains(configuration.ContinueChoiceText, StringComparison.InvariantCultureIgnoreCase) ||
                                    stepContext.Result is string continueText && continueText.Contains("Ja", StringComparison.InvariantCultureIgnoreCase);
            var dismissNba = stepContext.Result is FoundChoice dismissChoice && dismissChoice.Value.Contains(dialogData.TextLabels.GetValue("DismissNbaChoice", _textLabelGroupName), StringComparison.InvariantCultureIgnoreCase) ||
                                stepContext.Result is string dismissText && dismissText.Contains("Nee", StringComparison.InvariantCultureIgnoreCase);
            var ignoreNba = stepContext.Result is FoundChoice ignoreChoice && ignoreChoice.Value.Contains(dialogData.TextLabels.GetValue("IgnoreNbaChoice", _textLabelGroupName), StringComparison.InvariantCultureIgnoreCase) ||
                                stepContext.Result is string ignoreText && ignoreText.Contains("Later", StringComparison.InvariantCultureIgnoreCase);

            // add SUCCESS feedback, set CurrentTransactionIsNba flag and start the follow up dialog (with customerId verification in between if necessary)
            if (continueWithNba)
            {
                _ = await stepContext.Context.SendActivityAsync(configuration.ContinueFollowUpText, cancellationToken: cancellationToken).ConfigureAwait(false);
                return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
            }
            // add DISMISS feedback and end the dialog
            else if (dismissNba)
            {
                await _nextBestActionService.AddFeedback(dialogData, actionType, FeedbackStatus.Dismissed).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(NextBestActionDialog), nameof(NextBestActionDecisionStep), TransactionStatus.NextBestActionDismissed, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            // simply ignore and end the dialog
            else if (ignoreNba)
            {
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(NextBestActionDialog), nameof(NextBestActionDecisionStep), TransactionStatus.NextBestActionIgnored, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            // retry from the first step (should be catched by the retryPrompy logic of MBF)
            else
            {
                stepContext.SetNextStepIndex(Dialogs, nameof(ProposeNextBestActionStep));
                return await ProposeNextBestActionStep(stepContext, cancellationToken).ConfigureAwait(false);
            }
        }

        private async Task<DialogTurnResult> TriggerFollowUpDialog(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            var actionType = dialogData.NextBestAction.Data.ToNextBestActionInfo().Type;
            var configuration = actionType.GetNextBestActionConfiguration(dialogData.NextBestAction.Data, dialogData.TextLabels.TextLabels);

            //send the feedback status success (acted on by customer(click on))
            await _nextBestActionService.AddFeedback(dialogData, actionType, FeedbackStatus.Success).ConfigureAwait(false);
            return await stepContext.BeginDialogAsync(configuration.FollowUpDialogName, stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private void SetupChoice(string actionType, DialogData dialogData)
        {
            nbaChoice = new List<Choice>
            {
                // continue
                new Choice
                {
                    Value = actionType.GetNextBestActionConfiguration(dialogData.NextBestAction.Data, dialogData.TextLabels.TextLabels).ContinueChoiceText
                },
                // ignore
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("IgnoreNbaChoice", _textLabelGroupName)
                },
                // dismiss
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("DismissNbaChoice", _textLabelGroupName)
                },
            };
        }
    }
}
