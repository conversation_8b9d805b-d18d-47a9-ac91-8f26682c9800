﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Exceptions;
using DC.Domain.Models.Products;
using DC.Domain.Models.Products.ServiceOrders;
using DC.Products.Client.Models;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ServiceOrderType = DC.Products.Client.Models.ServiceOrderType;

namespace DC.Bot.BusinessLogic.Dialogs.Products
{
    /// <summary>
    /// CreateServiceOrder Dialog
    /// </summary>
    public class CreateServiceOrderDialog : BaseDialog
    {
        private readonly IProductsService _productsService;

        private List<Choice> SetupYesNoContractChoices(DialogData dialogData)
        {
            return new List<Choice>
            {
                SetupButton(dialogData, "YesContractChoice", new List<string> { "Ja" }),
                SetupButton(dialogData, "NoContractChoice", new List<string> { "Nee", "Neen" })
            };
        }

        private List<Choice> SetupYesNoMalCodeChoices(DialogData dialogData)
        {
            return new List<Choice>
            {
                SetupButton(dialogData, "YesMalCodeChoice", new List<string> { "Ja" }),
                SetupButton(dialogData, "NoMalCodeChoice", new List<string> { "Nee", "Neen" })
            };
        }

        private List<Choice> SetupYesNoCreateServiceOrderChoices(DialogData dialogData)
        {
            return new List<Choice>
            {
                SetupButton(dialogData, "YesCreateServiceOrderChoice", new List<string> { "Ja" }),
                SetupButton(dialogData, "NoCreateServiceOrderChoice", new List<string> { "Nee", "Neen" })
            };
        }

        private Choice SetupButton(DialogData dialogData, string key, List<string> synonyms)
        {
            return new Choice
            {
                Value = dialogData.TextLabels.GetValue(key, _textLabelGroupName),
                Synonyms = synonyms
            };
        }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="loggerFactory"></param>
        /// <param name="loggingService"></param>
        /// <param name="sessionManager"></param>
        /// <param name="productsService"></param>
        /// <param name="storageService"></param>
        /// <param name="configuration"></param>
        public CreateServiceOrderDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            IProductsService productsService,
            IStorageService storageService,
            IConfiguration configuration) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
                new WaterfallStep[]
                {
                    InitTextLabels,
                    AskForServiceContractStep,
                    AskForServiceContractStep,
                    AskForServiceContractMultiContractsAnswerStep,
                    AskForServiceContractOneContractAnswerStep,
                    AskForMalfunctionCodeStep,
                    AskForMalfunctionCodeStepAnswer,
                    MalfunctionCodeStepAnswer,
                    AskForCreateServiceOrderStep,
                    AskForCreateServiceOrderStepAnswer,
                    GetServiceOrderPlanLink
                }));

            AddDialog(new TextPrompt(nameof(InitTextLabels)));
            AddDialog(new ChoicePrompt(nameof(AskForServiceContractStep)));
            AddDialog(new TextPrompt(nameof(AskForServiceContractMultiContractsAnswerStep)));
            AddDialog(new TextPrompt(nameof(AskForServiceContractOneContractAnswerStep)));
            AddDialog(new ChoicePrompt(nameof(AskForMalfunctionCodeStep)));
            AddDialog(new TextPrompt(nameof(AskForMalfunctionCodeStepAnswer)));
            AddDialog(new TextPrompt(nameof(MalfunctionCodeStepAnswer)));
            AddDialog(new ChoicePrompt(nameof(AskForCreateServiceOrderStep)));
            AddDialog(new TextPrompt(nameof(AskForCreateServiceOrderStepAnswer)));
            AddDialog(new TextPrompt(nameof(GetServiceOrderPlanLink)));

            InitialDialogId = nameof(WaterfallDialog);

            _productsService = productsService;
        }

        [SuppressMessage("Critical Code Smell", "S3776:Cognitive Complexity of methods should not be too high", Justification = "<Pending>")]
        private async Task<DialogTurnResult> AskForServiceContractStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.CreateServiceOrder).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            TransactionStatus status = TransactionStatus.Unhappy;

            try
            {
                if (dialogData.IsVerified())
                {
                    if (!dialogData.HasActiveAccount())
                    {
                        return await CustomerHasNoActiveAccount(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
                    }

                    var products = await GetServiceProducts(dialogData).ConfigureAwait(false);

                    if (products?.Any() == true)
                    {
                        // Store the product(s) in the dialogdata so that it is accessible in other dialogsteps.
                        dialogData.ServiceOrder.Products = products.ToList();

                        //If the customers has more than 1 active servicable product and no product has been chosen yet
                        if (dialogData.ServiceOrder.ChosenProduct == null && dialogData.ServiceOrder.Products.Count > 1)
                        {
                            return await HasMoreThanOneServiceProductContract(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
                        }
                        //when you have 1 active service agreement or a chosen agreement from the multicontracts step
                        else if (dialogData.ServiceOrder.ChosenProduct != null || dialogData.ServiceOrder.Products.Count == 1)
                        {
                            return await HasOneSerivceProductContractOrOneChosen(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
                        }
                        // has no active serviceable product
                        else
                        {
                            return await ContactForAppointmentStep(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
                        }

                    }
                    //when has no active serviceable product
                    else
                    {
                        return await ContactForAppointmentStep(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
                    }
                }
            }
            // in case of a Digital Core failure
            catch (Exception ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(AskForServiceContractStep));
                return await SomethingWentWrongTryAgain(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
            }

            // non happy flow: end the dialog
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CreateServiceOrderDialog), nameof(AskForServiceContractStep), status, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Asks for service contract multi contracts answer step.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private async Task<DialogTurnResult> AskForServiceContractMultiContractsAnswerStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            if (stepContext.Result is FoundChoice choice)
            {
                // Index is used because descriptions that are used for the value can be identical.
                dialogData.ServiceOrder.ChosenProduct = dialogData.ServiceOrder.Products[choice.Index];
                var serviceAgreements = await _productsService.GetServiceAgreementsForAgreementId(dialogData, dialogData.ServiceOrder.ChosenProduct.AgreementId.Value).ConfigureAwait(false);

                dialogData.ServiceOrder.ChosenServiceAgreement = serviceAgreements.FirstOrDefault();
                stepContext.SetNextStepIndex(Dialogs, nameof(AskForServiceContractStep));
                return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
            }

            return await ContactForAppointmentStep(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Asks for service contract one contract answer step.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private async Task<DialogTurnResult> AskForServiceContractOneContractAnswerStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            if (stepContext.IsChoiceSelected("ja"))
            {
                stepContext.SetNextStepIndex(Dialogs,
                    dialogData.ServiceOrder.ProductType ==
                    ServiceOrderProductType.CVKetel
                        ? nameof(AskForMalfunctionCodeStep)
                        : nameof(AskForCreateServiceOrderStep));
                return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
            }

            return await ContactForAppointmentStep(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Asks for malfunction code step.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private async Task<DialogTurnResult> AskForMalfunctionCodeStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            var prompt = dialogData.TextLabels.GetValue("QuestionKnownMalfunction", _textLabelGroupName);
            return await stepContext.PromptAsync(nameof(AskForMalfunctionCodeStep), new PromptOptions
            {
                Prompt = MessageFactory.Text(prompt),
                Choices = SetupYesNoMalCodeChoices(dialogData),
                RetryPrompt = MessageFactory.Text(prompt),
                Style = ListStyle.SuggestedAction
            }, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Asks for malfunction code step answer.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private async Task<DialogTurnResult> AskForMalfunctionCodeStepAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            if (stepContext.IsChoiceSelected("ja"))
            {
                var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
                var promptText = dialogData.TextLabels.GetValue("AskForMalfunctionCodePromptText", _textLabelGroupName);
                return await stepContext.PromptAsync(nameof(AskForMalfunctionCodeStepAnswer), new PromptOptions
                {
                    Prompt = MessageFactory.Text(promptText),
                    RetryPrompt = MessageFactory.Text(promptText)
                }, cancellationToken).ConfigureAwait(false);
            }

            stepContext.SetNextStepIndex(Dialogs, nameof(AskForCreateServiceOrderStep));
            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Malfunctions the code step answer.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private async Task<DialogTurnResult> MalfunctionCodeStepAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            dialogData.ServiceOrder.MalfunctionCode = (string)stepContext.Result;
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("MalfunctionCodeAnswer", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Asks for create service order step.
        /// Shows a Summary to the user of the repair
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private async Task<DialogTurnResult> AskForCreateServiceOrderStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("AskForCreateServiceOrder", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            var prompt = dialogData.TextLabels.GetValue("AskForCreateServiceOrderPromptText", _textLabelGroupName).Replace("{hardware}", dialogData.ServiceOrder.Hardware);
            return await stepContext.PromptAsync(nameof(AskForMalfunctionCodeStep), new PromptOptions
            {
                Prompt = MessageFactory.Text(prompt),
                Choices = SetupYesNoCreateServiceOrderChoices(dialogData),
                RetryPrompt = MessageFactory.Text(prompt),
                Style = ListStyle.SuggestedAction
            }, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Asks for create service order step answer.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private async Task<DialogTurnResult> AskForCreateServiceOrderStepAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            if (stepContext.IsChoiceSelected("ja"))
            {
                try
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CreateServiceOrderAnswer", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    var serviceOrder = await _productsService.CreateServiceOrder(dialogData).ConfigureAwait(false);
                    dialogData.ServiceOrder.Number = serviceOrder;
                    if (!string.IsNullOrEmpty(serviceOrder))
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ServiceOrderCreatedAnswer", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
                    }
                    else
                        return await SomethingWentWrongTryAgain(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
                }
                // in case of a Digital Core failure
                catch (DigitalCoreException ex)
                {
                    _loggingService.LogException(ex, stepContext.Context, nameof(AskForCreateServiceOrderStepAnswer));
                    return await SomethingWentWrongTryAgain(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
                }
            }

            return await ContactStep(stepContext, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Gets the service order plan link.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private async Task<DialogTurnResult> GetServiceOrderPlanLink(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            try
            {
                string planLink = null;
                var retry = 0;
                // try to get plan link until successful or 10 seconds has passed (3 retries).
                while (retry <= 3 && string.IsNullOrWhiteSpace(planLink))
                {
                    await Task.Delay(3000, cancellationToken).ConfigureAwait(false);
                    planLink = await _productsService.GetServiceOrderPlanLink(dialogData, dialogData.ServiceOrder.Number, PlanLinkType.Customer).ConfigureAwait(false);
                    retry++;

                    if (retry == 3 && string.IsNullOrWhiteSpace(planLink))
                    {
                        // In case no planLink could be retrieved, log this information in AI.
                        _loggingService.LogInformation(stepContext.Context, nameof(GetServiceOrderPlanLink),
                            $"No planlink could be retrieved for serviceorder {dialogData.ServiceOrder?.Number} for customer {dialogData.Verification.CustomerId.GetValueOrDefault(0)} - {dialogData.SelectedAccount?.AccountId}");

                        return await SomethingWentWrongTryAgain(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
                    }
                }

                // When planlink is available the following messages can be sent back and the status SuccessLink will make sure the corresponding success flow will be triggered.
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("PlanLinkAvailable", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("PlanLink", _textLabelGroupName).Replace("{planLink}", planLink), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CreateServiceOrderDialog), nameof(GetServiceOrderPlanLink), TransactionStatus.SuccessLink, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            // in case of a Digital Core failure
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(AskForCreateServiceOrderStepAnswer));
                return await SomethingWentWrongTryAgain(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Somethings the went wrong try again.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="dialogData"> dialog Data</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private async Task<DialogTurnResult> SomethingWentWrongTryAgain(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
        {
            var status = TransactionStatus.PermanentFailure;

            // Service orders require a phone number, so set this status if no phone numbers are set up
            if (!dialogData.Contact.PhoneNumbers.Any())
            {
                status = TransactionStatus.UnhappyPhoneNumber;
            }
            else
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken);
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("TryAgianSomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken);
            }

            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CreateServiceOrderDialog), nameof(SomethingWentWrongTryAgain), status, cancellationToken);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Contacts for appointment step.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="dialogData"> dialog Data</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private async Task<DialogTurnResult> ContactForAppointmentStep(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
        {
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SelfServiceNotAllowed", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactForAppointment", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CreateServiceOrderDialog), nameof(ContactForAppointmentStep), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Contacts the step.
        /// Status must be MBFChat
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private async Task<DialogTurnResult> ContactStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CreateServiceOrderDialog), nameof(ContactStep), TransactionStatus.MBFChat, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Wrongs the type of the product.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="dialogData"> dialog Data</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private async Task<DialogTurnResult> WrongProductType(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
        {
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("WrongProductType", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("WrongProductTypeContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CreateServiceOrderDialog), nameof(WrongProductType), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Checks if there are service orders on the given agreement id wich are still open of the type repairs in the past 7 days.
        /// </summary>
        /// <param name="dialogData">The dialog data.</param>
        /// <param name="serviceAgreementId">The service agreement identifier.</param>
        /// <returns></returns>
        private async Task<bool> CheckIfOpenServiceOrderExists(DialogData dialogData, string serviceAgreementId)
        {
            var orders = await _productsService.GetServiceOrdersForAgreement(dialogData, serviceAgreementId, ServiceOrderStatus.Open).ConfigureAwait(false);
            return orders?.Exists(o => o.Type == ServiceOrderKind.Repairs && o.OrderDate >= DateTime.Today.AddDays(-7)) == true;
        }

        /// <summary>
        /// Gets the hardware product specification.
        /// </summary>
        /// <param name="dialogData">The dialog data.</param>
        /// <returns></returns>
        private async Task<HardwareProductSpecification> GetHardwareProductSpecification(DialogData dialogData)
        {
            var result = await _productsService.GetHardwareProductSpecifications(dialogData).ConfigureAwait(false);
            return result?.Find(x => x.Group.Code == dialogData?.ServiceOrder?.ChosenServiceAgreement?.Items?.FirstOrDefault()?.Resource?.CategoryNumber);
        }

        private async Task<DialogTurnResult> CustomerHasNoActiveAccount(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
        {
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindData", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CreateServiceOrderDialog), nameof(AskForServiceContractStep), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private async Task<IList<ProductModel>> GetServiceProducts(DialogData dialogData)
        {
            var products = await _productsService.GetCustomerProductsByAccount(dialogData, dialogData.SelectedAccount.AccountId).ConfigureAwait(false);
            // Only get the product(s) that are cattegorised as rental or service and are not ToonService, StukjeZon, OxxioPro or Telecom.
            products = products?.Where(x => x.IsActive && (x.Type.Category == ProductCategory.Rent ||
                                                                      x.Type.Category == ProductCategory.Service) &&
                                                                      x.Type.Name != ProductType.ToonService &&
                                                                      x.Type.Name != ProductType.StukjeZon &&
                                                                      x.Type.Name != ProductType.OxxioPro &&
                                                                      x.Type.Name != ProductType.Telecom)?.ToList();

            return products;
        }

        /// <summary>
        /// Have the more than one service product contract.
        /// </summary>
        private async Task<DialogTurnResult> HasMoreThanOneServiceProductContract(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
        {
            stepContext.SetNextStepIndex(Dialogs, nameof(AskForServiceContractMultiContractsAnswerStep));

            List<Choice> choices = new List<Choice>();

            for (var i = 1; i <= dialogData.ServiceOrder.Products.Count; i++)
            {
                var product = dialogData.ServiceOrder.Products[i - 1];
                choices.Add(new Choice
                {
                    Value = $"{i}-{product.Description}"
                });
            }

            var promptText = dialogData.TextLabels.GetValue("AskForWhichServiceContractPromptText", _textLabelGroupName);
            return await stepContext.PromptAsync(nameof(AskForServiceContractStep), new PromptOptions
            {
                Prompt = MessageFactory.Text(promptText),
                Choices = choices,
                RetryPrompt = MessageFactory.Text(promptText),
                Style = ListStyle.SuggestedAction
            }, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Have the one serivce product contract or one chosen.
        /// </summary>
        private async Task<DialogTurnResult> HasOneSerivceProductContractOrOneChosen(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
        {
            dialogData.ServiceOrder.ChosenProduct ??= dialogData.ServiceOrder.Products.FirstOrDefault();

            if (dialogData.ServiceOrder.ChosenProduct.AgreementId == null || dialogData.ServiceOrder.ChosenProduct.AgreementId == 0)
                return await ContactForAppointmentStep(stepContext, dialogData, cancellationToken).ConfigureAwait(false);

            var serviceAgreements = await _productsService.GetServiceAgreementsForAgreementId(dialogData, dialogData.ServiceOrder.ChosenProduct.AgreementId.Value).ConfigureAwait(false);
            dialogData.ServiceOrder.ChosenServiceAgreement = serviceAgreements?.FirstOrDefault();

            var serviceAgreementItem = dialogData.ServiceOrder.ChosenServiceAgreement?.Items?.FirstOrDefault();

            if (serviceAgreements?.Count == 0 || serviceAgreements?.Exists(c => c.Items.Count == 1) == false ||
                serviceAgreements?.Exists(c => c.Items?.Count(i => !i.EndDate.HasValue || i.EndDate > DateTime.Today) > 1) == true
                || serviceAgreementItem == null)
                return await ContactForAppointmentStep(stepContext, dialogData, cancellationToken).ConfigureAwait(false);

            var openOrders = await CheckIfOpenServiceOrderExists(dialogData, dialogData.ServiceOrder.ChosenServiceAgreement?.Number).ConfigureAwait(false);
            if (openOrders)
                return await ContactForAppointmentStep(stepContext, dialogData, cancellationToken).ConfigureAwait(false);

            var resource = serviceAgreementItem?.Resource;
            dialogData.ServiceOrder.Type = ServiceOrderType.SolveMalfunction;
            dialogData.ServiceOrder.ResourceNumber = resource?.Number;
            dialogData.ServiceOrder.Hardware = resource?.Name;

            if (string.IsNullOrEmpty(resource?.Number))
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoResourceFounded", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CreateServiceOrderDialog), nameof(AskForServiceContractStep), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            return await DetermineHardwareSpecifictionAndNextStep(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Determines the hardware specifiction and next step.
        /// </summary>
        private async Task<DialogTurnResult> DetermineHardwareSpecifictionAndNextStep(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
        {
            try
            {
                var hardwareSpecification = await GetHardwareProductSpecification(dialogData).ConfigureAwait(false);
                var productType = hardwareSpecification?.Group?.Description.ToServiceOrderProductType();
                if (!dialogData.ServiceOrder.ProductType.HasValue || dialogData.ServiceOrder.ProductType is ServiceOrderProductType.Unknown)
                    dialogData.ServiceOrder.ProductType = productType;

                if (productType is ServiceOrderProductType.Unknown && dialogData.ServiceOrder.HardwareType.HasValue)
                    dialogData.ServiceOrder.ProductType = dialogData.ServiceOrder.HardwareType;

                if (productType != dialogData.ServiceOrder.ProductType)
                    return await WrongProductType(stepContext, dialogData, cancellationToken).ConfigureAwait(false);

                string prompt;
                prompt = hardwareSpecification?.Group?.Description switch
                {
                    GroupDescription.EBoiler or GroupDescription.Warmtepompboiler => dialogData.TextLabels.GetValue("MailfunctionOnBoiler", _textLabelGroupName),
                    GroupDescription.Geiser or GroupDescription.Ventilatorgeiser => dialogData.TextLabels.GetValue("MailfunctionOnGeyser", _textLabelGroupName),
                    _ => dialogData.TextLabels.GetValue("MailfunctionOnCV", _textLabelGroupName)
                };

                stepContext.SetNextStepIndex(Dialogs, nameof(AskForServiceContractOneContractAnswerStep));
                return await stepContext.PromptAsync(nameof(AskForServiceContractStep), new PromptOptions
                {
                    Prompt = MessageFactory.Text(prompt),
                    Choices = SetupYesNoContractChoices(dialogData),
                    RetryPrompt = MessageFactory.Text(prompt),
                    Style = ListStyle.SuggestedAction
                }, cancellationToken).ConfigureAwait(false);
            }
            // in case of a Digital Core failure
            catch (Exception ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(AskForServiceContractStep));
                return await SomethingWentWrongTryAgain(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
            }
        }
    }
}
