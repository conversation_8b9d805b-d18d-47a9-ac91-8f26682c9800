﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Utilities.Formatters;
using Microsoft.Bot.Builder.Dialogs;
using System;
using System.Globalization;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Products.Validators
{
    public class ProductsDialogValidator : IProductsDialogValidator
    {
        private readonly ISessionManager _sessionManager;

        public ProductsDialogValidator(ISessionManager sessionManager)
        {
            _sessionManager = sessionManager;
        }

        public async Task<bool> FineCancelDateValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(promptContext.Context).ConfigureAwait(false);
            var cancelDate = promptContext.Context.Activity.Text?.RemoveWhitespaces();

            if (DateTime.TryParseExact(cancelDate, "dd-MM-yyyy", new CultureInfo("NL-nl"), DateTimeStyles.None, out var date)
                && date >= DateTime.Today
                && date < DateTime.Today.AddDays(60))
            {
                return true;
            }

            return await promptContext.ReachedMaxAmountAttempts(dialogData, cancellationToken).ConfigureAwait(false);
        }
    }
}
