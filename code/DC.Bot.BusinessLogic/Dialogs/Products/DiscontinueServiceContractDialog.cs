﻿using DC.Bot.BusinessLogic.Dialogs.Customers;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Exceptions;
using DC.Domain.Models.Products;
using DC.Products.Client.Models;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Products
{
    public class DiscontinueServiceContractDialog : BaseDialog
    {
        private List<Choice> SetupYesNoButtons(DialogData dialogData)
        {
            return new List<Choice> {
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("YesChoice", _textLabelGroupName),
                    Synonyms = new List<string> { "Ja" }
                },
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("NoChoice", _textLabelGroupName),
                    Synonyms = new List<string> { "Nee", "Neen" }
                }

            };
        }

        private readonly IProductsService _productsService;

        public DiscontinueServiceContractDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            IProductsService productsService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
                new WaterfallStep[]
                {
                    InitTextLabels,
                    SayIfFoundServiceContractStep,
                    WantsToCancelStep,
                    DiscontinueReasonStep,
                    DiscontinueServiceContractDialogStep
                }));

            AddDialog(new TextPrompt(nameof(InitTextLabels)));
            AddDialog(new TextPrompt(nameof(SayIfFoundServiceContractStep)));
            AddDialog(new ChoicePrompt(nameof(WantsToCancelStep)));
            AddDialog(new TextPrompt(nameof(DiscontinueReasonStep)));
            AddDialog(new ChoicePrompt(nameof(DiscontinueServiceContractDialogStep)));

            InitialDialogId = nameof(WaterfallDialog);

            _productsService = productsService;
        }

        private async Task<DialogTurnResult> SayIfFoundServiceContractStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.DiscontinueServiceContract).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            TransactionStatus status;
            var discontinuePrompt = dialogData.TextLabels.GetValue("DiscontinuePromptText", _textLabelGroupName);

            try
            {
                if (!dialogData.HasCustomerId() || !dialogData.HasLabel())
                {
                    stepContext.SetNextStepIndex(Dialogs, nameof(SayIfFoundServiceContractStep));
                    return await stepContext.BeginDialogAsync(nameof(CustomerVerificationDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
                }

                var intake = await _productsService.GetDiscontinueIntake(dialogData, DiscontinueProductType.ServiceContract).ConfigureAwait(false);
                dialogData.MapDiscontinueIntakeModel(intake);

                if (dialogData.DiscontinueContractData.Reasons.Any(x => x.Code.Equals(ProductDiscontinueReason.AAK.ToString(), StringComparison.InvariantCultureIgnoreCase)))
                    discontinuePrompt = dialogData.TextLabels.GetValue("ProductDiscontinueReasonAAK", _textLabelGroupName);

                if (dialogData.DiscontinueContractData.Allowed)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("FoundContract", _textLabelGroupName)
                         .Replace("{productname}", intake.ProductName), cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else
                {
                    var chooseContactIfIncorrect = dialogData.TextLabels.GetValue("ChooseContactIfIncorrect", _textLabelGroupName);
                    switch (intake.DiscontinueDisallowReason)
                    {
                        case DiscontinueDisallowReason.CancelledAlready:
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CancelledAlready", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            _ = await stepContext.Context.SendActivityAsync(chooseContactIfIncorrect, cancellationToken: cancellationToken).ConfigureAwait(false);
                            break;

                        case DiscontinueDisallowReason.NoActiveProduct:
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoActiveProduct", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            _ = await stepContext.Context.SendActivityAsync(chooseContactIfIncorrect, cancellationToken: cancellationToken).ConfigureAwait(false);
                            break;

                        case DiscontinueDisallowReason.MinimumContractPeriod:
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("MinimumContractPeriodContractName", _textLabelGroupName)
                                .Replace("{productname}", intake.ProductName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            _ = await stepContext.Context.SendActivityAsync(GetMinimumContractPeriodSentence(dialogData, intake), cancellationToken: cancellationToken).ConfigureAwait(false);
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("MinimumContractPeriodWantToDiscontinue", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            break;

                        case DiscontinueDisallowReason.MoreThanOneMatchedProduct:
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("MoreThanOneMatchedProduct", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("MoreThanOneMatchedProductContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

                            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(DiscontinueServiceContractDialog), nameof(SayIfFoundServiceContractStep), TransactionStatus.MBFChat, cancellationToken).ConfigureAwait(false);
                            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                    }

                    status = TransactionStatus.Unhappy;
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(DiscontinueServiceContractDialog), nameof(SayIfFoundServiceContractStep), status, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(SayIfFoundServiceContractStep));

                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            }


            return await stepContext.PromptAsync(nameof(WantsToCancelStep), new PromptOptions
            {
                Prompt = MessageFactory.Text(discontinuePrompt),
                RetryPrompt = MessageFactory.Text(discontinuePrompt),
                Choices = SetupYesNoButtons(dialogData),
            }, cancellationToken).ConfigureAwait(false);
        }

        private string GetMinimumContractPeriodSentence(DialogData dialogData, DiscontinueIntake intake)
        {
            var account = intake.Accounts?.FirstOrDefault(x => x.AccountId == dialogData.SelectedAccount.AccountId);

            if (account?.StartDate == null)
            {
                _logger.LogError($"StartDate in {nameof(GetMinimumContractPeriodSentence)} was null. This not expected");
                return dialogData.TextLabels.GetValue("MinimumContractPeriod", _textLabelGroupName);
            }

            return dialogData.TextLabels.GetValue("MinimumContractPeriodHasStartDate", _textLabelGroupName)
                .Replace("{startDate}", $"{account.StartDate.Value.AddYears(1):dd-MM-yyyy}");
        }

        private async Task<DialogTurnResult> WantsToCancelStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            // process answer: support choice and textual answers
            var wantsToCancel = stepContext.Result is FoundChoice choice && choice.Value.Contains("Ja", StringComparison.InvariantCultureIgnoreCase) ||
                                          stepContext.Result is string textAnswer && textAnswer.Contains("Ja", StringComparison.InvariantCultureIgnoreCase);

            if (wantsToCancel)
            {
                return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
            }
            else
            {
                var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("WantsToCancel", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(DiscontinueServiceContractDialog), nameof(WantsToCancelStep), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }
        }

        private async Task<DialogTurnResult> DiscontinueReasonStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.DiscontinueServiceContract).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            var cancelReasons = dialogData.DiscontinueContractData.Reasons.Select(x => new Choice { Value = x.Text, Synonyms = new List<string> { x.Text } }).ToList();

            var prompt = dialogData.TextLabels.GetValue("DiscontinueReasonPromptText", _textLabelGroupName);
            return await stepContext.PromptAsync(nameof(DiscontinueServiceContractDialogStep), new PromptOptions
            {
                Prompt = MessageFactory.Text(prompt),
                RetryPrompt = MessageFactory.Text(prompt),
                Choices = cancelReasons,
                Style = ListStyle.SuggestedAction
            }, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> DiscontinueServiceContractDialogStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.DiscontinueServiceContract).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            try
            {
                var reason = GetDiscontinueReason(stepContext, dialogData);
                if (!reason.HasValue)
                {
                    stepContext.SetNextStepIndex(Dialogs, nameof(DiscontinueReasonStep));
                    return await DiscontinueReasonStep(stepContext, cancellationToken).ConfigureAwait(false);
                }

                if (reason.Value == ProductDiscontinueReason.VER)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ProductDiscontinueReasonMoveOut", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(DiscontinueServiceContractDialog), nameof(DiscontinueServiceContractDialogStep), TransactionStatus.MBFChat, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }

                if (dialogData.DiscontinueContractData.Allowed)
                {
                    if (await _productsService.CancelProductContract(dialogData, ProductType.KetelComfort, reason.Value).ConfigureAwait(false))
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("DiscontinueContract", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("DiscontinueContractEnds", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(DiscontinueServiceContractDialog), nameof(DiscontinueServiceContractDialogStep), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                    }
                    else
                    {
                        await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("DiscontinueContractNotAllowed", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(DiscontinueServiceContractDialog), nameof(DiscontinueServiceContractDialogStep), TransactionStatus.PermanentFailure, cancellationToken).ConfigureAwait(false);
                    }

                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else
                {
                    throw new FunctionalException(Guid.Parse("19dbb3bd-9121-4fd2-99bb-452d36fe5858"),
                        $"Intake returned allowed == false on {nameof(DiscontinueServiceContractDialogStep)}, reason: {dialogData.DiscontinueContractData.DiscontinueDisallowReason}");
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(DiscontinueServiceContractDialogStep));

                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrongDiscontinueContract", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(DiscontinueServiceContractDialog), nameof(DiscontinueServiceContractDialogStep), TransactionStatus.PermanentFailure, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }
        }

        private static ProductDiscontinueReason? GetDiscontinueReason(WaterfallStepContext stepContext, DialogData dialogData)
        {
            var choice = stepContext.Result as FoundChoice;
            var reason = dialogData.DiscontinueContractData.Reasons?.FirstOrDefault(x => x.Text.Equals(choice.Value, StringComparison.OrdinalIgnoreCase));
            ProductDiscontinueReason colorValue;
            if (string.IsNullOrEmpty(reason?.Code) || !Enum.TryParse(reason.Code, true, out colorValue))
                return null;

            return colorValue;
        }
    }
}
