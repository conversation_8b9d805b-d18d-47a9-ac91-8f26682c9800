﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Exceptions;
using DC.Domain.Models.Products;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Products
{
    public class ProductEndDatesDialog : BaseDialog
    {
        private readonly ICustomersService _customersService;
        private readonly IProductsService _productsService;

        public ProductEndDatesDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            ICustomersService customersService,
            IProductsService productsService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
                new WaterfallStep[]
                {
                    InitTextLabels,
                    ProductEndDatesDialogStep
                }));

            InitialDialogId = nameof(WaterfallDialog);

            _customersService = customersService;
            _productsService = productsService;
        }

        /// <summary>
        /// Returns all relevant productrate data
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>                
        /// <returns></returns>
        private async Task<DialogTurnResult> ProductEndDatesDialogStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.GetProductEndDates).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            TransactionStatus status;
            try
            {
                if (dialogData.IsVerified())
                {
                    var activeAccountId = dialogData.SelectedAccount?.AccountId;
                    if (!dialogData.HasActiveAccount())
                    {
                        var customerModel = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);
                        var activeAccount = dialogData.GetActiveAccountFromCustomerModel(customerModel);
                        activeAccountId = activeAccount?.Id;
                    }

                    if (activeAccountId > 0)
                    {
                        var products = await _productsService.GetCustomerProductsByAccount(dialogData, activeAccountId.Value).ConfigureAwait(false);
                        if (products?.Any() == true)
                        {
                            await ShowProductEndDates(products, stepContext).ConfigureAwait(false);
                            status = TransactionStatus.Success;
                        }
                        else
                        {
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoProducts", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            status = TransactionStatus.TemporaryFailure;
                        }
                    }
                    else
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindData", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                        status = TransactionStatus.TemporaryFailure;
                    }
                }
                else
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindCustomerId", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                    status = TransactionStatus.TemporaryFailure;
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(ProductEndDatesDialogStep));

                status = TransactionStatus.TemporaryFailure;

                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SeeProductsApp", _textLabelGroupName).Replace("{MijnEnecoProducten}", $"{DialogContent.MijnEnecoProducten}"), cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ProductEndDatesDialog), nameof(ProductEndDatesDialogStep), status, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Critical Code Smell", "S3776:Cognitive Complexity of methods should not be too high", Justification = "Not too complex, the text string is well readable.")]
        private async Task ShowProductEndDates(IList<ProductModel> products, WaterfallStepContext stepContext)
        {
            products = products.Where(p => p.Type.Name != ProductType.StukjeZon).ToList();

            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            if (products.Any())
            {
                foreach (ProductModel product in products)
                {
                    var stringBuilder = new StringBuilder();
                    stringBuilder.AppendBoldLineWithMarkdown(product.Type?.Name == ProductType.Happypower ? "HappyPower" : product.Description);

                    // Indefinite contract with variable costs.
                    if (product.EndDateContract == null)
                    {
                        stringBuilder.AppendLineWithMarkdown(dialogData?.TextLabels.GetValue("ProductIndefinite", _textLabelGroupName));
                        if (product.EndDatePrice.HasValue)
                        {
                            stringBuilder.AppendLineWithMarkdown(dialogData?.TextLabels.GetValue("ProductEndDatePrice", _textLabelGroupName).Replace("{endDatePrice}", $"{product.EndDatePrice:dd-MM-yyyy}"));
                        }
                    }
                    else
                    {
                        stringBuilder.AppendLineWithMarkdown(product.GetEndDateString());
                    }
                    _ = await stepContext.Context.SendActivityAsync(stringBuilder.ToString()).ConfigureAwait(false);
                }
                
                var stukjeZonProducts = products.Where(p => p.Type.Name == ProductType.StukjeZon).ToArray();
                if (stukjeZonProducts.Length > 0)
                {
                    // We only take 1 stukje zon product because we only need the description + amount. 
                    var stukjeZon = stukjeZonProducts[0];
                    var stringBuilder = new StringBuilder();
                    stringBuilder.AppendBoldLineWithMarkdown(stukjeZon?.Description);
                    stringBuilder.AppendLineWithMarkdown(dialogData?.TextLabels
                        .GetValue("ProductStukjeZon", _textLabelGroupName)
                        .Replace("{numberOfStukjeZon}", $"{stukjeZonProducts.Length}"));
                    _ = await stepContext.Context
                        .SendActivityAsync(stringBuilder.ToString(), cancellationToken: CancellationToken.None)
                        .ConfigureAwait(false);
                }
            }
        }
    }
}