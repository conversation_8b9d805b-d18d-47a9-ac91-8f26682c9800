﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Exceptions;
using DC.Domain.Models.Products.ProductRates;
using DC.Domain.Models.Usages;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Products
{
    public class ProductRatesDialog : BaseDialog
    {
        private readonly ICustomersService _customersService;
        private readonly IProductsService _productsService;

        public ProductRatesDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            ICustomersService customersService,
            IProductsService productsService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
                new WaterfallStep[]
                {
                    InitTextLabels,
                    ProductRatesStep
                }));

            InitialDialogId = nameof(WaterfallDialog);

            _customersService = customersService;
            _productsService = productsService;
        }

        /// <summary>
        /// Returns all relevant productrate data
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>                
        /// <returns></returns>
        private async Task<DialogTurnResult> ProductRatesStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.GetProductRates).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            TransactionStatus status;
            try
            {
                if (dialogData.IsVerified())
                {
                    var activeAccountId = dialogData.SelectedAccount?.AccountId;
                    AccountData activeAccount = dialogData.SelectedAccount;
                    if (!dialogData.HasActiveAccount())
                    {
                        var customerModel = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);
                        dialogData.SetAllActiveAccountsFromCustomerModel(customerModel);
                        activeAccountId = dialogData.SelectedAccount?.AccountId;
                    }

                    if (activeAccountId > 0)
                    {
                        if (activeAccount?.HasDynamicPricing == true)
                        {
                            status = TransactionStatus.UnhappyDynamicPricing;
                        }
                        else
                        {
                            status = await ShowProductRatesNoDynamicCustomer(dialogData, activeAccountId.Value, stepContext, cancellationToken);
                        }
                    }
                    else
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindData", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                        status = TransactionStatus.TemporaryFailure;
                    }
                }
                else
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindData", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                    status = TransactionStatus.TemporaryFailure;
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(ProductRatesStep));

                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("TryAgianSomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                status = TransactionStatus.TemporaryFailure;
            }
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ProductRatesDialog), nameof(ProductRatesStep), status, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private async Task<TransactionStatus> ShowProductRatesNoDynamicCustomer(DialogData dialogData, int activeAccountId, WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            TransactionStatus status;
            var productRates = await _productsService.GetProductRates(dialogData, activeAccountId).ConfigureAwait(false);
            productRates = productRates?.Where(x => x.ProductType != Domain.Models.Products.ProductType.BronWarmte).ToList();
            if (productRates?.Any() == true)
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NextTariffsFound", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await ShowAllProductTypes(productRates, stepContext).ConfigureAwait(false);
                status = TransactionStatus.Success;
            }
            else
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoProductsFound", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                status = TransactionStatus.TemporaryFailure;
            }

            return status;
        }

        private async Task ShowAllProductTypes(IList<ProductRate> productRates, WaterfallStepContext stepContext)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            var measuredProductRates = productRates.GetMeasuredProductRates();
            if (measuredProductRates?.Any() == true)
            {
                foreach (ProductRate productRate in measuredProductRates)
                {
                    var stringBuilder = new StringBuilder();
                    stringBuilder.AppendBoldLineWithMarkdown(productRate.Name);
                    // Show productRateDetail data with type Tarrif
                    stringBuilder.Append(productRate.GetProductRateDetailTariffs());
                    // Show productRateDetail data with type Redelivery Tarrif
                    stringBuilder.Append(productRate.GetProductRateDetailRedeliveryTariffs());
                    // Show productRateDetail data with type Redelivery Costs Tarrif
                    stringBuilder.Append(productRate.GetProductRateDetailRedeliveryCostTariffs());
                    // Show productRateDetail data by monthly costs
                    stringBuilder.Append(productRate.GetProductRateDetailCostsByPeriod(ByPeriod.M));
                    // Show productRateDetail data by yearly costs calculate to monthly costs
                    stringBuilder.Append(productRate.GetProductRateDetailYearlyCostsCalculatedToMonthly());

                    // Show IsDoubleTariff in the output in case of producttype electricity
                    stringBuilder.AppendLineWithMarkdown(productRate.HasDoubleTariffIndicator(dialogData.TextLabels.GetValue("DoubleTariff", _textLabelGroupName)));
                    _ = await stepContext.Context.SendActivityAsync(stringBuilder.ToString()).ConfigureAwait(false);
                }
            }

            var nonMeasuredProductRates = productRates.GetNonMeasuredProductRates();
            if (nonMeasuredProductRates?.Any() == true)
            {
                foreach (ProductRate productRate in nonMeasuredProductRates)
                {
                    // Show productdetails data
                    var stringBuilder = new StringBuilder();
                    stringBuilder.AppendBoldLineWithMarkdown(productRate.Name.Replace("\n", ""));
                    stringBuilder.Append(productRate.GetNonVariableProductRateDetails());

                    _ = await stepContext.Context.SendActivityAsync(stringBuilder.ToString()).ConfigureAwait(false);
                }
            }

            string stukjeZonSummary = productRates.GetStukjeZonSummary(dialogData.TextLabels.GetValue("NumberStukjeZon", _textLabelGroupName));
            if (!string.IsNullOrEmpty(stukjeZonSummary))
            {
                // Show stukje summary
                _ = await stepContext.Context.SendActivityAsync(stukjeZonSummary).ConfigureAwait(false);
            }
        }
    }
}
