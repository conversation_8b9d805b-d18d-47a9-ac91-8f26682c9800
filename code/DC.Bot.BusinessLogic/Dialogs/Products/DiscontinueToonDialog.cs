﻿using DC.Bot.BusinessLogic.Dialogs.Customers;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Exceptions;
using DC.Domain.Models.Products;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Products
{
    public class DiscontinueToonDialog : BaseDialog
    {
        private List<Choice> SetupYesNoButtons(DialogData dialogData)
        {
            return new List<Choice> {
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("YesChoice", _textLabelGroupName),
                    Synonyms = new List<string> { "Ja" }
                },
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("NoChoice", _textLabelGroupName),
                    Synonyms = new List<string> { "Nee", "Neen" }
                }

            };
        }

        private readonly IProductsService _productsService;

        public DiscontinueToonDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            IProductsService productsService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
                new WaterfallStep[]
                {
                    InitTextLabels,
                    SayIfFoundToonContractStep,
                    WantsToCancelStep,
                    DiscontinueReasonStep,
                    DiscontinueToonDialogStep
                }));

            AddDialog(new TextPrompt(nameof(InitTextLabels)));
            AddDialog(new TextPrompt(nameof(SayIfFoundToonContractStep)));
            AddDialog(new ChoicePrompt(nameof(WantsToCancelStep)));
            AddDialog(new TextPrompt(nameof(DiscontinueReasonStep)));
            AddDialog(new ChoicePrompt(nameof(DiscontinueToonDialogStep)));

            InitialDialogId = nameof(WaterfallDialog);

            _productsService = productsService;
        }

        private async Task<DialogTurnResult> SayIfFoundToonContractStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.DiscontinueToon).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            try
            {
                if (!dialogData.HasCustomerId() || !dialogData.HasLabel())
                {
                    stepContext.SetNextStepIndex(Dialogs, nameof(SayIfFoundToonContractStep));
                    return await stepContext.BeginDialogAsync(nameof(CustomerVerificationDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
                }

                var intake = await _productsService.GetDiscontinueIntake(dialogData, DiscontinueProductType.Toon).ConfigureAwait(false);
                dialogData.MapDiscontinueIntakeModel(intake);

                if (intake.Allowed)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("FoundContract", _textLabelGroupName)
                        .Replace("{productname}", intake.ProductName), cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else
                {
                    _ = intake.DiscontinueDisallowReason switch
                    {
                        DiscontinueDisallowReason.CancelledAlready => await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CancelledAlready", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false),
                        DiscontinueDisallowReason.NoActiveProduct => await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoActiveProduct", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false),
                        _ => throw new FunctionalException(new Guid("6b3ae9a4-a652-4bb4-bb0a-d36801980047"), $"{nameof(_productsService.GetDiscontinueIntake)}" +
                                    $"returned intake !Allowed with reason: {intake.DiscontinueDisallowReason}, this should not be possible for Toon")
                    };
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(DiscontinueToonDialog), nameof(SayIfFoundToonContractStep), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(SayIfFoundToonContractStep));

                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("TryAgianSomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(DiscontinueToonDialog), nameof(SayIfFoundToonContractStep), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            var prompt = dialogData.TextLabels.GetValue("DiscontinuePromptText", _textLabelGroupName);
            return await stepContext.PromptAsync(nameof(WantsToCancelStep), new PromptOptions
            {
                Prompt = MessageFactory.Text(prompt),
                RetryPrompt = MessageFactory.Text(prompt),
                Choices = SetupYesNoButtons(dialogData)
            }, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> WantsToCancelStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            // process answer: support choice and textual answers
            var wantsToCancel = stepContext.Result is FoundChoice choice && choice.Value.Contains("Ja", StringComparison.InvariantCultureIgnoreCase) ||
                                          stepContext.Result is string textAnswer && textAnswer.Contains("Ja", StringComparison.InvariantCultureIgnoreCase);

            if (wantsToCancel)
            {
                // If customer still needs to verify the cusomterId, then go to the (next) verification step.
                return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
            }
            else
            {
                var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("DoNothing", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(DiscontinueToonDialog), nameof(WantsToCancelStep), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }
        }

        private async Task<DialogTurnResult> DiscontinueReasonStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.DiscontinueToon).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            var cancelReasons = dialogData.DiscontinueContractData.Reasons.Select(x => new Choice { Value = x.Text, Synonyms = new List<string> { x.Text } }).ToList();

            var prompt = dialogData.TextLabels.GetValue("DiscontinueReasonStep", _textLabelGroupName);
            return await stepContext.PromptAsync(nameof(DiscontinueToonDialogStep), new PromptOptions
            {
                Prompt = MessageFactory.Text(prompt),
                RetryPrompt = MessageFactory.Text(prompt),
                Choices = cancelReasons,
                Style = ListStyle.SuggestedAction
            }, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> DiscontinueToonDialogStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.DiscontinueToon).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            try
            {
                var reason = GetDiscontinueReason(stepContext, dialogData);
                if (!reason.HasValue)
                {
                    stepContext.SetNextStepIndex(Dialogs, nameof(DiscontinueReasonStep));
                    return await DiscontinueReasonStep(stepContext, cancellationToken).ConfigureAwait(false);
                }

                if (reason.Value == ProductDiscontinueReason.VER)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("DiscontinueReasonMoveOut", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(DiscontinueToonDialog), nameof(DiscontinueReasonStep), TransactionStatus.MBFChat, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }

                if (dialogData.DiscontinueContractData.Allowed)
                {
                    if (await _productsService.CancelProductContract(dialogData, ProductType.ToonService, reason.Value).ConfigureAwait(false))
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ProductCancelled", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ProductCancelledContractEnds", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(DiscontinueToonDialog), nameof(DiscontinueReasonStep), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                    }
                    else
                    {
                        await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrongProductCancelling", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(DiscontinueToonDialog), nameof(DiscontinueReasonStep), TransactionStatus.PermanentFailure, cancellationToken).ConfigureAwait(false);
                    }

                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else
                {
                    throw new FunctionalException(Guid.Parse("f307bd3c-b964-4819-8dd1-04e69bb36f8d"),
                        $"Intake returned allowed == false on {nameof(DiscontinueToonDialogStep)}, reason: {dialogData.DiscontinueContractData.DiscontinueDisallowReason}");
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(DiscontinueToonDialogStep));

                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(DiscontinueToonDialog), nameof(DiscontinueReasonStep), TransactionStatus.PermanentFailure, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }
        }

        private static ProductDiscontinueReason? GetDiscontinueReason(WaterfallStepContext stepContext, DialogData dialogData)
        {
            var choice = stepContext.Result as FoundChoice;
            var reason = dialogData.DiscontinueContractData.Reasons?.FirstOrDefault(x => x.Text.Equals(choice.Value, StringComparison.OrdinalIgnoreCase));
            ProductDiscontinueReason colorValue;
            if (string.IsNullOrEmpty(reason?.Code) || !Enum.TryParse(reason.Code, true, out colorValue))
                return null;

            return colorValue;
        }
    }
}
