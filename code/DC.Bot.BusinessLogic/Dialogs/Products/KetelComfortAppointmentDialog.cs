﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Exceptions;
using DC.Products.Client.Models;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Extensions.Logging;
using System.Globalization;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Products
{
    public class KetelComfortAppointmentDialog : BaseDialog
    {
        private readonly IProductsService _productsService;

        public KetelComfortAppointmentDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            IProductsService productsService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog(nameof(WaterfallDialog), new WaterfallStep[] {
                InitTextLabels,
                RetrieveAppointmentForCustomer
            }));

            AddDialog(new TextPrompt(nameof(InitTextLabels)));
            AddDialog(new TextPrompt(nameof(RetrieveAppointmentForCustomer)));

            InitialDialogId = nameof(WaterfallDialog);
            _productsService = productsService;
        }

        private async Task<DialogTurnResult> RetrieveAppointmentForCustomer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.KetelComfortAppointment).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            TransactionStatus status = TransactionStatus.Success;

            try
            {
                if (dialogData.IsVerified())
                {
                    var details = await _productsService.GetKetelComfortProductDetails(dialogData).ConfigureAwait(false);
                    await ReturnAppointmentDetailsAsync(stepContext, details, dialogData, cancellationToken).ConfigureAwait(false);
                }
                else
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindCustomerId", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindCustomerIdContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    status = TransactionStatus.Unhappy;
                }
            }
            // in case of a Digital Core failure
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(RetrieveAppointmentForCustomer));

                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                status = TransactionStatus.PermanentFailure;
            }
            // non happy flow: end the dialog
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(KetelComfortAppointmentDialog), nameof(RetrieveAppointmentForCustomer), status, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> ReturnAppointmentDetailsAsync(WaterfallStepContext stepContext, KetelComfortServiceModel details, DialogData dialogData, CancellationToken cancellationToken)
        {
            if (details == null)
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoServiceContract", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(KetelComfortAppointmentDialog), nameof(ReturnAppointmentDetailsAsync), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            CultureInfo ci = new("nl-NL");

            if (details.PreviousAppointment.HasValue)
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("LatestAppointment", _textLabelGroupName)
                    .Replace("{date}", details.PreviousAppointment.Value.ToString("dd MMMM yyyy", ci).ToLower()), cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            if (details.UpcomingAppointment?.Type == PlanActionType.Expired)
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("UpcomingAppointmentExpired", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(KetelComfortAppointmentDialog), nameof(ReturnAppointmentDetailsAsync), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
            }
            else if (details.UpcomingAppointment?.Type == PlanActionType.Invite)
            {
                //#1 Customer already got an invite
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("UpcomingAppointmentInvite", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(KetelComfortAppointmentDialog), nameof(ReturnAppointmentDetailsAsync), TransactionStatus.Appointment, cancellationToken).ConfigureAwait(false);
            }
            else if (details.UpcomingAppointment != null)
            {
                if (details.UpcomingAppointment.AppointmentDate.HasValue)
                {
                    //#2 Customer has an appointment
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("UpcomingAppointment", _textLabelGroupName)
                        .Replace("{appointmentDate}", details.UpcomingAppointment.AppointmentDate.Value.ToString("dd MMMM yyyy", ci).ToLower())
                        .Replace("{appointmentStartTime}", details.UpcomingAppointment.AppointmentStartTime)
                        .Replace("{appointmentEndTime}", details.UpcomingAppointment.AppointmentEndTime), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(KetelComfortAppointmentDialog), nameof(ReturnAppointmentDetailsAsync), TransactionStatus.Appointment, cancellationToken).ConfigureAwait(false);
                }
                else if (details.UpcomingAppointment.PlanWindowStartTime.HasValue)
                {
                    //#3 Customer has no invite of appointment. Can request an invite                    
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("UpcomingAppointmentPlanWindow", _textLabelGroupName)
                        .Replace("{planWindowStartTime}", details.UpcomingAppointment.PlanWindowStartTime.Value.ToString("MMMM yyyy", ci).ToLower())
                        .Replace("{planWindowEndTime}", details.UpcomingAppointment.PlanWindowEndTime.Value.ToString("MMMM yyyy", ci).ToLower()), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(KetelComfortAppointmentDialog), nameof(ReturnAppointmentDetailsAsync), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                }
                else if (details.UpcomingAppointment.Type == PlanActionType.Newcustomer || details.UpcomingAppointment.Type == PlanActionType.Unknown)
                {
                    //#4 Customer just got a contract
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("UpcomingAppointmentNewCustomerOrUnknown", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(KetelComfortAppointmentDialog), nameof(ReturnAppointmentDetailsAsync), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                }
                else
                {
                    //#5 No info found
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("UpcomingAppointmentNotFound", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(KetelComfortAppointmentDialog), nameof(ReturnAppointmentDetailsAsync), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                }
            }
            else
            {
                //#5 No info found
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("UpcomingAppointmentNotFound", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(KetelComfortAppointmentDialog), nameof(ReturnAppointmentDetailsAsync), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
            }

            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }
    }
}
