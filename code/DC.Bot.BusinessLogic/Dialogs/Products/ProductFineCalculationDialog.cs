﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.FinePolicy;
using DC.Domain.Exceptions;
using DC.Domain.Models.Products;
using DC.Products.Client.Models;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Products;

public partial class ProductFineCalculationDialog : BaseDialog
{
    private readonly ICustomersService _customersService;
    private readonly IProductsService _productsService;
    private readonly IProductFineCalculationService _productFineCalculationService;

    private FeatureToggle _featureToggle { get; set; }

    public ProductFineCalculationDialog(
        ILoggerFactory loggerFactory,
        ILoggingService loggingService,
        ISessionManager sessionManager,
        IDialogValidators validators,
        ICustomersService customersService,
        IProductsService productsService,
        IProductFineCalculationService productFineCalculationService,
        IStorageService storageService,
        IOptions<FeatureToggle> featureToggle) :
        base(loggerFactory, sessionManager, loggingService, storageService)
    {
        AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
        [
            InitTextLabels,
                ProductsStep,
                CheckFineCalculationStep,
                NewFineCalculationStep,
                CheckProductFineChoiceAnswer,
                AskCancelDate,
                AnswerNewFineCalculation
        ]));

        AddDialog(new TextPrompt(nameof(InitTextLabels)));
        AddDialog(new TextPrompt(nameof(ProductsStep)));
        AddDialog(new TextPrompt(nameof(CheckFineCalculationStep)));
        AddDialog(new ChoicePrompt(nameof(NewFineCalculationStep)));
        AddDialog(new TextPrompt(nameof(CheckProductFineChoiceAnswer)));
        AddDialog(new TextPrompt(nameof(AskCancelDate), validators.ProductsDialogValidator.FineCancelDateValidator));
        AddDialog(new TextPrompt(nameof(AnswerNewFineCalculation)));

        InitialDialogId = nameof(WaterfallDialog);

        _customersService = customersService;
        _productsService = productsService;
        _productFineCalculationService = productFineCalculationService;
        _featureToggle = featureToggle?.Value ?? new FeatureToggle();
    }

    [System.Diagnostics.CodeAnalysis.SuppressMessage("Critical Code Smell", "S3776:Cognitive Complexity of methods should not be too high", Justification = "Many possible paths, keep it readable by not splitting in this particular case.")]
    private async Task<DialogTurnResult> ProductsStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        Thread.CurrentThread.CurrentCulture = new CultureInfo("nl-NL");
        await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ProductFineCalculation).ConfigureAwait(false);
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
        TransactionStatus status;
        try
        {
            if (dialogData.IsVerified())
            {
                var activeAccountId = dialogData.SelectedAccount?.AccountId;
                if (!dialogData.HasActiveAccount())
                {
                    var customerModel = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);
                    var activeAccount = dialogData.GetActiveAccountFromCustomerModel(customerModel);
                    activeAccountId = activeAccount?.Id;
                }

                if (activeAccountId > 0)
                {
                    var products = await _productsService.GetCustomerProductsByAccount(dialogData, activeAccountId.Value, true).ConfigureAwait(false);
                    products = products?.GetMainEGWProducts();
                    if (products?.Count > 0 && !products.OnlyWarmthElements())
                    {
                        if (products.ContractInReconsiderationPeriod())
                        {
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContractReconsiderationPeriod", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            status = TransactionStatus.Unhappy;
                        }
                        else if (products.HasContractEndDate())
                        {
                            dialogData.ProductFineCalculationData.Products = products.ToList();
                            return await stepContext.NextAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                        }
                        else
                        {
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("OTContract", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            status = TransactionStatus.Unhappy;
                        }
                    }
                    else
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoEGContract", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        status = TransactionStatus.Unhappy;
                    }
                }
                else
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindData", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                    status = TransactionStatus.TemporaryFailure;
                }
            }
            else
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindCustomerId", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                status = TransactionStatus.TemporaryFailure;
            }
        }
        catch (DigitalCoreException ex)
        {
            _loggingService.LogException(ex, stepContext.Context, nameof(ProductsStep));
            return await SomethingWentWrongTryAgain(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        }

        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ProductFineCalculationDialog), nameof(ProductsStep), status, cancellationToken).ConfigureAwait(false);
        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// Checks the fine calculation step.
    /// </summary>
    private async Task<DialogTurnResult> CheckFineCalculationStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ProductFineCalculation).ConfigureAwait(false);
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
        var otProducts = dialogData.ProductFineCalculationData.Products.Where(x => !x.EndDateContract.HasValue).ToList();
        var oldFinePolicyProducts = dialogData.ProductFineCalculationData.Products.GetMainEGWProducts().Where(x => x.StartDate < _featureToggle.NewFinePolicyDate && x.EndDateContract.HasValue).ToList();
        var newFinePolicyProducts = dialogData.ProductFineCalculationData.Products.GetMainEGWProducts().Where(x => x.StartDate >= _featureToggle.NewFinePolicyDate && x.EndDateContract.HasValue).ToList();
        if (newFinePolicyProducts.Count != 0)
        {
            //go to new fine policy flow steps
            return await stepContext.NextAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        if (oldFinePolicyProducts.Count != 0)
        {
            dialogData.ProductFineCalculationData.Fines = new List<ProductFineCalculationModel>();
            foreach (var product in oldFinePolicyProducts)
            {
                ProductFineCalculationModel fine;
                try
                {
                    fine = await _productFineCalculationService.CalculateFineCancelledProduct(dialogData, new ProductCancelRequestModel
                    {
                        SubscriptionId = (int)product.Id,
                        CancelDate = DateTime.Today,
                        DiscontinueReason = ProductDiscontinueReason.LSH
                    }).ConfigureAwait(false);
                }
                catch (DigitalCoreException ex)
                {
                    _loggingService.LogException(ex, stepContext.Context, nameof(CheckFineCalculationStep));
                    return await ShowMvsErrorFineCalculation(null, stepContext, dialogData, cancellationToken).ConfigureAwait(false);
                }

                dialogData.ProductFineCalculationData.Fines.Add(fine);

                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("OldFinePolicyProductFine", _textLabelGroupName)
                    .Replace("{product}", product.Description)
                    .Replace("{enddate}", $"{product.EndDateContract:dd-MM-yyyy}")
                    .Replace("{fine}", $"{fine.FineAmount:N2}".ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("OldFinePolicyTotalFine", _textLabelGroupName)
                .Replace("{totalFine}", $"{dialogData.ProductFineCalculationData.Fines.Sum(x => x.FineAmount):N2}".ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        if (otProducts.Count != 0)
            await ShowOTContractMessage(otProducts, false, stepContext, dialogData, cancellationToken).ConfigureAwait(false);

        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ProductFineCalculationDialog), nameof(CheckFineCalculationStep), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// News the fine calculation step.
    /// </summary>
    private async Task<DialogTurnResult> NewFineCalculationStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ProductFineCalculation).ConfigureAwait(false);
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
        var otProducts = dialogData.ProductFineCalculationData.Products.Where(x => !x.EndDateContract.HasValue).ToList();
        var newFinePolicyProducts = dialogData.ProductFineCalculationData.Products.GetMainEGWProducts().Where(x => x.StartDate >= _featureToggle.NewFinePolicyDate && x.EndDateContract.HasValue).ToList();

        //start with an empty selected products
        dialogData.ProductFineCalculationData.SelectedProducts = new List<ProductModel>();

        foreach (var product in newFinePolicyProducts)
        {
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NewFinePolicyProductEndDate", _textLabelGroupName)
                .Replace("{product}", product.Description)
                .Replace("{enddate}", $"{product.EndDateContract:dd-MM-yyyy}"), cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        string text = null;
        if (newFinePolicyProducts.Count == 1 && otProducts.Count == 0)
            text = dialogData.TextLabels.GetValue("NewFinePolicyFine", _textLabelGroupName);
        else
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NewFinePolicyFine", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

        if (otProducts.Count > 0)
            text = await ShowOTContractMessage(otProducts, newFinePolicyProducts.Count == 1, stepContext, dialogData, cancellationToken).ConfigureAwait(false);

        //show flow of the fine calculation for 1 product 
        if (newFinePolicyProducts.Count == 1)
        {
            dialogData.ProductFineCalculationData.SelectedProducts = newFinePolicyProducts;
            return await stepContext.PromptAsync(nameof(NewFineCalculationStep), new PromptOptions
            {
                Choices = new List<Choice>
                {
                    new Choice
                    {
                        Value = dialogData.TextLabels.GetValue("CalculateFine", _textLabelGroupName)
                    }
                },
                Prompt = MessageFactory.Text(text),
                RetryPrompt = MessageFactory.Text(text),
                Style = ListStyle.SuggestedAction
            }, cancellationToken).ConfigureAwait(false);
        }
        else
        {
            return await stepContext.PromptAsync(nameof(NewFineCalculationStep), new PromptOptions
            {
                Prompt = MessageFactory.Text(dialogData.TextLabels.GetValue("AskProductCalculateFine", _textLabelGroupName)),
                Choices = SetupProductFineCalculationChoices(dialogData, newFinePolicyProducts),
                RetryPrompt = MessageFactory.Text(""),
                Style = ListStyle.SuggestedAction
            }, cancellationToken).ConfigureAwait(false);
        }
    }

    /// <summary>
    /// Checks the new fine choice answer.
    /// </summary>
    private async Task<DialogTurnResult> CheckProductFineChoiceAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ProductFineCalculation).ConfigureAwait(false);
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

        if (dialogData.ProductFineCalculationData.SelectedProducts.Count == 1)
            return await stepContext.NextAsync(cancellationToken: cancellationToken).ConfigureAwait(false);

        var newFinePolicyProducts = dialogData.ProductFineCalculationData.Products.GetMainEGWProducts().Where(x => x.StartDate >= _featureToggle.NewFinePolicyDate).ToList();

        // question is asked and answered in previous step
        if (stepContext.Result is FoundChoice choice)
        {
            if (choice.Value.Contains("beide", StringComparison.InvariantCultureIgnoreCase))
                dialogData.ProductFineCalculationData.SelectedProducts = newFinePolicyProducts;
            else
                dialogData.ProductFineCalculationData.SelectedProducts =
                    [newFinePolicyProducts.First(x => x.Description == choice.Value)];
        }

        return await stepContext.NextAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// Asks the cancel date.
    /// </summary>
    private async Task<DialogTurnResult> AskCancelDate(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ProductFineCalculation).ConfigureAwait(false);
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
        await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("FineDependsOnCancelDate", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
        await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("AskCancelDate", _textLabelGroupName)
            .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"), cancellationToken: cancellationToken).ConfigureAwait(false);
        return await stepContext.PromptAsync(nameof(AskCancelDate), new PromptOptions
        {
            Prompt = MessageFactory.Text(dialogData.TextLabels.GetValue("CancelDateFormat", _textLabelGroupName)),
            RetryPrompt = MessageFactory.Text(dialogData.TextLabels.GetValue("InvalidCancelDate", _textLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"))
        }, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// Answers the new fine calculation.
    /// </summary>
    private async Task<DialogTurnResult> AnswerNewFineCalculation(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ProductFineCalculation).ConfigureAwait(false);
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
        if (dialogData.Verification.TooManyAttempts)
        {
            dialogData.Verification.TooManyAttempts = false;

            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ThreeTimesWrongCancelDate", _textLabelGroupName)
                .Replace("{maxCancelDate}", $"{DateTime.Today.AddDays(59):dd-MM-yyyy}"), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ProductFineCalculationDialog), nameof(AnswerNewFineCalculation), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        DateTime.TryParseExact(stepContext.Context.Activity.Text, "dd-MM-yyyy", new CultureInfo("NL-nl"), DateTimeStyles.None, out var cancelDate);

        var products = dialogData.ProductFineCalculationData.SelectedProducts;

        //if for all selected products the contract end date is before of the chosen cancel date
        if (products.TrueForAll(x => x.EndDateContract <= cancelDate))
        {
            return await ShowContractEndDateBeforeCancelDate(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        }

        //if for all selected products the contract end date within 7 days of the chosen cancel date
        if (products.TrueForAll(x => x.EndDateContract <= cancelDate.AddDays(7)))
        {
            return await ShowContractEndDateWithInRangeCancelDate(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        }

        dialogData.ProductFineCalculationData.Fines = [];
        foreach (var productId in products.Select(x => (int)x.Id!))
        {
            try
            {
                ProductFineCalculationModel fine = await _productFineCalculationService.CalculateFineCancelledProduct(dialogData, new ProductCancelRequestModel
                {
                    SubscriptionId = productId,
                    CancelDate = cancelDate,
                    DiscontinueReason = ProductDiscontinueReason.LSH
                }).ConfigureAwait(false);
                if (fine != null)
                    dialogData.ProductFineCalculationData.Fines.Add(fine);
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(AnswerNewFineCalculation));
                return await ShowMvsErrorFineCalculation(null, stepContext, dialogData, cancellationToken).ConfigureAwait(false);
            }
        }
        var productsWithOutFine = products.Where(x => !dialogData.ProductFineCalculationData.Fines.Select(x => x.ProductId).Contains(x.Id));
        foreach (var product in productsWithOutFine)
        {
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("FinePolicyNoFine", _textLabelGroupName)
                .Replace("{product}", product.Description), cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        foreach (var fine in dialogData.ProductFineCalculationData.Fines)
        {
            if (fine.MVSErrors?.Count > 0)
            {
                return await ShowMvsErrorFineCalculation(fine.MVSErrors[0], stepContext, dialogData, cancellationToken).ConfigureAwait(false);
            }

            var product = products.Find(x => (int)x.Id! == (int)fine.ProductId!);
            if (product == null)
                continue;
            await SendActivityForSingleFine(stepContext, dialogData, fine, product, cancellationToken).ConfigureAwait(false);
        }

        await SendActivityForTotalFine(stepContext, dialogData, cancellationToken).ConfigureAwait(false);

        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ProductFineCalculationDialog), nameof(AnswerNewFineCalculation), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
    }

    private async Task SendActivityForSingleFine(WaterfallStepContext stepContext, DialogData dialogData, ProductFineCalculationModel fine, ProductModel product, CancellationToken cancellationToken)
    {
        if (fine.AverageWeightedDeliveryTariff - fine.AverageWeightedReferenceTariff <= 0)
        {
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("FinePolicyNoFine", _textLabelGroupName)
                .Replace("{product}", product.Description), cancellationToken: cancellationToken).ConfigureAwait(false);
        }
        else if (!fine.NewFinePolicy)
        {
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("OldFinePolicyProductFine", _textLabelGroupName)
                .Replace("{product}", product.Description)
                .Replace("{enddate}", $"{product.EndDateContract:dd-MM-yyyy}")
                .Replace("{fine}", $"{fine.FineAmount:N2}".ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
        }
        else
        {
            if (product.Type.Name == ProductType.Electricity)
                await ShowFineElectrictyProduct(product, fine, stepContext, dialogData, cancellationToken).ConfigureAwait(false);
            else if (product.Type.Name == ProductType.Gas)
                await ShowFineGasProduct(product, fine, stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        }
    }

    private async Task SendActivityForTotalFine(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
    {
        if (dialogData.ProductFineCalculationData.Fines.TrueForAll(x => !x.NewFinePolicy))
        {
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("OldFinePolicyTotalFine", _textLabelGroupName)
                .Replace("{totalFine}", $"{dialogData.ProductFineCalculationData.Fines.Sum(x => x.FineAmount):N2}".ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
        }
        else
        {
            if (dialogData.ProductFineCalculationData.Fines.Sum(x => x.FineAmount) > 0)
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("FinePolicyTotalFine", _textLabelGroupName)
                    .Replace("{fine}", $"{dialogData.ProductFineCalculationData.Fines.Sum(x => x.FineAmount):N2}".ToString(new CultureInfo("NL-nl")))
                    .Replace("{fineWithVat}", $"{dialogData.ProductFineCalculationData.Fines.Sum(x => x.FineAmountInclVat):N2}".ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("FineDisclaimer", _textLabelGroupName)
                    .Replace("&ast;", "*"),
                cancellationToken: cancellationToken).ConfigureAwait(false);
        }
    }

    /// <summary>
    /// Shows the contract end date before cancel date.
    /// </summary>
    private async Task<DialogTurnResult> ShowContractEndDateBeforeCancelDate(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
    {
        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue($"ContractEndDateAfterCancelDate",
            _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ProductFineCalculationDialog), nameof(ShowContractEndDateBeforeCancelDate), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// Shows the contract end date with in range cancel date.
    /// </summary>
    private async Task<DialogTurnResult> ShowContractEndDateWithInRangeCancelDate(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
    {
        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue($"ContractEndDateWithInRangeCancelDate",
            _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ProductFineCalculationDialog), nameof(ShowContractEndDateWithInRangeCancelDate), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// Shows the m v s error fine calculation.
    /// </summary>
    private async Task<DialogTurnResult> ShowMvsErrorFineCalculation(MvsError error, WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
    {
        string textLabel = null;
        if (error != null)
        {
            var mvsErrorCode = NumericRegex().Replace(error.Code, "");
            textLabel = dialogData.TextLabels.GetValue($"FinePolicyMVSError{mvsErrorCode}", _textLabelGroupName);
        }

        if (string.IsNullOrEmpty(textLabel))
            textLabel = dialogData.TextLabels.GetValue($"FinePolicyMVSError", _textLabelGroupName);

        _ = await stepContext.Context.SendActivityAsync(textLabel, cancellationToken: cancellationToken).ConfigureAwait(false);
        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ProductFineCalculationDialog), nameof(ShowMvsErrorFineCalculation), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
    }


    /// <summary>
    /// Shows the fine electricty product.
    /// </summary>
    private async Task ShowFineElectrictyProduct(ProductModel product, ProductFineCalculationModel fineCalculation, WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
    {
        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("FinePolicyTariffElectricity", _textLabelGroupName)
            .Replace("{product}", product.Description)
            .Replace("{tariff}", $"{fineCalculation.AverageWeightedDeliveryTariff ?? 0:N2}".ToString(new CultureInfo("NL-nl")))
            .Replace("{referenceProduct}", fineCalculation.ReferenceProductOMS)
            .Replace("{referenceTariff}", $"{fineCalculation.AverageWeightedReferenceTariff ?? 0:N2}".ToString(new CultureInfo("NL-nl")))
            .Replace("{tariffDifference}", $"{(fineCalculation.AverageWeightedDeliveryTariff - fineCalculation.AverageWeightedReferenceTariff) ?? 0:N2}".ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);

        var remainingSjv = fineCalculation.RemainingSJV ?? 0;
        if (fineCalculation.RemainingSJVHigh.HasValue && fineCalculation.RemainingSJVLow.HasValue)
            remainingSjv = (fineCalculation.RemainingSJVHigh.Value + fineCalculation.RemainingSJVLow.Value);

        var remainingSji = fineCalculation.RemainingSJI ?? 0;
        if (fineCalculation.RemainingSJIHigh.HasValue && fineCalculation.RemainingSJILow.HasValue)
            remainingSji = (fineCalculation.RemainingSJIHigh.Value + fineCalculation.RemainingSJILow.Value);

        string textLabelKey = remainingSji == 0 ? "FinePolicyRemainingUsagesElectricityWithoutRedelivery" : "FinePolicyRemainingUsagesElectricity";
        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue(textLabelKey, _textLabelGroupName)
            .Replace("{enddate}", $"{fineCalculation.FineAmountDeterminationDate:dd-MM-yyyy}")
            .Replace("{usagesSJV}", $"{remainingSjv}")
            .Replace("{usagesSJI}", $"{remainingSji}")
            .Replace("{remaingUsages}", $"{fineCalculation.RemainingAmount ?? 0}"), cancellationToken: cancellationToken).ConfigureAwait(false);
        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("FinePolicyFine", _textLabelGroupName)
            .Replace("{product}", product.Description)
            .Replace("{remaingUsages}", $"{fineCalculation.RemainingAmount ?? 0}")
            .Replace("{denotationType}", "kWh")
            .Replace("{tariffDifference}", $"{(fineCalculation.AverageWeightedDeliveryTariff - fineCalculation.AverageWeightedReferenceTariff) ?? 0:N2}".ToString(new CultureInfo("NL-nl")))
            .Replace("{fine}", $"{fineCalculation.FineAmount:N2}".ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// Shows the fine gas product.
    /// </summary>
    private async Task ShowFineGasProduct(ProductModel product, ProductFineCalculationModel fineCalculation, WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
    {
        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("FinePolicyTariffGas", _textLabelGroupName)
            .Replace("{product}", product.Description)
            .Replace("{tariff}", $"{fineCalculation.AverageWeightedDeliveryTariff ?? 0:N2}".ToString(new CultureInfo("NL-nl")))
            .Replace("{referenceProduct}", fineCalculation.ReferenceProductOMS)
            .Replace("{referenceTariff}", $"{fineCalculation.AverageWeightedReferenceTariff ?? 0:N2}".ToString(new CultureInfo("NL-nl")))
            .Replace("{tariffDifference}", $"{(fineCalculation.AverageWeightedDeliveryTariff - fineCalculation.AverageWeightedReferenceTariff) ?? 0:N2}".ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);

        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("FinePolicyRemainingUsagesGas", _textLabelGroupName)
            .Replace("{enddate}", $"{fineCalculation.FineAmountDeterminationDate:dd-MM-yyyy}")
            .Replace("{remaingUsages}", $"{fineCalculation.RemainingAmount ?? 0}"), cancellationToken: cancellationToken).ConfigureAwait(false);
        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("FinePolicyFine", _textLabelGroupName)
            .Replace("{product}", product.Description)
            .Replace("{remaingUsages}", $"{fineCalculation.RemainingAmount ?? 0}")
            .Replace("{denotationType}", "m³")
            .Replace("{tariffDifference}", $"{(fineCalculation.AverageWeightedDeliveryTariff - fineCalculation.AverageWeightedReferenceTariff) ?? 0:N2}".ToString(new CultureInfo("NL-nl")))
            .Replace("{fine}", $"{fineCalculation.FineAmount:N2}".ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// Shows the o t contract message.
    /// </summary>
    private async Task<string> ShowOTContractMessage(List<ProductModel> products, bool returnText, WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
    {
        string text = null;
        foreach (var description in products.Select(product => product.Description))
        {
            if (returnText && products.Count == 1)
                text = dialogData.TextLabels.GetValue("OldFinePolicyOTContract", _textLabelGroupName)
                    .Replace("{product}", description);
            else
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("OldFinePolicyOTContract", _textLabelGroupName)
                    .Replace("{product}", description), cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        return text;
    }

    /// <summary>
    /// Some things the went wrong try again.
    /// </summary>
    private async Task<DialogTurnResult> SomethingWentWrongTryAgain(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
    {
        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("TryAgianSomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ProductFineCalculationDialog), nameof(SomethingWentWrongTryAgain), TransactionStatus.PermanentFailure, cancellationToken).ConfigureAwait(false);
        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// Setups the product fine calculation choices.
    /// </summary>
    private List<Choice> SetupProductFineCalculationChoices(DialogData dialogData, List<ProductModel> products)
    {
        var buttons = products.Select(product => product.Description)
            .Select(description => SetupButton(description, [description])).ToList();

        buttons.Add(SetupButton(dialogData, "OptionBothProducts", new List<string> { "Beide", "beide" }));
        return buttons;
    }

    /// <summary>
    /// Setups the button.
    /// </summary>
    private Choice SetupButton(DialogData dialogData, string key, List<string> synonyms)
    {
        return new Choice
        {
            Value = dialogData.TextLabels.GetValue(key, _textLabelGroupName),
            Synonyms = synonyms
        };
    }

    /// <summary>
    /// Setups the button.
    /// </summary>
    private static Choice SetupButton(string value, List<string> synonyms)
    {
        return new Choice
        {
            Value = value,
            Synonyms = synonyms
        };
    }

    [GeneratedRegex(@"\D")]
    private static partial Regex NumericRegex();
}