﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Exceptions;
using DC.Domain.Models.Products;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using ProductModel = DC.Domain.Models.Products.ProductModel;

namespace DC.Bot.BusinessLogic.Dialogs.Products
{
    public class ZonOpDakDialog : BaseDialog
    {
        private readonly ICustomersService _customersService;
        private readonly IProductsService _productsService;

        /// <summary>
        /// Constructor
        /// </summary>
        public ZonOpDakDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            ICustomersService customersService,
            IProductsService productsService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
                new WaterfallStep[]
                {
                    InitTextLabels,
                    ZonOpDakStep
                }));

            InitialDialogId = nameof(WaterfallDialog);

            _customersService = customersService;
            _productsService = productsService;
        }

        /// <summary>
        /// Zon Op Dak step.
        /// </summary>
        private async Task<DialogTurnResult> ZonOpDakStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ZonOpDak).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            TransactionStatus status;
            bool hasDynamicPricing = false;
            try
            {
                if (dialogData.IsVerified())
                {
                    var activeAccountId = dialogData.SelectedAccount?.AccountId;
                    if (!dialogData.HasActiveAccount())
                    {
                        var customerModel = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);
                        var activeAccount = dialogData.GetActiveAccountFromCustomerModel(customerModel);
                        activeAccountId = activeAccount?.Id;
                        hasDynamicPricing = activeAccount?.HasDynamicPricing ?? false;
                    }
                    else
                    {
                        hasDynamicPricing = dialogData.SelectedAccount?.HasDynamicPricing ?? false;
                    }

                    if (activeAccountId > 0)
                    {
                        status = await ZonOpDakAdviceForProducts(stepContext, dialogData, activeAccountId, hasDynamicPricing).ConfigureAwait(false);
                    }
                    else
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindData", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                        status = TransactionStatus.TemporaryFailure;
                    }
                }
                else
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindCustomerId", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                    status = TransactionStatus.TemporaryFailure;
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(ZonOpDakStep));
                return await SomethingWentWrongTryAgain(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
            }

            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ZonOpDakDialog), nameof(ZonOpDakStep), status, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// ZonOpDak advise for Products
        /// </summary>
        private async Task<TransactionStatus> ZonOpDakAdviceForProducts(WaterfallStepContext stepContext, DialogData dialogData, int? activeAccountId, bool isDynamicPrice = false)
        {
            TransactionStatus status;

            var products = await _productsService.GetCustomerProductsByAccount(dialogData, activeAccountId.Value, true).ConfigureAwait(false);

            // Filter out all the non EGW products
            products = products.GetMainEGWProducts();
            if (products?.Any() == true)
                status = await ShowZonOpDakAdvice(products, dialogData, stepContext, isDynamicPrice).ConfigureAwait(false);
            else
                status = TransactionStatus.ZonOpDakNoElectricity;

            return status;
        }

        /// <summary>
        /// Shows the product end dates advice.
        /// </summary>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Critical Code Smell", "S3776:Cognitive Complexity of methods should not be too high", Justification = "<Pending>")]
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Major Code Smell", "S125:Sections of code should not be commented out", Justification = "<Pending>")]
        private async Task<TransactionStatus> ShowZonOpDakAdvice(IList<ProductModel> products, DialogData dialogData, WaterfallStepContext stepContext, bool isDynamicPrice)
        {
            products = products.GetMainEGProducts();
            foreach (ProductModel product in products)
            {
                var stringBuilder = new StringBuilder();
                stringBuilder.AppendBoldLineWithMarkdown(product.Type?.Name == ProductType.Happypower ? "HappyPower" : product.Description);
                AppendEndDateString(dialogData, product, stringBuilder);
                _ = await stepContext.Context.SendActivityAsync(stringBuilder.ToString()).ConfigureAwait(false);
            }

            if (isDynamicPrice)
            {
                return TransactionStatus.ZonOpDakElectricityDynamicPricing;
            }

            if (!products.AnyElectricity())
            {
                return TransactionStatus.ZonOpDakNoElectricity;
            }

            var productFirst = products.First(p => p.Type?.Name == ProductType.Electricity);
            TransactionStatus status = GetEndDateTransactionStatus(productFirst);

            return status;
        }

        private void AppendEndDateString(DialogData dialogData, ProductModel product, StringBuilder stringBuilder)
        {
            // Indefinite contract (OT) with variable costs.
            if (product.Indefinite == true || !product.EndDateContract.HasValue)
            {
                stringBuilder.AppendLineWithMarkdown(dialogData.TextLabels.GetValue("ProductIndefinite", _textLabelGroupName));
            }
            else
            {
                stringBuilder.AppendLineWithMarkdown(dialogData.TextLabels.GetValue("ProductEndDate", _textLabelGroupName).Replace("{endDateContract}", $"{product.EndDateContract.Value:dd-MM-yyyy}"));
            }
        }

        /// <summary>
        /// Gets the end date transaction status.
        /// </summary>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Critical Code Smell", "S3776:Cognitive Complexity of methods should not be too high", Justification = "<Pending>")]
        private static TransactionStatus GetEndDateTransactionStatus(ProductModel productModel)
        {
            var status = TransactionStatus.Success;
            var measureDate = new DateTime(2024, 6, 1, 0, 0, 0, DateTimeKind.Local); // Measure Data is 1st June
            int daysTillMeasureDate = (measureDate - DateTime.Now).Days;

            //Check if it's a Model Contract
            if (productModel.Duration == 0)
            {
                status = TransactionStatus.ZonOpDakElectricityTemplateContract;
            }
            else if ((productModel.EndDateContract.HasValue &&
                productModel.EndDatePrice.HasValue &&
                productModel.EndDatePrice.Value <= measureDate) ||
                (productModel.Indefinite == false && productModel.ContractDurationIsLessThan(daysTillMeasureDate)))
            {
                status = TransactionStatus.ZonOpDakElectricityDefiniteBefore1June;
            }
            else if ((productModel.EndDateContract.HasValue &&
                productModel.EndDatePrice.HasValue &&
                productModel.EndDatePrice.Value > measureDate) ||
                (productModel.Indefinite == false && !productModel.ContractDurationIsLessThan(daysTillMeasureDate)))
            {
                status = TransactionStatus.ZonOpDakElectricityDefiniteAfter1June;
            }
            else if (!productModel.EndDateContract.HasValue || (!productModel.EndDateContract.HasValue && productModel.Indefinite == true))
            {
                status = TransactionStatus.ZonOpDakElectricityIndefinite;
            }

            return status;
        }

        /// <summary>
        /// Function for when something goes wrong
        /// </summary>
        private async Task<DialogTurnResult> SomethingWentWrongTryAgain(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
        {
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("TryAgianSomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ProductEndDatesAdviceDialog), nameof(SomethingWentWrongTryAgain), TransactionStatus.PermanentFailure, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }
    }
}