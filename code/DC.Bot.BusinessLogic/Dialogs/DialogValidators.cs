﻿using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Dialogs.Financials.Validators;
using DC.Bot.BusinessLogic.Dialogs.Products.Validators;
using System.Diagnostics.CodeAnalysis;

namespace DC.Bot.BusinessLogic.Dialogs;

[ExcludeFromCodeCoverage]
public class DialogValidators : IDialogValidators
{
    public IFinancialsDialogValidator FinancialsDialogValidator { get; }
    public ICustomerValidator CustomerDialogValidator { get; }
    public IProductsDialogValidator ProductsDialogValidator { get; }

    public DialogValidators(IFinancialsDialogValidator financialsDialogValidator,
        ICustomerValidator customerValidator,
        IProductsDialogValidator productsDialogValidator)
    {
        FinancialsDialogValidator = financialsDialogValidator;
        CustomerDialogValidator = customerValidator;
        ProductsDialogValidator = productsDialogValidator;
    }
}
