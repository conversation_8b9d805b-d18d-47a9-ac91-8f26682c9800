﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Dialogs.Customers;
using DC.Bot.BusinessLogic.Dialogs.Customers.PhoneNumberDialogs;
using DC.Bot.BusinessLogic.Dialogs.Financials;
using DC.Bot.BusinessLogic.Dialogs.Products;
using DC.Bot.BusinessLogic.Dialogs.Usages;
using DC.Bot.BusinessLogic.Dialogs.UserAccounts;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Interfaces.DialogContainers;
using DC.Bot.BusinessLogic.Models;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs
{
    /// <summary>
    /// Main dialog, where the conversation starts.
    /// </summary>
    public class MainDialog : BaseDialog
    {
        private const string _INITIAL_DIALOG = "InitialDialog";
        private const string _ADVANCE_PAYMENTAMOUNT_DIALOG = "AdvancePaymentAmountWaterfallDialog";
        private const string _ADVANCE_PAYMENTDAY_DIALOG = "AdvancePaymentDay";
        private const string _ADVANCE_PAYMENTDIRECTDEBIT_DIALOG = "AdvancePaymentDirectDebit";
        private const string _YEARNOTE_DIALOG = "YearnoteDateWaterfallDialog";
        private const string _PRODUCTRATES = "ProductRates";
        private const string _PRODUCTUSAGES = "ProductUsages";
        private const string _USERNAME = "Username";
        private const string _PRODUCTENDDATES = "ProductEndDates";
        private const string _PAYMENTARRANGEMENT = "PaymentArrrangment";
        private const string _RELOCATIONDATE = "RelocationDate";
        private const string _VERIFICATION_DIALOG = "VerificationDialog";
        private const string _CHANGEEMAIL_DIALOG = "ChangeEmail";
        private const string _CHANGEIBAN_DIALOG = "ChangeIBAN";
        private const string _CHANGEPHONENUMBER_DIALOG = "ChangePhoneNumber";
        private const string _DISCONTINUE_TOON = "DiscontinueToon";
        private const string _DISCONTINUE_SERVICE_CONTRACT = "DiscontinueServiceContract";
        private const string _KETELCOMFORT_APPOINTMENT = "KetelComfortAppointment";
        private const string _READINGS_REPORT_REQUEST = "ReadingsReportRequest";
        private const string _SAVE_READING_PERSONAL = "SaveReadingPersonal";
        private const string _CREATE_SERVICE_ORDER = "CreateServiceOrder";
        private const string _PRODUCTENDDATESADVICE = "ProductEndDatesAdvice";
        private const string _NEXTBESTACTION = "NextBestAction";
        private const string _ADVANCE_PAYMENT_ADVICE = "AdvancePaymentAdvice";
        private const string _ISMA_MANDATE = "IsmaMandate";
        private const string _PRODUCT_FINE_CALCULATION = "ProductFineCalculation";
        private const string _ZON_OP_DAK = "ZonOpDak";
        private const string _CHANGE_CONTACT_PREFERENCES = "ChangeContactPreferences";

        private readonly ICustomersService _customersService;
        private readonly INextBestActionService _nbaService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="loggerFactory"></param>
        /// <param name="loggingService"></param>
        /// <param name="sessionManager"></param>
        /// <param name="dialogContainer"></param>
        /// <param name="customersService"></param>
        /// <param name="nbaService"></param>
        public MainDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            IDialogContainer dialogContainer,
            ICustomersService customersService,
            INextBestActionService nbaService)
            : base(loggerFactory, sessionManager, loggingService)
        {
            // setup the initial dialog
            AddDialog(new WaterfallDialog(_INITIAL_DIALOG,
                new WaterfallStep[]
                    {
                        BeginDialogStep
                    }
            ));

            // add the individual dialogs
            AddDialog(dialogContainer.Verification.CustomerVerificationDialog);
            AddDialog(dialogContainer.Verification.CustomerIdVerification);
            AddDialog(dialogContainer.Customers.CustomerAccountsDialog);
            AddDialog(dialogContainer.Customers.YearnoteDateDialog);
            AddDialog(dialogContainer.Customers.ChangeEmailDialog);
            AddDialog(dialogContainer.Customers.ChangeIbanDialog);
            AddDialog(dialogContainer.Customers.ChangePhoneNumberDialog);
            AddDialog(dialogContainer.Customers.RelocateDateDialog);
            AddDialog(dialogContainer.Customers.ChangeContactPreferencesDialog);
            AddDialog(dialogContainer.Financials.AdvancePaymentAdviceDialog);
            AddDialog(dialogContainer.Financials.AdvancePaymentAmountDialog);
            AddDialog(dialogContainer.Financials.GiroCardStepDialog);
            AddDialog(dialogContainer.Financials.AdvancePaymentDayDialog);
            AddDialog(dialogContainer.Financials.PaymentArrangementDialog);
            AddDialog(dialogContainer.Products.ProductRatesDialog);
            AddDialog(dialogContainer.Products.ProductEndDatesDialog);
            AddDialog(dialogContainer.Products.ProductEndDatesAdviceDialog);
            AddDialog(dialogContainer.Products.PersonalDiscontinueToonDialog);
            AddDialog(dialogContainer.Products.PersonalDiscontinueServiceContractDialog);
            AddDialog(dialogContainer.Products.KetelComfortAppointmentDialog);
            AddDialog(dialogContainer.Products.CreateServiceOrderDialog);
            AddDialog(dialogContainer.Products.ProductFineCalculationDialog);
            AddDialog(dialogContainer.Products.NextBestActionDialog);
            AddDialog(dialogContainer.Products.ZonOpDakDialog);
            AddDialog(dialogContainer.Usages.ProductUsagesDialog);
            AddDialog(dialogContainer.Usages.ReadingsReportRequestDialog);
            AddDialog(dialogContainer.Usages.SaveReadingPersonalDialog);
            AddDialog(dialogContainer.Usages.IsmaMandateDialog);
            AddDialog(dialogContainer.UserAccounts.UsernameDialog);

            // add a waterfall dialog for advance payment
            AddDialog(new WaterfallDialog(_ADVANCE_PAYMENTAMOUNT_DIALOG,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    AdvancePaymentAmountStep
                }
            ));

            // add a waterfall dialog for advance payment
            AddDialog(new WaterfallDialog(_ADVANCE_PAYMENTDAY_DIALOG,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    AdvancePaymentDayStep
                }
            ));

            // add a waterfall dialog for advance payment
            AddDialog(new WaterfallDialog(_ADVANCE_PAYMENTDIRECTDEBIT_DIALOG,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    NonDirectDebitStep
                }
            ));

            // add a waterfall dialog for yearnote date
            AddDialog(new WaterfallDialog(_YEARNOTE_DIALOG,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    YearnoteDateStep
                }
            ));

            // add a waterfall dialog for productrates
            AddDialog(new WaterfallDialog(_PRODUCTRATES,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    ProductRatesStep
                }
            ));
            AddDialog(new WaterfallDialog(_USERNAME,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    UsernameStep
                }
                ));
            AddDialog(new WaterfallDialog(_PRODUCTENDDATES,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    ProductEndDatesStep
                }
                ));
            AddDialog(new WaterfallDialog(_PAYMENTARRANGEMENT,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    PaymentArrangementDialogStep
                }
            ));
            AddDialog(new WaterfallDialog(_RELOCATIONDATE,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    RelocationDateStep
                }
            ));
            AddDialog(new WaterfallDialog(_PRODUCTUSAGES,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    ProductUsagesStep
                }
            ));
            AddDialog(new WaterfallDialog(_VERIFICATION_DIALOG,
                new WaterfallStep[]
                {
                    VerifyCustomerStep
                }
            ));
            AddDialog(new WaterfallDialog(_CHANGEEMAIL_DIALOG,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    ChangeEmailDialogStep
                }
            ));
            AddDialog(new WaterfallDialog(_CHANGEIBAN_DIALOG,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    ChangeIBANDialogStep
                }
            ));
            AddDialog(new WaterfallDialog(_CHANGEPHONENUMBER_DIALOG,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    ChangePhoneNumberDialogStep
                }
            ));
            AddDialog(new WaterfallDialog(_DISCONTINUE_TOON,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    DiscontinueToonDialogStep
                }
            ));
            AddDialog(new WaterfallDialog(_DISCONTINUE_SERVICE_CONTRACT,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    DiscontinueServiceContractDialogStep
                }
            ));
            // add a waterfall dialog for yearnote date
            AddDialog(new WaterfallDialog(_KETELCOMFORT_APPOINTMENT,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    KetelComfortAppointmentStep
                }
            ));
            AddDialog(new WaterfallDialog(_READINGS_REPORT_REQUEST,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    ReadingsReportRequestStep
                }
            ));
            AddDialog(new WaterfallDialog(_SAVE_READING_PERSONAL,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    SaveReadingPersonalStep
                }
            ));
            AddDialog(new WaterfallDialog(_CREATE_SERVICE_ORDER,
               new WaterfallStep[]
               {
                    VerifyCustomerStep,
                    CreateServiceOrderStep
               }
           ));
            AddDialog(new WaterfallDialog(_PRODUCTENDDATESADVICE,
                 new WaterfallStep[]
                 {
                    VerifyCustomerStep,
                    ProductEndDatesAdviceStep
                 }
             ));
            AddDialog(new WaterfallDialog(_NEXTBESTACTION,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    NextBestActionStep
                }
            ));
            AddDialog(new WaterfallDialog(_ADVANCE_PAYMENT_ADVICE,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    AdvancePaymentAdviceStep
                }
            ));
            AddDialog(new WaterfallDialog(_ISMA_MANDATE,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    IsmaMandateStep
                }
            ));
            AddDialog(new WaterfallDialog(_PRODUCT_FINE_CALCULATION,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    ProductFineCalculationStep
                }
            ));
            AddDialog(new WaterfallDialog(_ZON_OP_DAK,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    ZonOpDakStep
                }
            ));
            AddDialog(new WaterfallDialog(_CHANGE_CONTACT_PREFERENCES,
                new WaterfallStep[]
                {
                    VerifyCustomerStep,
                    ChangeContactPreferencesStep
                }));


            // The initial child Dialog to run.
            InitialDialogId = _INITIAL_DIALOG;

            _customersService = customersService;
            _nbaService = nbaService;
        }

        private static async Task<DialogTurnResult> DiscontinueServiceContractDialogStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(DiscontinueServiceContractDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> DiscontinueToonDialogStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(DiscontinueToonDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> ChangePhoneNumberDialogStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(ChangePhoneNumberDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> ChangeIBANDialogStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(ChangeIbanDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> NonDirectDebitStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(GiroCardStepDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> ChangeEmailDialogStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(ChangeEmailDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> BeginDialogStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            // parse TransactionStartValue object from the incoming activity
            var transactionStartValue = stepContext.Context.Activity.GetTransactionStartValue();

            // if transaction start value is correctly parsed
            if (transactionStartValue != null)
            {
                // store the label in the session state
                var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
                dialogData.Customer.Label = transactionStartValue.Label;
                dialogData.Channel = transactionStartValue.Channel;
                dialogData.NextBestAction.CurrentTransactionIsNba = transactionStartValue.IsNba;

                dialogData.ServiceOrder.Description = transactionStartValue.Description;
                dialogData.ServiceOrder.Hardware = transactionStartValue.Hardware;
                dialogData.ServiceOrder.HardwareType = transactionStartValue.Hardware.ToServiceOrderProductType();
                dialogData.ServiceOrder.IsUrgent = transactionStartValue.Urgent.ToServiceOrderUrgent();
                dialogData.CommandBeginDialog = transactionStartValue.StartCommand;
                await _sessionManager.HandleAuthorization(stepContext.Context, transactionStartValue, cancellationToken).ConfigureAwait(false);
            }

            return await BeginTransaction(stepContext, transactionStartValue, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> VerifyCustomerStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var options = new Dictionary<string, bool> {
                { DialogContextKeys._VERIFICATIONONLY, stepContext.ActiveDialog.Id == _VERIFICATION_DIALOG },
                { DialogContextKeys._CUSTOMERIDVERIFICATIONOBLIGATED, stepContext.ActiveDialog.Id == _CHANGEEMAIL_DIALOG }
            };

            return await stepContext.BeginDialogAsync(nameof(CustomerVerificationDialog), options, cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> AdvancePaymentAmountStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(AdvancePaymentAmountDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> AdvancePaymentDayStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(AdvancePaymentDayDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> YearnoteDateStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(YearnoteDateDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> ProductRatesStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(ProductRatesDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> UsernameStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(UsernameDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> ProductEndDatesStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(ProductEndDatesDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> PaymentArrangementDialogStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(PaymentArrangementDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> RelocationDateStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(RelocateDateDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> ProductUsagesStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(ProductUsagesDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> KetelComfortAppointmentStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(KetelComfortAppointmentDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Readingses the report request step.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private static async Task<DialogTurnResult> ReadingsReportRequestStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(ReadingsReportRequestDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private static async Task<DialogTurnResult> SaveReadingPersonalStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(SaveReadingPersonalDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Creates the service order step.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private static async Task<DialogTurnResult> CreateServiceOrderStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(CreateServiceOrderDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Products the end dates advice step.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private static async Task<DialogTurnResult> ProductEndDatesAdviceStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(ProductEndDatesAdviceDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Next best action step.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns></returns>
        private static async Task<DialogTurnResult> NextBestActionStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(NextBestActionDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Advance Payment Advice step.
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private static async Task<DialogTurnResult> AdvancePaymentAdviceStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(AdvancePaymentAdviceDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Isma Mandate step.
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private static async Task<DialogTurnResult> IsmaMandateStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(IsmaMandateDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Products the fine calculation step.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A Task.</returns>
        private static async Task<DialogTurnResult> ProductFineCalculationStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(ProductFineCalculationDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// ZonOpDak step.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A Task.</returns>
        private static async Task<DialogTurnResult> ZonOpDakStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(ZonOpDakDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// ChangeContactPreferences step.
        /// </summary>
        /// <param name="stepContext">The step context.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>A Task.</returns>
        private static async Task<DialogTurnResult> ChangeContactPreferencesStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.BeginDialogAsync(nameof(ChangeContactPreferencesDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        protected override async Task<DialogTurnResult> OnContinueDialogAsync(DialogContext innerDc, CancellationToken cancellationToken = default)
        {
            // parse TransactionStartValue object from the incoming activity
            var transactionStartValue = innerDc.Context.Activity.GetTransactionStartValue();

            // if transaction start value is correctly parsed
            if (innerDc.Context.Activity.IsStartOfTransactionActivity(out _) && transactionStartValue != null)
            {
                // cancel running dialog
                await innerDc.CancelAllDialogsAsync(cancellationToken: cancellationToken).ConfigureAwait(false);

                await _sessionManager.HandleAuthorization(innerDc.Context, transactionStartValue, cancellationToken).ConfigureAwait(false);

                // if user has not verified reset the state, with the right label provided in the new start command
                var dialogData = await _sessionManager.GetDialogData(innerDc.Context).ConfigureAwait(false);
                if (dialogData.Verification.IsVerified != true)
                {
                    await _sessionManager.ResetConversationData(innerDc.Context).ConfigureAwait(false);
                    dialogData = await _sessionManager.GetDialogData(innerDc.Context).ConfigureAwait(false);
                    dialogData.Customer.Label = transactionStartValue.Label;
                    dialogData.ServiceOrder.Hardware = transactionStartValue.Hardware;
                    dialogData.ServiceOrder.HardwareType = transactionStartValue.Hardware.ToServiceOrderProductType();
                    dialogData.ServiceOrder.Description = transactionStartValue.Description;
                    dialogData.ServiceOrder.IsUrgent = transactionStartValue.Urgent.ToServiceOrderUrgent();
                }

                // invoke base continue if there is no match
                return await BeginTransaction(innerDc, transactionStartValue, cancellationToken).ConfigureAwait(false);
            }
            return await base.OnContinueDialogAsync(innerDc, cancellationToken).ConfigureAwait(false);
        }


        /// <summary>
        /// Method to begin a desired transaction, given a start command event.
        /// </summary>
        /// <param name="dialogContext"></param>
        /// <param name="transactionStartValue"></param>
        /// <param name="onCannotMatch">Function to be invoked in case there is no start command that matches</param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Critical Code Smell", "S3776:Cognitive Complexity of methods should not be too high", Justification = "It's preferred to keep all the dialog commands in one method.")]
        private async Task<DialogTurnResult> BeginTransaction(
            DialogContext dialogContext,
            TransactionStartValue transactionStartValue,
            CancellationToken cancellationToken)
        {
            if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._ADVANCE_PAYMENT_AMOUNT) == true)
            {
                return await dialogContext.BeginDialogAsync(_ADVANCE_PAYMENTAMOUNT_DIALOG, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._ADVANCE_PAYMENT_DAY) == true)
            {
                return await dialogContext.BeginDialogAsync(_ADVANCE_PAYMENTDAY_DIALOG, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._ADVANCE_PAYMENT_DIRECTDEBIT) == true)
            {
                return await dialogContext.BeginDialogAsync(_ADVANCE_PAYMENTDIRECTDEBIT_DIALOG, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._NEXT_YEARNOTE_DATE) == true)
            {
                return await dialogContext.BeginDialogAsync(_YEARNOTE_DIALOG, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._RATES) == true)
            {
                return await dialogContext.BeginDialogAsync(_PRODUCTRATES, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._USERNAME) == true)
            {
                return await dialogContext.BeginDialogAsync(_USERNAME, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._PRODUCTENDDATESADVICE) == true)
            {
                return await dialogContext.BeginDialogAsync(_PRODUCTENDDATESADVICE, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._PRODUCTENDDATES) == true)
            {
                return await dialogContext.BeginDialogAsync(_PRODUCTENDDATES, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._PAYMENTARRANGEMENT) == true)
            {
                return await dialogContext.BeginDialogAsync(_PAYMENTARRANGEMENT, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._RELOCATEDATE) == true)
            {
                return await dialogContext.BeginDialogAsync(_RELOCATIONDATE, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._PRODUCTUSAGES) == true)
            {
                return await dialogContext.BeginDialogAsync(_PRODUCTUSAGES, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._VERIFICATION) == true)
            {
                return await dialogContext.BeginDialogAsync(_VERIFICATION_DIALOG, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._CHANGEEMAIL) == true)
            {
                return await dialogContext.BeginDialogAsync(_CHANGEEMAIL_DIALOG, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._CHANGEIBAN) == true)
            {
                return await dialogContext.BeginDialogAsync(_CHANGEIBAN_DIALOG, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._CHANGEPHONENUMBER) == true)
            {
                return await dialogContext.BeginDialogAsync(_CHANGEPHONENUMBER_DIALOG, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._DISCONTINUE_TOON) == true)
            {
                return await dialogContext.BeginDialogAsync(_DISCONTINUE_TOON, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._DISCONTINUE_SERVICE_CONTRACT) == true)
            {
                return await dialogContext.BeginDialogAsync(_DISCONTINUE_SERVICE_CONTRACT, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._AUTHENTICATE) == true)
            {
                return await AuthenticateDialog(dialogContext, cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._KETELCOMFORT_APPOINTMENT) == true)
            {
                return await dialogContext.BeginDialogAsync(_KETELCOMFORT_APPOINTMENT, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._READINGS_REPORT_REQUEST) == true)
            {
                return await dialogContext.BeginDialogAsync(_READINGS_REPORT_REQUEST, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._SAVE_READING_PERSONAL) == true)
            {
                return await dialogContext.BeginDialogAsync(_SAVE_READING_PERSONAL, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._CREATE_SERVICE_ORDER) == true)
            {
                return await dialogContext.BeginDialogAsync(_CREATE_SERVICE_ORDER, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._NEXTBESTACTION) == true)
            {
                return await dialogContext.BeginDialogAsync(_NEXTBESTACTION, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._ADVANCE_PAYMENT_ADVICE) == true)
            {
                return await dialogContext.BeginDialogAsync(_ADVANCE_PAYMENT_ADVICE, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._ISMA_MANDATE) == true)
            {
                return await dialogContext.BeginDialogAsync(_ISMA_MANDATE, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._PRODUCT_FINE_CALCULATION) == true)
            {
                return await dialogContext.BeginDialogAsync(_PRODUCT_FINE_CALCULATION, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._ZON_OP_DAK) == true)
            {
                return await dialogContext.BeginDialogAsync(_ZON_OP_DAK, cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (transactionStartValue?.StartCommand?.StartsWith(DialogCommands._CHANGE_CONTACT_PREFERENCES) == true)
            {
                return await dialogContext.BeginDialogAsync(_CHANGE_CONTACT_PREFERENCES, cancellationToken: cancellationToken);
            }

            await _sessionManager.SendEndOfTransactionActivity(dialogContext.Context, nameof(MainDialog), nameof(BeginTransaction), TransactionStatus.InvalidStartEvent, cancellationToken).ConfigureAwait(false);
            return await dialogContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AuthenticateDialog(DialogContext dialogContext, CancellationToken cancellationToken = default)
        {
            var dialogData = await _sessionManager.GetDialogData(dialogContext.Context).ConfigureAwait(false);
            TransactionStatus transactionStatus;
            if (dialogData.Verification.IsVerifiedWithToken)
            {
                // get customer
                var customer = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);

                // get personalization info + nba info parallel
                var personalisationInfoTask = _customersService.GetPersonalisationInfo(dialogData, customer);
                var nbaTask = _nbaService.GetNextBestActions(dialogData);
                await Task.WhenAll(personalisationInfoTask, nbaTask).ConfigureAwait(false);
                var nba = await nbaTask.ConfigureAwait(false);

                // enrich the dialog and send the info to the client
                _customersService.EnrichDialogData(dialogData, customer, nba);
                await _sessionManager.SendCustomerInfoEvent(dialogContext.Context, await personalisationInfoTask.ConfigureAwait(false), nba.ToNextBestActionInfo()).ConfigureAwait(false);

                transactionStatus = TransactionStatus.Success;
            }
            else
            {
                transactionStatus = TransactionStatus.PermanentFailure;
            }
            await _sessionManager.SendEndOfTransactionActivity(dialogContext.Context, nameof(MainDialog), nameof(AuthenticateDialog), transactionStatus, cancellationToken).ConfigureAwait(false);
            return await dialogContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }
    }
}
