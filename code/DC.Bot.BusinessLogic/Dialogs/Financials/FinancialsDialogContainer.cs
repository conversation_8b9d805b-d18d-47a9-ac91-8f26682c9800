﻿using DC.Bot.BusinessLogic.Interfaces.DialogContainers;

namespace DC.Bot.BusinessLogic.Dialogs.Financials
{
    public class FinancialsDialogContainer : IFinancialsDialogContainer
    {
        public AdvancePaymentAdviceDialog AdvancePaymentAdviceDialog { get; private set; }

        public AdvancePaymentAmountDialog AdvancePaymentAmountDialog { get; private set; }

        public GiroCardStepDialog GiroCardStepDialog { get; private set; }

        public AdvancePaymentDayDialog AdvancePaymentDayDialog { get; private set; }

        public PaymentArrangementDialog PaymentArrangementDialog { get; private set; }

        public FinancialsDialogContainer(
            AdvancePaymentAdviceDialog advancePaymentAdviceDialog,
            AdvancePaymentAmountDialog advancePaymentAmountDialog,
            GiroCardStepDialog giroCardStepDialog,
            AdvancePaymentDayDialog advancePaymentDayDialog,
            PaymentArrangementDialog paymentArrangementDialog)
        {
            AdvancePaymentAdviceDialog = advancePaymentAdviceDialog;
            AdvancePaymentAmountDialog = advancePaymentAmountDialog;
            GiroCardStepDialog = giroCardStepDialog;
            AdvancePaymentDayDialog = advancePaymentDayDialog;
            PaymentArrangementDialog = paymentArrangementDialog;
        }
    }
}
