﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Customers.Client.Models;
using DC.Domain.Exceptions;
using DC.Financials.Client.Models;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using TransactionStatus = DC.Bot.BusinessLogic.Enumerations.TransactionStatus;

namespace DC.Bot.BusinessLogic.Dialogs.Financials
{
    public class PaymentArrangementDialog : BaseDialog
    {
        private readonly ICustomersService _customersService;
        private readonly IFinancialsService _financialsService;

        public PaymentArrangementDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            ICustomersService customersService,
            IFinancialsService financialsService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
            new WaterfallStep[]
            {
                InitTextLabels,
                PaymentArrangementStep
            }));

            InitialDialogId = nameof(WaterfallDialog);

            _customersService = customersService;
            _financialsService = financialsService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<DialogTurnResult> PaymentArrangementStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.GetPaymentArrangement).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            TransactionStatus status;
            try
            {
                if (dialogData.IsVerified())
                {
                    var customerModel = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);
                    var activeAccount = dialogData.GetActiveAccountFromCustomerModel(customerModel);
                    if (activeAccount != null)
                    {
                        var paymentArrangement = await _financialsService.GetPaymentArrangement(dialogData, activeAccount.Id).ConfigureAwait(false);
                        if (paymentArrangement != null)
                        {
                            await ShowPaymentArrangementData(paymentArrangement, activeAccount, stepContext).ConfigureAwait(false);
                        }
                        else
                        {
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindActivePaymentArrangement", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        }
                        status = TransactionStatus.Success;
                    }
                    else
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindData", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                        status = TransactionStatus.TemporaryFailure;
                    }
                }
                else
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindCustomerId", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                    status = TransactionStatus.TemporaryFailure;
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(PaymentArrangementStep));

                status = TransactionStatus.TemporaryFailure;
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrongPaymentArrangementStatus", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("WhereToViewPaymentArrangement", _textLabelGroupName).Replace("{mijnEnecoBetalingsregelingContent}", 
                    DialogContent.MijnEnecoBetalingsregeling), cancellationToken: cancellationToken).ConfigureAwait(false);

            }
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(PaymentArrangementDialog), nameof(PaymentArrangementStep), status, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private async Task ShowPaymentArrangementData(PaymentArrangementModel paymentArrangementModel, CustomerAccountModel account, WaterfallStepContext stepContext)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("OutstandingAmountForAddress", _textLabelGroupName)
                .Replace("{account}", $"{account.Id}")
                .Replace("{street}", account.Address.Street)
                .Replace("{housenumber}", $"{account.Address.HouseNumber}")
                .Replace("{housenumbersuffix}", account.Address.HouseNumberSuffix)
                .Replace("{city}", account.Address.City.CityNameToFirstUpperLetter())
                .Replace("{outstandingAmount}", $"{paymentArrangementModel.OutstandingAmount:#.00}"), cancellationToken: CancellationToken.None).ConfigureAwait(false);

            var stringBuilder = new StringBuilder();
            stringBuilder.AppendLineWithMarkdown(dialogData.TextLabels.GetValue("PaymentArrangementForInvoiceNumber", _textLabelGroupName));
            foreach (var invoiceId in paymentArrangementModel.Invoices.Select(i => i.Id))
            {
                stringBuilder.AppendLineWithMarkdown(invoiceId.ToString());
            }
            _ = await stepContext.Context.SendActivityAsync(stringBuilder.ToString()).ConfigureAwait(false);

            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("WhereToViewInvoices", _textLabelGroupName)
                    .Replace("{mijnEnecoOpenstaandeNotasContent}", $"{DialogContent.MijnEnecoOpenstaandeNotas}"))
                .ConfigureAwait(false);

            var amountPaid = paymentArrangementModel.TotalPaid > 0 ? "€" + paymentArrangementModel.TotalPaid.ToString() 
                                                                   : dialogData.TextLabels.GetValue("NoAmountYet", _textLabelGroupName);

            _ = await stepContext.Context.SendActivityAsync(
                dialogData.TextLabels.GetValue("CurrentPaidAmount", _textLabelGroupName)
                    .Replace("{amountPaid}", $"{amountPaid}"), 
                    cancellationToken: CancellationToken.None)
                .ConfigureAwait(false);

            var terms = paymentArrangementModel.Terms.Where(t => !t.IsPaidStatus);
            if (terms.Any())
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NumberOfPaymentTerms", _textLabelGroupName)
                        .Replace("{numberOfTerms}", $"{terms.Count()}"), 
                        cancellationToken: CancellationToken.None)
                    .ConfigureAwait(false);

                stringBuilder = new StringBuilder();
                stringBuilder.AppendLineWithMarkdown(dialogData.TextLabels.GetValue("PaymentDay", _textLabelGroupName));
                stringBuilder.AppendLineWithMarkdown($"{string.Join(", ", terms.Select(t => t.DueDate.ToString("dd-MM-yyyy")))}");
                _ = await stepContext.Context.SendActivityAsync(stringBuilder.ToString()).ConfigureAwait(false);
            }
        }
    }
}
