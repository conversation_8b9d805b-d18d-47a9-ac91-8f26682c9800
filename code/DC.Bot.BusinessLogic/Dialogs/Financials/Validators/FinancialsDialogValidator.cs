﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Utilities.Formatters;
using DC.Utilities.Validators;
using Microsoft.Bot.Builder.Dialogs;
using System;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Financials.Validators
{
    public class FinancialsDialogValidator : IFinancialsDialogValidator
    {
        private readonly ISessionManager _sessionManager;

        public FinancialsDialogValidator(ISessionManager sessionManager)
        {
            _sessionManager = sessionManager;
        }

        public async Task<bool> AdjustPaymentDayOfMonthValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(promptContext.Context).ConfigureAwait(false);
            var matches = Regex.Match(promptContext.Context.Activity.Text, @"\d{1,2}", RegexOptions.None, TimeSpan.FromSeconds(2));
            var paymentDay = matches.Captures.FirstOrDefault() != null ? Convert.ToInt32(matches.Captures[0].Value) : 0;

            if (paymentDay >= 1 && paymentDay <= 28)
            {
                dialogData.AdvancePayment.PaymentDayOfMonth = paymentDay;
                return true;
            }

            return await promptContext.ReachedMaxAmountAttempts(dialogData, cancellationToken).ConfigureAwait(false);
        }

        public async Task<bool> IbanValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(promptContext.Context).ConfigureAwait(false);

            if (string.IsNullOrEmpty(promptContext.Context.Activity.Text))
                return false;

            var bankAccount = promptContext.Context.Activity.Text.RemoveWhitespaces().ToUpper();

            if (bankAccount.IsValidIban() && bankAccount.IbanIsFromAllowedCountry())
            {
                dialogData.AdvancePayment.BankAccountNumber = bankAccount;
                return true;
            }

            if (!bankAccount.IsValidIban())
            {
                return await promptContext.ReachedMaxAmountAttempts(dialogData, cancellationToken).ConfigureAwait(false);
            }

            if (!bankAccount.IbanIsFromAllowedCountry())
            {
                var hasMaxAmount = await promptContext.ReachedMaxAmountAttempts(dialogData, cancellationToken, 1).ConfigureAwait(false);
                dialogData.ValidationData.InvalidIBANCountry = hasMaxAmount;
                return hasMaxAmount;
            }

            return false;
        }
    }
}
