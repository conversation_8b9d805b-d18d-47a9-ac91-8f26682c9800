﻿using Microsoft.Bot.Builder.Dialogs;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Financials.Validators
{
    public interface IFinancialsDialogValidator
    {
        Task<bool> AdjustPaymentDayOfMonthValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken);

        Task<bool> IbanValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken);
    }
}