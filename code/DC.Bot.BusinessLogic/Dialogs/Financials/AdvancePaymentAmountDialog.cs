﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Exceptions;
using DC.Domain.Models.Financials;
using DC.Financials.Client.Models;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using TransactionStatus = DC.Bot.BusinessLogic.Enumerations.TransactionStatus;

namespace DC.Bot.BusinessLogic.Dialogs.Financials
{
    public class AdvancePaymentAmountDialog : BaseDialog
    {
        private const int AdvancePaymentLowerLimitAmount = 9;

        private readonly ICustomersService _customersService;
        private readonly IFinancialsService _financialsService;

        /// <summary>
        /// Instantiates an instance of <see cref="AdvancePaymentAdviceDialog"/>
        /// </summary>
        public AdvancePaymentAmountDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            ICustomersService customersService,
            IFinancialsService financialsService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog(nameof(WaterfallDialog),
                new WaterfallStep[]
                {
                    InitTextLabels,
                    TellAdvancePaymentAmountStep,
                    AskForAdjustmentAdvancePaymentAmount,
                    AskForAdjustmentAdvancePaymentAmountAnswerStep,
                    AskForValueAdvancePaymentAmount,
                    AdjustAdvancePaymentAmount,
                    AskForValueAdvancePaymentAmountAgain,
                    AskForValueAdvancePaymentAmountAgainAnswerStep
                }));

            AddDialog(new TextPrompt(nameof(InitTextLabels)));
            AddDialog(new TextPrompt(nameof(TellAdvancePaymentAmountStep)));
            AddDialog(new ChoicePrompt(nameof(AskForAdjustmentAdvancePaymentAmount)));
            AddDialog(new TextPrompt(nameof(AskForAdjustmentAdvancePaymentAmountAnswerStep)));
            AddDialog(new TextPrompt(nameof(AskForValueAdvancePaymentAmount)));
            AddDialog(new TextPrompt(nameof(AdjustAdvancePaymentAmount)));
            AddDialog(new ChoicePrompt(nameof(AskForValueAdvancePaymentAmountAgain), ValidateAskForValueAdvancePaymentAmountAgain));
            AddDialog(new TextPrompt(nameof(AskForValueAdvancePaymentAmountAgainAnswerStep)));

            InitialDialogId = nameof(WaterfallDialog);

            _customersService = customersService;
            _financialsService = financialsService;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Critical Code Smell", "S3776:Cognitive Complexity of methods should not be too high", Justification = "Many possible paths, keep it readable by not splitting in this particular case.")]
        private async Task<DialogTurnResult> TellAdvancePaymentAmountStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            // first step in dialog: set current dialog action
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.GetAdvancePaymentAmount).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            TransactionStatus status = TransactionStatus.TemporaryFailure;
            try
            {
                if (dialogData.IsVerified())
                {
                    var customerModel = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);
                    var activeAccount = dialogData.GetActiveAccountFromCustomerModel(customerModel);

                    if (activeAccount != null)
                    {
                        var advancePaymentModelTask = _financialsService.GetAdvancePaymentAmountAndDayOfPayment(dialogData, activeAccount);
                        var advancePaymentAdviceTask = _financialsService.GetAdvancePaymentAdviceV2(dialogData);
                        var paymentplanTask = _customersService.GetPaymentPlan(dialogData);

                        await Task.WhenAll(advancePaymentModelTask, advancePaymentAdviceTask, paymentplanTask).ConfigureAwait(false);

                        var advancePaymentModel = await advancePaymentModelTask.ConfigureAwait(false);
                        var advancePaymentAdvice = await advancePaymentAdviceTask.ConfigureAwait(false);
                        var paymentplan = await paymentplanTask.ConfigureAwait(false);
                        var nextChargeDate = paymentplan?.NextBillingCycle?.ChargeDate;
                        if (advancePaymentModel == null)
                        {
                            return await SomethingWentWrongContactUs(stepContext, cancellationToken).ConfigureAwait(false);
                        }

                        if (advancePaymentAdvice.AdviceStatus == AdvancePaymentAdviceStatus.YearNoteTooClose)
                        {
                            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("AdviceYearNoteTooClose", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NewAdviceOnYearNote", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(TellAdvancePaymentAmountStep), TransactionStatus.UnhappyCloseToYearNote, cancellationToken).ConfigureAwait(false);
                            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
                        }

                        if (advancePaymentAdvice.AdviceStatus == AdvancePaymentAdviceStatus.PaymentAmountTooLow)
                        {
                            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("AdvicePaymentAmountTooLow", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CannotUpdatePaymentAmountTooLow", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(TellAdvancePaymentAmountStep), TransactionStatus.UnhappyAmountTooLow, cancellationToken).ConfigureAwait(false);
                            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
                        }

                        if (advancePaymentAdvice.AdviceStatus == AdvancePaymentAdviceStatus.ActiveDebtCollection && dialogData.AdvancePayment.AdvancePaymentCurrentAmount.HasValue)
                        {
                            FormattableString formattedAmount = $"{dialogData.AdvancePayment.AdvancePaymentCurrentAmount.Value:N0}";

                            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CurrentAdvancePaymentAmount", _textLabelGroupName)
                                .Replace("{amount}", formattedAmount.ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
                            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("AdviceActiveDebtCollection", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(TellAdvancePaymentAmountStep), TransactionStatus.UnhappyActiveDebt, cancellationToken).ConfigureAwait(false);
                            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
                        }

                        dialogData.AdvancePayment.AdvancePaymentCurrentAmount = advancePaymentModel?.Amount;
                        dialogData.MapAdvancePaymentAdvice(advancePaymentAdvice);
                        dialogData.MapAdvancePaymentModel(advancePaymentModel);

                        // Receiving annual invoice within a month? Then tell the person that the advance payment amount can't
                        // be changed at the moment and end the conversation.
                        if (advancePaymentAdvice?.Limits?.UpdateStatus == AdvancePaymentUpdateStatus.PeriodBlock || nextChargeDate.HasValue && (nextChargeDate.Value - DateTime.Today).TotalDays <= 31)
                        {
                            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("UpdateStatusPeriodBlock", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NewAdviceOnYearNotePeriodBlock", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

                            dialogData.AdvancePayment.WaitingForAnnualInvoice = true;
                            dialogData.AdvancePayment.NextChargeDate = nextChargeDate;

                            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(TellAdvancePaymentAmountStep), TransactionStatus.UnhappyCloseToYearNote, cancellationToken).ConfigureAwait(false);

                            // If this is the case, end all parentdialogs as well. Otherwise NonDirectDebitStep and AdvancePaymentDayStep will be executed and each of these steps will send the same event to Seamly.
                            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
                        }

                        if (advancePaymentAdvice?.Limits == null || advancePaymentAdvice.Limits.MaximumRange == 0 || advancePaymentAdvice.Limits.UpdateStatus == AdvancePaymentUpdateStatus.CannotUpdate)
                        {
                            FormattableString formattedAmount = $"{advancePaymentModel.Amount:N0}";

                            if (advancePaymentAdvice.AdviceStatus == AdvancePaymentAdviceStatus.Ok)
                            {
                                await stepContext.Context.SendActivityAsync(
                                    dialogData.TextLabels.GetValue("CurrentAdvancePaymentAmount", _textLabelGroupName)
                                    .Replace("{amount}", formattedAmount.ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
                                await ShowAdviceData(dialogData, "AdvancePaymentAdviceIsTheSameCannotUpdate",
                                "AdvancePaymentAdviceHigherCannotUpdate", "AdvancePaymentAdviceLowerCannotUpdate",
                                stepContext, cancellationToken).ConfigureAwait(false);
                                await stepContext.Context.SendActivityAsync(
                                    dialogData.TextLabels.GetValue("AdviceCannotUpdateWithoutAmount", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            }
                            else
                            {
                                await stepContext.Context.SendActivityAsync(
                                    dialogData.TextLabels.GetValue("AdviceCannotUpdate", _textLabelGroupName)
                                    .Replace("{amount}", formattedAmount.ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
                            }

                            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("AdviceCannotUpdateContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(TellAdvancePaymentAmountStep), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
                        }

                        await ShowPaymentData(advancePaymentModel, stepContext, cancellationToken).ConfigureAwait(false);
                        status = TransactionStatus.Success;
                    }
                    else
                    {
                        await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindActiveAccount", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindActiveAccountContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        status = TransactionStatus.TemporaryFailure;
                    }
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(TellAdvancePaymentAmountStep));
                return await SomethingWentWrongTryAgain(stepContext, cancellationToken).ConfigureAwait(false);
            }

            if (status == TransactionStatus.TemporaryFailure)
            {
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(TellAdvancePaymentAmountStep), status, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task ShowPaymentData(
            AdvancePayment advancePayment,
            WaterfallStepContext stepContext,
            CancellationToken cancellationToken)
        {
            // When the advance payment amount is less than 9 euros, we need to include a disclaimer to the customer
            if (advancePayment.Amount < AdvancePaymentLowerLimitAmount)
            {
                await ShowAdvancePaymentLessThanNineEuros(stepContext, cancellationToken);
                return;
            }

            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            if (!advancePayment.PaymentWithGiroCard)
            {
                if (advancePayment.IsRecurringPaymentWithPreferedPaymentDay && advancePayment.Preferences.PaymentDayOfMonth.HasValue)
                {
                    await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("PaymentDataDirectDebitWithPreferedPaymentDay", _textLabelGroupName)
                        .Replace("{amount}", $"{advancePayment.Amount:N0}")
                        .Replace("{paymentday}", $"{advancePayment.Preferences.PaymentDayOfMonth.Value}"), cancellationToken: CancellationToken.None).ConfigureAwait(false);
                }
                else if (advancePayment.IsRecurringPaymentWithoutPreferedPaymentDay)
                {
                    await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("PaymentDataDirectDebitWithoutPreferedPaymentDay", _textLabelGroupName)
                        .Replace("{amount}", $"{advancePayment.Amount:N0}"), cancellationToken: CancellationToken.None).ConfigureAwait(false);
                }
            }
            else
            {
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("PaymentDataWithoutDirectDebit", _textLabelGroupName)
                    .Replace("{amount}", $"{advancePayment.Amount:N0}"), cancellationToken: CancellationToken.None).ConfigureAwait(false);
            }
        }

        private async Task ShowAdvancePaymentLessThanNineEuros(
            WaterfallStepContext stepContext,
            CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("PaymentOnlyOnYearNoteDisclaimer", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("PaymentIncreaseDisclaimer", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskForAdjustmentAdvancePaymentAmount(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            await ShowAdviceData(dialogData, "AdvancePaymentAdviceIsTheSame",
                "AdvancePaymentAdviceHigher", "AdvancePaymentAdviceLower",
                stepContext, cancellationToken).ConfigureAwait(false);

            var prompt = dialogData.TextLabels.GetValue("AskToChangeAmount", _textLabelGroupName);
            return await stepContext.PromptAsync(nameof(AskForAdjustmentAdvancePaymentAmount), new PromptOptions
            {
                Prompt = MessageFactory.Text(prompt),
                Choices = SetupYesNoButtons(dialogData),
                RetryPrompt = MessageFactory.Text(prompt)
            }, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskForAdjustmentAdvancePaymentAmountAnswerStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            // question is asked and answered in previous step
            if (stepContext.Result != null)
            {
                // process answer: support choice and textual answers
                bool adjustAdvancePaymentAdvice = stepContext.Result is FoundChoice choice && choice.Value.Contains("Ja", StringComparison.InvariantCultureIgnoreCase) ||
                                        stepContext.Result is string textAnswer && textAnswer.Contains("Ja", StringComparison.InvariantCultureIgnoreCase);

                if (!adjustAdvancePaymentAdvice)
                {
                    var status = TransactionStatus.Success;

                    if (dialogData.AdvancePayment.AdvancePaymentCurrentAmount is < AdvancePaymentLowerLimitAmount)
                    {
                        status = TransactionStatus.SuccessLowerThanNineEuros;
                    }

                    FormattableString formattedAmount = $"{dialogData.AdvancePayment.AdvancePaymentAmount:N0}";
                    await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("AnswerToKeepAmount", _textLabelGroupName)
                        .Replace("{amount}", formattedAmount.ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(AskForAdjustmentAdvancePaymentAmountAnswerStep), status, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
            }

            return await stepContext.NextAsync(dialogData.Verification.CustomerId, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskForValueAdvancePaymentAmount(WaterfallStepContext stepContext,
            CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            return await stepContext.PromptAsync(nameof(AskForValueAdvancePaymentAmount),
                new PromptOptions
                {
                    Prompt = MessageFactory.Text(
                        dialogData.TextLabels.GetValue("AskForValueAdvancePaymentAmountPrompt",
                            _textLabelGroupName)),
                }, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AdjustAdvancePaymentAmount(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            bool validAdvancePaymentAmount = false;
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            var matches = Regex.Match(stepContext.Context.Activity.Text, @"\d{2,4}", RegexOptions.None, TimeSpan.FromSeconds(2));
            var advancePaymentAmount = matches.Captures.FirstOrDefault() != null ? Convert.ToInt32(matches.Captures[0].Value) : 0;

            if (advancePaymentAmount >= dialogData.AdvancePayment.Minimum && advancePaymentAmount <= dialogData.AdvancePayment.Maximum)
            {
                validAdvancePaymentAmount = true;
                dialogData.AdvancePayment.AdvancePaymentAmount = advancePaymentAmount;
            }

            dialogData.AdvancePayment.AdjustAdvancePaymentAmountAttempts++;
            dialogData.Verification.TooManyAttempts = dialogData.AdvancePayment.AdjustAdvancePaymentAmountAttempts >= 3;

            if (dialogData.Verification.TooManyAttempts && !validAdvancePaymentAmount)
            {
                dialogData.Verification.TooManyAttempts = false;

                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("InvalidAmountThreeTimes", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(AdjustAdvancePaymentAmount), TransactionStatus.Unhappy3Strikes, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            if (!dialogData.Verification.TooManyAttempts && !validAdvancePaymentAmount)
            {
                return await stepContext.NextAsync(advancePaymentAmount, cancellationToken).ConfigureAwait(false);
            }

            if (dialogData.AdvancePayment.AdvancePaymentAmount == null)
            {
                return await SomethingWentWrongTryAgain(stepContext, cancellationToken).ConfigureAwait(false);
            }

            var result = await _financialsService.UpdateAdvancePaymentAmount(dialogData, dialogData.AdvancePayment.AdvancePaymentAmount.Value).ConfigureAwait(false);

            return await SetReplyBasedOnAdviceStatus(stepContext, dialogData, result.Status, result.ErrorCodes, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskForValueAdvancePaymentAmountAgain(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            var advancePaymentAmount = Convert.ToInt32(stepContext.Result);


            FormattableString formattedMinimum = $"{dialogData.AdvancePayment.Minimum:N0}";
            FormattableString formattedMaximum = $"{dialogData.AdvancePayment.Maximum:N0}";
            var prompt = dialogData.TextLabels.GetValue("AskForValueAdvancePaymentAmount", _textLabelGroupName)
                .Replace("{minAmount}", formattedMinimum.ToString(new CultureInfo("NL-nl")))
                .Replace("{maxAmount}", formattedMaximum.ToString(new CultureInfo("NL-nl")));

            IList<Choice> choices = null;
            if (advancePaymentAmount > dialogData.AdvancePayment.Maximum)
                choices = SetupAdvancePaymentHigherButtons(dialogData);
            else if (advancePaymentAmount < dialogData.AdvancePayment.Maximum)
                choices = SetupAdvancePaymentLowerButtons(dialogData);

            return await stepContext.PromptAsync(nameof(AskForValueAdvancePaymentAmountAgain), new PromptOptions
            {
                Prompt = MessageFactory.Text(prompt),
                Choices = choices ?? new List<Choice>(),
                RetryPrompt = MessageFactory.Text(prompt),
                Style = ListStyle.SuggestedAction
            }, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskForValueAdvancePaymentAmountAgainAnswerStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            // question is asked and answered in previous step
            if (stepContext.Result != null)
            {
                // process answer: support choice and textual answers
                bool tryAgain = stepContext.Result is FoundChoice choice && choice.Value.Contains("Probeer opnieuw", StringComparison.InvariantCultureIgnoreCase) ||
                                        stepContext.Result is string textAnswer && textAnswer.Contains("Probeer opnieuw", StringComparison.InvariantCultureIgnoreCase);

                // fallback: user has entered an amount, ValidateAskForValueAdvancePaymentAmountAgain converted the input amount as a fake choice
                bool isAmount = stepContext.Result is FoundChoice amountChoice && Regex.IsMatch(amountChoice.Value, @"\d{2,4}", RegexOptions.None, TimeSpan.FromSeconds(2));

                // user wants to try again
                if (tryAgain)
                {
                    stepContext.SetNextStepIndex(Dialogs, nameof(AskForValueAdvancePaymentAmount));
                    return await AskForValueAdvancePaymentAmount(stepContext, cancellationToken).ConfigureAwait(false);
                }
                // user already tried again by entering a new amount
                else if (isAmount)
                {
                    stepContext.SetNextStepIndex(Dialogs, nameof(AdjustAdvancePaymentAmount));
                    return await AdjustAdvancePaymentAmount(stepContext, cancellationToken).ConfigureAwait(false);
                }
                // user insists on the amount outside of the boundaries, end the transaction so the client can transfer the conversation to a real agent
                else
                {
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(AskForValueAdvancePaymentAmountAgainAnswerStep), TransactionStatus.UnhappyRange, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
            }

            stepContext.SetNextStepIndex(Dialogs, nameof(AskForValueAdvancePaymentAmount));
            return await AskForValueAdvancePaymentAmount(stepContext, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> SetReplyBasedOnAdviceStatus(WaterfallStepContext stepContext, DialogData dialogData, AdvancePaymentAdviceStatus result, List<string> errorCodes, CancellationToken cancellationToken)
        {
            var status = TransactionStatus.Success;

            if (result == AdvancePaymentAdviceStatus.Ok)
            {
                if (dialogData.AdvancePayment.AdvancePaymentCurrentAmount is < AdvancePaymentLowerLimitAmount)
                {
                    status = TransactionStatus.SuccessLowerThanNineEuros;
                }

                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SavedAmountStatusOk", _textLabelGroupName)
                    .Replace("{amount}", $"{dialogData.AdvancePayment.AdvancePaymentAmount:N0}"), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (result == AdvancePaymentAdviceStatus.YearlyInvoiceEstimatedOnMeterReadings && dialogData.AdvancePayment.AdvancePaymentCurrentAmount.HasValue)
            {
                status = TransactionStatus.UnhappyEstimatedMr;
                FormattableString formattedAmount = $"{dialogData.AdvancePayment.AdvancePaymentCurrentAmount.Value:N0}";
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SavedAmountStatusYearlyInvoiceEstimatedOnMeterReadings", _textLabelGroupName)
                    .Replace("{amount}", formattedAmount.ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("YearlyInvoiceEstimatedOnMeterReadingsContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (result == AdvancePaymentAdviceStatus.ActiveDebtCollection && dialogData.AdvancePayment.AdvancePaymentCurrentAmount.HasValue)
            {
                status = TransactionStatus.UnhappyActiveDebt;
                FormattableString formattedAmount = $"{dialogData.AdvancePayment.AdvancePaymentCurrentAmount.Value:N0}";
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SavedAmountStatusActiveDebtCollection", _textLabelGroupName)
                    .Replace("{amount}", formattedAmount.ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SavedAmountStatusActiveDebtCollectionAnswer", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (result == AdvancePaymentAdviceStatus.PaymentAmountTooLow)
            {
                status = TransactionStatus.UnhappyAmountTooLow;
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SavedAmountStatusPaymentAmountTooLow", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SavedAmountStatusPaymentAmountTooLowAnswer", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (result == AdvancePaymentAdviceStatus.NoAdviceYet)
            {
                status = GetTransactionStatus(errorCodes);

                //Different Text: Het bedrag dat je hebt ingevuld klopt niet of is niet tussen € XX en € XXXX.
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SavedAmountSomethingWentWrong", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (result == AdvancePaymentAdviceStatus.NotAvailable)
            {
                status = GetTransactionStatus(errorCodes);

                FormattableString formattedAmount = $"{dialogData.AdvancePayment.AdvancePaymentCurrentAmount.Value:N0}";
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SavedAmountStatusNotAvailable", _textLabelGroupName)
                    .Replace("{amount}", formattedAmount.ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SavedAmountStatusNotAvailableAnswer", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else //default
            {
                status = TransactionStatus.TemporaryFailure;
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SavedAmountSomethingWentWrong", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(SetReplyBasedOnAdviceStatus), status, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> SomethingWentWrongTryAgain(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("TryAgianSomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(SomethingWentWrongTryAgain), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> SomethingWentWrongContactUs(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUsSomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(SomethingWentWrongContactUs), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Accepts both the prompt choices as well as a number (AdvancePaymentAmount) input.
        /// </summary>
        /// <param name="promptContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private static Task<bool> ValidateAskForValueAdvancePaymentAmountAgain(PromptValidatorContext<FoundChoice> promptContext, CancellationToken cancellationToken)
        {
            if (!promptContext.Recognized.Succeeded)
            {
                if (Regex.IsMatch(promptContext.Context.Activity.Text, @"\d{2,4}", RegexOptions.None, TimeSpan.FromSeconds(2)))
                {
                    promptContext.Recognized.Succeeded = true;
                    promptContext.Recognized.Value = new FoundChoice { Value = promptContext.Context.Activity.Text };
                    return Task.FromResult(true);
                }
                return Task.FromResult(false);
            }
            return Task.FromResult(true);
        }

        private async Task ShowAdviceData(
            DialogData dialogData,
            string textLabelAdviceIsTheSame,
            string textLabelAdviceHigher,
            string textLabelAdviceLower,
            WaterfallStepContext stepContext,
            CancellationToken cancellationToken)
        {
            if (!dialogData.AdvancePayment.AdvancePaymentAdviceStatus.AdviceIsNotAvailable())
            {
                if (dialogData.AdvancePayment.Advice == dialogData.AdvancePayment.AdvancePaymentAmount)
                {
                    await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue(textLabelAdviceIsTheSame, _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else if (dialogData.AdvancePayment.Advice > dialogData.AdvancePayment.AdvancePaymentAmount)
                {
                    FormattableString formattedAmount = $"{dialogData.AdvancePayment.Advice:N0}";
                    await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue(textLabelAdviceHigher, _textLabelGroupName)
                    .Replace("{amount}", formattedAmount.ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else if (dialogData.AdvancePayment.Advice > 0 && dialogData.AdvancePayment.Advice < dialogData.AdvancePayment.AdvancePaymentAmount)
                {
                    FormattableString formattedAmount = $"{dialogData.AdvancePayment.Advice:N0}";
                    await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue(textLabelAdviceLower, _textLabelGroupName)
                    .Replace("{amount}", formattedAmount.ToString(new CultureInfo("NL-nl"))), cancellationToken: cancellationToken).ConfigureAwait(false);
                }
            }
        }

        private List<Choice> SetupYesNoButtons(DialogData dialogData)
        {
            return new List<Choice> {
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("YesChoice", _textLabelGroupName),
                    Synonyms = new List<string> { "Ja" }
                },
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("NoChoice", _textLabelGroupName),
                    Synonyms = new List<string> { "Nee", "Neen" }
                }

            };
        }

        private List<Choice> SetupAdvancePaymentHigherButtons(DialogData dialogData)
        {
            return new List<Choice> {
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("TryAgainChoiceHigherAmount", _textLabelGroupName),
                    Synonyms = new List<string> { "Probeer opnieuw", "Try again" }
                },
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("HigherAmountChoice", _textLabelGroupName),
                    Synonyms = new List<string> { "Hoger termijnbedrag kiezen", "ignore" }
                }

            };
        }

        private List<Choice> SetupAdvancePaymentLowerButtons(DialogData dialogData)
        {
            return new List<Choice> {
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("TryAgainChoiceLowerAmount", _textLabelGroupName),
                    Synonyms = new List<string> { "Probeer opnieuw", "Try again" }
                },
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("LowerAmountChoice", _textLabelGroupName),
                    Synonyms = new List<string> { "Lager termijnbedrag kiezen", "ignore" }
                }

            };
        }

        private static TransactionStatus GetTransactionStatus(List<string> errorCodes)
        {
            var errorCode = errorCodes?.FirstOrDefault(code => !string.IsNullOrEmpty(code))?.ToLowerInvariant();

            return errorCode switch
            {
                _ when errorCode?.Contains("c3a0befb-9c93-4af0-bc73-fea11e9e4309") == true => TransactionStatus.UnhappyFalseAmount,
                _ when errorCode?.Contains("399ed593-f66b-4db3-b320-5a448d637e05") == true => TransactionStatus.UnhappyMinimumAmount,
                _ when errorCode?.Contains("dbd61c7e-6519-4e43-a485-265b492107d0") == true => TransactionStatus.UnhappyIncorrectAmount,
                _ when errorCode?.Contains("5bdc3e53-c822-42c2-b27e-3b3eac4f2f93") == true => TransactionStatus.UnhappyNextChargeDateUnknown,
                _ when errorCode?.Contains("d94b9a81-a295-45af-9c5d-e4bdc5250fe6") == true => TransactionStatus.UnhappyPaymentAmountNotDetermined,
                _ when errorCode?.Contains("d338a243-965e-4f9b-8b74-d0ce56b09df2") == true => TransactionStatus.UnhappyTariffStructure,
                _ when errorCode?.Contains("a230fe21-f67c-4985-af09-e667fbd5ba4b") == true => TransactionStatus.Unhappy,
                _ when errorCode?.Contains("d65f09d6-4f5e-4fe1-8a9b-a1fe93a0e263") == true => TransactionStatus.UnhappyNoCustomerNumber,
                _ when errorCode?.Contains("e62c1c35-c5e9-4c72-8ea1-84b85751cf69") == true => TransactionStatus.UnhappyTariffStructureCorrection,
                _ when errorCode?.Contains("0cbe68e4-a7e8-40cb-95b4-5f763ae1319e") == true => TransactionStatus.UnhappyNoAgreement,
                _ when errorCode?.Contains("108c205f-85e9-47c8-be73-81e02b318453") == true => TransactionStatus.UnhappyMultipleAgreements,
                _ => TransactionStatus.Unhappy
            };
        }
    }
}
