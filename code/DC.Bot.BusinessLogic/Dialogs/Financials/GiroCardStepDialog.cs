﻿using DC.Bot.BusinessLogic.Dialogs.Customers;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Models.Financials;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Financials
{
    public class GiroCardStepDialog : BaseDialog
    {
        private readonly IFinancialsService _financialsService;
        private readonly ICustomersService _customersService;

        /// <summary>
        /// </summary>
        /// <param name="service"></param>
        /// <param name="loggerFactory"></param>
        /// <param name="sessionManager"></param>
        /// <param name="validators"></param>
        public GiroCardStepDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            IDialogValidators validators,
            IFinancialsService financialsService,
            ICustomersService customersService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog(nameof(WaterfallDialog),
                new WaterfallStep[]
                {
                    InitTextLabels,
                    AskIfCustomerWantsToChangeToRecurringPayment,
                    AskIfCustomerWantsToChangeToRecurringPaymentAnswer,
                    AskForBankAccountNumber,
                    AskForPaymentDay,
                    UpdateDirectDebitStatus
                }));

            AddDialog(new TextPrompt(nameof(InitTextLabels)));
            AddDialog(new ChoicePrompt(nameof(AskIfCustomerWantsToChangeToRecurringPayment)));
            AddDialog(new TextPrompt(nameof(AskIfCustomerWantsToChangeToRecurringPaymentAnswer)));
            AddDialog(new TextPrompt(nameof(AskForBankAccountNumber)));
            AddDialog(new TextPrompt(nameof(AskForPaymentDay)));
            AddDialog(new TextPrompt(nameof(UpdateDirectDebitStatus)));

            InitialDialogId = nameof(WaterfallDialog);

            _financialsService = financialsService;
            _customersService = customersService;
        }

        private async Task<DialogTurnResult> AskIfCustomerWantsToChangeToRecurringPayment(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.NonDirectDebit).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            if (dialogData.IsVerified())
            {
                var customerModel = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);
                var activeAccount = dialogData.GetActiveAccountFromCustomerModel(customerModel);

                if (activeAccount != null)
                {
                    var advancePaymentModel = await _financialsService.GetAdvancePaymentAmountAndDayOfPayment(dialogData, activeAccount).ConfigureAwait(false);

                    if (advancePaymentModel == null || advancePaymentModel.Amount == 0)
                    {
                        return await SomethingWentWrongContactUs(stepContext, cancellationToken).ConfigureAwait(false);
                    }

                    dialogData.MapAdvancePaymentModel(advancePaymentModel);
                }
                else
                {
                    await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindData", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                }
            }
            else
            {
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(GiroCardStepDialog), nameof(AskIfCustomerWantsToChangeToRecurringPayment), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
                return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
            }

            if (dialogData.AdvancePayment.PaymentWithGiroCard == false || dialogData.AdvancePayment.WaitingForAnnualInvoice)
            {
                var dialogTxt = !dialogData.AdvancePayment.PaymentDayOfMonth.HasValue ?
                    dialogData.TextLabels.GetValue("PaymentWithGiroCard", _textLabelGroupName) :
                    dialogData.TextLabels.GetValue("PaymentWithGiroCardWithPaymentDay", _textLabelGroupName)
                    .Replace("{paymentday}", $"{dialogData.AdvancePayment.PaymentDayOfMonth.Value}");
                await stepContext.Context.SendActivityAsync(dialogTxt, cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(GiroCardStepDialog), nameof(AskIfCustomerWantsToChangeToRecurringPayment), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CurrentPaymentWithGiroCard", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            var prompt = dialogData.TextLabels.GetValue("AskPaymentDirectDebit", _textLabelGroupName);
            return await stepContext.PromptAsync(nameof(AskIfCustomerWantsToChangeToRecurringPayment), new PromptOptions
            {
                Prompt = MessageFactory.Text(prompt),
                RetryPrompt = MessageFactory.Text(prompt),
                Choices = SetupButtons(dialogData),
            }, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskIfCustomerWantsToChangeToRecurringPaymentAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            // process answer: support choice and textual answers
            var changeToAutomaticPayment = stepContext.Result is FoundChoice choice && choice.Value.Contains("Ja", StringComparison.InvariantCultureIgnoreCase) ||
                                          stepContext.Result is string textAnswer && textAnswer.Contains("Ja", StringComparison.InvariantCultureIgnoreCase);

            if (!changeToAutomaticPayment)
            {
                // If "no", then end this dialog and goto next dialog.
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("KeepPaymentWithGiroCard", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(GiroCardStepDialog), nameof(AskIfCustomerWantsToChangeToRecurringPaymentAnswer), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            dialogData.AdvancePayment.AskToUpdatePaymentDay = true;
            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskForBankAccountNumber(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.NonDirectDebit).ConfigureAwait(false);

            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            dialogData.AdvancePayment.ChangeIBANTriggerdAsSeparateStep = true;

            return await stepContext.BeginDialogAsync(nameof(ChangeIbanDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskForPaymentDay(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            dialogData.AdvancePayment.ChangePaymentDayTriggerdAsSeparateStep = true;

            return await stepContext.BeginDialogAsync(nameof(AdvancePaymentDayDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Minor Code Smell", "S2221:\"Exception\" should not be caught when not required by called methods", Justification = "Must catch all to prevent bot from crashing")]
        private async Task<DialogTurnResult> UpdateDirectDebitStatus(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            try
            {
                var updateStatus = await _financialsService.UpdatePaymentToDirectDebit(dialogData, dialogData.AdvancePayment.BankAccountNumber, dialogData.AdvancePayment.PaymentDayOfMonth.Value).ConfigureAwait(false);

                if (updateStatus == AdvancePaymentAdviceStatus.Ok)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ChangedToPaymentDirectDebit", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(GiroCardStepDialog), nameof(UpdateDirectDebitStatus), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else if (updateStatus == AdvancePaymentAdviceStatus.ActiveDebtCollection)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ActiveDebtCollection", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(GiroCardStepDialog), nameof(UpdateDirectDebitStatus), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else if (updateStatus == AdvancePaymentAdviceStatus.NotAvailable)
                {
                    return await SomethingWentWrongTryAgain(stepContext, cancellationToken).ConfigureAwait(false);
                }

                return await SomethingWentWrongContactUs(stepContext, cancellationToken).ConfigureAwait(false);
            }
            // Updating financial preferences could throw different MVS errors for various reasons. End the conversation in that case.
            catch (Exception ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(UpdateDirectDebitStatus), "Failed to Set DirectDebit");
                return await SomethingWentWrongContactUs(stepContext, cancellationToken).ConfigureAwait(false);
            }
        }

        private async Task<DialogTurnResult> SomethingWentWrongTryAgain(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("TryAgianSomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(GiroCardStepDialog), nameof(SomethingWentWrongTryAgain), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> SomethingWentWrongContactUs(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUsSomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(GiroCardStepDialog), nameof(SomethingWentWrongContactUs), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
        }

        private List<Choice> SetupButtons(DialogData dialogData)
        {
            return new List<Choice> {
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("YesChoice", _textLabelGroupName),
                    Synonyms = new List<string> { "Ja" }
                },
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("NoChoice", _textLabelGroupName),
                    Synonyms = new List<string> { "Nee", "Neen" }
                }

            };
        }
    }
}