﻿using System;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Domain.Exceptions;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.Financials;
using DC.Financials.Client.Models;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TransactionStatus = DC.Bot.BusinessLogic.Enumerations.TransactionStatus;

namespace DC.Bot.BusinessLogic.Dialogs.Financials
{
    public class AdvancePaymentAdviceDialog : BaseDialog
    {

        private readonly ICustomersService _customersService;
        private readonly IFinancialsService _financialsService;

        /// <summary>
        /// </summary>
        /// <param name="service"></param>
        /// <param name="loggerFactory"></param>
        /// <param name="sessionManager"></param>
        /// <param name="validators"></param>
        public AdvancePaymentAdviceDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            ICustomersService customersService,
            IFinancialsService financialsService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog(nameof(WaterfallDialog),
                new WaterfallStep[]
                {
                    InitTextLabels,
                    TellAdvancePaymentAdviceStep
                }));

            AddDialog(new TextPrompt(nameof(InitTextLabels)));
            AddDialog(new TextPrompt(nameof(TellAdvancePaymentAdviceStep)));

            InitialDialogId = nameof(WaterfallDialog);

            _customersService = customersService;
            _financialsService = financialsService;
        }

        private async Task<DialogTurnResult> TellAdvancePaymentAdviceStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            // first step in dialog: set current dialog action
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.GetAdvancePaymentAdvice).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            TransactionStatus status = TransactionStatus.TemporaryFailure;
            try
            {
                if (dialogData.IsVerified())
                {
                    if (!dialogData.HasActiveAccount())
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindActiveAccount", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                        status = TransactionStatus.TemporaryFailure;
                        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAdviceDialog), nameof(TellAdvancePaymentAdviceStep), status, cancellationToken).ConfigureAwait(false);
                        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                    }

                    var advice = await _financialsService.GetAdvancePaymentAdviceV2(dialogData).ConfigureAwait(false);
                    return await ShowAdvice(advice, status, stepContext, cancellationToken).ConfigureAwait(false);
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(TellAdvancePaymentAdviceStep));
                return await SomethingWentWrongTryAgain(stepContext, cancellationToken).ConfigureAwait(false);
            }

            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(TellAdvancePaymentAdviceStep), status, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Critical Code Smell", "S3776:Cognitive Complexity of methods should not be too high", Justification = "Many possible paths, keep it readable by not splitting in this particular case.")]
        private async Task<DialogTurnResult> ShowAdvice(AdvancePaymentAdviceV2Response advice, TransactionStatus status, WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            dialogData.AdvancePayment.AdvancePaymentAdviceStatus = advice.AdviceStatus;
            status = status.GetAdvancePaymentAdviceTransactionStatus(advice.AdviceStatus);
            if (status == TransactionStatus.Success)
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("AdviceAvailable", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                return await stepContext.BeginDialogAsync(nameof(AdvancePaymentAmountDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
            }

            if (advice.AdviceStatus is AdvancePaymentAdviceStatus.NewCustomer or AdvancePaymentAdviceStatus.NoAdviceYet)
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoAdviceYet", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (advice.AdviceStatus == AdvancePaymentAdviceStatus.NotAvailable
                     &&!string.IsNullOrWhiteSpace(advice.MvsErrorCode)
                     && advice.MvsErrorCode.Equals("MVS-17116", StringComparison.OrdinalIgnoreCase))
            {
                // MVS-17116 indicates the customer has outstanding dept with an unauthorized collection step
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("AdviceNotAvailable", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(ShowAdvice), TransactionStatus.UnhappyOutstandingDebt, cancellationToken).ConfigureAwait(false);
                return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
            }
            else if (advice.AdviceStatus is AdvancePaymentAdviceStatus.HasWarmth or AdvancePaymentAdviceStatus.HasRedelivery)
            {
                var agreements = await _customersService.GetAgreements(dialogData, true).ConfigureAwait(false);
                var hasRedelivery = agreements?.Any(a => a.Connection?.HasReturnDelivery == true && a.Connection?.Meter?.HasRedeliveryInMeter == true) == true;
                var hasWarmth = agreements?.Any(a => a.Connection?.UtilityType == UtilityType.Heat) == true;
                if (hasRedelivery && hasWarmth)
                {
                    status = TransactionStatus.UnhappyWarmthAndRedelivery;
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("WarmthAndRedelivery", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else if (hasRedelivery || advice.AdviceStatus is AdvancePaymentAdviceStatus.HasRedelivery)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("Redelivery", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else if (hasWarmth || advice.AdviceStatus is AdvancePaymentAdviceStatus.HasWarmth)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("Warmth", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                }
            }
            else if (advice.AdviceStatus is AdvancePaymentAdviceStatus.YearNoteTooClose)
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("YearNoteTooClose", _textLabelGroupName).Replace("{yearnotedate}", $"{advice.YearNoteDate.Value:dd-MM-yyyy}"), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (advice.AdviceStatus is
                     AdvancePaymentAdviceStatus.UpdateAdvice or
                     AdvancePaymentAdviceStatus.AdviceTooOld)
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("UpdateAdvice", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (advice.AdviceStatus.AdviceIsNotAvailable())
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("AdviceNotAvailable", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAmountDialog), nameof(ShowAdvice), status, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> SomethingWentWrongTryAgain(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUsSomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentAdviceDialog), nameof(SomethingWentWrongTryAgain), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }
    }
}
