﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Exceptions;
using DC.Domain.Models.Financials;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TransactionStatus = DC.Bot.BusinessLogic.Enumerations.TransactionStatus;

namespace DC.Bot.BusinessLogic.Dialogs.Financials
{
    public class AdvancePaymentDayDialog : BaseDialog
    {
        private readonly IFinancialsService _financialsService;
        private readonly ICustomersService _customersService;

        /// <summary>
        /// </summary>
        /// <param name="service"></param>
        /// <param name="loggerFactory"></param>
        /// <param name="sessionManager"></param>
        /// <param name="validators"></param>
        public AdvancePaymentDayDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            IDialogValidators validators,
            ICustomersService customersService,
            IFinancialsService financialsService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog(nameof(WaterfallDialog),
                new WaterfallStep[]
                {
                    InitTextLabels,
                    CheckPaymentDayStatus,
                    AskIfCustomerWantsToChangePaymentDayOfMonth,
                    AskIfCustomerWantsToChangePaymentDayOfMonthAnswer,
                    AskForPaymenyDayOfMonth,
                    AdjustPaymentDayOfMonth,
                    FinishConversation,
                    ReturnToParentStep
                }));

            AddDialog(new TextPrompt(nameof(InitTextLabels)));
            AddDialog(new ChoicePrompt(nameof(CheckPaymentDayStatus)));
            AddDialog(new ChoicePrompt(nameof(AskIfCustomerWantsToChangePaymentDayOfMonth)));
            AddDialog(new ChoicePrompt(nameof(AskIfCustomerWantsToChangePaymentDayOfMonthAnswer)));
            AddDialog(new TextPrompt(nameof(AskForPaymenyDayOfMonth), validators.FinancialsDialogValidator.AdjustPaymentDayOfMonthValidator));
            AddDialog(new TextPrompt(nameof(AdjustPaymentDayOfMonth)));
            AddDialog(new TextPrompt(nameof(FinishConversation)));
            AddDialog(new TextPrompt(nameof(ReturnToParentStep)));

            InitialDialogId = nameof(WaterfallDialog);

            _financialsService = financialsService;
            _customersService = customersService;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Critical Code Smell", "S3776:Cognitive Complexity of methods should not be too high", Justification = "Many possible paths, keep it readable by not splitting in this particular case.")]
        private async Task<DialogTurnResult> CheckPaymentDayStatus(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            // first step in dialog: set current dialog action
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.GetAdvancePaymentDay).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            try
            {
                if (dialogData.IsVerified())
                {
                    var customerModel = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);
                    var activeAccount = dialogData.GetActiveAccountFromCustomerModel(customerModel);

                    if (activeAccount != null)
                    {
                        var advancePaymentModel = await _financialsService.GetAdvancePaymentAmountAndDayOfPayment(dialogData, activeAccount).ConfigureAwait(false);

                        if (advancePaymentModel == null)
                        {
                            return await SomethingWentWrongStopTransaction(stepContext, cancellationToken).ConfigureAwait(false);
                        }

                        dialogData.MapAdvancePaymentModel(advancePaymentModel);

                        if (!advancePaymentModel.PaymentWithGiroCard)
                        {
                            if (advancePaymentModel.IsRecurringPaymentWithPreferedPaymentDay && advancePaymentModel.Preferences.PaymentDayOfMonth.HasValue)
                            {
                                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("PayByDirectDebitPaymentDay", _textLabelGroupName).Replace("{paymentday}", $"{advancePaymentModel.Preferences.PaymentDayOfMonth.Value}"), cancellationToken: cancellationToken).ConfigureAwait(false);
                            }
                            else if (advancePaymentModel.IsRecurringPaymentWithoutPreferedPaymentDay)
                            {
                                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("PayByDirectDebit", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            }
                        }
                        else
                        {
                            if (dialogData.AdvancePayment.ChangePaymentDayTriggerdAsSeparateStep)
                            {
                                stepContext.SetNextStepIndex(Dialogs, nameof(AskForPaymenyDayOfMonth));
                                return await AskForPaymenyDayOfMonth(stepContext, cancellationToken).ConfigureAwait(false);
                            }

                            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("PayByGiroCard", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ChooseDirectDebit", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentDayDialog), nameof(CheckPaymentDayStatus), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                        }
                    }
                }
                else
                {
                    return await SomethingWentWrongStopTransaction(stepContext, cancellationToken).ConfigureAwait(false);
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(CheckPaymentDayStatus));
                return await SomethingWentWrongStopTransaction(stepContext, cancellationToken).ConfigureAwait(false);
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskIfCustomerWantsToChangePaymentDayOfMonth(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            if (!dialogData.IsVerified())
            {
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentDayDialog), nameof(AskIfCustomerWantsToChangePaymentDayOfMonth), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
            }

            var prompt = dialogData.TextLabels.GetValue("AskFixedDay", _textLabelGroupName);
            if (dialogData.AdvancePayment.IsRecurringPaymentWithPreferedPaymentDay.HasValue && dialogData.AdvancePayment.IsRecurringPaymentWithPreferedPaymentDay.Value)
                prompt = dialogData.TextLabels.GetValue("AskOtherDay", _textLabelGroupName);

            return await stepContext.PromptAsync(nameof(AskIfCustomerWantsToChangePaymentDayOfMonth), new PromptOptions
            {
                Prompt = MessageFactory.Text(prompt),
                RetryPrompt = MessageFactory.Text(prompt),
                Choices = SetupYesNoButtons(dialogData)
            }, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskIfCustomerWantsToChangePaymentDayOfMonthAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            // process answer: support choice and textual answers
            var adjustPaymentDayOfMonth = stepContext.Result is FoundChoice choice && choice.Value.Contains("Ja", StringComparison.InvariantCultureIgnoreCase) ||
                                          stepContext.Result is string textAnswer && textAnswer.Contains("Ja", StringComparison.InvariantCultureIgnoreCase);

            if (!adjustPaymentDayOfMonth)
            {
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("FineEverythingStaysTheSame", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentDayDialog), nameof(AskIfCustomerWantsToChangePaymentDayOfMonthAnswer), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
            }

            // If customer still needs to verify the cusomterId, then go to the (next) verification step.
            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskForPaymenyDayOfMonth(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.GetAdvancePaymentDay).ConfigureAwait(false);

            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("AskDayOfMonth", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            return await stepContext.PromptAsync(nameof(AskForPaymenyDayOfMonth), new PromptOptions
            {
                Prompt = MessageFactory.Text(dialogData.TextLabels.GetValue("ChooseFromFirstTwentyEightDaysOfMonth", _textLabelGroupName)),
                RetryPrompt = MessageFactory.Text(dialogData.TextLabels.GetValue("IncorrectDayNumber", _textLabelGroupName))
            }, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AdjustPaymentDayOfMonth(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            try
            {
                var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

                if (dialogData.Verification.TooManyAttempts)
                {
                    dialogData.Verification.TooManyAttempts = false;

                    await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ThreeTimesWrongNumber", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentDayDialog), nameof(AdjustPaymentDayOfMonth), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                    return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
                }

                if (dialogData.AdvancePayment.PaymentDayOfMonth == null)
                {
                    return await SomethingWentWrongStopTransaction(stepContext, cancellationToken).ConfigureAwait(false);
                }

                if (dialogData.AdvancePayment.ChangePaymentDayTriggerdAsSeparateStep)
                {
                    stepContext.SetNextStepIndex(Dialogs, nameof(ReturnToParentStep));
                    return await ReturnToParentStep(stepContext, cancellationToken).ConfigureAwait(false);
                }

                var response = await _financialsService.UpdatePaymentDayOfMonth(dialogData, dialogData.AdvancePayment.PaymentDayOfMonth.Value).ConfigureAwait(false);
                dialogData.AdvancePayment.AdvancePaymentAdviceStatus = response;
                return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(AdjustPaymentDayOfMonth));
                return await SomethingWentWrongStopTransaction(stepContext, cancellationToken).ConfigureAwait(false);
            }
        }

        private async Task<DialogTurnResult> FinishConversation(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            if (dialogData.AdvancePayment.AdvancePaymentAdviceStatus == AdvancePaymentAdviceStatus.Ok)
            {
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("PaymentDayChanged", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (dialogData.AdvancePayment.AdvancePaymentAdviceStatus == AdvancePaymentAdviceStatus.PaymentMethodShouldBeDirectDebit)
            {
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("PayByGiroCardCanChooseDirectDebit", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (dialogData.AdvancePayment.AdvancePaymentAdviceStatus == AdvancePaymentAdviceStatus.ActiveDebtCollection)
            {
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("PaymentArrangementPleaseContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            else if (dialogData.AdvancePayment.AdvancePaymentAdviceStatus == AdvancePaymentAdviceStatus.PaymentMethodShouldNotBeDirectDebit || dialogData.AdvancePayment.AdvancePaymentAdviceStatus == AdvancePaymentAdviceStatus.IbanMissingOrIncorrect)
            {
                return await SomethingWentWrongStopTransaction(stepContext, cancellationToken).ConfigureAwait(false);
            }
            else if (dialogData.AdvancePayment.AdvancePaymentAdviceStatus == AdvancePaymentAdviceStatus.Default)
            {
                return await SomethingWentWrongTryAgain(stepContext, cancellationToken).ConfigureAwait(false);
            }

            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentDayDialog), nameof(FinishConversation), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
        }
        private async Task<DialogTurnResult> ReturnToParentStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> SomethingWentWrongStopTransaction(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("OopsPleaseContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentDayDialog), nameof(SomethingWentWrongStopTransaction), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> SomethingWentWrongTryAgain(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("OopsTryAgainOrContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(AdvancePaymentDayDialog), nameof(SomethingWentWrongTryAgain), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
        }

        private List<Choice> SetupYesNoButtons(DialogData dialogData)
        {
            return new List<Choice>{
            new Choice
            {
                Value = dialogData.TextLabels.GetValue("YesChoice", _textLabelGroupName),
                Synonyms = new List<string> { "Ja" }
            },
            new Choice
            {
                Value = dialogData.TextLabels.GetValue("NoChoice", _textLabelGroupName),
                Synonyms = new List<string> { "Nee", "Neen" }
            } };
        }
    }
}