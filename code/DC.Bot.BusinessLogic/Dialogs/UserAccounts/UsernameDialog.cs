﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Domain.Exceptions;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
namespace DC.Bot.BusinessLogic.Dialogs.UserAccounts
{
    public class UsernameDialog : BaseDialog
    {
        private readonly IUserAccountsService _userAccountsService;
        public UsernameDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            IUserAccountsService userAccountsService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
                new WaterfallStep[]
                {
                    InitTextLabels,
                    UsernameStep
                }));

            InitialDialogId = nameof(WaterfallDialog);

            _userAccountsService = userAccountsService;
        }

        /// <summary>
        /// Returns username based on the emailadress
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>                
        /// <returns></returns>
        private async Task<DialogTurnResult> UsernameStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.GetUserName).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            TransactionStatus status;

            try
            {
                if (dialogData.IsVerified())
                {

                    var activeAccountId = dialogData.SelectedAccount.AccountId;
                    if (activeAccountId != 0)
                    {
                        var validationResponse = await _userAccountsService.GetUserName(dialogData, activeAccountId).ConfigureAwait(false);
                        if (validationResponse != null)
                        {
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SendMailUsername", _textLabelGroupName).Replace("{username}", validationResponse.Username), cancellationToken: cancellationToken).ConfigureAwait(false);
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SendMailCheckSpam", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SendMailForgotPassword", _textLabelGroupName).Replace("{MijnEnecoWachtwoordVergeten}", DialogContent.MijnEnecoWachtwoordVergeten), cancellationToken: cancellationToken).ConfigureAwait(false);
                            status = TransactionStatus.Success;
                        }
                        else
                        {
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("UsernameNotFound", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

                            status = TransactionStatus.TemporaryFailure;
                        }
                    }
                    else
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindActiveAccount", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        status = TransactionStatus.TemporaryFailure;
                    }
                }
                else
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindData", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    status = TransactionStatus.TemporaryFailure;
                }
            }
            catch (DataNotFoundException)
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

                status = TransactionStatus.TemporaryFailure;
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(UsernameStep));

                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindData", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                status = TransactionStatus.TemporaryFailure;
            }
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(UsernameDialog), nameof(UsernameStep), status, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }
    }
}
