﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Storage.Client.Models;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs;

public abstract class BaseDialog : ComponentDialog
{
    protected const string TextLabelGroupNameGeneral = "Bot_GeneralTextLabels";
    protected readonly string _textLabelGroupName;

    /// <summary>
    /// Logger for all derived dialogs
    /// </summary>
    protected readonly ILogger _logger;

    /// <summary>
    /// Session manager
    /// </summary>
    protected readonly ISessionManager _sessionManager;

    /// <summary>
    /// The logging Service for logging exceptions
    /// </summary>
    protected readonly ILoggingService _loggingService;

    /// <summary>
    /// Storage service
    /// </summary>
    protected readonly IStorageService _storageService;

    protected BaseDialog(ILoggerFactory loggerFactory, ISessionManager sessionManager, ILoggingService loggingService)
    {
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _sessionManager = sessionManager;
        _loggingService = loggingService;
    }

    protected BaseDialog(ILoggerFactory loggerFactory,
        ISessionManager sessionManager,
        ILoggingService loggingService,
        IStorageService storageService)
    {
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _sessionManager = sessionManager;
        _loggingService = loggingService;
        _storageService = storageService;
        _textLabelGroupName = $"Bot_{GetType().Name}";
    }

    public async Task<DialogTurnResult> InitTextLabels(WaterfallStepContext stepContext,
        CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
        
        var labelGroups = new[] { _textLabelGroupName, TextLabelGroupNameGeneral };
        var labelTasks = labelGroups.Select(groupName => GetTextLabels(dialogData, groupName));
        
        var results = await Task.WhenAll(labelTasks).ConfigureAwait(false);

        dialogData.TextLabels.TextLabels.AddRange(results.SelectMany(x => x.TextLabels));
        
        return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// Adds the text labels.
    /// </summary>
    /// <param name="dialogData">The dialog data.</param>
    /// <param name="groupName">The group name.</param>
    /// <returns>A Task.</returns>
    private async Task<TextLabelCollection> GetTextLabels(DialogData dialogData, string groupName)
    {
        if (!dialogData.HasTextLabels(groupName))
        {
            return await _storageService.GetTextLabels(dialogData.Customer.Label, groupName, null)
                .ConfigureAwait(false);
        }

        return new TextLabelCollection([]);
    }
}