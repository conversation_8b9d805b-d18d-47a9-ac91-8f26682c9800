﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.Session;
using DC.Domain.Exceptions;
using DC.Domain.Models.Agreements;
using DC.Domain.Models.Products;
using DC.Domain.Models.Usages;
using DC.Usages.Client.Models;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;

namespace DC.Bot.BusinessLogic.Dialogs.Usages;

public class SaveReadingPersonalDialog : BaseDialog
{
    private readonly IUsagesService _usagesService;
    private readonly ICustomersService _customersService;
    private readonly TimeProvider _timeProvider;

    public SaveReadingPersonalDialog(
        ILoggerFactory loggerFactory,
        ISessionManager sessionManager,
        ILoggingService loggingService,
        IUsagesService usagesService,
        IStorageService storageService,
        ICustomersService customersService,
        TimeProvider timeProvider)
        : base(
            loggerFactory,
            sessionManager,
            loggingService,
            storageService)
    {
        _usagesService = usagesService;
        _customersService = customersService;
        _timeProvider = timeProvider;

        InitialDialogId = nameof(WaterfallDialog);

        // Add prompts
        AddDialog(new ChoicePrompt(nameof(HandleOutstandingSmartMeterReadingRequest)));
        AddDialog(new ChoicePrompt(nameof(HandleOutstandingManualMeterReadingRequest)));
        AddDialog(new ChoicePrompt(nameof(HandleNoOutstandingReadingRequest)));
        AddDialog(new ChoicePrompt(nameof(HandleRecentPeriodicReading)));
        AddDialog(new ChoicePrompt(nameof(HandleProvideReadPermissionChoice)));
        AddDialog(new ChoicePrompt(nameof(PostReferToGridManager)));

        // Add waterfall dialogues
        AddDialog(
            new WaterfallDialog(
                nameof(WaterfallDialog),
                [
                    InitTextLabels,
                    InitializeDialog,
                    AnalyzeCustomerMeterSituation,
                    HandleCustomerChoice,
                    PostReferToGridManager,
                    ReferToWebFlow
                ]));
    }

    #region Main dialogs

    /// <summary>
    /// Initialize the dialog, ensuring the customer is verified and active
    /// </summary>
    private async Task<DialogTurnResult> InitializeDialog(
        WaterfallStepContext stepContext,
        CancellationToken cancellationToken)
    {
        await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.SaveReadingPersonal);
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context);

        // Ensure the customer is verified and has an active account
        if (!dialogData.IsVerified() || !dialogData.HasActiveAccount())
        {
            return await EndDialogNoActiveAccount(stepContext, dialogData, cancellationToken);
        }

        // Ensure SaveReadingDialogData is initialized
        dialogData.SaveReadingDialogData ??= new SaveReadingDialogData();

        return await stepContext.NextAsync(null, cancellationToken);
    }

    /// <summary>
    /// Check the customer's agreements and meters to determine next dialog steps
    /// </summary>
    private async Task<DialogTurnResult> AnalyzeCustomerMeterSituation(
        WaterfallStepContext stepContext,
        CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context);

        try
        {
            var agreements = await _customersService.GetAgreements(dialogData, onlyActive: true);

            var hasAgreementForCurrentAccount = agreements.Any(agreement =>
                agreement.AccountId == dialogData.SelectedAccount.AccountId);

            if (!hasAgreementForCurrentAccount)
            {
                return await EndDialogNoAgreement(stepContext, dialogData, cancellationToken);
            }

            var latestAgreement = agreements
                .Where(agreement => agreement.Connection is not null && agreement.Connection.AccountId == dialogData.SelectedAccount.AccountId)
                .OrderByDescending(agreement => agreement.StartDate)
                .FirstOrDefault();

            var hasWarmthProduct = latestAgreement
                ?.Products
                .Any(product => product.ProductType is ProductType.Warmth or ProductType.WarmthEkv) == true;

            if (hasWarmthProduct)
            {
                return await HandleHasWarmthProduct(stepContext, dialogData, latestAgreement, cancellationToken);
            }

            await LoadMeterData(dialogData);

            if (HasRecentPeriodReading(dialogData))
            {
                return await HandleRecentPeriodicReading(stepContext, dialogData, cancellationToken);
            }

            var hasAllSmartMeters = dialogData.SaveReadingDialogData.ReadingModel?.Meters?.All(meter => meter.IsSmart) ?? false;

            if (hasAllSmartMeters)
            {
                return await StartSmartMeterReadingFlow(
                    stepContext,
                    dialogData,
                    latestAgreement,
                    cancellationToken);
            }

            return await StartManualMeterReadingFlow(stepContext, dialogData, cancellationToken);
        }
        catch (DigitalCoreException)
        {
            // Oeps! Er ging helaas iets mis. Probeer het opnieuw, of kies voor contact. Dan kijken mijn collega's met je mee.
            string somethingWentWrong = dialogData.TextLabels.GetValue("SomethingWentWrong", _textLabelGroupName);

            _ = await stepContext.Context.SendActivityAsync(somethingWentWrong, cancellationToken: cancellationToken);
            await stepContext.Context.SendActivityAsync(somethingWentWrong, cancellationToken: cancellationToken);

            await _sessionManager.SendEndOfTransactionActivity(
                stepContext.Context,
                nameof(SaveReadingPersonalDialog),
                nameof(AnalyzeCustomerMeterSituation),
                TransactionStatus.Error,
                cancellationToken);

            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken);
        }
    }

    /// <summary>
    /// The customer has been given a choice and has selected an option. Transfer the customer to the next step.
    /// </summary>
    internal async Task<DialogTurnResult> HandleCustomerChoice(
        WaterfallStepContext stepContext,
        CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context);

        // question is asked and answered in the previous step
        if (stepContext.Result is FoundChoice choice)
        {
            if (choice.Value.Contains("correctie", StringComparison.InvariantCultureIgnoreCase))
            {
                return await HandleCorrectionChoice(stepContext, dialogData, cancellationToken);
            }
            if (choice.Value.Contains("niets", StringComparison.InvariantCultureIgnoreCase))
            {
                return await HandleDoNothingChoice(stepContext, dialogData, cancellationToken);
            }
            if (choice.Value.Contains("meterstand", StringComparison.InvariantCultureIgnoreCase))
            {
                return await stepContext.NextAsync(null, cancellationToken);
            }
            if (choice.Value.Contains("toestemming", StringComparison.InvariantCultureIgnoreCase))
            {
                return await HandleProvideReadPermissionChoice(stepContext, dialogData, cancellationToken);
            }
        }

        await _sessionManager.SendEndOfTransactionActivity(
            stepContext.Context,
            nameof(SaveReadingPersonalDialog),
            nameof(HandleCustomerChoice),
            TransactionStatus.Success,
            cancellationToken);

        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken);
    }

    /// <summary>
    /// Handles the customer's choice after being referred to the grid manager
    /// </summary>
    private async Task<DialogTurnResult> PostReferToGridManager(
        WaterfallStepContext stepContext,
        CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context);

        // question is asked and answered in the previous step
        if (stepContext.Result is FoundChoice choice)
        {
            if (choice.Value.Contains("niets", StringComparison.InvariantCultureIgnoreCase))
            {
                return await HandleDoNothingChoice(stepContext, dialogData, cancellationToken);
            }
        }

        return await stepContext.NextAsync(null, cancellationToken);
    }

    /// <summary>
    /// Refers the user to the web flow, where they can add meter readings
    /// </summary>
    private async Task<DialogTurnResult> ReferToWebFlow(
        WaterfallStepContext stepContext,
        CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context);
        var status = dialogData.SaveReadingDialogData.TransactionStatusMap[SaveReadingPersonalChoiceOption.SubmitMeterReading];

        // Als je op de link klikt, zie je dat we je gegevens vast hebben ingevuld!
        string ifYouClickYouWillSeeYourData = dialogData.TextLabels.GetValue("IfYouClickYouWillSeeYourData", _textLabelGroupName);
        _ = await stepContext.Context.SendActivityAsync(ifYouClickYouWillSeeYourData ?? "nog vragen?", cancellationToken: cancellationToken);

        var readingLink = $"{dialogData.Customer.CurrentDomain}/meterstanden-doorgeven?opnamekenmerk={dialogData.SaveReadingDialogData.ReadingModel.ReadingId}&huisnummer={dialogData.SaveReadingDialogData.ReadingModel.Address.HouseNumber}";
        string clickHereForMeterReadings = dialogData.TextLabels.GetValue("ClickHereForMeterReadings", _textLabelGroupName);
        _ = await stepContext.Context.SendActivityAsync(clickHereForMeterReadings.Replace("{readinglink}", readingLink), cancellationToken: cancellationToken);

        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(SaveReadingPersonalDialog), nameof(ReferToWebFlow), status, cancellationToken);
        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken);
    }

    #endregion

    #region Customer choice handlers

    /// <summary>
    /// The customer has not given permission to read their smart meter.
    /// Inform the customer and prompt what the customer wants to do next.
    /// </summary>
    private async Task<DialogTurnResult> HandleProvideReadPermissionChoice(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        CancellationToken cancellationToken)
    {
        dialogData.SaveReadingDialogData.TransactionStatusMap = new Dictionary<SaveReadingPersonalChoiceOption, TransactionStatus>
        {
            { SaveReadingPersonalChoiceOption.SubmitMeterReading, dialogData.SaveReadingDialogData.IsTariffMigrationReadingAvailable ?
                TransactionStatus.NoReadingPermissionTmeLink : TransactionStatus.NoReadingPermissionLink },
            { SaveReadingPersonalChoiceOption.DoNothing, dialogData.SaveReadingDialogData.IsTariffMigrationReadingAvailable ?
                TransactionStatus.NoReadingPermissionTmeSkip : TransactionStatus.NoReadingPermissionSkip }
        };

        string setPermissionSmartMeter = dialogData.TextLabels.GetValue("SetPermissionSmartMeter", _textLabelGroupName);

        _ = await stepContext.Context.SendActivityAsync(setPermissionSmartMeter, cancellationToken: cancellationToken);

        var choices = SetupButtons(dialogData);

        return await stepContext.PromptAsync(
            nameof(HandleProvideReadPermissionChoice),
            new PromptOptions
            {
                Choices = choices,
                Style = ListStyle.SuggestedAction,
                Prompt = MessageFactory.Text("Wat wil je nu doen?")
            },
            cancellationToken);
    }

    /// <summary>
    /// The customer chose to do nothing.
    /// </summary>
    private async Task<DialogTurnResult> HandleDoNothingChoice(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        CancellationToken cancellationToken)
    {
        var status = dialogData.SaveReadingDialogData.TransactionStatusMap[SaveReadingPersonalChoiceOption.DoNothing];

        if (dialogData.SaveReadingDialogData.IsPeriodicReadingAvailable)
        {
            string doNothingPeriodicAnswer = dialogData.TextLabels
                .GetValue("DoNothingPeriodicAnswer", _textLabelGroupName)?.Replace("{type}",
                    GetTypeReplacement(dialogData.SaveReadingDialogData.ReadingModel.ReadingType));
            _ = await stepContext.Context.SendActivityAsync(
                doNothingPeriodicAnswer ?? "we hebben wel je standen nodig.",
                cancellationToken: cancellationToken);
        }
        else if (dialogData.SaveReadingDialogData.IsTariffMigrationReadingAvailable)
        {
            string doNothingPeriodicAnswerTME = dialogData.TextLabels
                .GetValue("DoNothingPeriodicAnswerTME", _textLabelGroupName)?.Replace("{type}",
                    GetTypeReplacement(dialogData.SaveReadingDialogData.ReadingModel.ReadingType));
            _ = await stepContext.Context.SendActivityAsync(
                doNothingPeriodicAnswerTME ?? "we hebben wel je standen nodig.",
                cancellationToken: cancellationToken);
        }
        else if (dialogData.SaveReadingDialogData.PeriodReadingInsertedWithin7Days)
        {
            string doNothingWithin7DaysAnswer = dialogData.TextLabels
                .GetValue("DoNothingWithin7DaysAnswer", _textLabelGroupName)?.Replace("{type}",
                    GetTypeReplacement(dialogData.SaveReadingDialogData.ReadingModel.ReadingType));
            _ = await stepContext.Context.SendActivityAsync(doNothingWithin7DaysAnswer ?? "Top!",
                cancellationToken: cancellationToken);
        }
        else
        {
            string doNothingForInsightsAnswer =
                dialogData.TextLabels.GetValue("DoNothingForInsightsAnswer", _textLabelGroupName);
            _ = await stepContext.Context.SendActivityAsync(doNothingForInsightsAnswer ?? "Prima!",
                cancellationToken: cancellationToken);
        }

        await _sessionManager.SendEndOfTransactionActivity(
            stepContext.Context,
            nameof(SaveReadingPersonalDialog),
            nameof(HandleCustomerChoice),
            status,
            cancellationToken);

        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken);
    }

    /// <summary>
    /// The customer wants to correct their meter readings.
    /// </summary>
    private async Task<DialogTurnResult> HandleCorrectionChoice(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        CancellationToken cancellationToken)
    {
        var status = dialogData.SaveReadingDialogData.TransactionStatusMap[SaveReadingPersonalChoiceOption.Alternative];

        string correctionNotaAnswer =
            dialogData.TextLabels.GetValue("CorrectionNotaAnswer", _textLabelGroupName);
        _ = await stepContext.Context.SendActivityAsync(correctionNotaAnswer ?? "correctie",
            cancellationToken: cancellationToken);

        await _sessionManager.SendEndOfTransactionActivity(
            stepContext.Context,
            nameof(SaveReadingPersonalDialog),
            nameof(HandleCustomerChoice),
            status,
            cancellationToken);

        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken);
    }

    #endregion

    #region Meter reading handlers

    private async Task<DialogTurnResult> HandleHasWarmthProduct(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        Agreement latestAgreement,
        CancellationToken cancellationToken)
    {
        var hasEkvWarmth = latestAgreement.Products.Any(product => product.ProductType is ProductType.WarmthEkv);

        if (hasEkvWarmth)
        {
            string noReadingAvailableEKVWarmth = dialogData.TextLabels.GetValue("NoReadingAvailableEKVWarmth", _textLabelGroupName);

            noReadingAvailableEKVWarmth = !string.IsNullOrEmpty(noReadingAvailableEKVWarmth)
                ? noReadingAvailableEKVWarmth
                : "Je neemt warmte bij ons af";

            _ = await stepContext.Context.SendActivityAsync(noReadingAvailableEKVWarmth, cancellationToken: cancellationToken);

            await _sessionManager.SendEndOfTransactionActivity(
                stepContext.Context,
                nameof(SaveReadingPersonalDialog),
                nameof(HandleCustomerChoice),
                TransactionStatus.UnhappyEkvWarmth,
                cancellationToken);

            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken);
        }

        string noReadingAvailableECOWarmth = dialogData.TextLabels.GetValue("NoReadingAvailableECOWarmth", _textLabelGroupName);

        noReadingAvailableECOWarmth = !string.IsNullOrEmpty(noReadingAvailableECOWarmth)
            ? noReadingAvailableECOWarmth
            : "Je neemt warmte bij ons af";

        _ = await stepContext.Context.SendActivityAsync(noReadingAvailableECOWarmth, cancellationToken: cancellationToken);

        await _sessionManager.SendEndOfTransactionActivity(
            stepContext.Context,
            nameof(SaveReadingPersonalDialog),
            nameof(HandleCustomerChoice),
            TransactionStatus.UnhappyEcoWarmth,
            cancellationToken);

        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken);
    }

    /// <summary>
    /// In case the customer smart meters and an outstanding reading request
    /// </summary>
    private async Task<DialogTurnResult> HandleOutstandingSmartMeterReadingRequest(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        Agreement latestAgreement,
        CancellationToken cancellationToken)
    {
        // Do we have an open outstanding reading, which isn't ad-hoc?
        var outstandingReading = dialogData.SaveReadingDialogData.OutstandingReadings.Find(reading =>
            reading.ReadingType is ReadingType.PER or ReadingType.TME
            && reading.IsOpen);

        // Alternatively, can we save non-adhoc readings?
        var canSaveMeterReading =
            dialogData.SaveReadingDialogData.ReadingModel.ReadingType is ReadingType.PER or ReadingType.TME
            && (dialogData.SaveReadingDialogData.ReadingModel.Meters?.Any(meter => meter.Saveable) ?? false);

        if (outstandingReading is not null || canSaveMeterReading)
        {
            var readingType = outstandingReading?.ReadingType ?? dialogData.SaveReadingDialogData.ReadingModel.ReadingType;

            dialogData.SaveReadingDialogData.TransactionStatusMap = SetupOutstandingSmartMeterStatusMap(readingType, latestAgreement);

            dialogData.SaveReadingDialogData.IsPeriodicReadingAvailable = readingType is not ReadingType.TME;
            dialogData.SaveReadingDialogData.IsTariffMigrationReadingAvailable = readingType is ReadingType.TME;

            var choices = SetupButtons(dialogData);

            // Je kan je meterstanden nu doorgeven voor je {type}.
            string key = readingType == ReadingType.TME ? "SaveReadingAvailableTME" : "SaveReadingAvailable";
            string saveReadingAvailable = dialogData.TextLabels.GetValue(key, _textLabelGroupName).Replace("{type}", GetTypeReplacement(readingType));
            return await stepContext.PromptAsync(nameof(HandleOutstandingSmartMeterReadingRequest), new PromptOptions
            {
                Choices = choices,
                Style = ListStyle.SuggestedAction,
                Prompt = MessageFactory.Text(saveReadingAvailable)
            }, cancellationToken);
        }

        // Unreadable and unsaveable meter
        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken);
    }

    /// <summary>
    /// The customer has manual meters and an outstanding reading request.
    /// </summary>
    private async Task<DialogTurnResult> HandleOutstandingManualMeterReadingRequest(WaterfallStepContext stepContext,
        DialogData dialogData,
        OutstandingReading outstandingReadingItem,
        CancellationToken cancellationToken)
    {
        var readingType = outstandingReadingItem?.ReadingType ?? dialogData.SaveReadingDialogData.ReadingModel.ReadingType;

        dialogData.SaveReadingDialogData.TransactionStatusMap = SetupOutstandingManualMeterStatusMap(readingType);

        dialogData.SaveReadingDialogData.IsPeriodicReadingAvailable = readingType is not ReadingType.TME;
        dialogData.SaveReadingDialogData.IsTariffMigrationReadingAvailable = readingType is ReadingType.TME;

        var choices = SetupButtons(dialogData);

        // Je kan je meterstanden nu doorgeven voor je {type}.
        string key = readingType == ReadingType.TME ? "SaveReadingAvailableTME" : "SaveReadingAvailable";
        string saveReadingAvailable = dialogData.TextLabels.GetValue(key, _textLabelGroupName).Replace("{type}", GetTypeReplacement(readingType));
        return await stepContext.PromptAsync(nameof(HandleOutstandingManualMeterReadingRequest), new PromptOptions
        {
            Choices = choices,
            Style = ListStyle.SuggestedAction,
            Prompt = MessageFactory.Text(saveReadingAvailable)
        }, cancellationToken);
    }

    /// <summary>
    /// The customer does not have an outstanding reading request
    /// </summary>
    private async Task<DialogTurnResult> HandleNoOutstandingReadingRequest(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        CancellationToken cancellationToken)
    {
        dialogData.SaveReadingDialogData.TransactionStatusMap = new Dictionary<SaveReadingPersonalChoiceOption, TransactionStatus>
        {
            { SaveReadingPersonalChoiceOption.SubmitMeterReading, TransactionStatus.SuccessNoPeriodicReadingLink },
            { SaveReadingPersonalChoiceOption.DoNothing, TransactionStatus.SuccessNoPeriodicReadingSkip }
        };

        dialogData.SaveReadingDialogData.IsPeriodicReadingAvailable = false;
        dialogData.SaveReadingDialogData.IsTariffMigrationReadingAvailable = false;
        dialogData.SaveReadingDialogData.CorrectionNotaAvailable = false;
        dialogData.SaveReadingDialogData.PeriodReadingInsertedWithin7Days = false;

        var choices = SetupButtons(dialogData);
        // Op dit moment hebben we je meterstanden niet nodig voor bijvoorbeeld de jaarnota.
        string noPeriodicReadingAvailable = dialogData.TextLabels.GetValue("NoPeriodicReadingAvailable", _textLabelGroupName);
        _ = await stepContext.Context.SendActivityAsync(noPeriodicReadingAvailable ?? "texts", cancellationToken: cancellationToken);

        // Je kan wel je meterstanden doorgeven om inzicht te krijgen in je verbruik, in de {type} app of Mijn {type}.
        string youCanAddReadingForInsights = dialogData.TextLabels.GetValue("YouCanAddReadingForInsights", _textLabelGroupName);
        return await stepContext.PromptAsync(nameof(HandleNoOutstandingReadingRequest), new PromptOptions
        {
            Choices = choices,
            Style = ListStyle.SuggestedAction,
            Prompt = MessageFactory.Text(youCanAddReadingForInsights ?? "text")
        }, cancellationToken);
    }

    /// <summary>
    /// The customer has recently submitted meter readings.
    /// </summary>
    private async Task<DialogTurnResult> HandleRecentPeriodicReading(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        CancellationToken cancellationToken)
    {
        dialogData.SaveReadingDialogData.TransactionStatusMap = new Dictionary<SaveReadingPersonalChoiceOption, TransactionStatus>
        {
            { SaveReadingPersonalChoiceOption.SubmitMeterReading, TransactionStatus.SuccessReadingAddedLink },
            { SaveReadingPersonalChoiceOption.DoNothing, TransactionStatus.SuccessReadingAddedSkip },
            { SaveReadingPersonalChoiceOption.Alternative, TransactionStatus.MBFChatReadingAddedCorrection }
        };

        // Je hebt kort geleden je meterstanden al doorgegeven voor je jaarnota. Dit kan maar één keer.
        string readingAddedWithin7DaysOfYearnote = dialogData.TextLabels.GetValue("ReadingAddedWithin7DaysOfYearnote", _textLabelGroupName);

        _ = await stepContext.Context.SendActivityAsync(readingAddedWithin7DaysOfYearnote, cancellationToken: cancellationToken);
        // Kloppen je meterstanden niet? Kies dan voor: 'Correctienota aanvragen'.
        string ifNotCorrectAskForCorrection = dialogData.TextLabels.GetValue("IfNotCorrectAskForCorrection", _textLabelGroupName);
        _ = await stepContext.Context.SendActivityAsync(ifNotCorrectAskForCorrection, cancellationToken: cancellationToken);

        dialogData.SaveReadingDialogData.CorrectionNotaAvailable = true;
        dialogData.SaveReadingDialogData.PeriodReadingInsertedWithin7Days = true;
        // send a choice question to the client
        var choices = SetupButtons(dialogData);

        // Je kan wel je meterstanden doorgeven om inzicht te krijgen in je verbruik, in de Eneco/Oxxio app of Mijn Eneco/Oxxio.
        string youCanAddReadingForInsights = dialogData.TextLabels.GetValue("YouCanAddReadingForInsights", _textLabelGroupName);
        return await stepContext.PromptAsync(nameof(HandleRecentPeriodicReading), new PromptOptions
        {
            Choices = choices,
            Style = ListStyle.SuggestedAction,
            Prompt = MessageFactory.Text(youCanAddReadingForInsights)
        }, cancellationToken);
    }

    #endregion

    #region Meter flows

    /// <summary>
    /// If the customer only has smart meters
    /// </summary>
    private async Task<DialogTurnResult> StartSmartMeterReadingFlow(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        Agreement latestAgreement,
        CancellationToken cancellationToken)
    {
        var isReadingMandateDisabled = !(latestAgreement?.Connection.Meter?.IsSmartMeterReadingAllowed ?? true);
        var isSmartMeterUnreadable = latestAgreement?.Connection.Meter?.ReadoutState is MeterReadoutState.NotReadable;

        if (isReadingMandateDisabled)
        {
            string noPermissionSmartMeter = dialogData.TextLabels.GetValue("NoPermissionSmartMeter", _textLabelGroupName);
            _ = await stepContext.Context.SendActivityAsync(noPermissionSmartMeter, cancellationToken: cancellationToken);
        }

        if (isSmartMeterUnreadable)
        {
            string smartMeterMalfunction = dialogData.TextLabels.GetValue("SmartMeterMalfunction", _textLabelGroupName);
            _ = await stepContext.Context.SendActivityAsync(smartMeterMalfunction, cancellationToken: cancellationToken);
        }

        if (isReadingMandateDisabled || isSmartMeterUnreadable)
        {
            dialogData.SaveReadingDialogData.InformAboutSmartMeterReadingMandate = isReadingMandateDisabled;

            // The only option is to check if we have an outstanding reading request
            return await HandleOutstandingSmartMeterReadingRequest(
                stepContext,
                dialogData,
                latestAgreement,
                cancellationToken);
        }

        // Je meters worden op afstand uitgelezen. Daarom hoef je zelf geen meterstanden door te geven.
        string smartMeterNoSaveReadingExplanation = dialogData.TextLabels.GetValue("SmartMeterNoSaveReadingExplenation", _textLabelGroupName);
        _ = await stepContext.Context
            .SendActivityAsync(smartMeterNoSaveReadingExplanation, cancellationToken: cancellationToken);

        // Inzicht in je verbruik? BeLog in Mijn Eneco of download de Eneco app!
        string referenceCustomerToUsageInsights = dialogData.TextLabels.GetValue("ReferenceCustomerToUsageInsights", _textLabelGroupName);
        _ = await stepContext.Context
            .SendActivityAsync(referenceCustomerToUsageInsights, cancellationToken: cancellationToken);

        await _sessionManager.SendEndOfTransactionActivity(
            stepContext.Context,
            nameof(SaveReadingPersonalDialog),
            nameof(StartSmartMeterReadingFlow),
            TransactionStatus.SuccessSmNoSaveReading,
            cancellationToken);

        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken);
    }

    /// <summary>
    /// The customer has manual meters
    /// </summary>
    private async Task<DialogTurnResult> StartManualMeterReadingFlow(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        CancellationToken cancellationToken)
    {
        var outstandingReading = dialogData.SaveReadingDialogData.OutstandingReadings;
        var reading = dialogData.SaveReadingDialogData.ReadingModel;

        // Check if the customer has an outstanding reading request
        var outstandingReadingItem = outstandingReading?.Find(x =>
            x.IsOpen
            && x.ReadingType is ReadingType.PER or ReadingType.TME);
        var isReadingSaveable = reading?.ReadingType is ReadingType.PER or ReadingType.TME
                                && reading?.Meters?.Any(x => x.Saveable) == true;

        if (outstandingReadingItem is not null || isReadingSaveable)
        {
            return await HandleOutstandingManualMeterReadingRequest(
                stepContext,
                dialogData,
                outstandingReadingItem,
                cancellationToken);
        }

        // Customer does not have an outstanding reading request
        return await HandleNoOutstandingReadingRequest(
            stepContext,
            dialogData,
            cancellationToken);
    }

    #endregion

    #region Data loading and mapping

    /// <summary>
    /// Initializes required meter information into the dialog data
    /// </summary>
    private async Task LoadMeterData(DialogData dialogData)
    {
        // Fetch all required data in parallel
        var readingTask = _usagesService.GetReadingForCustomer(dialogData);
        var readingHistoryTask = _usagesService.GetReadingsHistoryForCustomer(dialogData);
        var outstandingReadingTask = _usagesService.GetOutstandingReadings(dialogData);

        await Task.WhenAll(readingTask, readingHistoryTask, outstandingReadingTask);

        // Store results in dialog data
        dialogData.SaveReadingDialogData.ReadingModel = await readingTask;
        dialogData.SaveReadingDialogData.ReadingHistory = await readingHistoryTask;
        dialogData.SaveReadingDialogData.OutstandingReadings = await outstandingReadingTask;
    }

    /// <summary>
    /// Set up the transaction status map for the smart meter situation
    /// </summary>
    private static Dictionary<SaveReadingPersonalChoiceOption, TransactionStatus> SetupOutstandingSmartMeterStatusMap(
        ReadingType readingType,
        Agreement latestAgreement)
    {
        // The reading mandate is disabled
        if (!(latestAgreement?.Connection.Meter?.IsSmartMeterReadingAllowed ?? true))
        {
            if (readingType is ReadingType.TME)
            {
                return new Dictionary<SaveReadingPersonalChoiceOption, TransactionStatus>
                {
                    { SaveReadingPersonalChoiceOption.SubmitMeterReading, TransactionStatus.NoReadingPermissionTmeLink },
                    { SaveReadingPersonalChoiceOption.DoNothing, TransactionStatus.NoReadingPermissionTmeSkip }
                };
            }

            return new Dictionary<SaveReadingPersonalChoiceOption, TransactionStatus>
            {
                { SaveReadingPersonalChoiceOption.SubmitMeterReading, TransactionStatus.NoReadingPermissionLink },
                { SaveReadingPersonalChoiceOption.DoNothing, TransactionStatus.NoReadingPermissionSkip }
            };
        }

        if (readingType is ReadingType.TME)
        {
            return new Dictionary<SaveReadingPersonalChoiceOption, TransactionStatus>
            {
                { SaveReadingPersonalChoiceOption.SubmitMeterReading, TransactionStatus.MBFChatSaveReadingTmeSmn },
                { SaveReadingPersonalChoiceOption.DoNothing, TransactionStatus.SuccessSaveReadingTmeSmnSkip }
            };
        }

        return new Dictionary<SaveReadingPersonalChoiceOption, TransactionStatus>
        {
            { SaveReadingPersonalChoiceOption.SubmitMeterReading, TransactionStatus.MBFChatSaveReadingPerSmn },
            { SaveReadingPersonalChoiceOption.DoNothing, TransactionStatus.SuccessSaveReadingPerSmnSkip }
        };
    }

    /// <summary>
    /// Set up the transaction status map for the manual meter situation
    /// </summary>
    private static Dictionary<SaveReadingPersonalChoiceOption, TransactionStatus> SetupOutstandingManualMeterStatusMap(
        ReadingType readingType)
    {
        if (readingType is ReadingType.TME)
        {
            return new Dictionary<SaveReadingPersonalChoiceOption, TransactionStatus>
            {
                { SaveReadingPersonalChoiceOption.SubmitMeterReading, TransactionStatus.SuccessSaveReadingTmeLink },
                { SaveReadingPersonalChoiceOption.DoNothing, TransactionStatus.SuccessSaveReadingTmeSkip }
            };
        }

        return new Dictionary<SaveReadingPersonalChoiceOption, TransactionStatus>
        {
            { SaveReadingPersonalChoiceOption.SubmitMeterReading, TransactionStatus.SuccessSaveReadingLink },
            { SaveReadingPersonalChoiceOption.DoNothing, TransactionStatus.SuccessSaveReadingSkip }
        };
    }

    private static string GetTypeReplacement(ReadingType type)
    {
        return type switch
        {
            ReadingType.PER => "jaarnota",
            ReadingType.UIT or ReadingType.INV => "verhuizing",
            ReadingType.EOS => "opzegging",
            ReadingType.TME => "nieuwe tarieven",
            _ => "eindnota"
        };
    }

    private List<Choice> SetupButtons(DialogData dialogData)
    {
        // Determine appropriate reading choice text
        string insertReadingChoice = dialogData.TextLabels.GetValue("InsertReadingForInsightsChoice", _textLabelGroupName);
        if (dialogData.SaveReadingDialogData.IsPeriodicReadingAvailable || dialogData.SaveReadingDialogData.IsTariffMigrationReadingAvailable)
        {
            insertReadingChoice = dialogData.TextLabels.GetValue("InsertReadingForPeriodChoice", _textLabelGroupName);
        }

        // Create choice options
        var choices = new List<Choice>
        {
            new()
            {
                Value = insertReadingChoice ?? "meterstand doorgeven",
                Synonyms = ["meterstanden doorgeven", "meterstand doorgeven", "meterstand invullen"]
            }
        };

        // Add correction nota option if applicable
        if (dialogData.SaveReadingDialogData.CorrectionNotaAvailable)
        {
            string correctionNotaChoice = dialogData.TextLabels.GetValue("CorrectionNotaChoice", _textLabelGroupName);
            choices.Add(new Choice
            {
                Value = correctionNotaChoice ?? "correctie nota",
                Synonyms = ["correctie", "correctienota"]
            });
        }

        if (dialogData.SaveReadingDialogData.InformAboutSmartMeterReadingMandate)
        {
            /*string providePermissionChoice = dialogData.TextLabels.GetValue(
                "ProvidePermissionChoice",
                _textLabelGroupName);*/

            choices.Add(new Choice
            {
                Value = "toestemming geven",
                Synonyms = ["toestemming"]
            });
        }

        // Add "do nothing" option
        string doNothingChoice = dialogData.TextLabels.GetValue("DoNothingChoice", _textLabelGroupName);
        choices.Add(new Choice
        {
            Value = doNothingChoice ?? "niets doen",
            Synonyms = ["nee", "neen", "overslaan", "niets"]
        });

        return choices;
    }

    #endregion

    #region Helper methods

    /// <summary>
    /// In case the customer has no agreement, we inform the customer and cancel the dialog
    /// </summary>
    private async Task<DialogTurnResult> EndDialogNoAgreement(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        CancellationToken cancellationToken)
    {
        string noActiveAgreementForAccount = dialogData.TextLabels.GetValue("NoActiveAgreementForAccount", _textLabelGroupName);
        await stepContext.Context.SendActivityAsync(noActiveAgreementForAccount, cancellationToken: cancellationToken);

        await _sessionManager.SendEndOfTransactionActivity(
            stepContext.Context,
            nameof(SaveReadingPersonalDialog),
            nameof(EndDialogNoAgreement),
            TransactionStatus.UnhappyNoContract,
            cancellationToken);

        return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken);
    }

    /// <summary>
    /// In case no active account is found, we inform the customer and cancel the dialog
    /// </summary>
    private async Task<DialogTurnResult> EndDialogNoActiveAccount(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        CancellationToken cancellationToken)
    {
        // Ik heb je gegevens niet kunnen vinden.
        string couldNotFindData = dialogData.TextLabels.GetValue("CouldNotFindData", _textLabelGroupName);
        _ = await stepContext.Context.SendActivityAsync(couldNotFindData, cancellationToken: cancellationToken);

        // Om je meterstanden door te kunnen geven heb je een energiecontract nodig bij Eneco.
        string needAnEnergyContract = dialogData.TextLabels.GetValue("NeedAnEnergyContract", _textLabelGroupName);
        _ = await stepContext.Context.SendActivityAsync(needAnEnergyContract, cancellationToken: cancellationToken);

        await _sessionManager.SendEndOfTransactionActivity(
            stepContext.Context,
            nameof(SaveReadingPersonalDialog),
            nameof(EndDialogNoActiveAccount),
            TransactionStatus.TemporaryFailure,
            cancellationToken);

        return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken);
    }

    private bool HasRecentPeriodReading(DialogData dialogData)
    {
        var readingHistory = dialogData.SaveReadingDialogData.ReadingHistory;
        return readingHistory?.Find(x =>
            x.Date > _timeProvider.GetUtcNow().AddDays(-7).Date
            && x.Type is CollectorType.Periodic or CollectorType.TariffMigration) is not null;
    }

    #endregion
}
