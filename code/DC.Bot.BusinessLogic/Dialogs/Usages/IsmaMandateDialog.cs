﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Exceptions;
using DC.Domain.Models.Products;
using DC.Usages.Client.Models;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Usages
{
    public class IsmaMandateDialog : BaseDialog
    {
        /// <summary>
        /// Depending services
        /// </summary>
        private readonly IUsagesService _usagesService;
        private readonly ICustomersService _customersService;

        /// <summary>
        /// Pre-defined choice objects
        /// </summary>
        #region Choices
        private List<Choice> SetupActiveMandateChoices(DialogData dialogData)
        {
            return new List<Choice>
            {
                SetupNotNowChoice(dialogData),
                SetupAdjustChoice(dialogData)
            };
        }

        private List<Choice> SetupInActiveMandateChoices(DialogData dialogData)
        {
            return new List<Choice>
            {
                SetupEnableChoice(dialogData),
                SetupNotNowChoice(dialogData)
            };
        }

        private List<Choice> SetupDisableMandateChoices(DialogData dialogData)
        {
            return new List<Choice>
            {
                SetupKeepChoice(dialogData),
                SetupDisableChoice(dialogData)
            };
        }

        private Choice SetupNotNowChoice(DialogData dialogData)
        {
            return SetupButton(dialogData, "NotNowChoice", new List<string> { "Nee", "Niet" });
        }

        private Choice SetupAdjustChoice(DialogData dialogData)
        {
            return SetupButton(dialogData, "AdjustChoice", new List<string> { "Ja", "Aanpassen" });
        }

        private Choice SetupEnableChoice(DialogData dialogData)
        {
            return SetupButton(dialogData, "EnableChoice", new List<string> { "Ja" });
        }

        private Choice SetupKeepChoice(DialogData dialogData)
        {
            return SetupButton(dialogData, "KeepChoice", new List<string> { "Nee", "Behouden" });
        }

        private Choice SetupDisableChoice(DialogData dialogData)
        {
            return SetupButton(dialogData, "DisableChoice", new List<string> { "Ja", "Intrekken" });
        }

        private Choice SetupButton(DialogData dialogData, string key, List<string> synonyms)
        {
            return new Choice
            {
                Value = dialogData.TextLabels.GetValue(key, _textLabelGroupName),
                Synonyms = synonyms
            };
        }

        #endregion

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="loggerFactory"></param>
        /// <param name="sessionManager"></param>
        /// <param name="loggingService"></param>
        /// <param name="usagesService"></param>
        /// <param name="customersService"></param>
        public IsmaMandateDialog(ILoggerFactory loggerFactory,
            ISessionManager sessionManager,
            ILoggingService loggingService,
            IUsagesService usagesService,
            ICustomersService customersService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
                new WaterfallStep[]
                {
                    InitTextLabels,
                    GetMandateStep,
                    MandateUnavailableStep,
                    AskChangeMandateStep,
                    AnswerChangeMandateStep,
                    ConfirmDisableMandateStep
                }));

            AddDialog(new ChoicePrompt(nameof(AskChangeMandateStep)));
            AddDialog(new ChoicePrompt(nameof(AnswerChangeMandateStep)));

            InitialDialogId = nameof(WaterfallDialog);

            _usagesService = usagesService;
            _customersService = customersService;
        }

        /// <summary>
        /// First step in the dialog. Gets the mandate and proceeds to the next step without chatting.
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<DialogTurnResult> GetMandateStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.IsmaMandate).ConfigureAwait(false);

            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            // get the mandate from DC Usages with error handling
            ServiceProductVersionModel mandate;
            try
            {
                mandate = await _usagesService.GetMandate(dialogData, ServiceProductType.Isma).ConfigureAwait(false);
            }
            catch (DataNotFoundException)
            {
                mandate = new ServiceProductVersionModel { IsActive = false, IsAvailable = false };
            }
            catch (DigitalCoreException)
            {
                return await SomethingWentWrongContactUs(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
            }

            // 2xx or 404 response can proceed to the next step
            return await stepContext.NextAsync(mandate, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Second step in the dialog. If the mandate is available, it will immediately proceed to the third step, else it will end.
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<DialogTurnResult> MandateUnavailableStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            // mandate is available, proceed to the next step
            var mandate = (ServiceProductVersionModel)stepContext.Result;
            if (mandate?.IsAvailable != false)
            {
                dialogData.MandateData.IsmaIsActive = mandate?.IsActive == true;
                return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
            }

            // when unavailable, check if the customer has indeed no smart meters
            var customer = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);
            if (customer.HasReadableSmartMeter(ProductType.Electricity, dialogData.SelectedAccount.AccountId) != true &&
                customer.HasReadableSmartMeter(ProductType.Gas, dialogData.SelectedAccount.AccountId) != true)
            {
                // specific unhappy flow for customers without smart meters
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoSmartMeters", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(IsmaMandateDialog), nameof(MandateUnavailableStep), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
            }
            else
            {
                // unclear happy flow
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("UnhappyFlow", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(IsmaMandateDialog), nameof(MandateUnavailableStep), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
            }
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);

        }

        /// <summary>
        /// Third step, dynamically based on the activeness of the mandate
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<DialogTurnResult> AskChangeMandateStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            // mandate is active, check the customer if still desired
            if (dialogData.MandateData.IsmaIsActive)
            {
                var prompt = dialogData.TextLabels.GetValue("AskForPermissionIsmaMandate", _textLabelGroupName);

                return await stepContext.PromptAsync(nameof(AskChangeMandateStep), new PromptOptions
                {
                    Prompt = MessageFactory.Text(prompt),
                    Choices = SetupActiveMandateChoices(dialogData) ?? new List<Choice>(),
                    RetryPrompt = MessageFactory.Text(prompt),
                    Style = ListStyle.SuggestedAction
                }, cancellationToken).ConfigureAwait(false);
            }
            // mandate is inactive, ask the customer for approval
            else
            {
                var prompt = dialogData.TextLabels.GetValue("AskForEnableIsmaMandate", _textLabelGroupName);

                return await stepContext.PromptAsync(nameof(AskChangeMandateStep), new PromptOptions
                {
                    Prompt = MessageFactory.Text(prompt),
                    Choices = SetupInActiveMandateChoices(dialogData) ?? new List<Choice>(),
                    RetryPrompt = MessageFactory.Text(prompt),
                    Style = ListStyle.SuggestedAction
                }, cancellationToken).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Fourth step, handles the answer of the third step
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<DialogTurnResult> AnswerChangeMandateStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            string answer = null;
            if (stepContext.Result is FoundChoice choice)
                answer = choice.Value;
            else if (stepContext.Result is string text)
                answer = text;

            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            // no action (customer likes the current situation, in both active/nonactive cases)
            if (answer == SetupNotNowChoice(dialogData).Value)
            {
                return await DoNothingSuccess(stepContext, dialogData.MandateData.IsmaIsActive, dialogData, cancellationToken).ConfigureAwait(false);
            }
            // inactive state, customer wants to activate
            else if (answer == SetupEnableChoice(dialogData).Value)
            {
                return await EnableMandate(stepContext, cancellationToken).ConfigureAwait(false);
            }
            // active state, customer wants to deactivate. ask a second question to double check
            else if (answer == SetupAdjustChoice(dialogData).Value)
            {
                var prompt = dialogData.TextLabels.GetValue("AskForDisableIsmaMandate", _textLabelGroupName);

                return await stepContext.PromptAsync(nameof(AnswerChangeMandateStep), new PromptOptions
                {
                    Prompt = MessageFactory.Text(prompt),
                    Choices = SetupDisableMandateChoices(dialogData) ?? new List<Choice>(),
                    RetryPrompt = MessageFactory.Text(prompt),
                    Style = ListStyle.SuggestedAction
                }, cancellationToken).ConfigureAwait(false);
            }

            return await SomethingWentWrongContactUs(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Fifth step (in the active - wants to deactivate case, double check)
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<DialogTurnResult> ConfirmDisableMandateStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            string answer = null;
            if (stepContext.Result is FoundChoice choice)
                answer = choice.Value;
            else if (stepContext.Result is string text)
                answer = text;

            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            // no action (customer is convinced of the current situation in the second answer )
            if (answer == SetupKeepChoice(dialogData).Value)
                return await DoNothingSuccess(stepContext, true, dialogData, cancellationToken).ConfigureAwait(false);
            // active state, customer wants to deactivate
            else if (answer == SetupDisableChoice(dialogData).Value)
                return await DisableMandate(stepContext, cancellationToken).ConfigureAwait(false);

            return await SomethingWentWrongContactUs(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Fifth step (in the inactive - wants to activate case)
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<DialogTurnResult> EnableMandate(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            // enable the mandate (failsafe with log)
            try
            {
                await _usagesService.EnableMandate(dialogData, ServiceProductType.Isma);
            }
            catch (DigitalCoreException dx)
            {
                _logger?.LogError(dx, $"{nameof(AnswerChangeMandateStep)} threw a {nameof(DigitalCoreException)} during the EnableMandate step");
                return await SomethingWentWrongContactUs(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
            }

            // close the transaction with success
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("IsmaMandateEnabled", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(IsmaMandateDialog), nameof(EnableMandate), TransactionStatus.SuccessMandate, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Sixth step (in the active - wants to deactivate case, confirm the double check)
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<DialogTurnResult> DisableMandate(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            // disable the mandate (failsafe with log)
            try
            {
                await _usagesService.DisableMandate(dialogData, ServiceProductType.Isma).ConfigureAwait(false);
            }
            catch (DigitalCoreException dx)
            {
                _logger?.LogError(dx, $"{nameof(ConfirmDisableMandateStep)} threw a {nameof(DigitalCoreException)} during the DisableMandate step");
                return await SomethingWentWrongContactUs(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
            }

            // close the transaction with success
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("IsmaMandateDisabled", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(IsmaMandateDialog), nameof(ConfirmDisableMandateStep), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// 4th/5th step when the customers wants to keep the situation as-is
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<DialogTurnResult> DoNothingSuccess(WaterfallStepContext stepContext, bool mandateIsActive, DialogData dialogData, CancellationToken cancellationToken)
        {
            // close the transaction with success
            await stepContext.Context.SendActivityAsync(mandateIsActive ? dialogData.TextLabels.GetValue("DoNothingIsmaMandateActive", _textLabelGroupName) : dialogData.TextLabels.GetValue("DoNothingIsmaMandateInActive", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(IsmaMandateDialog), nameof(DoNothingSuccess), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Exception/unexpected behavior escape.
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<DialogTurnResult> SomethingWentWrongContactUs(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
        {
            // close the transaction with TemporaryFailure status
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrongContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(IsmaMandateDialog), nameof(SomethingWentWrongContactUs), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
        }
    }
}
