﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Exceptions;
using DC.Domain.Models.Extensions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Usages
{
    public class ReadingsReportRequestDialog : BaseDialog
    {
        private List<Choice> SetupSendReadingChoices(DialogData dialogData)
        {
            return new List<Choice>
            {
                SetupButton(dialogData, "YesSendReadingChoice", new List<string> { "Ja" }),
                SetupButton(dialogData, "NoSendReadingChoice", new List<string> { "Nee", "Neen" }),
            };
        }

        private List<Choice> SetupAskEmailChoices(DialogData dialogData)
        {
            return new List<Choice>
            {
                SetupButton(dialogData, "YesEmailChoice", new List<string> { "Ja" }),
                SetupButton(dialogData, "NoEmailChoice", new List<string> { "Nee", "Neen" }),
            };
        }

        private Choice SetupButton(DialogData dialogData, string key, List<string> synonyms)
        {
            return new Choice
            {
                Value = dialogData.TextLabels.GetValue(key, _textLabelGroupName),
                Synonyms = synonyms
            };
        }

        private readonly IUsagesService _usagesService;

        private DialogData dialogData;

        public ReadingsReportRequestDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            IDialogValidators validators,
            IUsagesService usagesService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
                new WaterfallStep[]
                {
                    InitTextLabels,
                    AskToReadingsReportRequestDialog,
                    AskToReadingsReportRequestAnswer,
                    AskForEmailStep,
                    AskForEmailAnswerStep,
                    AskToSendReadingsReportRequestAnswerStep,
                    SendReadingsReportRequestStep
                }));

            AddDialog(new TextPrompt(nameof(InitTextLabels)));
            AddDialog(new ChoicePrompt(nameof(AskToReadingsReportRequestDialog)));
            AddDialog(new TextPrompt(nameof(AskToReadingsReportRequestAnswer)));
            AddDialog(new ChoicePrompt(nameof(AskForEmailStep)));
            AddDialog(new TextPrompt(nameof(AskForEmailAnswerStep), validators.CustomerDialogValidator.CustomerEmailValidator));
            AddDialog(new TextPrompt(nameof(AskToSendReadingsReportRequestAnswerStep)));
            AddDialog(new TextPrompt(nameof(SendReadingsReportRequestStep)));

            InitialDialogId = nameof(WaterfallDialog);
            _usagesService = usagesService;
        }

        private async Task<DialogTurnResult> AskToReadingsReportRequestDialog(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ReadingsReportRequest).ConfigureAwait(false);
            dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            if (dialogData.IsVerified() && dialogData.HasActiveAccount())
            {
                var canRequest = await _usagesService.CanRequestRegisterReportRequest(dialogData).ConfigureAwait(false);
                if (!canRequest.HasEG)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("HasNoEG", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("HasNoEGContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ReadingsReportRequestDialog), nameof(AskToReadingsReportRequestDialog), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else if (!canRequest.HasSmartMeter)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("HasNoSmartMeter", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("HasNoSmartMeter2", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ReadingsReportRequestDialog), nameof(AskToReadingsReportRequestDialog), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
            }

            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ReadingsAvailable", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CustomerLessThan24Month", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            var prompt = dialogData.TextLabels.GetValue("AskToSendReadingsReport", _textLabelGroupName);
            return await stepContext.PromptAsync(nameof(AskToReadingsReportRequestDialog), new PromptOptions
            {
                Prompt = MessageFactory.Text(prompt),
                Choices = SetupSendReadingChoices(dialogData),
                RetryPrompt = MessageFactory.Text(prompt),
                Style = ListStyle.SuggestedAction
            }, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskToReadingsReportRequestAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            if (stepContext.IsChoiceSelected("meterstanden opsturen"))
            {
                if (!dialogData.IsVerified() || !dialogData.HasActiveAccount() || !dialogData.IsCustomerIdVerified())
                {
                    if (string.IsNullOrWhiteSpace(dialogData.Customer.EmailAddress))
                        stepContext.SetNextStepIndex(Dialogs, nameof(AskForEmailStep));
                    else
                        stepContext.SetNextStepIndex(Dialogs, nameof(AskToSendReadingsReportRequestAnswerStep));
                }
                else if (string.IsNullOrWhiteSpace(dialogData.Customer.EmailAddress))
                {
                    stepContext.SetNextStepIndex(Dialogs, nameof(AskForEmailStep));
                    return await AskForEmailStep(stepContext, cancellationToken).ConfigureAwait(false);
                }
                else
                {
                    return await SendReadingsReportRequestStep(stepContext, cancellationToken).ConfigureAwait(false);
                }
            }

            return await DoNothingAnswer(stepContext, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskForEmailStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            if (stepContext.IsChoiceSelected("doorgeven e-mailadres"))
            {
                await EnsureTextLabelsInitialized(stepContext, cancellationToken);

                return await stepContext.PromptAsync(nameof(AskForEmailAnswerStep), new PromptOptions
                {
                    Prompt = MessageFactory.Text(dialogData.TextLabels.GetValue("AskForEmailAddressPromptText", _textLabelGroupName)),
                    RetryPrompt = MessageFactory.Text(dialogData.TextLabels.GetValue("InvalidEmailAddress", _textLabelGroupName))
                }, cancellationToken).ConfigureAwait(false);
            }

            if (stepContext.IsChoiceSelected("niets doen"))
            {
                return await DoNothingAnswer(stepContext, cancellationToken);
            }

            await EnsureTextLabelsInitialized(stepContext, cancellationToken);

            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoEmailAddress", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            var prompt = dialogData.TextLabels.GetValue("DoesntSaveEmailAddress", _textLabelGroupName);
            return await stepContext.PromptAsync(nameof(AskForEmailStep), new PromptOptions
            {
                Prompt = MessageFactory.Text(prompt),
                Choices = SetupAskEmailChoices(dialogData),
                RetryPrompt = MessageFactory.Text(prompt),
                Style = ListStyle.SuggestedAction
            }, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskForEmailAnswerStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            if (stepContext.IsChoiceSelected("doorgeven e-mailadres"))
            {
                await EnsureTextLabelsInitialized(stepContext, cancellationToken);

                return await stepContext.PromptAsync(nameof(AskForEmailAnswerStep), new PromptOptions
                {
                    Prompt = MessageFactory.Text(dialogData.TextLabels.GetValue("AskForEmailAddressPromptText", _textLabelGroupName)),
                    RetryPrompt = MessageFactory.Text(dialogData.TextLabels.GetValue("InvalidEmailAddress", _textLabelGroupName))
                }, cancellationToken).ConfigureAwait(false);
            }

            if (stepContext.Result is not string result || string.IsNullOrEmpty(result))
            {
                return await DoNothingAnswer(stepContext, cancellationToken).ConfigureAwait(false);
            }

            return await AskToSendReadingsReportRequestAnswerStep(stepContext, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskToSendReadingsReportRequestAnswerStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            if (dialogData.Verification.TooManyAttempts)
            {
                dialogData.Verification.TooManyAttempts = false;

                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ThreeTimesWrongEmailAddress", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ReadingsReportRequestDialog), nameof(AskToSendReadingsReportRequestAnswerStep), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            if (!string.IsNullOrEmpty((string)stepContext.Result))
            {
                dialogData.Customer.EmailAddress = (string)stepContext.Result;

                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("OKEmailAddress", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                return await SendReadingsReportRequestStep(stepContext, cancellationToken).ConfigureAwait(false);
            }

            return await AskForEmailStep(stepContext, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> SendReadingsReportRequestStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            try
            {
                var result = await _usagesService.RegisterReportRequest(dialogData, dialogData.SelectedAccount.AccountId).ConfigureAwait(false);
                if (result.EmailSent || !result.ErrorCode.HasValue)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SendReadingsReportRequestEmailSent", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SendReadingsReportRequestEmailSentWithInThreeDay", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ReadingsReportRequestDialog), nameof(SendReadingsReportRequestStep), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                if (result.ErrorCode == ReadingReportRequestErrorCode.FailedSendEmail)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SendReadingsReportRequestFailedSendEmail", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SendReadingsReportRequestFailedSendEmailWithInThreeDay", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ReadingsReportRequestDialog), nameof(SendReadingsReportRequestStep), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else if (result.ErrorCode == ReadingReportRequestErrorCode.AlreadyRequested)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SendReadingsReportRequestAlreadyRequested", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SendReadingsReportRequestAlreadyRequestedWithInThreeDay", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SendReadingsReportRequestAlreadyRequestedSendTo", _textLabelGroupName).Replace("{emailaddress}", dialogData.Customer.EmailAddress.ObfuscateEmail()), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ReadingsReportRequestDialog), nameof(SendReadingsReportRequestStep), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else if (result.ErrorCode == ReadingReportRequestErrorCode.NoSmartMeters || result.ErrorCode == ReadingReportRequestErrorCode.NoActiveSmartMeterAgreement)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SendReadingsReportRequestNoSmartMeters", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SendReadingsReportRequestNoSmartMeters2", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ReadingsReportRequestDialog), nameof(SendReadingsReportRequestStep), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else if (result.ErrorCode == ReadingReportRequestErrorCode.CustomerNotFound)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SendReadingsReportRequestCustomerNotFound", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SendReadingsReportRequestCustomerNotFoundContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ReadingsReportRequestDialog), nameof(SendReadingsReportRequestStep), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else if (result.ErrorCode == ReadingReportRequestErrorCode.EmailRequired)
                {
                    stepContext.SetNextStepIndex(Dialogs, nameof(AskForEmailStep));
                    return await AskForEmailStep(stepContext, cancellationToken).ConfigureAwait(false);
                }
                else if (result.ErrorCode.HasValue)
                {
                    return await SomethingWentWrongTryAgain(stepContext, cancellationToken).ConfigureAwait(false);
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(SendReadingsReportRequestStep));
                return await SomethingWentWrongTryAgain(stepContext, cancellationToken).ConfigureAwait(false);
            }

            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> DoNothingAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            if (stepContext.IsChoiceSelected("niets doen"))
            {
                await EnsureTextLabelsInitialized(stepContext, cancellationToken);

                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("DoNothing", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ReadingsReportRequestDialog), nameof(DoNothingAnswer), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> SomethingWentWrongTryAgain(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("TryAgianSomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ReadingsReportRequestDialog), nameof(SomethingWentWrongTryAgain), TransactionStatus.PermanentFailure, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private async Task EnsureTextLabelsInitialized(
            WaterfallStepContext stepContext,
            CancellationToken cancellationToken)
        {
            var textLabelsInitialized = dialogData?.TextLabels?.TextLabels?.Exists(i => i.GroupName == _textLabelGroupName) == true;

            if (!textLabelsInitialized)
            {
                await InitTextLabels(stepContext, cancellationToken);
            }
        }
    }
}
