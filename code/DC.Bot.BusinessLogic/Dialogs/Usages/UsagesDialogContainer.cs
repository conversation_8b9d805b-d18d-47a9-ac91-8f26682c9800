﻿using DC.Bot.BusinessLogic.Interfaces.DialogContainers;

namespace DC.Bot.BusinessLogic.Dialogs.Usages
{
    public class UsagesDialogContainer : IUsagesDialogContainer
    {
        public ProductUsagesDialog ProductUsagesDialog { get; private set; }
        public ReadingsReportRequestDialog ReadingsReportRequestDialog { get; private set; }
        public SaveReadingPersonalDialog SaveReadingPersonalDialog { get; private set; }
        public IsmaMandateDialog IsmaMandateDialog { get; private set; }

        public UsagesDialogContainer(
            ProductUsagesDialog productUsagesDialog,
            ReadingsReportRequestDialog readingsReportRequestDialog,
            SaveReadingPersonalDialog saveReadingPersonalDialog,
            IsmaMandateDialog ismaMandateDialog)
        {
            ProductUsagesDialog = productUsagesDialog;
            ReadingsReportRequestDialog = readingsReportRequestDialog;
            SaveReadingPersonalDialog = saveReadingPersonalDialog;
            IsmaMandateDialog = ismaMandateDialog;
        }
    }
}
