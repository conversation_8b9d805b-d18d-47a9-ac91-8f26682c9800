﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Customers.Client.Models;
using DC.Domain.Exceptions;
using DC.Domain.Models.Agreements;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Usages
{
    public class ProductUsagesDialog : BaseDialog
    {
        private readonly ICustomersService _customersService;
        private readonly IUsagesService _usagesService;

        public ProductUsagesDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            ICustomersService customersService,
            IUsagesService usagesService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
                new WaterfallStep[]
                {
                    InitTextLabels,
                    ProductUsagesStep
                }));

            InitialDialogId = nameof(WaterfallDialog);

            _customersService = customersService;
            _usagesService = usagesService;
        }

        /// <summary>
        /// Returns all relevant productrate data
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>                
        /// <returns></returns>
        private async Task<DialogTurnResult> ProductUsagesStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.GetProductUsages).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            TransactionStatus status;

            //getting the textlabels of this dialog agian when there are empty
            if (dialogData.TextLabels?.TextLabels?.Exists(i => i.GroupName == _textLabelGroupName) != true)
                await InitTextLabels(stepContext, cancellationToken);

            try
            {
                if (dialogData.IsVerified())
                {
                    var customerModel = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);
                    var activeAccountId = dialogData.SelectedAccount?.AccountId;
                    if (!dialogData.HasActiveAccount())
                    {
                        var activeAccount = dialogData.GetActiveAccountFromCustomerModel(customerModel);
                        activeAccountId = activeAccount?.Id;
                    }

                    if (activeAccountId > 0)
                    {
                        var agreements = await _customersService.GetAgreements(dialogData, true, true).ConfigureAwait(false);
                        agreements = agreements.GetMainEGWProducts()?.ToList();
                        if (agreements?.Any() == true)
                        {
                            return await ShowData(stepContext, customerModel, activeAccountId.Value, dialogData, agreements, cancellationToken).ConfigureAwait(false);
                        }

                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoContract", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        status = TransactionStatus.NoActiveEnergyContract;
                    }
                    else
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoActiveAccount", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        status = TransactionStatus.TemporaryFailure;
                    }
                }
                else
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFound", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    status = TransactionStatus.TemporaryFailure;
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(ProductUsagesStep));

                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SeeUsagesApp", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                status = TransactionStatus.TemporaryFailure;
            }
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ProductUsagesDialog), nameof(ProductUsagesStep), status, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }
        private async Task<DialogTurnResult> ShowData(WaterfallStepContext stepContext, CustomerModel customerModel, int activeAccountId, DialogData dialogData, IList<Agreement> agreements, CancellationToken cancellationToken)
        {
            var usages = await _usagesService.GetPastHalfYearUsages(dialogData, activeAccountId).ConfigureAwait(false);
            var hasSmartMeter = customerModel.HasOnlySmartMetersAndActiveMandate(activeAccountId);
            if (usages?.UsageActuals?.Count > 0)
            {
                if (!hasSmartMeter)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoSmartMeterHasUsages", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SmartMeterHasUsages", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                }

                await ShowAllUsages(usages, stepContext).ConfigureAwait(false);
                return await ShowYearNoteUsages(customerModel, dialogData, true, agreements, stepContext, cancellationToken).ConfigureAwait(false);

            }
            else
            {
                if (hasSmartMeter)
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SmartMeterHasNoUsages", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                else
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoSmartMeterHasNoUsages", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

                return await ShowYearNoteUsages(customerModel, dialogData, false, agreements, stepContext, cancellationToken).ConfigureAwait(false);
            }
        }

        private static async Task ShowAllUsages(UsagesSummaryModel usagesSummaryModel, WaterfallStepContext stepContext)
        {
            foreach (var actual in usagesSummaryModel.UsageActuals)
            {
                var stringBuilder = new StringBuilder();
                stringBuilder.AppendBoldLineWithMarkdown(actual.Title);

                foreach (var usageItem in actual.UsageItems)
                    AppendUsageItem(stringBuilder, actual, usageItem);

                _ = await stepContext.Context.SendActivityAsync(stringBuilder.ToString().Trim(), cancellationToken: CancellationToken.None).ConfigureAwait(false);
            }
        }


        /// <summary>
        /// Appends the usage item.
        /// </summary>
        /// <param name="stringBuilder">The string builder.</param>
        /// <param name="actual">The actual.</param>
        /// <param name="usageItem">The usage item.</param>
        /// <returns>A StringBuilder.</returns>
        private static void AppendUsageItem(StringBuilder stringBuilder, UsagesSummaryActual actual, UsagesSummaryUsageItem usageItem)
        {
            var month = usageItem.Month;
            var item = usageItem.UsageItem;
            stringBuilder.AppendLineWithMarkdown(item.GetDescriptionByMonth(month, actual.MeasureType));
        }

        private async Task<DialogTurnResult> ShowYearNoteUsages(CustomerModel customerModel, DialogData dialogData, bool hasUsages, IList<Agreement> agreements, WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var status = TransactionStatus.Success;
            var usages = "";
            agreements = agreements?.Where(i => i.ProductUsages?.Count > 0).OrderBy(p => p.Products.FirstOrDefault()?.ProductType).ToList();
            bool isNewCustomer = customerModel.IsNewCustomer();
            bool isEstimated = false;

            var stringBuilder = new StringBuilder();
            if (agreements?.Count > 0)
            {
                foreach (var agreement in agreements)
                {
                    var product = agreement.Products.FirstOrDefault();
                    var data = GetProductUsage(agreement, isEstimated);
                    isEstimated = data.isEstimated;
                    stringBuilder.AppendLineWithMarkdown($"{data.productUsages.GetDescriptionByProduct(product?.ProductType ?? Domain.Models.Products.ProductType.Unknown)}");
                    if (data.productUsages.HasDoubleRate())
                        stringBuilder.AppendLineWithMarkdown($"{data.productUsages.GetDescriptionByProductTotal(product?.ProductType ?? Domain.Models.Products.ProductType.Unknown)}");
                }
            }

            usages = stringBuilder.ToString();

            if (!string.IsNullOrEmpty(usages))
            {
                if (!isNewCustomer)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("HasYearNote", _textLabelGroupName)
                            .Replace("{usages}", usages).Trim(), cancellationToken: CancellationToken.None).ConfigureAwait(false);
                    //show message when has usages and at least 1 or more estimated usages on the last yearnote
                    if (isEstimated && hasUsages)
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ProductUsagesYearNote", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        status = TransactionStatus.SuccessCalculated;
                    }
                }
                else
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("HasNoYearNote", _textLabelGroupName)
                            .Replace("{usages}", usages).Trim(), cancellationToken: CancellationToken.None).ConfigureAwait(false);
            }

            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ProductUsagesDialog), nameof(ProductUsagesStep), status, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private static (bool isEstimated, List<ProductUsage> productUsages) GetProductUsage(Agreement agreement, bool isEstimated)
        {
            var productUsages = agreement.ProductUsages
                        ?.Where(i => i.CounterType != Domain.Models.Usages.CounterType.ElectricityRedeliveryLow && i.CounterType != Domain.Models.Usages.CounterType.ElectricityRedeliveryHigh)
                        .OrderBy(i => i.StartDate)
                        .ToList(); //filter out redelivery
            if (productUsages?.Exists(i => i.EndDate != null) ?? false)
            {
                //get productusage of the last yearnote period
                productUsages = productUsages.Where(i => i.EndDate != null).ToList();
                //check if the productusage is estimated or derived on the last yearnote period 
                if (!isEstimated && productUsages.Exists(i => i.QuantityType == Domain.Models.Usages.QuantityType.Estimated))
                    isEstimated = true;
            }
            else
            {
                if (!isEstimated)
                    isEstimated = productUsages?.TrueForAll(i => i.QuantityType == Domain.Models.Usages.QuantityType.Estimated) == true;
            }

            return (isEstimated, productUsages);
        }
    }
}