﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Interfaces;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Components;

public class CustomerIdVerification : BaseDialog
{

    public CustomerIdVerification(
        ILoggerFactory loggerFactory,
        ILoggingService loggingService,
        ISessionManager sessionManager,
        IDialogValidators validators,
        IStorageService storageService) :
        base(loggerFactory, sessionManager, loggingService, storageService)
    {
        AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
            new WaterfallStep[]
            {
                InitTextLabels,
                AskForCustomerId,
                AskForCustomerIdAnswerStep
            }));

        AddDialog(new TextPrompt(nameof(AskForCustomerId), validators.CustomerDialogValidator.CustomerIdValidator));
        AddDialog(new TextPrompt(nameof(AskForCustomerIdAnswerStep)));

        InitialDialogId = nameof(WaterfallDialog);
    }

    private async Task<DialogTurnResult> AskForCustomerId(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.CustomerIdVerification).ConfigureAwait(false);

        _ = await stepContext.Context.SendActivityAsync("Prima! Dan heb ik je klantnummer nodig. Je vindt deze onder andere in de app, onze e-mails en op je jaarnota.", cancellationToken: cancellationToken).ConfigureAwait(false);
        return await stepContext.PromptAsync(nameof(AskForCustomerId), new PromptOptions
        {
            Prompt = MessageFactory.Text("Wat is je klantnummer?"),
            RetryPrompt = MessageFactory.Text("Je klantnummer klopt niet of past niet bij de eerder doorgegeven gegevens. Je klantnummer bestaat uit maximaal 10 cijfers. Probeer het opnieuw.")
        }, cancellationToken).ConfigureAwait(false);
    }

    private async Task<DialogTurnResult> AskForCustomerIdAnswerStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
        if (dialogData.Verification.TooManyAttempts)
        {
            dialogData.Verification.TooManyAttempts = false;
            dialogData.AdvancePayment.AskToUpdatePaymentDay = false;

            _ = await stepContext.Context.SendActivityAsync("Je hebt 3 keer een verkeerd klantnummer ingevuld. Daardoor kan ik je gegevens niet vinden.", cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CustomerIdVerification), nameof(AskForCustomerIdAnswerStep), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
        }

        await stepContext.Context.SendActivityAsync("Bedankt!", cancellationToken: cancellationToken).ConfigureAwait(false);

        return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
    }
}
