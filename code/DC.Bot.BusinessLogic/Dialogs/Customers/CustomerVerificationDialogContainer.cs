﻿using DC.Bot.BusinessLogic.Dialogs.Components;
using DC.Bot.BusinessLogic.Dialogs.Customers;
using DC.Bot.BusinessLogic.Interfaces.DialogContainers;

namespace DC.Bot.BusinessLogic.Dialogs
{
    public class CustomerVerificationDialogContainer : ICustomerVerificationDialogContainer
    {
        public CustomerVerificationDialog CustomerVerificationDialog { get; private set; }

        public CustomerIdVerification CustomerIdVerification { get; private set; }

        public CustomerVerificationDialogContainer(
            CustomerVerificationDialog customerVerificationDialog,
            CustomerIdVerification customerIdVerification)
        {
            CustomerVerificationDialog = customerVerificationDialog;
            CustomerIdVerification = customerIdVerification;
        }
    }
}
