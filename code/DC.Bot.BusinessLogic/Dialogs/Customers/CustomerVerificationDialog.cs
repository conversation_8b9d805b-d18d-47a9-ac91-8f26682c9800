﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Customers.Client.Models;
using DC.Utilities.Environments;
using DC.Utilities.Formatters;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Schema;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Customers;

/// <summary>
/// Customer verification dialog
/// </summary>
public class CustomerVerificationDialog : BaseDialog
{
    private readonly ICustomersService _customersService;
    private readonly INextBestActionService _nbaService;

    /// <summary>
    /// Constructor. Sets up the waterfall dialog.
    /// </summary>
    public CustomerVerificationDialog(
        ILoggerFactory loggerFactory,
        ILoggingService loggingService,
        ISessionManager sessionManager,
        IDialogValidators validators,
        ICustomersService customersService,
        INextBestActionService nbaService,
        IStorageService storageService) :
        base(loggerFactory, sessionManager, loggingService, storageService)
    {
        // Waterfall dialog with consecutive steps.
        AddDialog(new WaterfallDialog(nameof(WaterfallDialog), new WaterfallStep[] {
            InitTextLabels,
            AskForPostalCodeStep,
            AskForHouseNumberStep,
            AskForIbanSuffixStep,
            AskForDateOfBirthStep,
            AskForCustomerNumberStep,
            ProcessCustomerNumberStep,
            MultiFactorAuthenticationStep,
            MultiFactorAuthenticationValidationStep,
            CustomerAccountsStep,
            CloseVerificationOnlyStep
        }));

        // individual dialogs
        AddDialog(new TextPrompt(nameof(InitTextLabels)));
        AddDialog(new TextPrompt(nameof(AskForPostalCodeStep), validators.CustomerDialogValidator.VerificationPostalCodeValidator));
        AddDialog(new TextPrompt(nameof(AskForHouseNumberStep), validators.CustomerDialogValidator.VerificationHouseNumberValidator));
        AddDialog(new TextPrompt(nameof(AskForIbanSuffixStep)));
        AddDialog(new TextPrompt(nameof(AskForDateOfBirthStep), validators.CustomerDialogValidator.VerificationDateOfBirthValidator));
        AddDialog(new TextPrompt(nameof(AskForCustomerNumberStep), validators.CustomerDialogValidator.VerificationCustomerIdValidator));
        AddDialog(new ConfirmPrompt(nameof(ProcessCustomerNumberStep)));
        AddDialog(new TextPrompt(nameof(MultiFactorAuthenticationStep)));
        AddDialog(new TextPrompt(nameof(MultiFactorAuthenticationValidationStep)));
        AddDialog(new ChoicePrompt(nameof(CustomerAccountsStep)));
        //With this as a last step we can close the transaction if it was an $verification call
        AddDialog(new TextPrompt(nameof(CloseVerificationOnlyStep)));

        // initial dialog is the waterfall wrapper
        InitialDialogId = nameof(WaterfallDialog);

        _customersService = customersService;
        _nbaService = nbaService;
    }

    /// <summary>
    /// Checks if the dialog was a part of multiple dialogs
    /// $verification only triggers this dialog. Therefore the transaction should be closed at the end
    /// </summary>
    /// <param name="stepContext"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task<DialogTurnResult> CloseVerificationOnlyStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        //In case the dialog is used as an individually step
        var endDialog = stepContext.Options is Dictionary<string, bool> options && options.ContainsKey(DialogContextKeys._VERIFICATIONONLY) && options[DialogContextKeys._VERIFICATIONONLY];
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
        //// dialog is called individually
        if (endDialog)
        {
            TransactionStatus status = TransactionStatus.Success;
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CustomerVerificationDialog), nameof(CloseVerificationOnlyStep), status, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        // proceed to the next step after the verification dialog
        return await stepContext.NextAsync(dialogData, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// AskForPostalCodeStep default no-retry
    /// </summary>
    /// <param name="stepContext"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task<DialogTurnResult> AskForPostalCodeStep(WaterfallStepContext stepContext, CancellationToken cancellationToken) =>
        await AskForPostalCodeStep(stepContext, false, cancellationToken).ConfigureAwait(false);

    /// <summary>
    /// Postal code dialog.
    /// </summary>
    /// <param name="stepContext"></param>
    /// <param name="cancellationToken"></param>
    /// <param name="retry"></param>
    /// <returns></returns>
    private async Task<DialogTurnResult> AskForPostalCodeStep(WaterfallStepContext stepContext, bool retry, CancellationToken cancellationToken)
    {
        // first step in dialog: set current dialog action
        await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.CustomerVerification).ConfigureAwait(false);

        // get dialogdata from memory
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

        if (dialogData.Verification.IsVerified == true)
            return await stepContext.NextAsync(dialogData, cancellationToken).ConfigureAwait(false);

        //getting the textlabels of this dialog again when there are empty
        if (dialogData.TextLabels?.TextLabels?.Exists(i => i.GroupName == _textLabelGroupName) != true)
            await InitTextLabels(stepContext, cancellationToken);

        // if no postalcode is present in current user data
        if (string.IsNullOrWhiteSpace(dialogData.Verification.PostalCode))
        {
            if (!retry)
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("HaveToAskValidationQuestions", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

            // prompt for postal code
            return await stepContext.PromptAsync(nameof(AskForPostalCodeStep), new PromptOptions
            {
                Prompt = MessageFactory.Text(dialogData.TextLabels.GetValue("AskPostalCode", _textLabelGroupName))
            }, cancellationToken).ConfigureAwait(false);
        }

        // go to the next step in case the postal code is already present in the user data
        return await stepContext.NextAsync(dialogData.Verification.PostalCode, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// House number dialog.
    /// </summary>
    /// <param name="stepContext"></param>
    /// <param name="cancellationToken"></param>
    /// <param name="retry"></param>
    /// <returns></returns>
    private async Task<DialogTurnResult> AskForHouseNumberStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        // get dialogdata from memory
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

        if (dialogData.Verification.IsVerified == true)
            return await stepContext.NextAsync(dialogData, cancellationToken).ConfigureAwait(false);

        if (dialogData.Verification.TooManyAttempts)
            return await TooManyFormatErrors(VerificationStep.Postalcode, stepContext, cancellationToken).ConfigureAwait(false);

        // postal code is valid, format to desired pattern for back-end
        dialogData.Verification.PostalCode = (dialogData.Verification.PostalCode ?? ((string)stepContext.Result)?.RemoveWhitespaces())?.FormatToUpperAndWithoutSpace();

        // if no house number is present in current user data
        if (dialogData.Verification.HouseNumber == null)
        {
            // determine prompttext and prompt for house number.
            return await stepContext.PromptAsync(nameof(AskForHouseNumberStep), new PromptOptions
            {
                Prompt = MessageFactory.Text(dialogData.TextLabels.GetValue("AskHouseNumber", _textLabelGroupName))
            }, cancellationToken).ConfigureAwait(false);
        }

        // go to the next step in case the house number is already present in the user data
        return await stepContext.NextAsync(dialogData.Verification.HouseNumber.Value.ToString(), cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// Iban Suffix dialog.
    /// </summary>
    /// <param name="stepContext"></param>
    /// <param name="cancellationToken"></param>
    /// <param name="retry"></param>
    /// <returns></returns>
    private async Task<DialogTurnResult> AskForIbanSuffixStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        // get dialogdata from memory
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

        //check the dialogdata from memory
        if (dialogData.Verification?.CustomerId.HasValue == true &&
            !int.TryParse(dialogData.Verification.CustomerId.Value.ToString(), out _))
            _logger?.LogError("{StepName} -> CustomerId is too small or too big for customer {CustomerId}",
                nameof(AskForIbanSuffixStep), dialogData.Verification.CustomerId.Value);

        if (dialogData.Verification?.IsVerified == true)
            return await stepContext.NextAsync(dialogData, cancellationToken).ConfigureAwait(false);

        if (dialogData.Verification.TooManyAttempts)
            return await TooManyFormatErrors(VerificationStep.HouseNumber, stepContext, cancellationToken).ConfigureAwait(false);

        // house number is valid, put into user data
        dialogData.Verification.HouseNumber ??= int.Parse(((string)stepContext.Result).RemoveWhitespaces());

        // check for organisation to skip a few steps and proceed directly to the customer id step
        var organisationResult = await OrganisationCheck(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        
        if (organisationResult != null)
            return organisationResult;

        // If the user has entered invalid address details, offer 2 more attempts to enter the correct address.
        if (dialogData.Verification.PostalCode == null && dialogData.Verification.FindUserAttempts < 2)
        {
            dialogData.Verification.FindUserAttempts += 1;
            stepContext.SetNextStepIndex(Dialogs, nameof(AskForPostalCodeStep));
            return await AskForPostalCodeStep(stepContext, true, cancellationToken).ConfigureAwait(false);
        }

        // if no iban suffix is present in current user data
        if (string.IsNullOrWhiteSpace(dialogData.Verification.IbanSuffix) && dialogData.Verification.IsVerified != true)
        {
            // determine prompttext and prompt for iban suffix
            return await stepContext.PromptAsync(nameof(AskForIbanSuffixStep), new PromptOptions
            {
                Prompt = MessageFactory.Text(dialogData.TextLabels.GetValue("AskIbanDigits", _textLabelGroupName))
            }, cancellationToken).ConfigureAwait(false);
        }

        // go to the next step in case the iban suffix is already present in the user data
        return await stepContext.NextAsync(dialogData.Verification.IbanSuffix, cancellationToken).ConfigureAwait(false);
    }

    private async Task<DialogTurnResult> OrganisationCheck(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
    {
        if (dialogData.Verification?.CustomerId.HasValue == true &&
            !int.TryParse(dialogData.Verification.CustomerId.Value.ToString(), out _))
            _logger?.LogError("{StepName} -> CustomerId is too small or too big for customer {CustomerId}",
                nameof(OrganisationCheck), dialogData.Verification.CustomerId.Value);

        // always returns null, checks if there is a minimum of one business customer on provided address
        // if so, skip all personal questions and proceed to customer id 
        VerificationStatus isVerified = await TryToVerifyCustomer(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        switch (isVerified)
        {
            case VerificationStatus.OneCustomerFound:
                return await stepContext.NextAsync(dialogData, cancellationToken);
            case VerificationStatus.NoCustomersFound:
                return await EndDialog(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        }

        if (dialogData.Verification.IsPossiblyOrganisation == true)
        {
            // erase the current customer id
            dialogData.Verification.CustomerId = null;
            // go one step back
            stepContext.SetNextStepIndex(Dialogs, nameof(AskForCustomerNumberStep));
            // trigger retry
            return await AskForCustomerNumberStep(stepContext, cancellationToken).ConfigureAwait(false);
        }
        // else, return null to proceed as usual
        return null;
    }

    /// <summary>
    /// Date of birth dialog.
    /// </summary>
    /// <param name="stepContext"></param>
    /// <param name="cancellationToken"></param>
    /// <param name="retry"></param>
    /// <returns></returns>
    private async Task<DialogTurnResult> AskForDateOfBirthStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        // get dialogdata from memory
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

        if (dialogData.Verification.IsVerified == true)
            return await stepContext.NextAsync(dialogData, cancellationToken).ConfigureAwait(false);

        if (dialogData.Verification.TooManyAttempts)
            return await TooManyFormatErrors(VerificationStep.Iban, stepContext, cancellationToken).ConfigureAwait(false);

        // iban suffix is valid, put into user data
        dialogData.Verification.IbanSuffix ??= ((string)stepContext.Result)?.RemoveWhitespaces();

        if (dialogData.Verification?.CustomerId.HasValue == true &&
            !int.TryParse(dialogData.Verification.CustomerId.Value.ToString(), out _))
            _logger?.LogError("{StepName} -> CustomerId is too small or too big for customer {CustomerId}",
                nameof(AskForDateOfBirthStep), dialogData.Verification.CustomerId.Value);

        // try to confirm with the data already gathered, before proceeding to next questions
        var confirmed = await TryToVerifyCustomer(stepContext, dialogData, cancellationToken).ConfigureAwait(false);

        if (confirmed == VerificationStatus.OneCustomerFound)
            return await stepContext.NextAsync(dialogData, cancellationToken);


        if (confirmed == VerificationStatus.NoCustomersFound)
        {
            return await EndDialog(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        }

        // if no date of birth is present in current user data
        if (dialogData.Verification.DateOfBirth == null)
        {
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("InputNotValid", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

            // determine prompt text and prompt for date of birth.
            return await stepContext.PromptAsync(nameof(AskForDateOfBirthStep), new PromptOptions
            {
                Prompt = MessageFactory.Text(dialogData.TextLabels.GetValue("AskDateOfBirth", _textLabelGroupName))
            }, cancellationToken).ConfigureAwait(false);
        }

        // go to the next step in case the date of birth is already present in the user data
        return await stepContext.NextAsync(dialogData.Verification.DateOfBirth, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// Customer number dialog.
    /// </summary>
    /// <param name="stepContext"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task<DialogTurnResult> AskForCustomerNumberStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        // user is already confirmed, no further questions needed.
        if (stepContext.Result is DialogData confirmData)
            return await stepContext.NextAsync(confirmData, cancellationToken).ConfigureAwait(false);

        // get dialogdata from memory
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

        if (dialogData.Verification.IsVerified == true)
            return await stepContext.NextAsync(dialogData, cancellationToken).ConfigureAwait(false);

        if (dialogData.Verification.TooManyAttempts)
            return await TooManyFormatErrors(VerificationStep.DateOfBirth, stepContext, cancellationToken).ConfigureAwait(false);

        if (dialogData.Verification.IsPossiblyOrganisation == false)
        {
            // VerificationDateOfBirthValidator Ensures this Parse alwways succeeds
            var dateOfBirthString = dialogData.Verification.DateOfBirth?.ToString("dd-MM-yyyy") ?? ((string)stepContext.Result)?.RemoveWhitespaces();
            DateTime.TryParseExact(dateOfBirthString, "dd-MM-yyyy", new CultureInfo("NL-nl"), DateTimeStyles.None, out var dateOfBirth);

            // Date of Birth is valid
            dialogData.Verification.DateOfBirth = dateOfBirth;

            if (dialogData.Verification?.CustomerId.HasValue == true &&
                !int.TryParse(dialogData.Verification.CustomerId.Value.ToString(), out _))
                _logger?.LogError("{StepName} -> CustomerId is too small or too big for customer {CustomerId}",
                    nameof(AskForCustomerNumberStep), dialogData.Verification.CustomerId.Value);

            // try to confirm with the data already gathered (address + phone number), before proceeding to next questions
            var confirmed = await TryToVerifyCustomer(stepContext, dialogData, cancellationToken).ConfigureAwait(false);

            // will return null in case not confirmed
            if (confirmed == VerificationStatus.OneCustomerFound)
                return await stepContext.NextAsync(dialogData, cancellationToken);

            if (confirmed == VerificationStatus.NoCustomersFound)
            {
                return await EndDialog(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
            }
        }

        // if no customer id is already present in the user data
        if (dialogData.Verification.CustomerId == null)
        {
            return await stepContext.PromptAsync(nameof(AskForCustomerNumberStep), new PromptOptions
            {
                Prompt = MessageFactory.Text(dialogData.TextLabels.GetValue("AskCustomerId", _textLabelGroupName))
            }, cancellationToken).ConfigureAwait(false);
        }

        // go to the next step in case the customer id is already present in the user data
        return await stepContext.NextAsync(dialogData.Verification.CustomerId, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// Processes the customer number prompt answer.
    /// End of the current waterfall.
    /// </summary>
    /// <param name="stepContext"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task<DialogTurnResult> ProcessCustomerNumberStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        // user is already confirmed via dialog (not via token), no further questions needed.
        if (stepContext.Result is DialogData confirmData && !confirmData.Verification.IsVerifiedWithToken)
            return await stepContext.NextAsync(confirmData, cancellationToken).ConfigureAwait(false);

        // get dialogdata from memory
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

        // if already verified via dialog (not via token), proceed with the next step
        if (dialogData.Verification.IsVerified == true && !dialogData.Verification.IsVerifiedWithToken)
            return await stepContext.NextAsync(dialogData, cancellationToken).ConfigureAwait(false);

        if (dialogData.Verification.TooManyAttempts)
            return await TooManyFormatErrors(VerificationStep.CustomerId, stepContext, cancellationToken).ConfigureAwait(false);

        // VerificationCustomerIdValidator ensures this never fails and added stripping of possible account ID
        dialogData.Verification.CustomerId = long.Parse(dialogData.Verification.CustomerId?.ToString().FormatWithoutSequenceNumber() ?? ((string)stepContext.Result)?.RemoveWhitespaces().FormatWithoutSequenceNumber());
        dialogData.Verification.PhoneNumber = null;

        if (dialogData.Verification?.CustomerId.HasValue == true &&
            !int.TryParse(dialogData.Verification.CustomerId.Value.ToString(), out _))
            _logger?.LogError("{StepName} -> CustomerId is too small or too big for customer {CustomerId}",
                nameof(ProcessCustomerNumberStep), dialogData.Verification.CustomerId.Value);

        // try to confirm with the data gathered (address + customer id). this is the last possible step
        var confirmed = await TryToVerifyCustomer(stepContext, dialogData, cancellationToken).ConfigureAwait(false);

        // proceed to the next step outside of the verification dialog, if verified
        if (confirmed == VerificationStatus.OneCustomerFound)
        {
            dialogData.Verification.CustomerIdVerified = true;
            return await stepContext.NextAsync(dialogData, cancellationToken).ConfigureAwait(false);
        }
        if (confirmed == VerificationStatus.NoCustomersFound)
        {
            return await EndDialog(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        }

        // confirmed == null means not verified. the dialog ends here. Log non-success event in AI            
        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CustomerVerificationDialog), nameof(ProcessCustomerNumberStep), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
    }

    private async Task<DialogTurnResult> MultiFactorAuthenticationStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        // get dialogdata from memory
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

        if (!DigitalCoreEnvironment.IsProduction())
            return await stepContext.NextAsync(dialogData, cancellationToken).ConfigureAwait(false);

        if (dialogData.Verification.IsVerified == true && dialogData.Verification.MfaSuccessful)
            return await stepContext.NextAsync(dialogData, cancellationToken).ConfigureAwait(false);

        // if MFA was not yet successful
        if (!dialogData.Verification.MfaSuccessful)
        {
            return await stepContext.PromptAsync(nameof(MultiFactorAuthenticationStep), new PromptOptions
            {
                Prompt = GetVerificationActivityWithMfaMetadata(dialogData)
            }, cancellationToken).ConfigureAwait(false);
        }

        // if the user has already been successfully authenticated, continue to the next step
        return await stepContext.NextAsync(dialogData, cancellationToken).ConfigureAwait(false);
    }

    private async Task<DialogTurnResult> MultiFactorAuthenticationValidationStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        // get dialogdata from memory
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

        if (dialogData.Verification.MfaSuccessful)
            return await stepContext.NextAsync(dialogData, cancellationToken).ConfigureAwait(false);

        if (!DigitalCoreEnvironment.IsProduction() || IsMfaSuccessResponse(stepContext.Context.Activity))
        {
            dialogData.Verification.MfaSuccessful = true;
        }

        if (!dialogData.Verification.MfaSuccessful)
        {
            dialogData.Verification.IsVerified = false;
            dialogData.Verification.VerificationCommunicated = false;

            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("MfaCodeIncorrect", _textLabelGroupName),
                cancellationToken: cancellationToken).ConfigureAwait(false);

            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CustomerVerificationDialog), nameof(MultiFactorAuthenticationValidationStep), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
            await _sessionManager.ResetConversationData(stepContext.Context).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        // gets customer profile to communicate the name
        var customer = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);
        EnrichDialogDataWithCustomerData(dialogData, customer);

        var nbaTask = _nbaService.GetNextBestActions(dialogData);
        var personalisationInfoTask = _customersService.GetPersonalisationInfo(dialogData, customer);

        await Task.WhenAll(personalisationInfoTask, nbaTask).ConfigureAwait(false);
        var nba = await nbaTask.ConfigureAwait(false);
        var personalisationInfo = await personalisationInfoTask.ConfigureAwait(false);

        dialogData.NextBestAction.Data = nba;

        // if verification has not been communicated, prompt the user with the verification notification
        // and log the successful verification in AI
        if (dialogData.Verification.VerificationCommunicated != true)
        {
            TransactionStatus status = TransactionStatus.Success;

            //If there are no phone numbers for this customer, status must be UnhappyPhoneNumber
            if (!dialogData.Contact.PhoneNumbers.Any())
            {
                status = TransactionStatus.UnhappyPhoneNumber;
            }

            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("DataFoundWelcomeMessage", _textLabelGroupName).Replace("{customerName}", dialogData.Customer.Name), cancellationToken: cancellationToken).ConfigureAwait(false);
            // set bool to true so the verification confirmation is not prompted on any request that needs verification
            dialogData.Verification.VerificationCommunicated = true;

            await _sessionManager.LogEvent(stepContext.Context, nameof(CustomerVerificationDialog), nameof(MultiFactorAuthenticationValidationStep), status).ConfigureAwait(false);
            await _sessionManager.SendCustomerInfoEvent(stepContext.Context, personalisationInfo, nba.ToNextBestActionInfo()).ConfigureAwait(false);
        }

        // proceed to the next step after the verification dialog
        return await stepContext.NextAsync(dialogData, cancellationToken).ConfigureAwait(false);
    }

    private static bool IsMfaSuccessResponse(IActivity mfaResponseActivity)
    {
        var mfaEntity = mfaResponseActivity?.Entities?.SingleOrDefault(entity => entity.Type == "mfa");

        return (mfaEntity?.Properties)?["value"] is JObject propertiesAsObject && (bool?)propertiesAsObject.GetValue("success") == true;
    }

    private static void EnrichDialogDataWithCustomerData(DialogData dialogData, CustomerModel customer)
    {
        dialogData.Customer.Name = customer.GetName();
        dialogData.Customer.Firstname = customer.GetFirstname();
        dialogData.Customer.Surname = customer.GetSurname();
        dialogData.Customer.Gender = customer.GetGender();
        dialogData.Customer.EmailAddress = customer.Contact?.EmailAddress;

        //Get user PhoneNumbers
        dialogData.Contact.PhoneNumbers = customer.GetPhoneNumbers();

        dialogData.SetAllActiveAccountsFromCustomerModel(customer);
    }

    private Activity GetVerificationActivityWithMfaMetadata(DialogData dialogData)
    {
        Activity activity = new()
        {
            Text = dialogData.TextLabels.GetValue("MfaPromptMessage", _textLabelGroupName),
            Type = "message"
        };

        var mfaMetadata = new MfaMetadata
        {
            MobilePhoneNumbers = [],
            EmailAddresses = []
        };

        if (dialogData.Contact.MobilePhoneNumber is not null)
        {
            mfaMetadata.MobilePhoneNumbers.Add(dialogData.Contact.MobilePhoneNumber);
        }

        if (dialogData.Customer.EmailAddress is not null)
        {
            mfaMetadata.EmailAddresses.Add(dialogData.Customer.EmailAddress);
        }

        activity.Entities = new List<Entity>()
        {
            new()
            {
                Properties = JObject.FromObject(new EntityProperties<MfaMetadata>("expectedEntry", mfaMetadata))
            }
        };
        return activity;
    }

    /// <summary>
    /// Unhappy output for too many PostalCode Format errors
    /// </summary>
    /// <param name="stepContext"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task<DialogTurnResult> TooManyFormatErrors(VerificationStep step, WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        // get dialogdata from memory
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

        string text = step switch
        {
            VerificationStep.Postalcode => dialogData.TextLabels.GetValue("ValidationErrorPostalCode", _textLabelGroupName),
            VerificationStep.HouseNumber => dialogData.TextLabels.GetValue("ValidationErrorHouseNumber", _textLabelGroupName),
            VerificationStep.Iban => dialogData.TextLabels.GetValue("ValidationErrorIbanDigits", _textLabelGroupName),
            VerificationStep.DateOfBirth => dialogData.TextLabels.GetValue("ValidationErrorDateOfBirth", _textLabelGroupName),
            VerificationStep.CustomerId => dialogData.TextLabels.GetValue("ValidationErrorCustomerId", _textLabelGroupName),
            _ => dialogData.TextLabels.GetValue("ValidationErrorOtherError", _textLabelGroupName)
        };

        var errorMessage = dialogData.TextLabels.GetValue("ValidationError", _textLabelGroupName).Replace("{validationErrorReason}", text);

        _ = await stepContext.Context.SendActivityAsync(errorMessage, cancellationToken: cancellationToken).ConfigureAwait(false);

        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CustomerVerificationDialog), nameof(TooManyFormatErrors), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// Tries to verify the customer with the current state of customer data.
    /// </summary>
    /// <param name="stepContext"></param>
    /// <param name="dialogData"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task<VerificationStatus> TryToVerifyCustomer(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
    {
        // verify with current user data if not verified with the token already
        if (!dialogData.Verification.IsVerifiedWithToken)
            dialogData = await _customersService.VerifyCustomer(dialogData).ConfigureAwait(false);

        // customer is found (based on address) AND verified (based on additional data).
        if (dialogData.Verification.IsFound == true && dialogData.Verification.IsVerified == true)
        {
            // if verification has not been communicated, prompt the user with the verification notification
            // and log the successful verification in AI
            if (dialogData.Verification.VerificationCommunicated == true)
            {
                // proceed to the next step after the verification dialog
                return VerificationStatus.OneCustomerFound;
            }
            var customer = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);

            dialogData.Customer.EmailAddress = customer.Contact?.EmailAddress;
            dialogData.Contact.MobilePhoneNumber = customer.Contact?.MobilePhoneNumber;

            // proceed to the next step after the verification dialog
            return VerificationStatus.OneCustomerFound;
        }
        // customer is found, cannot be verified. return null to proceed with further questions or end the dialog.
        else if (dialogData.Verification.IsFound == true && dialogData.Verification.IsVerified == false)
        {
            return VerificationStatus.MultipleCustomersFound;
        }
        // customer is not found and not verified. Offer the user 1 more attempt to enter a valid address. Go back to the postal step
        else if (dialogData.Verification.IsFound == false && dialogData.Verification.IsVerified == false && dialogData.Verification.FindUserAttempts < 2)
        {
            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("DataNotFound", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            dialogData.Verification.PostalCode = null;
            dialogData.Verification.HouseNumber = null;
            dialogData.Verification.HouseNumberSuffix = null;
            dialogData.Verification.CustomerId = null;
            dialogData.SelectedAccount = null;
            return VerificationStatus.NoCustomersFoundTryAgain;
        }
        // customer is not found based on address. end the dialog, log non-success event in AI and reset the state.
        else
        {
            return VerificationStatus.NoCustomersFound;
        }
    }

    private async Task<DialogTurnResult> EndDialog(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
    {
        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CustomerIdNotFound", _textLabelGroupName),
                        cancellationToken: cancellationToken).ConfigureAwait(false);

        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CustomerVerificationDialog), nameof(EndDialog), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
        await _sessionManager.ResetConversationData(stepContext.Context).ConfigureAwait(false);
        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
    }

    private static async Task<DialogTurnResult> CustomerAccountsStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        return await stepContext.BeginDialogAsync(nameof(CustomerAccountsDialog), stepContext.Result, cancellationToken).ConfigureAwait(false);
    }
}
