﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Models.Extensions;
using DC.Utilities.Formatters;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Customers.PhoneNumberDialogs
{
    /// <summary>
    /// This is a waterfalldialog that contains 3 flows
    /// User has
    /// - no phonenumbers
    /// - one phonenumber
    /// - two phonenumbbers
    /// In the end the CustomerPatch contains all the phonenumbers that the customer has approved.
    /// If the customer has two phonenumbers and changes the second one. The patch contains the first one and the changed second phonenumber
    /// 
    /// The dialogflow is controlled based on the AmountOfContactPhoneNumbers.
    /// </summary>
    public class ChangePhoneNumberDialog : BaseDialog
    {
        private readonly ICustomersService _customersService;
        private readonly INextBestActionService _nextBestActionService;

        /// <summary>
        /// Contructor
        /// </summary>
        public ChangePhoneNumberDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            IDialogValidators dialogValidators,
            ICustomersService customersService,
            INextBestActionService nextBestActionService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
                 new WaterfallStep[]
                 {
                    InitTextLabels,

                    AskToChangeFirstOfTwoPhoneNumbersDialog,
                    AskToChangeFirstOfTwoPhoneNumbersAnswer,

                    AdjustPhoneNumberDialog,
                    AdjustPhoneNumberAnswer,

                    AskToChangeSecondOfTwoPhoneNumbersDialog,
                    AskToChangeSecondOfTwoPhoneNumbersAnswer,

                    AdjustPhoneNumberDialog,
                    AdjustPhoneNumberAnswer,

                    AskToChangeFirstPhoneNumberDialog,
                    AskToChangeFirstPhoneNumberAnswer,

                    AdjustPhoneNumberDialog,
                    AdjustPhoneNumberAnswer,

                    AskToAddFirstPhoneNumberDialog,
                    AskToAddFirstPhoneNumberAnswer,

                    AdjustPhoneNumberDialog,
                    AdjustPhoneNumberAnswer,

                    AskToAddSecondPhoneNumberDialog,
                    AskToAddSecondPhoneNumberAnswer,

                    AdjustPhoneNumberDialog,
                    AdjustPhoneNumberAnswer,

                    SaveData
                 }));

            AddDialog(new TextPrompt(nameof(InitTextLabels)));

            AddDialog(new ChoicePrompt(nameof(AskToChangeFirstOfTwoPhoneNumbersDialog)));
            AddDialog(new TextPrompt(nameof(AskToChangeFirstOfTwoPhoneNumbersAnswer)));

            AddDialog(new ChoicePrompt(nameof(AskToChangeSecondOfTwoPhoneNumbersDialog)));
            AddDialog(new TextPrompt(nameof(AskToChangeSecondOfTwoPhoneNumbersAnswer)));

            AddDialog(new ChoicePrompt(nameof(AskToChangeFirstPhoneNumberDialog)));
            AddDialog(new TextPrompt(nameof(AskToChangeFirstPhoneNumberAnswer)));

            AddDialog(new ChoicePrompt(nameof(AskToAddFirstPhoneNumberDialog)));
            AddDialog(new TextPrompt(nameof(AskToAddFirstPhoneNumberAnswer)));

            AddDialog(new ChoicePrompt(nameof(AskToAddSecondPhoneNumberDialog)));
            AddDialog(new TextPrompt(nameof(AskToAddSecondPhoneNumberAnswer)));

            AddDialog(new TextPrompt(nameof(AdjustPhoneNumberDialog), dialogValidators.CustomerDialogValidator.CustomerPhoneValidator));
            AddDialog(new TextPrompt(nameof(AdjustPhoneNumberAnswer)));

            InitialDialogId = nameof(WaterfallDialog);

            _customersService = customersService;
            _nextBestActionService = nextBestActionService;
        }

        private static int AmountOfContactPhoneNumbers(DialogData dialogData)
        {
            return dialogData.PhoneNumber.AmountOfPhoneNumbersKnown;
        }

        /// <summary>
        /// Setup startingpoint to choose which scenario we have
        /// </summary>
        private async Task<DialogData> SetupDialogData(WaterfallStepContext stepContext)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ChangePhoneNumber).ConfigureAwait(false);

            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            if (dialogData.IsVerified())
            {
                var customer = await _customersService.GetCustomerV2(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.Value).ConfigureAwait(false);
                dialogData.Contact = customer?.ToContact();

                dialogData.PhoneNumber.ChangePhoneNumber = false;
                dialogData.PhoneNumber.AmountOfPhoneNumbersKnown = dialogData.Contact?.PhoneNumbers?.Count ?? 0;
            }

            return dialogData;
        }

        private async Task<DialogTurnResult> AskToChangeFirstOfTwoPhoneNumbersDialog(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ChangePhoneNumber).ConfigureAwait(false);
            var dialogData = await SetupDialogData(stepContext).ConfigureAwait(false);
            // to be sure the verificationproces has been handled correctly
            if (!dialogData.IsVerified())
            {
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangePhoneNumberDialog), nameof(AskToChangeFirstOfTwoPhoneNumbersDialog), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
                return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
            }
            //Only if the user starts with 2 numbers
            if (AmountOfContactPhoneNumbers(dialogData) == 2)
            {
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CurrentKnownPhoneNumbers", _textLabelGroupName)
                    .Replace("{phonenumber1}", dialogData.Contact.PhoneNumbers[0].ObfuscatePhoneNumber())
                    .Replace("{phonenumber2}", dialogData.Contact.PhoneNumbers[1].ObfuscatePhoneNumber()), cancellationToken: cancellationToken).ConfigureAwait(false);
                var prompt = dialogData.TextLabels.GetValue("AdjustPhoneNumberOne", _textLabelGroupName).Replace("{phonenumber1}", dialogData.Contact.PhoneNumbers[0].ObfuscatePhoneNumber());

                //ADJUST main phonenumber or add second
                return await stepContext.PromptAsync(nameof(AskToChangeFirstOfTwoPhoneNumbersDialog), new PromptOptions
                {
                    Prompt = MessageFactory.Text(prompt),
                    Choices = SetupButtons(dialogData),
                    RetryPrompt = MessageFactory.Text(prompt)
                }, cancellationToken).ConfigureAwait(false);
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskToChangeFirstOfTwoPhoneNumbersAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            //Only if the user starts with 2 numbers
            if (AmountOfContactPhoneNumbers(dialogData) == 2 && dialogData != null)
            {
                dialogData.PhoneNumber.ChangePhoneNumber = stepContext.IsChoiceSelected("ja");

                //Add the old number as an approved phonenumber
                if (!stepContext.IsChoiceSelected("ja"))
                    dialogData.PhoneNumber.ApprovedPhoneNumbers.Add(dialogData.Contact.PhoneNumbers[0].RemoveWhitespaces());
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskToChangeSecondOfTwoPhoneNumbersDialog(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            //Only if the user starts with 2 numbers
            if (AmountOfContactPhoneNumbers(dialogData) == 2)
            {
                var prompt = dialogData.TextLabels.GetValue("AdjustPhoneNumberTwo", _textLabelGroupName).Replace("{phonenumber2}", dialogData.Contact.PhoneNumbers[1].ObfuscatePhoneNumber());

                //ADJUST main phonenumber or add second
                return await stepContext.PromptAsync(nameof(AskToChangeSecondOfTwoPhoneNumbersDialog), new PromptOptions
                {
                    Prompt = MessageFactory.Text(prompt),
                    Choices = SetupButtons(dialogData),
                    RetryPrompt = MessageFactory.Text(prompt)
                }, cancellationToken).ConfigureAwait(false);
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskToChangeSecondOfTwoPhoneNumbersAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            //Only if the user starts with 2 numbers
            if (AmountOfContactPhoneNumbers(dialogData) == 2 && dialogData != null)
            {
                dialogData.PhoneNumber.ChangePhoneNumber = stepContext.IsChoiceSelected("ja");

                //Add the old number as an approved phonenumber 
                if (!stepContext.IsChoiceSelected("ja"))
                    dialogData.PhoneNumber.ApprovedPhoneNumbers.Add(dialogData.Contact.PhoneNumbers[1].RemoveWhitespaces());
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskToChangeFirstPhoneNumberDialog(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            //Only if the user starts with 1 number
            if (AmountOfContactPhoneNumbers(dialogData) == 1)
            {
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CurrentKnownPhoneNumber", _textLabelGroupName)
                    .Replace("{phonenumber1}", dialogData.Contact.PhoneNumbers[0].ObfuscatePhoneNumber()), cancellationToken: cancellationToken).ConfigureAwait(false);
                var prompt = dialogData.TextLabels.GetValue("AskToAdjustPhoneNumber", _textLabelGroupName);
                var yesNoNewNumber = SetupButtons(dialogData).ToList();
                yesNoNewNumber.Add(new Choice
                {
                    Value = dialogData.TextLabels.GetValue("AddPhoneNumberTwoChoice", _textLabelGroupName)
                });

                return await stepContext.PromptAsync(nameof(AskToChangeFirstPhoneNumberDialog), new PromptOptions
                {
                    Prompt = MessageFactory.Text(prompt),
                    Choices = yesNoNewNumber,
                    RetryPrompt = MessageFactory.Text(prompt)
                }, cancellationToken).ConfigureAwait(false);
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskToChangeFirstPhoneNumberAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            //Only if the user starts with 1 number
            if (AmountOfContactPhoneNumbers(dialogData) == 1)
            {
                var changeFirstNumber = stepContext.IsChoiceSelected("ja");
                var addSecondNumber = stepContext.IsChoiceSelected("toevoegen");
                dialogData.PhoneNumber.ChangePhoneNumber = changeFirstNumber || addSecondNumber;

                if (changeFirstNumber || addSecondNumber)
                {
                    //When the user selects this option, the first number stay's the way it is
                    if (addSecondNumber)
                        dialogData.PhoneNumber.ApprovedPhoneNumbers.Add(dialogData.Contact.PhoneNumbers[0].RemoveWhitespaces());

                    return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
                }

                _ = await stepContext.Context.SendActivityAsync(
                    dialogData.TextLabels.GetValue("KeepPhoneNumberOne", _textLabelGroupName)
                    .Replace("{phonenumber1}", dialogData.Contact.PhoneNumbers[0].ObfuscatePhoneNumber()), cancellationToken: cancellationToken).ConfigureAwait(false);

                if (dialogData.NextBestAction?.CurrentTransactionIsNba == true)
                    await _nextBestActionService.AddFeedback(dialogData, "CustomerPhoneNumber", Domain.Models.NextBestAction.FeedbackStatus.Conversion_success).ConfigureAwait(false);

                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangePhoneNumberDialog), nameof(AskToChangeFirstPhoneNumberAnswer), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskToAddFirstPhoneNumberDialog(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            //Only if the user starts with 0 numbers
            if (AmountOfContactPhoneNumbers(dialogData) == 0)
            {
                // if this dialog is called from the NBA dialog, skip prompting  the customer because this has already been prompted in the NBA dialog.
                if (dialogData.NextBestAction?.CurrentTransactionIsNba == true)
                    return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);

                await stepContext.Context.SendActivityAsync(
                    dialogData.TextLabels.GetValue("UnknownPhoneNumbers", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

                var prompt = dialogData.TextLabels.GetValue("AskToAddPhoneNumber", _textLabelGroupName);
                return await stepContext.PromptAsync(nameof(AskToAddFirstPhoneNumberDialog), new PromptOptions
                {
                    Prompt = MessageFactory.Text(prompt),
                    Choices = SetupButtons(dialogData),
                    RetryPrompt = MessageFactory.Text(prompt)
                }, cancellationToken).ConfigureAwait(false);
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskToAddFirstPhoneNumberAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            //Only if the user starts with 0 numbers
            if (AmountOfContactPhoneNumbers(dialogData) == 0 && dialogData != null)
            {
                // if this dialog is called from the NBA dialog, skip prompting  the customer because this has already been prompted in the NBA dialog.
                if (dialogData.NextBestAction?.CurrentTransactionIsNba == true)
                {
                    dialogData.PhoneNumber.ChangePhoneNumber = true;
                    return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
                }

                if (!stepContext.IsChoiceSelected("ja"))
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CustomerDoesNotChangePhoneNumber", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);

                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangePhoneNumberDialog), nameof(AskToAddFirstPhoneNumberAnswer), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }

                dialogData.PhoneNumber.ChangePhoneNumber = stepContext.IsChoiceSelected("ja");
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskToAddSecondPhoneNumberDialog(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            //Only if the user starts with 0 numbers or if the user added the first one
            if (AmountOfContactPhoneNumbers(dialogData) == 0 || dialogData.PhoneNumber.ApprovedPhoneNumbers.Count == 1)
            {
                var prompt = dialogData.TextLabels.GetValue("AskToAddPhoneNumberTwo", _textLabelGroupName);
                return await stepContext.PromptAsync(nameof(AskToAddSecondPhoneNumberDialog), new PromptOptions
                {
                    Prompt = MessageFactory.Text(prompt),
                    Choices = SetupButtons(dialogData),
                    RetryPrompt = MessageFactory.Text(prompt)
                }, cancellationToken).ConfigureAwait(false);
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskToAddSecondPhoneNumberAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            if (AmountOfContactPhoneNumbers(dialogData) == 0 || dialogData.PhoneNumber.ApprovedPhoneNumbers.Count == 1)
                dialogData.PhoneNumber.ChangePhoneNumber = stepContext.IsChoiceSelected("ja");

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> SaveData(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            dialogData.CheckApprovedPhoneNumbers();

            if (dialogData.Contact != null && dialogData.Contact.PhoneNumbers.Count == dialogData.PhoneNumber.ApprovedPhoneNumbers.Count && !dialogData.PhoneNumber.ApprovedPhoneNumbers.Except(dialogData.Contact.PhoneNumbers).Any())
            {
                //current transaction is nba and keep the current phonenumber set the nba conversion on success
                if (dialogData.NextBestAction?.CurrentTransactionIsNba == true)
                    await _nextBestActionService.AddFeedback(dialogData, "CustomerPhoneNumber", Domain.Models.NextBestAction.FeedbackStatus.Conversion_success).ConfigureAwait(false);

                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CustomerDoesNotChangePhoneNumber", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangePhoneNumberDialog), nameof(SaveData), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            var newPhoneNumbers = dialogData.PhoneNumber.ApprovedPhoneNumbers.Except(dialogData.Contact.PhoneNumbers).ToList();
            if (newPhoneNumbers.Count >= 2)
            {
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NewPhoneNumbers", _textLabelGroupName)
                    .Replace("{phonenumber1}", dialogData.PhoneNumber.ApprovedPhoneNumbers[0].ObfuscatePhoneNumber())
                    .Replace("{phonenumber2}", dialogData.PhoneNumber.ApprovedPhoneNumbers[1].ObfuscatePhoneNumber()),
                    cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            //SAVE DATA
            return await PatchCustomerProfile(stepContext, dialogData, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Patches the customer profile.
        /// </summary>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Minor Code Smell", "S2221:\"Exception\" should not be caught when not required by called methods", Justification = "Must catch all to prevent bot from crashing")]
        private async Task<DialogTurnResult> PatchCustomerProfile(WaterfallStepContext stepContext, DialogData dialogData, CancellationToken cancellationToken)
        {
            try
            {
                await _customersService.PatchCustomerProfile(dialogData).ConfigureAwait(false);

                if (dialogData.PhoneNumber.ApprovedPhoneNumbers.Count == 1)
                {
                    _ = await stepContext.Context.SendActivityAsync(
                        dialogData.TextLabels.GetValue("NewPhoneNumber", _textLabelGroupName)
                        .Replace("{phonenumber1}", dialogData.PhoneNumber.ApprovedPhoneNumbers[0].ObfuscatePhoneNumber()),
                        cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else
                {
                    _ = await stepContext.Context.SendActivityAsync(
                        dialogData.TextLabels.GetValue("NewPhoneNumbers", _textLabelGroupName)
                        .Replace("{phonenumber1}", dialogData.PhoneNumber.ApprovedPhoneNumbers[0].ObfuscatePhoneNumber())
                        .Replace("{phonenumber2}", dialogData.PhoneNumber.ApprovedPhoneNumbers[1].ObfuscatePhoneNumber()),
                        cancellationToken: cancellationToken).ConfigureAwait(false);
                }

                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangePhoneNumberDialog), nameof(SaveData), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(SaveData), "Failed to change phonenumber");

                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangePhoneNumberDialog), nameof(SaveData), TransactionStatus.PermanentFailure, cancellationToken).ConfigureAwait(false);
                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            finally
            {
                // Reset data
                dialogData.PhoneNumber = new PhoneNumberDialogData();
            }

            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        #region repeating part to add or change phonenumber
        private async Task<DialogTurnResult> AdjustPhoneNumberDialog(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ChangePhoneNumber).ConfigureAwait(false);

            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            var prompt = !dialogData.NextBestAction.CurrentTransactionIsNba ?
                dialogData.TextLabels.GetValue("AddPhoneNumber", _textLabelGroupName) :
                dialogData.TextLabels.GetValue("NbaAddPhoneNumber", _textLabelGroupName);

            if (PhoneNumberAdjusted(dialogData))
            {
                return await stepContext.PromptAsync(nameof(AdjustPhoneNumberDialog), new PromptOptions
                {
                    Prompt = MessageFactory.Text(prompt),
                    RetryPrompt = MessageFactory.Text(dialogData.TextLabels.GetValue("InvalidPhoneNumber", _textLabelGroupName))
                }, cancellationToken).ConfigureAwait(false);
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AdjustPhoneNumberAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            if (PhoneNumberAdjusted(dialogData))
            {
                if (dialogData.Verification.TooManyAttempts)
                {
                    dialogData.Verification.TooManyAttempts = false;

                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("InvalidPhoneNumberThreeTimes", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangePhoneNumberDialog), nameof(AdjustPhoneNumberAnswer), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }

                dialogData.PhoneNumber.ApprovedPhoneNumbers.Add((string)stepContext.Result);
                dialogData.PhoneNumber.ChangePhoneNumber = false;

                await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("PhoneNumberSaved", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private static bool PhoneNumberAdjusted(DialogData dialogData)
        {
            return dialogData.PhoneNumber.ChangePhoneNumber;
        }

        private List<Choice> SetupButtons(DialogData dialogData)
        {
            return new List<Choice> {
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("YesChoice", _textLabelGroupName),
                    Synonyms = new List<string> { "Ja" }
                },
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("NoChoice", _textLabelGroupName),
                    Synonyms = new List<string> { "Nee", "Neen" }
                }

            };
        }

        #endregion
    }
}