﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.Session;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;

namespace DC.Bot.BusinessLogic.Dialogs.Customers;

public class ChangeContactPreferencesDialog : BaseDialog
{
    private readonly IList<Choice> _yesNoChoice = new List<Choice>
    {
        new()
        {
            Value = "Ja, natuurlijk",
            Synonyms = ["Ja"]
        },
        new()
        {
            Value = "Nee",
            Synonyms = ["Nee", "Neen"]
        }
    };

    private readonly IContactPreferencesService _contactPreferencesService;

    public ChangeContactPreferencesDialog(
        ILoggerFactory loggerFactory,
        ISessionManager sessionManager,
        ILoggingService loggingService,
        IStorageService storageService,
        IContactPreferencesService contactPreferencesService)
        : base(
            loggerFactory,
            sessionManager,
            loggingService,
            storageService)
    {
        _contactPreferencesService = contactPreferencesService;

        AddDialog(
            new WaterfallDialog(
                nameof(WaterfallDialog),
                [
                    InitTextLabels,
                    InitializeDialog,
                    AnalyzeCustomerContactPreferences,
                    HandleCustomerChoice
                ]));

        AddDialog(new ChoicePrompt(nameof(HandleOnlyInvoiceEmailPreference)));
        AddDialog(new ChoicePrompt(nameof(HandleOnlyMonthlyEnergyReportEmailPreference)));
        AddDialog(new ChoicePrompt(nameof(HandleNoEmailPreference)));

        InitialDialogId = nameof(WaterfallDialog);
    }

    private async Task<DialogTurnResult> InitializeDialog(
        WaterfallStepContext stepContext,
        CancellationToken cancellationToken)
    {
        await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ChangeContactPreferences);
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context);

        if (!dialogData.IsVerified())
        {
            await _sessionManager.SendEndOfTransactionActivity(
                stepContext.Context,
                nameof(ChangeContactPreferencesDialog),
                nameof(AnalyzeCustomerContactPreferences),
                TransactionStatus.TemporaryFailure,
                cancellationToken);
            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken);
        }

        if (string.IsNullOrWhiteSpace(dialogData.Customer.EmailAddress))
        {
            return await stepContext.ReplaceDialogAsync(nameof(ChangeEmailDialog), cancellationToken: cancellationToken);
        }

        dialogData.ChangeContactPreferencesData = new ChangeContactPreferencesData();

        return await stepContext.NextAsync(null, cancellationToken);
    }

    private async Task<DialogTurnResult> AnalyzeCustomerContactPreferences(
        WaterfallStepContext stepContext,
        CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context);

        var emailContactPreferences = await _contactPreferencesService.GetEmailContactPreferences(
            dialogData.Customer.Label,
            dialogData.Verification.CustomerId.GetValueOrDefault(0));

        dialogData.ChangeContactPreferencesData.CurrentPreferences = emailContactPreferences;

        if (emailContactPreferences.IsInvoicePreferenceEmail &&
            emailContactPreferences.IsMonthlyEnergyReportPreferenceEmail)
        {
            return await AllPreferencesAlreadyEmail(stepContext, dialogData, cancellationToken);
        }

        if (emailContactPreferences.IsInvoicePreferenceEmail
            && !emailContactPreferences.IsMonthlyEnergyReportPreferenceEmail)
        {
            return await HandleOnlyInvoiceEmailPreference(stepContext, dialogData, cancellationToken);
        }

        if (!emailContactPreferences.IsInvoicePreferenceEmail
            && emailContactPreferences.IsMonthlyEnergyReportPreferenceEmail)
        {
            return await HandleOnlyMonthlyEnergyReportEmailPreference(stepContext, dialogData, cancellationToken);
        }

        return await HandleNoEmailPreference(stepContext, dialogData, cancellationToken);
    }

    private async Task<DialogTurnResult> AllPreferencesAlreadyEmail(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        CancellationToken cancellationToken)
    {
        string alreadyEmail = dialogData.TextLabels.GetValue("AlreadyEmail", _textLabelGroupName);

        await stepContext.Context.SendActivityAsync(alreadyEmail, cancellationToken: cancellationToken);

        await _sessionManager.SendEndOfTransactionActivity(
            stepContext.Context,
            nameof(ChangeContactPreferencesDialog),
            nameof(AnalyzeCustomerContactPreferences),
            TransactionStatus.SuccessAlreadyEmail,
            cancellationToken);
        return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken);
    }

    private async Task<DialogTurnResult> HandleOnlyInvoiceEmailPreference(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        CancellationToken cancellationToken)
    {
        string merPost = dialogData.TextLabels.GetValue("MerPost", _textLabelGroupName);
        string retryPrompt = dialogData.TextLabels.GetValue("InvalidEmailRetry", _textLabelGroupName);

        dialogData.ChangeContactPreferencesData.ChoiceMap = new Dictionary<bool, TransactionStatus>
        {
            { true, TransactionStatus.SuccessChangedNota },
            { false, TransactionStatus.SuccessNoChange }
        };

        return await stepContext.PromptAsync(
            nameof(HandleOnlyInvoiceEmailPreference),
            new PromptOptions
            {
                Prompt = MessageFactory.Text(merPost),
                Choices = _yesNoChoice,
                RetryPrompt = MessageFactory.Text(retryPrompt)
            },
            cancellationToken);
    }

    private async Task<DialogTurnResult> HandleOnlyMonthlyEnergyReportEmailPreference(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        CancellationToken cancellationToken)
    {
        string notaPost = dialogData.TextLabels.GetValue("NotaPost", _textLabelGroupName);
        string retryPrompt = dialogData.TextLabels.GetValue("InvalidEmailRetry", _textLabelGroupName);

        dialogData.ChangeContactPreferencesData.ChoiceMap = new Dictionary<bool, TransactionStatus>
        {
            { true, TransactionStatus.SuccessChangedMer },
            { false, TransactionStatus.SuccessNoChange }
        };

        return await stepContext.PromptAsync(
            nameof(HandleOnlyMonthlyEnergyReportEmailPreference),
            new PromptOptions
            {
                Prompt = MessageFactory.Text(notaPost),
                Choices = _yesNoChoice,
                RetryPrompt = MessageFactory.Text(retryPrompt)
            },
            cancellationToken);
    }

    private async Task<DialogTurnResult> HandleNoEmailPreference(
        WaterfallStepContext stepContext,
        DialogData dialogData,
        CancellationToken cancellationToken)
    {
        string notaAndMerPost = dialogData.TextLabels.GetValue("NotaAndMerPost", _textLabelGroupName);
        string retryPrompt = dialogData.TextLabels.GetValue("InvalidEmailRetry", _textLabelGroupName);

        dialogData.ChangeContactPreferencesData.ChoiceMap = new Dictionary<bool, TransactionStatus>
        {
            { true, TransactionStatus.SuccessChangedAll },
            { false, TransactionStatus.SuccessNoChange }
        };

        return await stepContext.PromptAsync(
            nameof(HandleNoEmailPreference),
            new PromptOptions
            {
                Prompt = MessageFactory.Text(notaAndMerPost),
                Choices = _yesNoChoice,
                RetryPrompt = MessageFactory.Text(retryPrompt)
            },
            cancellationToken);
    }

    private async Task<DialogTurnResult> HandleCustomerChoice(
        WaterfallStepContext stepContext,
        CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context);

        bool wantsToAdjustPreference = stepContext.Result is FoundChoice choice
                                       && choice.Value.Contains("Ja", StringComparison.InvariantCultureIgnoreCase)
                                       || stepContext.Result is string textAnswer
                                       && textAnswer.Contains("Ja", StringComparison.InvariantCultureIgnoreCase);

        if (wantsToAdjustPreference)
        {
            await _contactPreferencesService.ChangeContactPreference(
                dialogData.Customer.Label,
                dialogData.Verification.CustomerId.GetValueOrDefault(0),
                dialogData.ChangeContactPreferencesData.CurrentPreferences);

            string customerChangedToEmail = dialogData.TextLabels.GetValue("CustomerChangedToEmail", _textLabelGroupName);
            await stepContext.Context.SendActivityAsync(customerChangedToEmail, cancellationToken: cancellationToken);

            await _sessionManager.SendEndOfTransactionActivity(
                stepContext.Context,
                nameof(ChangeContactPreferencesDialog),
                nameof(HandleCustomerChoice),
                dialogData.ChangeContactPreferencesData.ChoiceMap[true],
                cancellationToken);
            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken);
        }

        string customerNoChange = dialogData.TextLabels.GetValue("CustomerNoChange", _textLabelGroupName);
        await stepContext.Context.SendActivityAsync(customerNoChange, cancellationToken: cancellationToken);

        await _sessionManager.SendEndOfTransactionActivity(
            stepContext.Context,
            nameof(ChangeContactPreferencesDialog),
            nameof(HandleCustomerChoice),
            dialogData.ChangeContactPreferencesData.ChoiceMap[false],
            cancellationToken);
        return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken);
    }
}
