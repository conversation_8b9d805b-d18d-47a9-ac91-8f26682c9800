﻿using DC.Bot.BusinessLogic.Dialogs.Customers.PhoneNumberDialogs;
using DC.Bot.BusinessLogic.Interfaces.DialogContainers;

namespace DC.Bot.BusinessLogic.Dialogs.Customers
{
    public class CustomersDialogContainer : ICustomersDialogContainer
    {
        public CustomerAccountsDialog CustomerAccountsDialog { get; }

        public YearnoteDateDialog YearnoteDateDialog { get; }

        public RelocateDateDialog RelocateDateDialog { get; }

        public ChangeEmailDialog ChangeEmailDialog { get; }

        public ChangeIbanDialog ChangeIbanDialog { get; }

        public ChangePhoneNumberDialog ChangePhoneNumberDialog { get; }

        public ChangeContactPreferencesDialog ChangeContactPreferencesDialog { get; }

        public CustomersDialogContainer(
            CustomerAccountsDialog customerAccountsDialog,
            YearnoteDateDialog yearnoteDateDialog,
            RelocateDateDialog relocateDateDialog,
            ChangeEmailDialog changeEmailDialog,
            ChangeIbanDialog changeIbanDialog,
            ChangePhoneNumberDialog changePhoneNumberDialog,
            ChangeContactPreferencesDialog changeContactPreferencesDialog)
        {
            CustomerAccountsDialog = customerAccountsDialog;
            YearnoteDateDialog = yearnoteDateDialog;
            RelocateDateDialog = relocateDateDialog;
            ChangeEmailDialog = changeEmailDialog;
            ChangeIbanDialog = changeIbanDialog;
            ChangePhoneNumberDialog = changePhoneNumberDialog;
            ChangeContactPreferencesDialog = changeContactPreferencesDialog;
        }
    }
}
