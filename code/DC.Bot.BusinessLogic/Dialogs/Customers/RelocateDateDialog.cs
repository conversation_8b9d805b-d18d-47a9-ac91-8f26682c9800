﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Domain.Exceptions;
using DC.Products.Client.Models;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Customers
{
    public class RelocateDateDialog : BaseDialog
    {
        private readonly ICustomersService _customersService;
        private readonly IProductsService _productsService;

        public RelocateDateDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            ICustomersService customersService,
            IProductsService productsService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
            new WaterfallStep[]
            {
                InitTextLabels,
                RelocationDialogStep
            }));

            InitialDialogId = nameof(WaterfallDialog);

            _customersService = customersService;
            _productsService = productsService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<DialogTurnResult> RelocationDialogStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.GetRelocationDate).ConfigureAwait(false);
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            TransactionStatus status;
            try
            {
                if (dialogData.IsVerified())
                {
                    var customerModel = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);
                    var activeAccount = dialogData.GetActiveAccountFromCustomerModel(customerModel);
                    if (activeAccount != null)
                    {
                        var relocations = await _productsService.GetCustomerRelocations(dialogData).ConfigureAwait(false);
                        if (relocations?.Any() == true)
                        {
                            await ShowRelocationDates(relocations, stepContext).ConfigureAwait(false);
                        }
                        else
                        {
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoMoveOutDate", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                            _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        }
                        status = TransactionStatus.Success;
                    }
                    else
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindData", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        status = TransactionStatus.TemporaryFailure;
                    }
                }
                else
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NoContracts", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    status = TransactionStatus.TemporaryFailure;
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(RelocationDialogStep));

                status = TransactionStatus.TemporaryFailure;
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SeeYourContracts", _textLabelGroupName).Replace("{MijnEnecoProducten}", DialogContent.MijnEnecoProducten), cancellationToken: cancellationToken).ConfigureAwait(false);
            }
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(RelocateDateDialog), nameof(RelocationDialogStep), status, cancellationToken).ConfigureAwait(false);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private async Task ShowRelocationDates(IList<Relocations> relocations, WaterfallStepContext stepContext)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            foreach (Relocations relocation in relocations)
            {
                if (relocation.MoveIn?.Id != null && relocation.MoveIn.Address != null)
                {
                    var moveIn = relocation.MoveIn;
                    _ = await stepContext.Context.SendActivityAsync(
                        dialogData.TextLabels.GetValue("MoveInContract", _textLabelGroupName)
                        .Replace("{street}", moveIn.Address.Street)
                        .Replace("{housenumber}", moveIn.Address.HouseNumber?.ToString())
                        .Replace("{housenumbersuffix}", moveIn.Address.HouseNumberSuffix)
                        .Replace("{city}", moveIn.Address.City.CityNameToFirstUpperLetter())
                        .Replace("{date}", $"{moveIn.Dates?.Current:dd-MM-yyyy}")).ConfigureAwait(false);
                }
                if (relocation.MoveOut?.Id != null && relocation.MoveOut.Address != null)
                {
                    var moveOut = relocation.MoveOut;
                    if (moveOut.Dates == null)
                    {
                        _ = await stepContext.Context.SendActivityAsync(
                            dialogData.TextLabels.GetValue("MoveOutContractNoDate", _textLabelGroupName)
                            .Replace("{accountId}", dialogData.SelectedAccount.AccountId.ToString())
                            .Replace("{street}", moveOut.Address.Street)
                            .Replace("{housenumber}", moveOut.Address.HouseNumber?.ToString())
                            .Replace("{housenumbersuffix}", moveOut.Address.HouseNumberSuffix)
                            .Replace("{city}", moveOut.Address.City.CityNameToFirstUpperLetter()), cancellationToken: CancellationToken.None).ConfigureAwait(false);
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("AskToStopContract", _textLabelGroupName), cancellationToken: CancellationToken.None).ConfigureAwait(false);
                    }
                    else
                    {
                        _ = await stepContext.Context.SendActivityAsync(
                            dialogData.TextLabels.GetValue("MoveOutContract", _textLabelGroupName)
                            .Replace("{street}", moveOut.Address.Street)
                            .Replace("{housenumber}", moveOut.Address.HouseNumber?.ToString())
                            .Replace("{housenumbersuffix}", moveOut.Address.HouseNumberSuffix)
                            .Replace("{city}", moveOut.Address.City.CityNameToFirstUpperLetter())
                            .Replace("{date}", $"{moveOut.Dates?.Current:dd-MM-yyyy}"), cancellationToken: CancellationToken.None).ConfigureAwait(false);
                    }
                }
            }
        }
    }
}