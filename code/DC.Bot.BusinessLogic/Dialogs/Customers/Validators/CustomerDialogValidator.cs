﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Utilities.Formatters;
using DC.Utilities.Validators;
using Microsoft.Bot.Builder.Dialogs;
using System;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Customers.Validators;

public class CustomerDialogValidator : ICustomerValidator
{
    private readonly ISessionManager _sessionManager;

    private readonly string _textLabelGroupName = "Bot_CustomerVerificationDialog";

    public CustomerDialogValidator(
        ISessionManager sessionManager)
    {
        _sessionManager = sessionManager;
    }

    public async Task<bool> VerificationPostalCodeValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(promptContext.Context).ConfigureAwait(false);

        var postalCode = promptContext.Context.Activity.Text?.Replace(" ", "");
        if (!Regex.IsMatch(postalCode, Domain.Models.ValidationRegexes.PostalCode.Dutch, RegexOptions.None, TimeSpan.FromSeconds(2)))
        {
            return await promptContext.ReachedMaxAmountAttempts(dialogData, cancellationToken,
                retryPrompText: dialogData.TextLabels.GetValue("ValidationErrorPostalCodeRetry", _textLabelGroupName)).ConfigureAwait(false);
        }

        return true;
    }

    public async Task<bool> VerificationHouseNumberValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(promptContext.Context).ConfigureAwait(false);
        var houseNumberText = promptContext.Context.Activity.Text?.RemoveWhitespaces();
        if (!int.TryParse(houseNumberText, out var houseNumber) || houseNumber <= 0 || houseNumber > 18926)
        {
            return await promptContext.ReachedMaxAmountAttempts(dialogData, cancellationToken,
                retryPrompText: dialogData.TextLabels.GetValue("ValidationErrorHouseNumberRetry", _textLabelGroupName)).ConfigureAwait(false);
        }

        return true;
    }

    public async Task<bool> VerificationDateOfBirthValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(promptContext.Context).ConfigureAwait(false);
        var dateOfBirthResult = promptContext.Context.Activity.Text?.RemoveWhitespaces();

        if (!DateTime.TryParseExact(dateOfBirthResult, "dd-MM-yyyy", new CultureInfo("NL-nl"), DateTimeStyles.None, out var _))
        {
            var retryPrompTextArray = new[] {
                dialogData.TextLabels.GetValue("ValidationErrorDateOfBirthRetryPartOne", _textLabelGroupName),
                dialogData.TextLabels.GetValue("ValidationErrorDateOfBirthRetryPartTwo", _textLabelGroupName)};

            return await promptContext.ReachedMaxAmountAttempts(dialogData, cancellationToken, retryPrompText: retryPrompTextArray).ConfigureAwait(false);
        }

        return true;
    }

    public async Task<bool> VerificationCustomerIdValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(promptContext.Context).ConfigureAwait(false);

        //Added split for when customers fill in their customer account id as well. By splitting and taking first this solves the matter.
        var customerId = promptContext.Context.Activity.Text.RemoveWhitespaces().FormatWithoutSequenceNumber();

        if (!long.TryParse(customerId, out var _) || customerId.Length < 4)
        {
            return await promptContext.ReachedMaxAmountAttempts(dialogData, cancellationToken, retryPrompText:
                dialogData.TextLabels.GetValue("ValidationErrorCustomerIdRetry", _textLabelGroupName))
                .ConfigureAwait(false);
        }

        return true;
    }

    public async Task<bool> CustomerIdValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(promptContext.Context).ConfigureAwait(false);

        var customerIdText = promptContext.Context.Activity.Text?.RemoveWhitespaces().FormatWithoutSequenceNumber();

        if (!Regex.IsMatch(customerIdText, @"^[0-9]{1,10}$", RegexOptions.None, TimeSpan.FromSeconds(2)))
        {
            return await promptContext.ReachedMaxAmountAttempts(dialogData, cancellationToken).ConfigureAwait(false);
        }

        var customerId = Convert.ToInt64(customerIdText);

        if (customerId != dialogData?.Verification.CustomerId)
        {
            return await promptContext.ReachedMaxAmountAttempts(dialogData, cancellationToken).ConfigureAwait(false);
        }

        dialogData.Verification.CustomerIdVerified = true;
        return true;
    }

    public async Task<bool> CustomerEmailValidator(
        PromptValidatorContext<string> promptContext,
        CancellationToken cancellationToken)
    {
        return await CustomerEmailValidator(
            promptContext,
            "ValidationErrorMailAddressRetry",
            _textLabelGroupName,
            cancellationToken);
    }

    public async Task<bool> CustomerEmailValidator(
        PromptValidatorContext<string> promptContext,
        string retryTextLabel,
        string textLabelGroupName,
        CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(promptContext.Context).ConfigureAwait(false);
        var userInput = promptContext.Context.Activity.Text;

        //valid on regex or dns check
        if (!userInput.IsValidEmail(true, false, true))
        {
            var textLabelContent = dialogData.TextLabels.GetValue(retryTextLabel, textLabelGroupName);

            return await promptContext.ReachedMaxAmountAttempts(dialogData, cancellationToken,
                retryPrompText: textLabelContent).ConfigureAwait(false);
        }

        return true;
    }

    public async Task<bool> CustomerPhoneValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(promptContext.Context).ConfigureAwait(false);
        var userInput = promptContext.Context.Activity.Text.RemoveWhitespaces();

        // Apigee regex om het consistent te houden.
        if (!Regex.IsMatch(userInput, @"(^\+[0-9]{2}|^\+[0-9]{2}\(0\)|^\(\+[0-9]{2}\)\(0\)|^00[0-9]{2}|^0)([0-9]{9}$|6\-[0-9]{8}$|[0-9]{2}\-[0-9]{7}$|[0-9]{3}\-[0-9]{6}$)",
                           RegexOptions.None, TimeSpan.FromSeconds(2)))
        {
            return await promptContext.ReachedMaxAmountAttempts(dialogData, cancellationToken,
                retryPrompText: dialogData.TextLabels.GetValue("ValidationErrorPhoneNumberRetry", _textLabelGroupName)).ConfigureAwait(false);
        }

        return true;
    }
}
