﻿using Microsoft.Bot.Builder.Dialogs;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Customers.Validators;

public interface ICustomerValidator
{
    Task<bool> VerificationPostalCodeValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken);

    Task<bool> VerificationHouseNumberValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken);

    Task<bool> VerificationDateOfBirthValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken);

    Task<bool> VerificationCustomerIdValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken);

    Task<bool> CustomerIdValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken);

    Task<bool> CustomerEmailValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken);

    Task<bool> CustomerEmailValidator(
        PromptValidatorContext<string> promptContext,
        string retryTextLabel,
        string textLabelGroupName,
        CancellationToken cancellationToken);

    Task<bool> CustomerPhoneValidator(PromptValidatorContext<string> promptContext, CancellationToken cancellationToken);
}
