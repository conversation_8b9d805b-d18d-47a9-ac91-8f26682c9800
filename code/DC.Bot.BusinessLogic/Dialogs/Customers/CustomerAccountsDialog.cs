﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Domain.Exceptions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Customers
{
    public class CustomerAccountsDialog : BaseDialog
    {

        public CustomerAccountsDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog(nameof(WaterfallDialog), new WaterfallStep[] {
                InitTextLabels,
                RetrieveCustomerAccounts,
                HandleAccountSelectionStep
            }));

            AddDialog(new TextPrompt(nameof(InitTextLabels)));
            AddDialog(new ChoicePrompt(nameof(RetrieveCustomerAccounts)));
            AddDialog(new TextPrompt(nameof(HandleAccountSelectionStep)));

            // initial dialog is the waterfall wrapper
            InitialDialogId = nameof(WaterfallDialog);
        }

        /// <summary>
        /// Checks if there are any active accounts for the verified customer
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<DialogTurnResult> RetrieveCustomerAccounts(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            // start with the new dialog
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            try
            {
                // 1 active account. Don't show any choices. Save the accountId to the dialogdata object and proceed to the next step.
                if (dialogData.IsVerified() && dialogData.ActiveAccounts?.Count == 1)
                {
                    dialogData?.SetSelectedAccountFromDialogData(dialogData?.ActiveAccounts?.FirstOrDefault()?.AccountId ?? 0);
                    return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
                }

                //Exception - ZonOpDak returns success when more than 3 accounts are found
                if (dialogData.IsVerified() && dialogData.ActiveAccounts?.Count > 3 && dialogData.CommandBeginDialog?.StartsWith(DialogCommands._ZON_OP_DAK) == true)
                {
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CustomerAccountsDialog), nameof(RetrieveCustomerAccounts), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }

                // in case we have more than 1 active account, present a menu of accounts.
                if (dialogData.IsVerified() && dialogData.ActiveAccounts?.Count > 1)
                {
                    await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.CustomerAccountSelection).ConfigureAwait(false);
                    var choices = new List<Choice>();
                    foreach (var account in dialogData.ActiveAccounts)
                    {
                        choices.Add(new Choice
                        {
                            Value = $"{account.AccountId}) {account.Street} {account.HouseNumber}{account.HouseNumberSuffix} {account.City}",
                            Synonyms = new List<string> { $"{account.AccountId}" }
                        });
                    }
                    return await stepContext.PromptAsync(nameof(RetrieveCustomerAccounts), new PromptOptions
                    {
                        Prompt = MessageFactory.Text(dialogData.TextLabels.GetValue("WhichAddress", _textLabelGroupName)),
                        Choices = choices,
                        RetryPrompt = MessageFactory.Text(dialogData.TextLabels.GetValue("WhichAddress", _textLabelGroupName)),
                        Style = ListStyle.SuggestedAction
                    }, cancellationToken).ConfigureAwait(false);
                }

                // verification check - no active account
                if (dialogData.IsVerified() && dialogData.ActiveAccounts?.Count < 1)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindActiveAccount", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CustomerAccountsDialog), nameof(RetrieveCustomerAccounts), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
                }
                // not verified
                else
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindData", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CustomerAccountsDialog), nameof(RetrieveCustomerAccounts), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
                }
            }
            catch (DigitalCoreException ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(RetrieveCustomerAccounts));
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SeeYourContracts", _textLabelGroupName).Replace("{MijnEnecoProducten}", DialogContent.MijnEnecoProducten), cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(CustomerAccountsDialog), nameof(RetrieveCustomerAccounts), TransactionStatus.PermanentFailure, cancellationToken).ConfigureAwait(false);
            }
            // non happy flow: end the dialog
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> HandleAccountSelectionStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            // Result not null so we expect the user made a choice from the menu in the previous step.
            if ((dialogData.SelectedAccount == null || dialogData.SelectedAccount.AccountId == 0) && stepContext.Result != null)
            {
                if (stepContext.Result is FoundChoice choice
                    && int.TryParse(choice.Value.Split(')')[0], out int accountId)
                    && dialogData.ActiveAccounts.Select(account => account.AccountId).Contains(accountId))
                {
                    dialogData.SetSelectedAccountFromDialogData(accountId);
                }
                else
                {
                    // Invalid input from the menu. Go back to the previous step.
                    stepContext.SetNextStepIndex(Dialogs, nameof(RetrieveCustomerAccounts));
                    return await RetrieveCustomerAccounts(stepContext, cancellationToken).ConfigureAwait(false);
                }
            }
            return await stepContext.NextAsync(dialogData, cancellationToken: cancellationToken).ConfigureAwait(false);
        }
    }
}
