﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Domain.Models.Extensions;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Customers;

public class ChangeEmailDialog : BaseDialog
{
    private readonly IList<Choice> _yesNoChoice = new List<Choice>
    {
        new Choice
        {
            Value = "Ja, graag",
            Synonyms = new List<string> { "Ja" }
        },
        new Choice
        {
            Value = "Nee, bedankt",
            Synonyms = new List<string> { "Nee", "Neen" }
        }
    };

    private readonly ICustomersService _customersService;
    private readonly INextBestActionService _nextBestActionService;
    private readonly IContactPreferencesService _contactPreferencesService;

    /// <summary>
    /// Dialog allowing customers to change their email preference
    /// </summary>
    public ChangeEmailDialog(
        ILoggerFactory loggerFactory,
        ILoggingService loggingService,
        ISessionManager sessionManager,
        IDialogValidators validators,
        ICustomersService customersService,
        INextBestActionService nextBestActionService,
        IStorageService storageService,
        IContactPreferencesService contactPreferencesService) :
        base(loggerFactory, sessionManager, loggingService, storageService)
    {
        AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
            new WaterfallStep[]
            {
                InitTextLabels,
                AskForAdjustmentEmail,
                AskForAdjustmentEmailAnswer,
                AskNewEmail,
                AskNewEmailAnswerStep
}));

        AddDialog(new TextPrompt(nameof(InitTextLabels)));
        AddDialog(new ChoicePrompt(nameof(AskForAdjustmentEmail)));
        AddDialog(new TextPrompt(nameof(AskForAdjustmentEmailAnswer)));
        AddDialog(new TextPrompt(nameof(AskNewEmail), (context, token) =>  validators.CustomerDialogValidator.CustomerEmailValidator(context, "InvalidEmailRetry", _textLabelGroupName, token)));
        AddDialog(new TextPrompt(nameof(AskNewEmailAnswerStep)));

        InitialDialogId = nameof(WaterfallDialog);

        _customersService = customersService;
        _nextBestActionService = nextBestActionService;
        _contactPreferencesService = contactPreferencesService;
    }

    private async Task<DialogTurnResult> AskForAdjustmentEmail(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ChangeEmail);
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context);
        var retryPrompt = dialogData.TextLabels.GetValue("InvalidEmailRetry", _textLabelGroupName);

        // to be sure the verificationproces has been handled correctly
        if (!dialogData.IsVerified())
        {
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangeEmailDialog), nameof(AskForAdjustmentEmail), TransactionStatus.TemporaryFailure, cancellationToken);
            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken);
        }

        //ADD Email
        if (string.IsNullOrEmpty(dialogData.Customer.EmailAddress))
        {
            // If this dialog is called from NBA, skip prompting the customer because this prompt has already been answered in the NBA dialog.
            // Go to AskNewEmail step
            if (dialogData.NextBestAction.CurrentTransactionIsNba)
            {
                stepContext.SetNextStepIndex(Dialogs, nameof(AskNewEmailAnswerStep));
                return await AskNewEmail(stepContext, cancellationToken);
            }

            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("UnknownEmail", _textLabelGroupName), cancellationToken: cancellationToken);

            return await stepContext.NextAsync(null, cancellationToken);
        }

        //ADJUST Email
        await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CurrentKnownEmail", _textLabelGroupName, new
        {
            email = dialogData.Customer.EmailAddress.ObfuscateEmail()
        }), cancellationToken: cancellationToken);

        return await stepContext.PromptAsync(nameof(AskForAdjustmentEmail), new PromptOptions
        {
            Prompt = MessageFactory.Text(dialogData.TextLabels.GetValue("WantToAdjust", _textLabelGroupName)),
            Choices = _yesNoChoice,
            RetryPrompt = MessageFactory.Text(retryPrompt)
        }, cancellationToken);
    }


    /// <summary>
    /// Note: This dialog supports adding a new mail or changing the current one.
    /// The choice doesn't matter. We're just changing the profile with a new emailaddress
    /// </summary>
    /// <param name="stepContext"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task<DialogTurnResult> AskForAdjustmentEmailAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        // process answer: support choice and textual answers
        var answer = stepContext.Result is FoundChoice choice && choice.Value.Contains("Ja", StringComparison.InvariantCultureIgnoreCase) ||
                                      stepContext.Result is string textAnswer && textAnswer.Contains("Ja", StringComparison.InvariantCultureIgnoreCase);

        if (answer)
        {
            return await stepContext.NextAsync(null, cancellationToken);
        }
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context);

        if (string.IsNullOrWhiteSpace(dialogData.Customer.EmailAddress))
        {
            // If the customer does not have an email, we immediately ask the customer for an email
            return await AskNewEmail(stepContext, cancellationToken);
        }

        await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CustomerKeepsSameEmail", _textLabelGroupName, new
        {
            email = dialogData.Customer.EmailAddress.ObfuscateEmail()
        }), cancellationToken: cancellationToken);

        //if current transaction is nba and keep the current emailaddress set the nba conversion on success
        if (dialogData.NextBestAction?.CurrentTransactionIsNba == true)
            await _nextBestActionService.AddFeedback(dialogData, "CustomerEmailAddress", Domain.Models.NextBestAction.FeedbackStatus.Conversion_success);

        var contactPreferences = await _contactPreferencesService.GetEmailContactPreferences(
            dialogData.Customer.Label,
            dialogData.Verification.CustomerId.GetValueOrDefault(0));

        if (!contactPreferences.IsInvoicePreferenceEmail || !contactPreferences.IsMonthlyEnergyReportPreferenceEmail)
        {
            return await stepContext.ReplaceDialogAsync(nameof(ChangeContactPreferencesDialog), null, cancellationToken);
        }

        await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangeEmailDialog), nameof(AskForAdjustmentEmailAnswer), TransactionStatus.Success, cancellationToken);
        return await stepContext.EndDialogAsync(cancellationToken: cancellationToken);
    }

    private async Task<DialogTurnResult> AskNewEmail(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        if (stepContext.Result is string emailAddress && !string.IsNullOrWhiteSpace(emailAddress))
        {
            return await stepContext.NextAsync(emailAddress, cancellationToken);
        }

        await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ChangeEmail);
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context);

        return await stepContext.PromptAsync(nameof(AskNewEmail), new PromptOptions
        {
            Prompt = MessageFactory.Text(dialogData.TextLabels.GetValue("AskWhatEmailToUse", _textLabelGroupName)),
            RetryPrompt = MessageFactory.Text(dialogData.TextLabels.GetValue("InvalidEmailRetry", _textLabelGroupName))
        }, cancellationToken);
    }

    [System.Diagnostics.CodeAnalysis.SuppressMessage("Minor Code Smell", "S2221:\"Exception\" should not be caught when not required by called methods", Justification = "Must catch all to prevent bot from crashing")]
    private async Task<DialogTurnResult> AskNewEmailAnswerStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
    {
        var dialogData = await _sessionManager.GetDialogData(stepContext.Context);
        if (dialogData.Verification.TooManyAttempts)
        {
            dialogData.Verification.TooManyAttempts = false;

            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("InvalidEmailThreeTimes", _textLabelGroupName), cancellationToken: cancellationToken);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangeEmailDialog), nameof(AskNewEmailAnswerStep), TransactionStatus.Unhappy, cancellationToken);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken);
        }

        try
        {
            var newEmail = (string)stepContext.Result;

            await _customersService.UpdateEmailAddress(dialogData, newEmail);
            dialogData.Customer.EmailAddress = newEmail;

            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NewEmailSaved", _textLabelGroupName, new
            {
                email = newEmail
            }), cancellationToken: cancellationToken);

            var contactPreferences = await _contactPreferencesService.GetEmailContactPreferences(
                dialogData.Customer.Label,
                dialogData.Verification.CustomerId.GetValueOrDefault(0));

            if (!contactPreferences.IsInvoicePreferenceEmail || !contactPreferences.IsMonthlyEnergyReportPreferenceEmail)
            {
                return await stepContext.ReplaceDialogAsync(nameof(ChangeContactPreferencesDialog), null, cancellationToken);
            }

            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangeEmailDialog), nameof(AskNewEmailAnswerStep), TransactionStatus.Success, cancellationToken);

            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken);
        }
        catch (Exception ex)
        {
            _loggingService.LogException(ex, stepContext.Context, nameof(AskNewEmailAnswerStep));

            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangeEmailDialog), nameof(AskNewEmailAnswerStep), TransactionStatus.PermanentFailure, cancellationToken);
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken);
        }
    }
}
