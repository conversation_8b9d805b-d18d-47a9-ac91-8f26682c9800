﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Models.Extensions;
using DC.Domain.Models.Financials;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Bot.Builder.Dialogs.Choices;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;


namespace DC.Bot.BusinessLogic.Dialogs.Customers
{
    public class ChangeIbanDialog : BaseDialog
    {
        private readonly IFinancialsService _financialsService;
        private readonly INextBestActionService _nextBestActionService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="service"></param>
        /// <param name="loggerFactory"></param>
        /// <param name="sessionManager"></param>
        /// <param name="validators"></param>
        public ChangeIbanDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            IDialogValidators validators,
            IFinancialsService financialsService,
            INextBestActionService nextBestActionService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
                new WaterfallStep[]
                {
                    InitTextLabels,
                    AskForAdjustmentIban,
                    AskForAdjustmentIbanAnswer,
                    AskNewIban,
                    AskNewIbanAnswerStep,
                    ReturnToParentStep
                }));

            AddDialog(new TextPrompt(nameof(InitTextLabels)));
            AddDialog(new ChoicePrompt(nameof(AskForAdjustmentIban)));
            AddDialog(new TextPrompt(nameof(AskForAdjustmentIbanAnswer)));
            AddDialog(new TextPrompt(nameof(AskNewIban), validators.FinancialsDialogValidator.IbanValidator));
            AddDialog(new TextPrompt(nameof(AskNewIbanAnswerStep)));
            AddDialog(new TextPrompt(nameof(ReturnToParentStep)));

            InitialDialogId = nameof(WaterfallDialog);

            _financialsService = financialsService;
            _nextBestActionService = nextBestActionService;
        }

        private async Task<DialogTurnResult> AskForAdjustmentIban(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ChangeIBAN).ConfigureAwait(false);

            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            // to be sure the verificationproces has been handled correctly
            if (!dialogData.IsVerified())
            {
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangeIbanDialog), nameof(AskForAdjustmentIban), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
                return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
            }

            var result = await _financialsService.GetFinancialsPreferences(dialogData, dialogData.SelectedAccount.AccountId).ConfigureAwait(false);
            dialogData.SelectedAccount.BankAccountNumber = result.BankAccount?.Number;

            // When dialog is called as separate step. We only want to give the option to change the IBAN
            if (string.IsNullOrEmpty(result.BankAccount?.Number) && (dialogData.AdvancePayment.ChangeIBANTriggerdAsSeparateStep || dialogData.NextBestAction.CurrentTransactionIsNba))
            {
                stepContext.SetNextStepIndex(Dialogs, nameof(AskNewIban));
                return await AskNewIban(stepContext, cancellationToken).ConfigureAwait(false);
            }

            var prompt = string.IsNullOrEmpty(result.BankAccount?.Number) ? dialogData.TextLabels.GetValue("UnknownIban", _textLabelGroupName) : dialogData.TextLabels.GetValue("CurrentKnownIban", _textLabelGroupName).Replace("{iban}", result.BankAccount?.Number.ObfuscateBankAccount());
            return await stepContext.PromptAsync(nameof(AskForAdjustmentIban), new PromptOptions
            {
                Prompt = MessageFactory.Text(prompt),
                Choices = SetupButtons(dialogData),
                RetryPrompt = MessageFactory.Text(prompt)
            }, cancellationToken).ConfigureAwait(false);
        }


        /// <summary>
        /// Note: This dialog supports adding a new mail or changing the current one. 
        /// The choice doesn't matter. We're just changing the profile with a new emailaddress
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<DialogTurnResult> AskForAdjustmentIbanAnswer(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);

            // process answer: support choice and textual answers
            var answer = stepContext.Result is FoundChoice choice && choice.Value.Contains("Ja", StringComparison.InvariantCultureIgnoreCase) ||
                                          stepContext.Result is string textAnswer && textAnswer.Contains("Ja", StringComparison.InvariantCultureIgnoreCase);

            if (!answer)
            {
                //Return when this dialog is called from another advancedPayment dialog.
                if (dialogData.AdvancePayment.ChangeIBANTriggerdAsSeparateStep)
                {
                    stepContext.SetNextStepIndex(Dialogs, nameof(ReturnToParentStep));
                    return await ReturnToParentStep(stepContext, cancellationToken).ConfigureAwait(false);
                }

                var promptText = string.IsNullOrEmpty(dialogData.SelectedAccount.BankAccountNumber) ? dialogData.TextLabels.GetValue("NoAnswerUnknownIban", _textLabelGroupName) : dialogData.TextLabels.GetValue("NoAnswerKnownIban", _textLabelGroupName);

                //if current transaction is nba and keep the current iban when is not empty set the nba conversion on success
                if (!string.IsNullOrEmpty(dialogData.SelectedAccount.BankAccountNumber) && dialogData.NextBestAction?.CurrentTransactionIsNba == true)
                    await _nextBestActionService.AddFeedback(dialogData, "BankAccountNumber", Domain.Models.NextBestAction.FeedbackStatus.Conversion_success).ConfigureAwait(false);

                await stepContext.Context.SendActivityAsync(promptText, cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangeIbanDialog), nameof(AskForAdjustmentIbanAnswer), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> AskNewIban(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.ChangeIBAN).ConfigureAwait(false);

            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            var emptyBankAccount = string.IsNullOrEmpty(dialogData.SelectedAccount.BankAccountNumber);
            var newBankAccountText = !dialogData.NextBestAction.CurrentTransactionIsNba ? dialogData.TextLabels.GetValue("AddIban", _textLabelGroupName) : dialogData.TextLabels.GetValue("NbaAddIban", _textLabelGroupName);
            var emptyBankAccountText = dialogData.AdvancePayment.ChangeIBANTriggerdAsSeparateStep ? dialogData.TextLabels.GetValue("EmptyIban", _textLabelGroupName) : newBankAccountText;
            var promptText = emptyBankAccount ? emptyBankAccountText : dialogData.TextLabels.GetValue("AdjustIban", _textLabelGroupName);

            return await stepContext.PromptAsync(nameof(AskNewIban), new PromptOptions
            {
                Prompt = MessageFactory.Text(promptText),
                RetryPrompt = MessageFactory.Text(dialogData.TextLabels.GetValue("InvalidIban", _textLabelGroupName))
            }, cancellationToken).ConfigureAwait(false);
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Minor Code Smell", "S2221:\"Exception\" should not be caught when not required by called methods", Justification = "Must catch all to prevent bot from crashing")]
        private async Task<DialogTurnResult> AskNewIbanAnswerStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            if (dialogData.Verification.TooManyAttempts)
            {
                dialogData.Verification.TooManyAttempts = false;

                var endText = dialogData.ValidationData.InvalidIBANCountry ? dialogData.TextLabels.GetValue("InvalidIbanIncorrectCountry", _textLabelGroupName) : dialogData.TextLabels.GetValue("InvalidIbanThreeTimes", _textLabelGroupName);
                _ = await stepContext.Context.SendActivityAsync(endText, cancellationToken: cancellationToken).ConfigureAwait(false);
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangeIbanDialog), nameof(AskNewIbanAnswerStep), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            if (dialogData.AdvancePayment.ChangeIBANTriggerdAsSeparateStep)
            {
                stepContext.SetNextStepIndex(Dialogs, nameof(ReturnToParentStep));
                return await ReturnToParentStep(stepContext, cancellationToken).ConfigureAwait(false);
            }

            try
            {
                var bankAccount = dialogData.AdvancePayment.BankAccountNumber;
                var updateStatus = await _financialsService.UpdateBankAccount(dialogData, bankAccount).ConfigureAwait(false);

                if (updateStatus == AdvancePaymentAdviceStatus.Ok)
                {
                    if (dialogData.AdvancePayment.ChangeIBANTriggerdAsSeparateStep)
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("IbanSaved", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
                    }

                    var promptText = string.IsNullOrEmpty(dialogData.SelectedAccount.BankAccountNumber) ? dialogData.TextLabels.GetValue("NewIbanSaved", _textLabelGroupName) : dialogData.TextLabels.GetValue("AdjustIbanSaved", _textLabelGroupName);
                    _ = await stepContext.Context.SendActivityAsync(promptText, cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangeIbanDialog), nameof(AskNewIbanAnswerStep), TransactionStatus.Success, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else if (updateStatus == AdvancePaymentAdviceStatus.IbanMissingOrIncorrect)
                {
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("InvalidIban", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangeIbanDialog), nameof(AskNewIbanAnswerStep), TransactionStatus.Unhappy, cancellationToken).ConfigureAwait(false);
                    return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else if (updateStatus == AdvancePaymentAdviceStatus.NotAvailable)
                {
                    return await SomethingWentWrongTryAgain(dialogData, stepContext, cancellationToken).ConfigureAwait(false);
                }

                return await SomethingWentWrongStopTransaction(dialogData, stepContext, cancellationToken).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _loggingService.LogException(ex, stepContext.Context, nameof(AskNewIbanAnswerStep));
                return await SomethingWentWrongStopTransaction(dialogData, stepContext, cancellationToken).ConfigureAwait(false);
            }
        }

        private static async Task<DialogTurnResult> ReturnToParentStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            return await stepContext.NextAsync(null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> SomethingWentWrongStopTransaction(DialogData dialogData, WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUsSomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangeIbanDialog), nameof(SomethingWentWrongStopTransaction), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
        }

        private async Task<DialogTurnResult> SomethingWentWrongTryAgain(DialogData dialogData, WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("TryAgainSomethingWentWrong", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
            await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(ChangeIbanDialog), nameof(SomethingWentWrongTryAgain), TransactionStatus.TemporaryFailure, cancellationToken).ConfigureAwait(false);
            return await stepContext.CancelAllDialogsAsync(true, null, null, cancellationToken).ConfigureAwait(false);
        }

        private List<Choice> SetupButtons(DialogData dialogData)
        {
            return new List<Choice> {
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("YesChoice", _textLabelGroupName),
                    Synonyms = new List<string> { "Ja" }
                },
                new Choice
                {
                    Value = dialogData.TextLabels.GetValue("NoChoice", _textLabelGroupName),
                    Synonyms = new List<string> { "Nee", "Neen" }
                }

            };
        }
    }
}