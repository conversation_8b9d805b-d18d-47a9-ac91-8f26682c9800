﻿using DC.Bot.BusinessLogic.Constants;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Domain.Exceptions;
using Microsoft.Bot.Builder.Dialogs;
using Microsoft.Extensions.Logging;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic.Dialogs.Customers
{
    /// <summary>
    /// 
    /// </summary>
    public class YearnoteDateDialog : BaseDialog
    {
        private readonly ICustomersService _customersService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="customersRepository"></param>
        public YearnoteDateDialog(
            ILoggerFactory loggerFactory,
            ILoggingService loggingService,
            ISessionManager sessionManager,
            ICustomersService customersService,
            IStorageService storageService) :
            base(loggerFactory, sessionManager, loggingService, storageService)
        {
            AddDialog(new WaterfallDialog($"{nameof(WaterfallDialog)}",
                new WaterfallStep[]
                {
                    InitTextLabels,
                    TellYearnoteStep
                }));

            InitialDialogId = nameof(WaterfallDialog);

            _customersService = customersService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="stepContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private async Task<DialogTurnResult> TellYearnoteStep(WaterfallStepContext stepContext, CancellationToken cancellationToken)
        {
            // first step in dialog: set current dialog action
            await _sessionManager.SetCurrentDialogAction(stepContext.Context, DialogAction.GetNextYearnoteDate).ConfigureAwait(false);
            TransactionStatus status;

            var dialogData = await _sessionManager.GetDialogData(stepContext.Context).ConfigureAwait(false);
            if (dialogData.IsVerified() && dialogData.HasActiveAccount())
            {
                try
                {
                    var customerTask = _customersService.GetCustomer(dialogData);
                    //get next charge date from paymentplan instead of the agreement (the next charge date in the agreement is obsolete)
                    var paymentplanTask = _customersService.GetPaymentPlan(dialogData);

                    await Task.WhenAll(customerTask, paymentplanTask).ConfigureAwait(false);

                    var customerModel = await customerTask.ConfigureAwait(false);
                    var paymentplan = await paymentplanTask.ConfigureAwait(false);

                    var accountsWithNextChargeDate = customerModel.Accounts.Where(x => x.NextChargeDate.HasValue && x.Id == dialogData.SelectedAccount.AccountId);
                    if (accountsWithNextChargeDate.Count() > 1)
                    {
                        status = TransactionStatus.Success;
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("MoreThanOneContract", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                        foreach (var account in accountsWithNextChargeDate)
                        {
                            _ = await stepContext.Context.SendActivityAsync(
                                dialogData.TextLabels.GetValue("YearNoteDateContract", _textLabelGroupName)
                                .Replace("{accountId}", $"{account.Id}")
                                .Replace("{street}", account.Address.Street)
                                .Replace("{housenumber}", account.Address.HouseNumber?.ToString())
                                .Replace("{housenumbersuffix}", account.Address.HouseNumberSuffix)
                                .Replace("{city}", account.Address.City.CityNameToFirstUpperLetter())
                                .Replace("{date}", account.NextChargeDate.Value.ToString("d MMMM yyyy", new CultureInfo("NL-nl")))
                                , cancellationToken: cancellationToken).ConfigureAwait(false);
                        }
                    }
                    else if (paymentplan?.NextBillingCycle?.ChargeDate.HasValue == true)
                    {
                        status = TransactionStatus.Success;
                        var nextYearnoteDate = paymentplan.NextBillingCycle.ChargeDate.Value.ToString("d MMMM yyyy", new CultureInfo("NL-nl"));
                        _ = await stepContext.Context.SendActivityAsync(
                             dialogData.TextLabels.GetValue("DateReceiveYearNote", _textLabelGroupName)
                             .Replace("{nextYearnoteDate}", $"{nextYearnoteDate}"), cancellationToken: cancellationToken).ConfigureAwait(false);
                    }
                    else
                    {
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("CouldNotFindData", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                        _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("ContactUs", TextLabelGroupNameGeneral), cancellationToken: cancellationToken).ConfigureAwait(false);
                        status = TransactionStatus.TemporaryFailure;
                    }
                }
                catch (DigitalCoreException ex)
                {
                    _loggingService.LogException(ex, stepContext.Context, nameof(TellYearnoteStep));

                    status = TransactionStatus.TemporaryFailure;
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SomethingWentWrongNextYearNoteDate", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("NextYearNoteDate", _textLabelGroupName), cancellationToken: cancellationToken).ConfigureAwait(false);
                    _ = await stepContext.Context.SendActivityAsync(dialogData.TextLabels.GetValue("SeeYourPayments", _textLabelGroupName).Replace("{MijnEnecoBetaalgegevens}", DialogContent.MijnEnecoBetaalgegevens), cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                await _sessionManager.SendEndOfTransactionActivity(stepContext.Context, nameof(YearnoteDateDialog), nameof(TellYearnoteStep), status, cancellationToken).ConfigureAwait(false);
            }
            return await stepContext.EndDialogAsync(cancellationToken: cancellationToken).ConfigureAwait(false);
        }
    }
}
