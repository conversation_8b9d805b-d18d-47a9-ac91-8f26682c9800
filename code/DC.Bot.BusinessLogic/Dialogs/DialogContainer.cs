﻿using DC.Bot.BusinessLogic.Interfaces.DialogContainers;

namespace DC.Bot.BusinessLogic.Dialogs
{
    public class DialogContainer : IDialogContainer
    {

        public ICustomersDialogContainer Customers { get; private set; }

        public ICustomerVerificationDialogContainer Verification { get; private set; }

        public IFinancialsDialogContainer Financials { get; private set; }

        public IProductsDialogContainer Products { get; private set; }

        public IUserAccountsDialogContainer UserAccounts { get; private set; }

        public IUsagesDialogContainer Usages { get; private set; }

        public DialogContainer(
            ICustomersDialogContainer customers,
            ICustomerVerificationDialogContainer verification,
            IFinancialsDialogContainer financials,
            IProductsDialogContainer products,
            IUsagesDialogContainer usages,
            IUserAccountsDialogContainer userAccounts)
        {
            Customers = customers;
            Verification = verification;
            Financials = financials;
            Products = products;
            Usages = usages;
            UserAccounts = userAccounts;
        }
    }
}
