﻿using DC.Api.Base.FeatureManagement;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.BusinessLogic.Base;
using DC.Domain.Exceptions.ResponseModels;
using DC.Domain.Models.NextBestAction;
using DC.ESP.Services;
using DC.Products.Client.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using System;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic;

/// <summary>
/// Gets the NBA's from the back-end and submits the feedback via the DC servicebus to ESP.
/// </summary>
public class NextBestActionService : BaseService, INextBestActionService
{
    /// <summary>
    /// Dependencies
    /// </summary>
    private readonly IDcProductsRepository _productsRepository;
    private readonly IJourneyTrackerService _journeyTrackerService;

    /// <summary>
    /// Constructor
    /// </summary>
    public NextBestActionService(
        ILoggerFactory loggerFactory,
        IConfiguration configuration,
        IDcProductsRepository productsRepository,
        IJourneyTrackerService journeyTrackerService) : base(loggerFactory, configuration)
    {
        _productsRepository = productsRepository;
        _journeyTrackerService = journeyTrackerService;
    }

    /// <summary>
    /// Returns the next best actions from DC Products.
    /// </summary>
    public async Task<NextBestAction> GetNextBestActions(DialogData dialogData, bool filterSupported = true)
    {
        var apiCall = _productsRepository.GetNextBestActions(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0));
        var onSuccess = new Func<HttpOperationResponse<object>, NextBestAction>(httpResponse =>
        {
            var data = ((ResponseDataNextBestAction)httpResponse.Body).Data;
            if (filterSupported)
                data = data.FilterSupportedActions(dialogData.TextLabels?.TextLabels);
            return data;
        });
        return await ExecuteDcCall<NextBestAction, ErrorResponse>(apiCall, onSuccess, new Guid("b46ce72b-78f1-4df6-b70d-803b48741711"), nameof(GetNextBestActions));
    }

    /// <summary>
    /// Puts feedback to the ESP stream
    /// </summary>
    public async Task AddFeedback(DialogData dialogData, string actionType, FeedbackStatus status)
    {
        var feedbackData = new ActionFeedbackData().EnrichActionFeedbackData(actionType, dialogData, status);
        if (feedbackData == null)
        {
            return;
        }

        await _journeyTrackerService.PublishCustomerJourneyEvent(feedbackData, dialogData.Customer.Label, dialogData.Verification.CustomerId.GetValueOrDefault());
    }
}
