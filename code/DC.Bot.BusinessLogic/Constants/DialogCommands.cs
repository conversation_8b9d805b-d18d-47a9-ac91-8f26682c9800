﻿namespace DC.Bot.BusinessLogic.Constants;

public static class DialogCommands
{
    public const string _NEXT_YEARNOTE_DATE = "$next_yearnote_date";

    public const string _ADVANCE_PAYMENT_AMOUNT = "$advance_payment_amount";

    public const string _ADVANCE_PAYMENT_DAY = "$advance_paymentday";

    public const string _ADVANCE_PAYMENT_DIRECTDEBIT = "$advance_payment_directdebit";

    public const string _PAYMENTARRANGEMENT = "$payment_arrangement";

    public const string _RATES = "$product_rates";

    public const string _PRODUCTENDDATES = "$product_enddates";

    public const string _PRODUCTUSAGES = "$product_usages";

    public const string _RELOCATEDATE = "$relocatedate";

    public const string _USERNAME = "$username";

    public const string _AUTHENTICATE = "$authenticate";

    public const string _VERIFICATION = "$verification";

    public const string _CHANGEEMAIL = "$change_email";

    public const string _CHANGEIBAN = "$change_iban";

    public const string _CHANGEPHONENUMBER = "$change_phonenumber";

    public const string _DISCONTINUE_TOON = "$terminate_toon";

    public const string _DISCONTINUE_SERVICE_CONTRACT = "$terminatemaintenance";

    public const string _KETELCOMFORT_APPOINTMENT = "$ketelcomfortappointment";

    public const string _READINGS_REPORT_REQUEST = "$readings_report_request";

    public const string _SAVE_READING_PERSONAL = "$save_reading_personal";

    public const string _CREATE_SERVICE_ORDER = "$create_serviceorder";

    public const string _PRODUCTENDDATESADVICE = "$product_enddates_advice";

    public const string _NEXTBESTACTION = "$nba";

    public const string _ADVANCE_PAYMENT_ADVICE = "$advice_advance_paymentamount";

    public const string _ISMA_MANDATE = "$ismamandate";

    public const string _PRODUCT_FINE_CALCULATION = "$product_fine_calculation";

    public const string _ZON_OP_DAK = "$zonopdak";

    public const string _CHANGE_CONTACT_PREFERENCES = "$change_contact_preferences";
}
