﻿namespace DC.Bot.BusinessLogic.Constants
{
    public static class DialogContent
    {
        // [label](hyperlink)
        public const string EnecoApp = "[Eneco App](https://www.eneco.nl/energieproducten/eneco-app)";
        public const string MijnEneco = "[Mijn Eneco](https://www.eneco.nl/mijn-eneco/)";
        public const string MijnEnecoTermijnBedrag = "[Mijn Eneco](https://www.eneco.nl/mijn-eneco/termijnbedrag)";
        public const string MijnEnecoBetaalgegevens = "[Mijn Eneco](https://www.eneco.nl/mijn-eneco/gegevens/betaalgegevens)";
        public const string MijnEnecoProducten = "[Mijn Eneco](https://www.eneco.nl/mijn-eneco/producten)";
        public const string MijnEnecoOpenstaandeNotas = "[Mijn Eneco](https://www.eneco.nl/mijn-eneco/notas/openstaand/)";
        public const string MijnEnecoBetalingsregeling = "[Mijn Eneco](https://www.eneco.nl/mijn-eneco/notas/betalingsregeling/)";
        public const string MijnEnecoWachtwoordVergeten = "[wachtwoord aanvragen](https://www.eneco.nl/wachtwoord-vergeten)";
        public const string MijnEnecoVerbruik = "[Mijn Eneco](https://www.eneco.nl/mijn-eneco/verbruik)";
        public const string SuniverseTelefoonnummer = "[088 - 244 0880](tel:0882440880)";
        public const string EnecoTelefoonnummer = "[088 - 895 5000](tel:0888955000)";
        public const string GedragscodeLeveranciersSlimmeMeters = "[Gedragscode Leveranciers Slimme Meters](https://www.eneco.nl/~/media/eneco-nl/pdf/verklaring-naleving-gedragscode-leveranciers-slimme-meters-2021-ondertekend.pdf)";
        public const string Privacystatement = "[Privacystatement](https://www.eneco.nl/privacystatement/)";
    }
}
