﻿using System.Collections.Generic;

namespace DC.Bot.BusinessLogic.Constants;
public static class TextLabelGroups
{
    public static readonly IReadOnlyList<string> AllGroupNames = new List<string>
    {
        "Bot_ChangeEmailDialog",
        "Bot_ChangeIbanDialog",
        "Bot_ChangePhoneNumberDialog",
        "Bot_CustomerAccountsDialog",
        "Bot_GeneralTextLabels",
        "Bot_SaveReadingPersonalDialog",
        "Bot_RelocateDateDialog",
        "Bot_YearnoteDateDialog",
        "Bot_AdvancePaymentAdviceDialog",
        "Bot_GiroCardStepDialog",
        "Bot_UsernameDialog",
        "Bot_ProductEndDatesAdviceDialog",
        "Bot_ProductEndDatesDialog",
        "Bot_ProductRatesDialog",
        "Bot_ProductUsagesDialog",
        "Bot_AdvancePaymentAmountDialog",
        "Bot_NextBestActionDialog",
        "Bot_CreateServiceOrderDialog",
        "Bot_DiscontinueServiceContractDialog",
        "Bot_DiscontinueToonDialog",
        "Bot_KetelComfortAppointmentDialog",
        "Bot_IsmaMandateDialog",
        "Bot_ReadingsReportRequestDialog",
        "Bot_CustomerVerificationDialog",
        "Bot_AdvancePaymentDayDialog",
        "Bot_PaymentArrangementDialog",
        "Bot_UsageCapAlarmDialog",
        "Bot_ProductFineCalculationDialog",
        "Bot_ZonOpDakDialog"
    };
}
