﻿namespace DC.Bot.BusinessLogic.Constants
{
    public static class LoggingTags
    {
        // Conversation ID is added by the framework to certain exceptions. Don't prefix with DC to keep this consistent
        public const string ConversationId = "Conversation ID";

        // CustomerId is used throughout DC. Don't prefix to keep it consistent
        public const string CustomerId = "CustomerId";

        public const string FromId = "DC-FromId";

        public const string FromName = "DC-FromName";

        public const string RecipientId = "DC-RecipientId";

        public const string RecipientName = "DC-RecipientName";

        public const string Name = "DC-Name";

        public const string Type = "DC-Type";

        public const string Action = "DC-Action";

        public const string Value = "DC-Value";

        public const string Text = "DC-Text";

        public const string LastUserInput = "DC-LastUserInput";
    }
}
