﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories.Interfaces;
using DC.BusinessLogic.Base;
using DC.Domain.Exceptions;
using DC.Domain.Exceptions.Helpers;
using DC.Domain.Exceptions.ResponseModels;
using DC.Domain.Models.Products;
using DC.Usages.Client.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ResponseDataEnableMandateForCustomerResult = DC.Usages.Client.Models.ResponseDataEnableMandateForCustomerResult;

namespace DC.Bot.BusinessLogic
{
    public class UsagesService : BaseService, IUsagesService
    {
        private readonly IDcUsagesRepository _usagesRepository;
        private readonly ICustomersService _customersService;

        /// <summary>
        /// Constructor
        /// </summary>
        public UsagesService(
            ILoggerFactory loggerFactory,
            IConfiguration configuration,
            IDcUsagesRepository usagesRepository,
            ICustomersService customersService
            ) : base(loggerFactory, configuration)
        {
            _usagesRepository = usagesRepository;
            _customersService = customersService;
        }

        /// <summary>
        /// Gets the most recent usages from the past half year.
        /// </summary>   
        public async Task<UsagesSummaryModel> GetPastHalfYearUsages(DialogData dialogData, int accountId)
        {
            // Get the first day of the today's month, then subtract 6 months so that we can measure the usages of the past 6 months.
            var dateFrom = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1, 0, 0, 0, DateTimeKind.Utc).AddMonths(-6);
            var dateTo = dateFrom.AddMonths(6);

            var response = await _usagesRepository.GetUsages(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), accountId, dateFrom, dateTo).ConfigureAwait(false);
            if (response.Response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var data = ((ResponseDataUsagesModel)response.Body).Data.Usages.OrderByDescending(d => d.Period.FromProperty).ToList();
                if (data.Count == 0)
                {
                    return null;
                }

                var usagesSummaryModel = new UsagesSummaryModel
                {
                    FromDate = data.Select(u => u.Period).Min(p => p.FromProperty),
                    ToDate = data.Select(u => u.Period).Max(p => p.FromProperty),
                    UsageActuals = new List<UsagesSummaryActual>()
                };

                AddEntriesToActual(usagesSummaryModel, "Stroom", "kWh", data.Where(u => u.Entries.Any(e => e.Actual.Electricity != null)).Select(d => d.Entries.FirstOrDefault()).ToList());
                AddEntriesToActual(usagesSummaryModel, "Teruglevering", "kWh", data.Where(u => u.Entries.Any(e => e.Actual.Redelivery != null)).Select(d => d.Entries.FirstOrDefault()).ToList());
                AddEntriesToActual(usagesSummaryModel, "Gas", "m³", data.Where(u => u.Entries.Any(e => e.Actual.Gas != null)).Select(d => d.Entries.FirstOrDefault()).ToList());
                AddEntriesToActual(usagesSummaryModel, "Warmte", "GJ", data.Where(u => u.Entries.Any(e => e.Actual.Warmth != null)).Select(d => d.Entries.FirstOrDefault()).ToList());
                AddEntriesToActual(usagesSummaryModel, "Warm tapwater", "m³", data.Where(u => u.Entries.Any(e => e.Actual.TapWater != null)).Select(d => d.Entries.FirstOrDefault()).ToList());
                return usagesSummaryModel;
            }
            // in case there is no Usage data the response will be 400. We return a null object.
            else if (response.Response.StatusCode == System.Net.HttpStatusCode.BadRequest)
            {
                return null;
            }
            else if (response.Body is ErrorResponse errorResponse)
            {
                throw DigitalCoreExceptionHelper.RecreateExceptionFromDerivedErrorResponse(errorResponse);
            }
            else
            {
                throw new TechnicalException(new Guid("4dbc6918-4aae-4fd5-9e9e-4728f7c82c6f"),
                    $"{nameof(GetPastHalfYearUsages)} threw an un-expected statuscode {(int)response.Response.StatusCode} with message {await response.Response.Content.ReadAsStringAsync().ConfigureAwait(false)}");
            }
        }

        /// <summary>
        /// Determines whether this instance [can request register report request] the specified dialog data.
        /// </summary>
        public async Task<ReadingsReportRequest> CanRequestRegisterReportRequest(DialogData dialogData)
        {
            var result = await _customersService.GetAgreements(dialogData, true).ConfigureAwait(false);
            if (result?.Any() == false || result?.Any(a => a.Products?.Any(p => p.IsActive && (p.ProductType == ProductType.Electricity || p.ProductType == ProductType.Gas)) == true) == false)
                return new ReadingsReportRequest { HasEG = false, HasSmartMeter = false };
            else if (result?.Any(i => i.CanRequestMeterReadingHistoryReport) == false)
                return new ReadingsReportRequest { HasEG = true, HasSmartMeter = false };

            return new ReadingsReportRequest { HasEG = true, HasSmartMeter = true };
        }

        /// <summary>
        /// Registers the report request.
        /// </summary>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Critical Code Smell", "S3776:Cognitive Complexity of methods should not be too high", Justification = "<Pending>")]
        public async Task<SaveReadingModel> RegisterReportRequest(DialogData dialogData, int accountId)
        {
            var response = await _usagesRepository.RegisterReportRequest(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), accountId, dialogData.Customer.EmailAddress.ToRequestDataReportRequestModel()).ConfigureAwait(false);
            if (response.Response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                return new SaveReadingModel
                {
                    EmailSent = true
                };
            }
            else if (response.Body is ErrorResponse errorResponse)
            {
                if (errorResponse.Errors?.Any(e => !string.IsNullOrWhiteSpace(e.Code)) == true)
                {
                    ReadingReportRequestErrorCode status = ReadingReportRequestErrorCode.Unknown;
                    var errorDetail = new Guid(errorResponse.Errors.First().Code.Split('|')[1]);
                    if (errorDetail == new Guid("525da9e1-646a-41d3-b9ea-32f97ef1392e"))
                        status = ReadingReportRequestErrorCode.CustomerNotFound;
                    if (errorDetail == new Guid("********-cd0d-4148-b29b-0f8424b331a5"))
                        status = ReadingReportRequestErrorCode.EmailRequired;
                    if (errorDetail == new Guid("7dff0c3e-147c-46bb-b536-7848948e8d90"))
                        status = ReadingReportRequestErrorCode.AlreadyRequested;
                    if (errorDetail == new Guid("85ba42ea-d374-4372-b04b-f2e235cf4b44"))
                        status = ReadingReportRequestErrorCode.NoActiveSmartMeterAgreement;
                    if (errorDetail == new Guid("ed0e2fed-bd95-4dba-9cfb-e4a28640a15a") || errorDetail == new Guid("19ddee98-4223-40b1-9467-6b8ab8269806"))
                        status = ReadingReportRequestErrorCode.NoSmartMeters;
                    if (errorDetail == new Guid("614d27b2-df65-4359-90a1-53bfdd1fc6c2"))
                        status = ReadingReportRequestErrorCode.FailedSendEmail;

                    return new SaveReadingModel { ErrorCode = status };
                }

                throw DigitalCoreExceptionHelper.RecreateExceptionFromDerivedErrorResponse(errorResponse);
            }
            else
            {
                throw new TechnicalException(new Guid("0ba6ad9c-591a-4d50-a304-2617ecc1fd53"),
                    $"{nameof(RegisterReportRequest)} threw an un-expected statuscode {(int)response.Response.StatusCode} with message {await response.Response.Content.ReadAsStringAsync().ConfigureAwait(false)}");
            }
        }

        /// <summary>
        /// Gets the reading set-up for the customer. 
        /// </summary>
        public async Task<GetReadingOutputModel> GetReadingForCustomer(DialogData dialogData)
        {
            var response = await _usagesRepository.GetReading(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.Value, dialogData.SelectedAccount.AccountId).ConfigureAwait(false);

            if (response.Response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                return ((ResponseDataGetReadingOutputModel)response.Body).Data;
            }
            if (response.Body is ErrorResponse errorResponse)
            {
                throw DigitalCoreExceptionHelper.RecreateExceptionFromDerivedErrorResponse(errorResponse);
            }

            throw new TechnicalException(new Guid("43F1F4A4-D298-492D-BFAB-76E9F25DD8B8"),
                $"{nameof(RegisterReportRequest)} threw an un-expected statuscode {(int)response.Response.StatusCode} with message {await response.Response.Content.ReadAsStringAsync().ConfigureAwait(false)}");
        }

        /// <summary>
        /// GetReadingsHistoryForCustomer
        /// </summary>
        public async Task<List<ReadingHistoryItem>> GetReadingsHistoryForCustomer(DialogData dialogData)
        {
            var response = await _usagesRepository.GetReadingsHistory(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.Value, dialogData.SelectedAccount.AccountId).ConfigureAwait(false);

            if (response.Response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                return ((ResponseDataListReadingHistoryItem)response.Body).Data.ToList();
            }
            if (response.Body is ErrorResponse errorResponse)
            {
                throw DigitalCoreExceptionHelper.RecreateExceptionFromDerivedErrorResponse(errorResponse);
            }

            throw new TechnicalException(new Guid("566EA16D-E0E1-4C49-AF4A-AB48B742A56E"),
                $"{nameof(RegisterReportRequest)} threw an un-expected statuscode {(int)response.Response.StatusCode} with message {await response.Response.Content.ReadAsStringAsync().ConfigureAwait(false)}");

        }

        /// <summary>
        /// GetOutstandingReadings
        /// </summary>
        public async Task<List<OutstandingReading>> GetOutstandingReadings(DialogData dialogData)
        {
            var response = await _usagesRepository.GetOutstandingReadings(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.Value, dialogData.SelectedAccount.AccountId).ConfigureAwait(false);

            if (response.Response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                return ((ResponseDataListOutstandingReading)response.Body).Data.ToList();
            }
            if (response.Body is ErrorResponse errorResponse)
            {
                throw DigitalCoreExceptionHelper.RecreateExceptionFromDerivedErrorResponse(errorResponse);
            }

            throw new TechnicalException(new Guid("7ED16970-97A4-4E77-9FAD-C291A490C679"),
                $"{nameof(RegisterReportRequest)} threw an un-expected statuscode {(int)response.Response.StatusCode} with message {await response.Response.Content.ReadAsStringAsync().ConfigureAwait(false)}");
        }

        /// <summary>
        /// GetMandate
        /// </summary>
        public async Task<ServiceProductVersionModel> GetMandate(DialogData dialogData, ServiceProductType serviceProductType)
        {
            var customerId = dialogData.Verification.CustomerId.Value;
            var accountId = dialogData.SelectedAccount.AccountId;
            var apiCall = _usagesRepository.GetMandate(dialogData.Customer.Label, dialogData.Channel, customerId, accountId, serviceProductType);
            var onSuccess = new Func<HttpOperationResponse<object>, ServiceProductVersionModel>(httpResponse =>
            {
                var mandates = ((ResponseDataListServiceProductVersionModel)httpResponse?.Body)?.Data;
                var mandate = mandates?.FirstOrDefault(m => m.ProductType == serviceProductType);
                if (mandate != null)
                    return mandate;
                throw new DataNotFoundException(new Guid("********-d5da-42d2-8f93-d9dea3a264b2"), $"{nameof(GetMandate)}-{serviceProductType}: Not found for {customerId}-{accountId}.");
            });
            return await ExecuteDcCall<ServiceProductVersionModel, ErrorResponse>(apiCall, onSuccess, new Guid("572ace63-2bf4-443c-8185-180ae09b8ce6"), nameof(GetMandate)).ConfigureAwait(false);
        }

        /// <summary>
        /// EnableMandate
        /// </summary>
        public async Task<ResponseDataEnableMandateForCustomerResult> EnableMandate(DialogData dialogData, ServiceProductType serviceProductType)
        {
            var apiCall = _usagesRepository.EnableMandate(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.Value, dialogData.SelectedAccount.AccountId, serviceProductType);
            var onSuccess = new Func<HttpOperationResponse<object>, ResponseDataEnableMandateForCustomerResult>(httpResponse =>
            {
                return (ResponseDataEnableMandateForCustomerResult)httpResponse?.Body;
            });
            return await ExecuteDcCall<ResponseDataEnableMandateForCustomerResult, ErrorResponse>(apiCall, onSuccess, new Guid("0a194169-f22d-4890-9554-156451663d5c"), nameof(EnableMandate)).ConfigureAwait(false);
        }

        /// <summary>
        /// DisableMandate
        /// </summary>
        public async Task DisableMandate(DialogData dialogData, ServiceProductType serviceProductType)
        {
            var apiCall = _usagesRepository.DisableMandate(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.Value, dialogData.SelectedAccount.AccountId, serviceProductType);
            await ExecuteDcCall(apiCall, new Guid("acdf9b14-5664-4c45-b388-94ab2a513c7c"), nameof(DisableMandate)).ConfigureAwait(false);
        }

        private static void AddEntriesToActual(UsagesSummaryModel usagesSummaryModel, string title, string measureType, List<UsagesEntry> entries)
        {
            if (entries?.Count > 0)
            {
                var usagesSummaryActual = new UsagesSummaryActual
                {
                    Title = title,
                    MeasureType = measureType,
                    UsageItems = new List<UsagesSummaryUsageItem>()
                };

                foreach (var entry in entries)
                {
                    usagesSummaryActual.UsageItems.Add(
                        new UsagesSummaryUsageItem(
                            entry.Actual.Date.Month, GetUsageItemByTitle(title, entry)
                        )
                    );
                }
                usagesSummaryModel.UsageActuals.Add(usagesSummaryActual);
            }
        }

        private static UsageItem GetUsageItemByTitle(string title, UsagesEntry entry)
        {
            return title switch
            {
                "Stroom" => entry.Actual.Electricity,
                "Gas" => entry.Actual.Gas,
                "Warmte" => entry.Actual.Warmth,
                "Warm tapwater" => entry.Actual.TapWater,
                "Koude" => entry.Actual.Cooling,
                "Teruglevering" => entry.Actual.Redelivery,
                _ => null
            };
        }
    }
}