﻿using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.Financial;
using DC.Bot.Repositories.Interfaces;
using DC.BusinessLogic.Base;
using DC.Customers.Client.Models;
using DC.Domain.Exceptions;
using DC.Domain.Exceptions.Helpers;
using DC.Domain.Exceptions.ResponseModels;
using DC.Domain.Models.Financials;
using DC.Domain.Models.General;
using DC.Domain.Models.NextBestAction;
using DC.Financials.Client.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace DC.Bot.BusinessLogic
{
    public class FinancialsService : BaseService, IFinancialsService
    {
        private readonly IDcFinancialsRepository _financialsRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        public FinancialsService(
            ILoggerFactory loggerFactory,
            IConfiguration configuration,
            IDcFinancialsRepository financialsRepository) : base(loggerFactory, configuration)
        {
            _financialsRepository = financialsRepository;
        }

        /// <summary>
        /// GetAdvancePaymentAmountAndDayOfPayment
        /// </summary>
        public async Task<AdvancePayment> GetAdvancePaymentAmountAndDayOfPayment(DialogData dialogData, CustomerAccountModel account)
        {
            var advancePaymentAmount = await GetAdvancePaymentAmount(dialogData, account.Id).ConfigureAwait(false);
            var preferences = await GetFinancialsPreferences(dialogData, account.Id).ConfigureAwait(false);
            var advancePayment = new AdvancePayment()
            {
                Account = account,
                Amount = advancePaymentAmount,
                Preferences = preferences
            };
            return advancePayment;
        }

        /// <summary>
        /// Get advance payment amount (Financials PAPI)
        /// </summary>
        public async Task<int> GetAdvancePaymentAmount(DialogData dialogData, int accountId)
        {
            var response = await _financialsRepository.GetAdvancePayment(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), accountId).ConfigureAwait(false);

            if (response.Response.StatusCode == HttpStatusCode.OK)
            {
                var details = ((ResponseDataAdvancePaymentDetails)response.Body).Data;
                return (int)Math.Round(details.AdvancePaymentTotal ?? 0);
            }
            // in case there is no PaymentArrangement the response will be 400. We return a null object.
            else if (response.Response.StatusCode == HttpStatusCode.BadRequest)
            {
                return 0;
            }
            else if (response.Body is ErrorResponse errorResponse)
            {
                throw DigitalCoreExceptionHelper.RecreateExceptionFromDerivedErrorResponse(errorResponse);
            }
            else
            {
                throw new TechnicalException(new Guid("5e6bd2ff-ab44-41d5-806e-56d4ef85f8f8"), $"{nameof(GetAdvancePaymentAmount)} threw an un-expected statuscode {(int)response.Response.StatusCode} with message {await response.Response.Content.ReadAsStringAsync().ConfigureAwait(false)}");
            }
        }

        /// <summary>
        /// Update advance payment amount (Financials PAPI)
        /// </summary>
        public async Task<AdvancedPaymentAdviceExtraStatus> UpdateAdvancePaymentAmount(DialogData dialogData, int amount)
        {
            var response = await _financialsRepository.PutAdvancePayment(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), dialogData.SelectedAccount.AccountId,
                new RequestDataAdvancePaymentChangeRequest
                {
                    Data = new AdvancePaymentChangeRequest { Amount = amount, Source = Source.App }
                }).ConfigureAwait(false);

            AdvancePaymentAdviceStatus status = AdvancePaymentAdviceStatus.Ok;
            List<string> errorCodes = new List<string>();

            if (response?.Body is ErrorResponse error)
            {
                errorCodes = error.Errors.Select(x => x.Code).ToList();
                status = TranslateErrorGuidToAdviceStatus(error);
            }

            return new AdvancedPaymentAdviceExtraStatus
            {
                Status = status,
                ErrorCodes = errorCodes
            };
        }

        /// <summary>
        /// Update the payment day of month (Financials PAPI)
        /// </summary>
        public async Task<AdvancePaymentAdviceStatus> UpdatePaymentDayOfMonth(DialogData dialogData, int paymentDayOfMonth)
        {
            var currentPreferencesResponse = await _financialsRepository.GetPaymentPreferences(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), dialogData.SelectedAccount.AccountId).ConfigureAwait(false);
            if ((int)currentPreferencesResponse.Response.StatusCode / 100 != 2)
            {
                throw new TechnicalException(new Guid("96444fbb-ff08-4c1d-91f8-178d11eefff8"),
                    $"{nameof(UpdatePaymentDayOfMonth)} threw an un-expected statuscode {(int)currentPreferencesResponse.Response.StatusCode} with message {await currentPreferencesResponse.Response.Content.ReadAsStringAsync().ConfigureAwait(false)}");
            }

            var currentPreferences = ((ResponseDataFinancialPreferences)currentPreferencesResponse.Body).Data;

            var response = await _financialsRepository.UpdatePaymentPreferences(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.Value, dialogData.SelectedAccount.AccountId, new RequestDataFinancialPreferences
            {
                Data = new FinancialPreferences
                {
                    PaymentDayOfMonth = paymentDayOfMonth,
                    PaymentMethodIsDirectDebit = currentPreferences.PaymentMethodIsDirectDebit,
                    BankAccount = currentPreferences.BankAccount
                }
            }).ConfigureAwait(false);

            if (response?.Body is ErrorResponse error)
            {
                return TranslateErrorGuidToAdviceStatus(error);
            }

            return AdvancePaymentAdviceStatus.Ok;
        }

        /// <summary>
        /// Update payment preference to direct debit (Financials PAPI)
        /// </summary>
        public async Task<AdvancePaymentAdviceStatus> UpdatePaymentToDirectDebit(DialogData dialogData, string bankAccountNumber, int paymentDayOfMonth)
        {
            var response = await _financialsRepository.UpdatePaymentPreferences(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), dialogData.SelectedAccount.AccountId, new RequestDataFinancialPreferences
            {
                Data = new FinancialPreferences
                {
                    PaymentDayOfMonth = paymentDayOfMonth,
                    PaymentMethodIsDirectDebit = true,
                    BankAccount = new BankAccount
                    {
                        Number = bankAccountNumber
                    }
                }
            }).ConfigureAwait(false);

            if (response?.Body is ErrorResponse error)
            {
                return TranslateErrorGuidToAdviceStatus(error);
            }

            return AdvancePaymentAdviceStatus.Ok;
        }

        /// <summary>
        /// Get GetPaymentDayOfMonth (Financials PAPI)
        /// </summary>
        public async Task<FinancialPreferences> GetFinancialsPreferences(DialogData dialogData, int accountId)
        {
            var apiCall = _financialsRepository.GetFinancialsPreferences(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), accountId);
            var onSuccess = new Func<HttpOperationResponse<object>, FinancialPreferences>(httpResponse =>
            {
                var data = ((ResponseDataFinancialPreferences)httpResponse.Body).Data;
                return data;
            });

            return await ExecuteDcCall<FinancialPreferences, ErrorResponse>(apiCall, onSuccess, new Guid("5e6bd2ff-ab44-41d5-806e-56d4ef85f8f8"), nameof(GetFinancialsPreferences)).ConfigureAwait(false);
        }

        /// <summary>
        /// Get GetPaymentArrangement (Financials PAPI)
        /// </summary>
        public async Task<PaymentArrangementModel> GetPaymentArrangement(DialogData dialogData, int accountId)
        {
            var response = await _financialsRepository.GetPaymentArrangement(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), accountId).ConfigureAwait(false);
            if (response.Response.StatusCode == HttpStatusCode.OK)
            {
                var paymentArrangementModel = ((ResponseDataListPaymentArrangementModel)response.Body)?.Data?.FirstOrDefault();
                return paymentArrangementModel;
            }
            // in case there is no PaymentArrangement the response will be 400. We return a null object.
            else if (response.Response.StatusCode == HttpStatusCode.BadRequest)
            {
                return null;
            }
            else if (response.Body is ErrorResponse errorResponse)
            {
                throw DigitalCoreExceptionHelper.RecreateExceptionFromDerivedErrorResponse(errorResponse);
            }
            else
            {
                throw new TechnicalException(new Guid("1b36c86e-c618-4a5c-99a1-61864b303083"), $"{nameof(GetPaymentArrangement)} threw an un-expected statuscode {(int)response.Response.StatusCode} with message {await response.Response.Content.ReadAsStringAsync().ConfigureAwait(false)}");
            }
        }

        /// <summary>
        /// UpdateBankAccount
        /// </summary>
        public async Task<AdvancePaymentAdviceStatus> UpdateBankAccount(DialogData dialogData, string bankAccount)
        {
            var mutation = new FinancialPreferences
            {
                BankAccount = new BankAccount { Number = bankAccount },
                ActionFeedbackData = new ActionFeedbackData()
            };
            mutation.ActionFeedbackData.EnrichActionFeedbackData("BankAccountNumber", dialogData);

            var requestData = new RequestDataFinancialPreferences { Data = mutation };
            var response = await _financialsRepository.UpdatePaymentPreferences(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), dialogData.SelectedAccount.AccountId, requestData).ConfigureAwait(false);

            if (response?.Body is ErrorResponse error)
            {
                return TranslateErrorGuidToAdviceStatus(error);
            }

            return AdvancePaymentAdviceStatus.Ok;
        }

        /// <summary>
        /// GetAdvancePaymentAdviceV2
        /// </summary>
        public async Task<AdvancePaymentAdviceV2Response> GetAdvancePaymentAdviceV2(DialogData dialogData)
        {
            try
            {
                var response = await _financialsRepository.GetAdvancePaymentAdviceV2(dialogData.Customer.Label, dialogData.Channel, dialogData.Verification.CustomerId.GetValueOrDefault(0), dialogData.SelectedAccount.AccountId).ConfigureAwait(false);
                if (response.Response.StatusCode == HttpStatusCode.OK)
                {
                    var details = ((ResponseDataAdvancePaymentAdviceV2Response)response.Body).Data;
                    return details;
                }

                // No advice available.
                return new AdvancePaymentAdviceV2Response
                {
                    AdviceStatus = AdvancePaymentAdviceStatus.NotAvailable
                };
            }
            catch (Exception ex)
            {
                throw new TechnicalException(new Guid("7fcdc2e6-f72f-4db9-819d-b6f230ef0b57"), $"{nameof(GetAdvancePaymentAdviceV2)} threw an expected error", ex);
            }
        }

        private static AdvancePaymentAdviceStatus TranslateErrorGuidToAdviceStatus(ErrorResponse error)
        {
            var invalidIBANGuids = new List<string>
            {
                "ad03bf92-6df2-4b22-8d07-1b317dcf5721", //MVS-06286
                "7f381f43-8acc-4d8a-887d-e50105373cb8", //MVS-22096
                "5d04f307-86f9-4d26-9425-c7c32825bb5b",
                "faf71cd7-2632-45ac-b283-69a4dc2f0494"
            };

            var notAvailableGuids = new List<string>
            {
                "5BDC3E53-C822-42C2-B27E-3B3EAC4F2F93", //MVS-11114
                "d94b9a81-a295-45af-9c5d-e4bdc5250fe6", //MVS-15596
                "D338A243-965E-4F9B-8B74-D0CE56B09DF2", //MVS-15595
                "a230fe21-f67c-4985-af09-e667fbd5ba4b", //NVT
                "D65F09D6-4F5E-4FE1-8A9B-A1FE93A0E263", //MVS-11110
                "e62c1c35-c5e9-4c72-8ea1-84b85751cf69", //MVS-11111
                "0CBE68E4-A7E8-40CB-95B4-5F763AE1319E", //MVS-11113
                "108C205F-85E9-47C8-BE73-81E02B318453", //MVS-14602
                "47da6e03-3a08-440a-9026-af7d689fb1cc",
                "8e884043-2688-4246-a43e-ed4d3a74ef69" //MVS-14251
            };

            var noAdviceYetGuids = new List<string>
            {
                "C3A0BEFB-9C93-4AF0-BC73-FEA11E9E4309", //MVS-11582
                "399ed593-f66b-4db3-b320-5a448d637e05", //MVS-15608
                "DBD61C7E-6519-4E43-A485-265B492107D0"  //MVS-15594
            };

            var errorCodes = error.Errors.Select(x => x.Code).ToList();

            if (errorCodes.Exists(x => invalidIBANGuids.Exists(y => x.Contains(y, StringComparison.CurrentCultureIgnoreCase))))
            {
                return AdvancePaymentAdviceStatus.IbanMissingOrIncorrect;
            }
            else if (errorCodes.Exists(x => notAvailableGuids.Exists(y => x.Contains(y, StringComparison.CurrentCultureIgnoreCase))))
            {
                return AdvancePaymentAdviceStatus.NotAvailable;
            }
            else if (errorCodes.Exists(x => x.Contains("16cabc53-cfd0-4c72-a1f7-deef51de07ba"))) //MVS-01056
            {
                return AdvancePaymentAdviceStatus.PaymentMethodShouldBeDirectDebit;
            }
            else if (errorCodes.Exists(x => x.Contains("1774e629-cba8-4f5a-ae89-166ce1b7d8d7"))) //MVS-22097
            {
                return AdvancePaymentAdviceStatus.ActiveDebtCollection;
            }
            else if (errorCodes.Exists(x => x.Contains("b6c222f6-fc17-4275-bfbe-efc9ad4bc6c6"))) //MVS-08563
            {
                return AdvancePaymentAdviceStatus.PaymentMethodShouldNotBeDirectDebit;
            }
            if (errorCodes.Exists(x => x.Contains("b863bd68-a964-48a6-9a90-adf42560bd5b")))//MVS-14661
            {
                return AdvancePaymentAdviceStatus.YearNoteTooClose;
            }
            else if (errorCodes.Exists(x => x.Contains("3286cf97-3126-4dd6-8276-59036e99ad47")))//MVS-15400
            {
                return AdvancePaymentAdviceStatus.ActiveDebtCollection;
            }
            else if (errorCodes.Exists(x => x.Contains("cbdfcf60-c705-4316-a383-75ede2a71fad")))// MVS-15401
            {
                return AdvancePaymentAdviceStatus.YearlyInvoiceEstimatedOnMeterReadings;
            }
            else if (errorCodes.Exists(x => x.Contains("3180497f-318e-4da7-a06a-6afdb157ec5e")))// MVS-15609
            {
                return AdvancePaymentAdviceStatus.PaymentAmountTooLow;
            }
            else if (errorCodes.Exists(x => noAdviceYetGuids.Exists(y => x.Contains(y, StringComparison.CurrentCultureIgnoreCase))))
            {
                return AdvancePaymentAdviceStatus.NoAdviceYet;
            }

            return AdvancePaymentAdviceStatus.Default;
        }
    }
}
