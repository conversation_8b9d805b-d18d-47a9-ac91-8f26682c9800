{"version": 2, "dependencies": {"net8.0": {"AutoMapper": {"type": "Direct", "requested": "[12.0.1, )", "resolved": "12.0.1", "contentHash": "hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "dependencies": {"Microsoft.CSharp": "4.7.0"}}, "DC.BusinessLogic.Base": {"type": "Direct", "requested": "[1.2.1229175, )", "resolved": "1.2.1229175", "contentHash": "bdG85hEfuzAnY3ZTgkIJDKlLyGCLbdyNuCoozbSghT/OCcFKXIBnOBU4R/iHBenWIj4LS4Hj7mNINGYnZBHsYQ==", "dependencies": {"Azure.Core": "1.38.0", "Azure.Identity": "1.11.0", "DC.Domain.Exceptions": "1.2.1229175", "DC.Repositories.Base": "1.2.1229175", "DC.Security.ActiveDirectory": "1.2.1229175", "DC.Security.Encryption": "1.2.1229175", "DnsClient": "1.7.0", "DocumentFormat.OpenXml": "3.0.1", "IdentityModel": "6.2.0", "Microsoft.ApplicationInsights": "2.22.0", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Caching.StackExchangeRedis": "8.0.2", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Identity.Core": "8.0.1", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.3", "System.Drawing.Common": "8.0.1", "ZiggyCreatures.FusionCache": "1.0.0"}}, "DC.SAPI": {"type": "Direct", "requested": "[1.2.1229175, )", "resolved": "1.2.1229175", "contentHash": "LatPN64cZcN1gCIb69S/ZJV/AHNozIWbGgtrkRMbcgWCDuA3OdZuQZF/tQLEI1LmYbHPOrooHHHoiCcPeyHYTw==", "dependencies": {"Azure.Core": "1.38.0", "Azure.Identity": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.5.0", "Confluent.SchemaRegistry.Serdes.Avro": "2.3.0", "DC.Api.Base": "1.2.1229175", "DC.ESP": "1.2.1229175", "DC.Repositories.Base": "1.2.1229175", "DC.Utilities": "1.2.1229175", "DnsClient": "1.7.0", "DocumentFormat.OpenXml": "3.0.1", "IdentityModel": "6.2.0", "MassTransit.Azure.ServiceBus.Core": "8.2.1", "MassTransit.Kafka": "8.2.1", "Microsoft.ApplicationInsights": "2.22.0", "Microsoft.ApplicationInsights.AspNetCore": "2.22.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.16", "Microsoft.Azure.AppConfiguration.AspNetCore": "7.0.0", "Microsoft.EntityFrameworkCore": "8.0.1", "Microsoft.EntityFrameworkCore.Cosmos": "8.0.1", "Microsoft.Extensions.Azure": "1.7.1", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Caching.StackExchangeRedis": "8.0.2", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.DiagnosticAdapter": "3.1.32", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Identity.Core": "8.0.1", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.ApplicationInsights": "2.22.0", "Microsoft.Extensions.Logging.AzureAppServices": "8.0.1", "Microsoft.FeatureManagement": "3.1.1", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.3", "PnP.Core": "1.11.0", "PnP.Core.Auth": "1.11.0", "Swashbuckle.AspNetCore": "6.2.3", "Swashbuckle.AspNetCore.Newtonsoft": "6.2.3", "System.Drawing.Common": "8.0.1", "ZiggyCreatures.FusionCache": "1.0.0", "ZiggyCreatures.FusionCache.Backplane.StackExchangeRedis": "1.0.0", "ZiggyCreatures.FusionCache.Serialization.SystemTextJson": "1.0.0", "libphonenumber-csharp": "8.13.29"}}, "DC.Security.Encryption": {"type": "Direct", "requested": "[1.2.1229175, )", "resolved": "1.2.1229175", "contentHash": "yQQfAoH5qEjFZCYiw+5VG8fRCZDuYVTDHsl5W2AC4+5lRswphOW0sDZ4draylOrsV4+1GRuQjoEpvIj8BGJ7YQ==", "dependencies": {"Azure.Core": "1.38.0", "Azure.Identity": "1.11.0", "IdentityModel": "6.2.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Identity.Core": "8.0.1", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "DC.Telemetry": {"type": "Direct", "requested": "[1.2.1229175, )", "resolved": "1.2.1229175", "contentHash": "MP/8ulaUdb0/oTozvAQsBAfSAluFxXDUemKjqa/4UmCKzx+rxdteKoRoTCbe2NPczAqbe2HzWhbIVRHNsW82hg==", "dependencies": {"Azure.Core": "1.38.0", "Azure.Identity": "1.11.0", "DC.Utilities": "1.2.1229175", "DnsClient": "1.7.0", "DocumentFormat.OpenXml": "3.0.1", "Microsoft.ApplicationInsights": "2.22.0", "Microsoft.ApplicationInsights.AspNetCore": "2.22.0", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Caching.StackExchangeRedis": "8.0.2", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.ApplicationInsights": "2.22.0", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.3", "System.Drawing.Common": "8.0.1"}}, "DC.Utilities": {"type": "Direct", "requested": "[1.2.1229175, )", "resolved": "1.2.1229175", "contentHash": "12TIMoDBIiF+Idc/SaCJAbx+oNfzjUJ6GcHUcgC7f3PRLwdlycxRvuo8b0o/0hAT2D4WmxTxG6BHb5vu7nLmPg==", "dependencies": {"Azure.Core": "1.38.0", "Azure.Identity": "1.11.0", "DC.Domain.Exceptions": "1.2.1229175", "DnsClient": "1.7.0", "DocumentFormat.OpenXml": "3.0.1", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Caching.StackExchangeRedis": "8.0.2", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.3", "System.Drawing.Common": "8.0.1"}}, "JWT": {"type": "Direct", "requested": "[10.1.1, )", "resolved": "10.1.1", "contentHash": "OJ7Vr6Rf539Ty8iw1FDcfM2Adp7BVT0tOEld5NZnxcVguzczFfv2dsdUKGWf9FVc4VCcVRXv0KZ7Q3/zs+b0Iw==", "dependencies": {"Newtonsoft.Json": "13.0.2", "System.Text.Json": "6.0.7"}}, "Microsoft.Bot.Builder": {"type": "Direct", "requested": "[4.22.0, )", "resolved": "4.22.0", "contentHash": "/3V/pY2aGB5cOMO8J4Pg4naqTv9qAGIKs7Gofo0r1d+sQILgtSP1mcmi6NaJC5W8enB0gaNOQ50nH7wjSc8qgA==", "dependencies": {"Microsoft.Bot.Connector": "4.22.0", "Microsoft.Bot.Connector.Streaming": "4.22.0", "Microsoft.Bot.Streaming": "4.22.0", "Microsoft.Extensions.DependencyInjection": "2.1.0", "Microsoft.Extensions.Logging": "2.1.0"}}, "Microsoft.Bot.Builder.Dialogs": {"type": "Direct", "requested": "[4.22.0, )", "resolved": "4.22.0", "contentHash": "zelvZBcCMnxh4e4JyhMSJ8D8Ep4h1Ytkbd8J2uTxEmLou3U2welyhLQ+LGlZZB12NXTpbDbXovz1i9MDe4+7dw==", "dependencies": {"Microsoft.Bot.Builder": "4.22.0", "Microsoft.Recognizers.Text.Choice": "1.3.2", "Microsoft.Recognizers.Text.DateTime": "1.3.2"}}, "Microsoft.Bot.Builder.Integration.AspNet.Core": {"type": "Direct", "requested": "[4.22.0, )", "resolved": "4.22.0", "contentHash": "xBViVMU+gpu03FRi2hDrIVsjmKfXYoBZov/rNXd2V6Ggj75v/nDaofSr+5oI6nDaLQXPRJX8pu09fNQv/d8syA==", "dependencies": {"Microsoft.Bot.Builder": "4.22.0", "Microsoft.Bot.Configuration": "4.22.0", "Microsoft.Bot.Connector.Streaming": "4.22.0", "Microsoft.Bot.Streaming": "4.22.0", "Newtonsoft.Json": "13.0.1"}}, "Microsoft.TestPlatform.TestHost": {"type": "Direct", "requested": "[17.8.0, )", "resolved": "17.8.0", "contentHash": "9ivcl/7SGRmOT0YYrHQGohWiT5YCpkmy/UEzldfVisLm6QxbLaK3FAJqZXI34rnRLmqqDCeMQxKINwmKwAPiDw==", "dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.8.0", "Newtonsoft.Json": "13.0.1"}}, "MimeTypeMapOfficial": {"type": "Direct", "requested": "[1.0.17, )", "resolved": "1.0.17", "contentHash": "f6DSQSYTckCjq1ifRj0fSLiEmDv+22uo4+JjTeb3MNZK3Ban1XLjA+bTQtD+/7x2NBEAGzBLYIRpS1IU2OgIAw=="}, "AdaptiveCards": {"type": "Transitive", "resolved": "1.2.3", "contentHash": "C3lR/uspvCmWmbE9ku4xGD7M5GKjd+1VksQ5HWlu8Lmv99P4tfya3gGArDO99GqGodwK5z9IHmhfemcqk84VCA==", "dependencies": {"Microsoft.CSharp": "4.5.0", "Newtonsoft.Json": "11.0.2"}}, "AngleSharp": {"type": "Transitive", "resolved": "0.17.0", "contentHash": "74haoXINcj4SdMsmiNzk+9VUwIX1U9P61O6AZd5Uao8SGNnJJB8Y/r8VJRc8orn4c7Vk/oURAKSNF9XcSDxbfA==", "dependencies": {"System.Buffers": "4.5.1", "System.Text.Encoding.CodePages": "5.0.0"}}, "Apache.Avro": {"type": "Transitive", "resolved": "1.11.0", "contentHash": "JpBxUTE/JeVt9yugkIiu0N+FHl+SIihDjs4VZsGFmlZM42nntj2QB8nAdj5yu8LOla54pSAQqdqHcxPa458KWQ==", "dependencies": {"Newtonsoft.Json": "10.0.3", "System.CodeDom": "4.4.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0"}}, "Azure.Core": {"type": "Transitive", "resolved": "1.38.0", "contentHash": "IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Azure.Core.Amqp": {"type": "Transitive", "resolved": "1.3.0", "contentHash": "6GG4gyFkAuHtpBVkvj0wE5+lCM+ttsZlIWAipBkI+jlCUlTgrTiNUROBFnb8xuKoymVDw9Tf1W8RoKqgbd71lg==", "dependencies": {"Microsoft.Azure.Amqp": "2.6.1", "System.Memory": "4.5.4", "System.Memory.Data": "1.0.2"}}, "Azure.Data.AppConfiguration": {"type": "Transitive", "resolved": "1.3.0", "contentHash": "9f+HzKqf56R2clVMBbbC6u1aQiiL6ov+7e3evcrMFo+cuhKGZhPWKO7vLLun8u3pY2jQ6VJ7ltZZyvzapYZNhA==", "dependencies": {"Azure.Core": "1.35.0", "Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Text.Json": "4.7.2"}}, "Azure.Identity": {"type": "Transitive", "resolved": "1.11.0", "contentHash": "JcpkMpW8Wx/XfQfMiSc9ecdIy0GhdpsMCT3Lh8EJZQ/NN6OxPeY7OAcfmucsvdfrldbFJd04m+Kfd+1QILBAgg==", "dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.60.1", "Microsoft.Identity.Client.Extensions.Msal": "4.60.1", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Azure.Messaging.EventGrid": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "Wm5+RY6hNoIPVLPwmr3T1ijVm5GdLVZBij93c4Brwe9iB3X8nlUYNjlnQVVJqK4QLs85nGwqBGUpB4BfYdGXVQ==", "dependencies": {"Azure.Core": "1.20.0", "System.Memory.Data": "1.0.2", "System.Text.Json": "4.6.0"}}, "Azure.Messaging.ServiceBus": {"type": "Transitive", "resolved": "7.17.4", "contentHash": "18t39hYHiGNC6mFtJhkyGAxLgK2mVPfAgBJwHphDZrSzls66U1ITAr70YjpY9coEEObSCNbCGvk44Z9qptmzdA==", "dependencies": {"Azure.Core": "1.38.0", "Azure.Core.Amqp": "1.3.0", "Microsoft.Azure.Amqp": "2.6.5", "Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Memory.Data": "1.0.2"}}, "Azure.Security.KeyVault.Secrets": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "ztr26Ai4K7AZGuw68/ffLDn+2G3WL0myjC7nY1RYkxPMnsplTPEH+Ke4RGxvSkg4kC7bJ9NwdlqpEwfDX0qhdw==", "dependencies": {"Azure.Core": "1.30.0", "System.Memory": "4.5.4", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Confluent.Kafka": {"type": "Transitive", "resolved": "2.3.0", "contentHash": "JSBXN/X7bBNS92bgZp82v1oT58kw9ndpKSGC5VgELeM/HgXUTssFkG3gEPEGd3cOIa5MMJSLe6+gYwzzjdAJPw==", "dependencies": {"System.Memory": "4.5.0", "librdkafka.redist": "2.3.0"}}, "Confluent.SchemaRegistry": {"type": "Transitive", "resolved": "2.3.0", "contentHash": "DmpuBe7EnD+4R6/F1PucL1eAuQd0u8Og++R2ZoQNGT9IyRSdD6sRKxzGeeNI3gwRMmvCDxJVjWPjDCSi7QaIBg==", "dependencies": {"Confluent.Kafka": "2.3.0", "Newtonsoft.Json": "13.0.1", "System.Net.Http": "4.3.4"}}, "Confluent.SchemaRegistry.Serdes.Avro": {"type": "Transitive", "resolved": "2.3.0", "contentHash": "tVDfktOXjdVdesL3g4imIHViaPK+CxtnuPUEKKJZWHo+K0PeXqf+0mk9ZML5cn9Ro7iEXMEAdSVR7v9ApJyaKQ==", "dependencies": {"Apache.Avro": "1.11.0", "Confluent.Kafka": "2.3.0", "Confluent.SchemaRegistry": "2.3.0", "System.Net.NameResolution": "4.3.0", "System.Net.Sockets": "4.3.0"}}, "DC.Domain.Models": {"type": "Transitive", "resolved": "1.2.1229175", "contentHash": "Sul0ggZxaH8n5JQORyJa3V70Ky8/kSkibvDpjOY0JuAWFLNuk+Yasz2bD1h7D1krVyLBFyGEJKg/sJyaH8hpgw==", "dependencies": {"Azure.Core": "1.38.0", "Azure.Identity": "1.11.0", "DC.Domain.Exceptions": "1.2.1229175", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.3"}}, "DC.ESP": {"type": "Transitive", "resolved": "1.2.1229175", "contentHash": "mtqYfu+aDvua89IIgRpFZI9i9bwC4brvMo7BaBkDdVPy18ufgNBcWyMbStSifqUQgO8CpifVVov2FfiqJQPKMw==", "dependencies": {"Azure.Core": "1.38.0", "Azure.Identity": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.5.0", "Confluent.SchemaRegistry.Serdes.Avro": "2.3.0", "DC.Domain.Models": "1.2.1229175", "DC.MassTransit": "1.2.1229175", "DC.Repositories.Base": "1.2.1229175", "DC.Telemetry": "1.2.1229175", "DC.Utilities": "1.2.1229175", "DnsClient": "1.7.0", "DocumentFormat.OpenXml": "3.0.1", "IdentityModel": "6.2.0", "MassTransit.Azure.ServiceBus.Core": "8.2.1", "MassTransit.Kafka": "8.2.1", "Microsoft.ApplicationInsights": "2.22.0", "Microsoft.ApplicationInsights.AspNetCore": "2.22.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.16", "Microsoft.Azure.AppConfiguration.AspNetCore": "7.0.0", "Microsoft.EntityFrameworkCore": "8.0.1", "Microsoft.EntityFrameworkCore.Cosmos": "8.0.1", "Microsoft.Extensions.Azure": "1.7.1", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Caching.StackExchangeRedis": "8.0.2", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.DiagnosticAdapter": "3.1.32", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Identity.Core": "8.0.1", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.ApplicationInsights": "2.22.0", "Microsoft.Extensions.Logging.AzureAppServices": "8.0.1", "Microsoft.FeatureManagement": "3.1.1", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.3", "Swashbuckle.AspNetCore": "6.2.3", "Swashbuckle.AspNetCore.Newtonsoft": "6.2.3", "System.Drawing.Common": "8.0.1", "ZiggyCreatures.FusionCache": "1.0.0", "ZiggyCreatures.FusionCache.Backplane.StackExchangeRedis": "1.0.0", "ZiggyCreatures.FusionCache.Serialization.SystemTextJson": "1.0.0"}}, "DC.MassTransit": {"type": "Transitive", "resolved": "1.2.1229175", "contentHash": "KcHIVYBahIalk/U/9F5F5xnPm7GU3BuRFCa2AGWpL9HrUMFuKSLLm3Jd54yQUtfBLxrFYehOLgby/qjDspeMpQ==", "dependencies": {"Azure.Core": "1.38.0", "Azure.Identity": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.5.0", "Confluent.SchemaRegistry.Serdes.Avro": "2.3.0", "DC.Api.Base": "1.2.1229175", "DC.Domain.Models": "1.2.1229175", "DnsClient": "1.7.0", "DocumentFormat.OpenXml": "3.0.1", "IdentityModel": "6.2.0", "MassTransit.Azure.ServiceBus.Core": "8.2.1", "MassTransit.Kafka": "8.2.1", "Microsoft.ApplicationInsights": "2.22.0", "Microsoft.ApplicationInsights.AspNetCore": "2.22.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.16", "Microsoft.Azure.AppConfiguration.AspNetCore": "7.0.0", "Microsoft.EntityFrameworkCore": "8.0.1", "Microsoft.EntityFrameworkCore.Cosmos": "8.0.1", "Microsoft.Extensions.Azure": "1.7.1", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Caching.StackExchangeRedis": "8.0.2", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.DiagnosticAdapter": "3.1.32", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Identity.Core": "8.0.1", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.ApplicationInsights": "2.22.0", "Microsoft.Extensions.Logging.AzureAppServices": "8.0.1", "Microsoft.FeatureManagement": "3.1.1", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.3", "Swashbuckle.AspNetCore": "6.2.3", "Swashbuckle.AspNetCore.Newtonsoft": "6.2.3", "System.Drawing.Common": "8.0.1", "ZiggyCreatures.FusionCache": "1.0.0", "ZiggyCreatures.FusionCache.Backplane.StackExchangeRedis": "1.0.0", "ZiggyCreatures.FusionCache.Serialization.SystemTextJson": "1.0.0"}}, "DC.Security.ActiveDirectory": {"type": "Transitive", "resolved": "1.2.1229175", "contentHash": "eDRHWAJQ9rlXzP3YL3Yi1BXnxCg/66jvmr5Vf5dfsXGFmmlkfw1Z7pLAlsm8J2JEp92ym91GwqMO6noqpcYRpg==", "dependencies": {"Azure.Core": "1.38.0", "Azure.Identity": "1.11.0", "DC.Domain.Exceptions": "1.2.1229175", "DC.Utilities": "1.2.1229175", "DnsClient": "1.7.0", "DocumentFormat.OpenXml": "3.0.1", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Caching.StackExchangeRedis": "8.0.2", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.3", "System.Drawing.Common": "8.0.1"}}, "DC.Security.MutualSsl": {"type": "Transitive", "resolved": "1.2.1229175", "contentHash": "K9aeusTtsmDWXO22EPQbrdJnh82Z+gzoL81gWTL+TWJuosH3sW5EoH5wKk+AFDr+rcGkITRahvavzQucH4DwWw==", "dependencies": {"Azure.Core": "1.38.0", "Azure.Identity": "1.11.0", "DC.Utilities": "1.2.1229175", "DnsClient": "1.7.0", "DocumentFormat.OpenXml": "3.0.1", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Caching.StackExchangeRedis": "8.0.2", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.3", "System.Drawing.Common": "8.0.1"}}, "DnsClient": {"type": "Transitive", "resolved": "1.7.0", "contentHash": "2hrXR83b5g6/ZMJOA36hXp4t56yb7G1mF3Hg6IkrHxvtyaoXRn2WVdgDPN3V8+GugOlUBbTWXgPaka4dXw1QIg==", "dependencies": {"Microsoft.Win32.Registry": "5.0.0"}}, "DocumentFormat.OpenXml": {"type": "Transitive", "resolved": "3.0.1", "contentHash": "DCK1cwFUJ1FGGyYyo++HWl9H1RkqMWIu+FGOLRy6E4L4y0/HIhlJ7N/n1HKboFfOwKn1cMBRxt1RCuDbIEy5YQ==", "dependencies": {"DocumentFormat.OpenXml.Framework": "3.0.1"}}, "DocumentFormat.OpenXml.Framework": {"type": "Transitive", "resolved": "3.0.1", "contentHash": "ifyI7OW7sggz7LQMIAD2aUsY/zVUON9QaHrpZ4MK33iVMeHlTG4uhUE2aLWb31nry+LCs2ALDAwf8OfUJGjgBg==", "dependencies": {"System.IO.Packaging": "8.0.0"}}, "IdentityModel": {"type": "Transitive", "resolved": "6.2.0", "contentHash": "4AXZ6Tp+DNwrSSeBziiX/231i8ZpD77A9nEMyc68gLSCWG0kgWsIBeFquYcBebiIPkfB7GEXzCYuuLeR1QZJIQ=="}, "libphonenumber-csharp": {"type": "Transitive", "resolved": "8.13.29", "contentHash": "SSEUnbiyhjb/8yiDRBHdlw0XR3dKi0hT+gWdYoPCn+kPYbPKmweaKXCUfyLqG/emIAqtoQFrJf1+t82bH1Hyug=="}, "librdkafka.redist": {"type": "Transitive", "resolved": "2.3.0", "contentHash": "pH5zFZ0S56Wl6UfRkmDJN2AjHlPdVxlTskncFnL27LLGQuuY2dAU8YrZBkduBOws4tURS2TaTp1aPsY3qeJ0bw=="}, "MassTransit": {"type": "Transitive", "resolved": "8.2.1", "contentHash": "bjDmaE+XaYYRytDM2W5hFm62lb+gQpJtsZD9Eyol1ULBwQxlnQAIszKovc1GHmgJ2cLmwl8gI5KjCj23ev9ilg==", "dependencies": {"MassTransit.Abstractions": "8.2.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "MassTransit.Abstractions": {"type": "Transitive", "resolved": "8.2.1", "contentHash": "9dk3hcAlXXXEVPZC+5UPJnYnQ9Hcv71kXtkHL85w5wtQ43go1Ieso6QJYvCVj++mq61LCjnhJSPlN/eWgScbkw=="}, "MassTransit.Azure.ServiceBus.Core": {"type": "Transitive", "resolved": "8.2.1", "contentHash": "/emlrQQIyvYyYWdVo866Muu6D081wZrUkJvTnJmiQsk77/MWyLWM/fLvzaDkjxh/Or0iVlss3E0grR+Slsi4MA==", "dependencies": {"Azure.Identity": "1.10.4", "Azure.Messaging.ServiceBus": "7.17.4", "MassTransit": "8.2.1"}}, "MassTransit.Kafka": {"type": "Transitive", "resolved": "8.2.1", "contentHash": "Efs+ZPEpmqisO6ha3/G7Wlpmt8kjbJKML5IevN/lGTk/BQdBfdP/RPcbwDWbTREJYCDlKH+TywFtDaA5uZbOsw==", "dependencies": {"Confluent.Kafka": "2.3.0", "MassTransit": "8.2.1"}}, "Microsoft.ApplicationInsights": {"type": "Transitive", "resolved": "2.22.0", "contentHash": "3AOM9bZtku7RQwHyMEY3tQMrHIgjcfRDa6YQpd/QG2LDGvMydSlL9Di+8LLMt7J2RDdfJ7/2jdYv6yHcMJAnNw==", "dependencies": {"System.Diagnostics.DiagnosticSource": "5.0.0"}}, "Microsoft.ApplicationInsights.AspNetCore": {"type": "Transitive", "resolved": "2.22.0", "contentHash": "OuiZgRDX0zm3a1DRk/GT54ZsyTg8a88n3cpkVEYFJoRhT5X84l2C68BuKrglE0sIj+C0+o2WTR8S21YBD/ZWgA==", "dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.22.0", "Microsoft.ApplicationInsights.EventCounterCollector": "2.22.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.22.0", "Microsoft.ApplicationInsights.WindowsServer": "2.22.0", "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": "2.22.0", "Microsoft.AspNetCore.Hosting": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.22", "Microsoft.Extensions.Configuration.Json": "3.1.0", "Microsoft.Extensions.Logging.ApplicationInsights": "2.22.0", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.ApplicationInsights.DependencyCollector": {"type": "Transitive", "resolved": "2.22.0", "contentHash": "gseSmuCshdZqcn5r6EW1Zx52e5/p2RpAsHSanlxs8pq+Pbg1RZP678tXtxfVuHC0fA3MVV852pnfFC7ZGB0jew==", "dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "System.Diagnostics.DiagnosticSource": "5.0.0"}}, "Microsoft.ApplicationInsights.EventCounterCollector": {"type": "Transitive", "resolved": "2.22.0", "contentHash": "/fXUyZIMwaWfETgire4fygaBhY8J+hXvTVhSFXKV0JOFBenzzU4smGW8iRUFhE534a3QrczuFfmfCCkXRKbsNg==", "dependencies": {"Microsoft.ApplicationInsights": "2.22.0"}}, "Microsoft.ApplicationInsights.PerfCounterCollector": {"type": "Transitive", "resolved": "2.22.0", "contentHash": "nExsJsbN7694ueUNNBms/UNgza9WH4W/I6i5CnF9ujJ1sp57EL5Uk0NP9MDwlLVtYaaiznKPatVSv3Nu8vAplw==", "dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.Extensions.Caching.Memory": "1.0.0", "System.Diagnostics.PerformanceCounter": "6.0.0"}}, "Microsoft.ApplicationInsights.WindowsServer": {"type": "Transitive", "resolved": "2.22.0", "contentHash": "9k1x1+Kq1fElvcv0o/w9w8tRWAa2Y0f4NPBeHF5b2xCety4GM1yv3K3Ra0lZwO3kW0SHlm9M8nrySuyKQlHyYA==", "dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.22.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.22.0", "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": "2.22.0", "System.Diagnostics.DiagnosticSource": "5.0.0"}}, "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": {"type": "Transitive", "resolved": "2.22.0", "contentHash": "Blb6S8UJSJ/jo6mxeO38gKgui75D2brp5NpXJoZUhyJzfmYsfhn7a4t5f+CDfAKyvie7sQB2FIzeEDQSiRE5zw==", "dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "System.IO.FileSystem.AccessControl": "4.7.0"}}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"type": "Transitive", "resolved": "8.0.16", "contentHash": "WtKPOmlqzwWGQZCOixnOE+3bIYzxmUw7zjfs7VRWDhZlxfcmw5Qw2c4r87XWPk+kkurntzvE0sWyruI6KDpTiA==", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}}, "Microsoft.AspNetCore.Cryptography.Internal": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "ryzsiEKr1qJ8f/CARxK8/zTX41aGUpoYOrZuKpsWiK6LwnuynxSFrzBDF04bT7xHF/i0EOeqkIRvfIohI/EsTg=="}, "Microsoft.AspNetCore.Cryptography.KeyDerivation": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "soX8sz1IUCEpsx3UEZUhdZ0RIi890qr6l1LEbFWMcbzSs0MhYM/WK+W889dkEBycVMh/fUzodMBbFVSp9tM0AQ==", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.1"}}, "Microsoft.AspNetCore.Hosting": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "MqYc0DUxrhAPnb5b4HFspxsoJT+gJlLsliSxIgovf4BsbmpaXQId0/pDiVzLuEbmks2w1/lRfY8w0lQOuK1jQQ==", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.1.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.1.1", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Microsoft.Extensions.FileProviders.Physical": "2.1.1", "Microsoft.Extensions.Hosting.Abstractions": "2.1.1", "Microsoft.Extensions.Logging": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Reflection.Metadata": "1.6.0"}}, "Microsoft.AspNetCore.Hosting.Abstractions": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "76cKcp2pWhvdV2TXTqMg/DyW7N6cDzTEhtL8vVWFShQN+Ylwv3eO/vUQr2BS3Hz4IZHEpL+FOo2T+MtymHDqDQ==", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.Hosting.Abstractions": "2.1.1"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "+vD7HJYzAXNq17t+NgRkpS38cxuAyOBu8ixruOiA3nWsybozolUdALWiZ5QFtGRzajSLPFA2YsbO3NPcqoUwcw==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.1", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1"}}, "Microsoft.AspNetCore.Http": {"type": "Transitive", "resolved": "2.1.22", "contentHash": "+Blk++1JWqghbl8+3azQmKhiNZA5wAepL9dY2I6KVmu2Ri07MAcvAVC888qUvO7yd7xgRgZOMfihezKg14O/2A==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.AspNetCore.WebUtilities": "2.1.1", "Microsoft.Extensions.ObjectPool": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1"}}, "Microsoft.AspNetCore.Http.Abstractions": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "kQUEVOU4loc8CPSb2WoHFTESqwIa8Ik7ysCBfTwzHAd0moWovc9JQLmhDIHlYLjHbyexqZAlkq/FPRUZqokebw==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.1", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.AspNetCore.Http.Extensions": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "ncAgV+cqsWSqjLXFUTyObGh4Tr7ShYYs3uW8Q/YpRwZn7eLV7dux5Z6GLY+rsdzmIHiia3Q2NWbLULQi7aziHw==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1", "System.Buffers": "4.5.0"}}, "Microsoft.AspNetCore.Http.Features": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "VklZ7hWgSvHBcDtwYYkdMdI/adlf7ebxTZ9kdzAhX+gUs5jSHE9mZlTamdgf9miSsxc1QjNazHXTDJdVPZKKTw==", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.1"}}, "Microsoft.AspNetCore.JsonPatch": {"type": "Transitive", "resolved": "6.0.0-rc.1.21452.15", "contentHash": "+27ce/d0w7jyEmCCMrzwFRg6PHZxQ37Fmnf+es1qQudbip2rryxPdM8TRoXT6wNtdlzqHnIyXTtLjSs0uIDhxA==", "dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.1"}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"type": "Transitive", "resolved": "6.0.0-rc.1.21452.15", "contentHash": "/dspuIknGgOdBygH3bmjsIH5neuuJ7DHILXrKVPAoCykPARAIUBckZlIW6RrYqmbGTZYT/YFCzr/xDR5f0YiAA==", "dependencies": {"Microsoft.AspNetCore.JsonPatch": "6.0.0-rc.1.21452.15", "Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2"}}, "Microsoft.AspNetCore.WebUtilities": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "PGKIZt4+412Z/XPoSjvYu/QIbTxcAQuEFNoA1Pw8a9mgmO0ZhNBmfaNyhgXFf7Rq62kP0tT/2WXpxdcQhkFUPA==", "dependencies": {"Microsoft.Net.Http.Headers": "2.1.1", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.Azure.Amqp": {"type": "Transitive", "resolved": "2.6.5", "contentHash": "Hx0XrpkASCfZbwKqr5bc/5DveYZyhh+737AdUh0jq6nXFO+LbhSMOS3KHI4DtoHDTWR1WFycdxNJdByCZ/bzXQ=="}, "Microsoft.Azure.AppConfiguration.AspNetCore": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "onLEml0xN+4bb699sHV+jUJGiCuuBOwx83pOMiC2eO7vSwXNRDzQ6df+335RufUj8khJVrzQQtZ8hqyPa1EMRg==", "dependencies": {"Microsoft.Extensions.Configuration.AzureAppConfiguration": "7.0.0"}}, "Microsoft.Azure.Cosmos": {"type": "Transitive", "resolved": "3.35.4", "contentHash": "oDwaE4TlQHec3R8CH5xLgvci5Fpj4q/W1kSPu3gm2Q49cJXXyEQqGp/j3dgM7O/fwrm9u5ZgvYtkpor2f4i2wQ==", "dependencies": {"Azure.Core": "1.19.0", "Microsoft.Bcl.AsyncInterfaces": "1.0.0", "Microsoft.Bcl.HashCode": "1.1.0", "Newtonsoft.Json": "10.0.2", "System.Buffers": "4.5.1", "System.Collections.Immutable": "1.7.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}}, "Microsoft.Bcl.AsyncInterfaces": {"type": "Transitive", "resolved": "1.1.1", "contentHash": "yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w=="}, "Microsoft.Bcl.HashCode": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "J2G1k+u5unBV+aYcwxo94ip16Rkp65pgWFb0R6zwJipzWNMgvqlWeuI7/+R+e8bob66LnSG+llLJ+z8wI94cHg=="}, "Microsoft.Bot.Configuration": {"type": "Transitive", "resolved": "4.22.0", "contentHash": "ugLGig7jv8q3GpYZP4qiEl38Gjuv/+bzhcDcMK3+MN9+EneNt23uUXakvknRse7NA03Pjeyw5V2pPIfGs43Jtg==", "dependencies": {"Newtonsoft.Json": "13.0.1", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Microsoft.Bot.Connector": {"type": "Transitive", "resolved": "4.22.0", "contentHash": "2KFUhgIPeLWGBrlE0C39YYEBa6ExiK3e9W1TVWuivRu9SZT08k0MJZloM35faEQG/XXlRmUce3nUC0Noq4RLyw==", "dependencies": {"Microsoft.Bot.Schema": "4.22.0", "Microsoft.CSharp": "4.5.0", "Microsoft.Extensions.Http": "2.1.0", "Microsoft.Extensions.Logging": "2.1.0", "Microsoft.Identity.Client": "4.55.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "5.6.0", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.1"}}, "Microsoft.Bot.Connector.Streaming": {"type": "Transitive", "resolved": "4.22.0", "contentHash": "gIYDiAMdv505pUuo6ml6YqhXlrpn42u8oOB5S0gBgFzFOMi/AGyTuAEm/tc83BAzo5+oE0RpNUPXYyzf9I+DXw==", "dependencies": {"Microsoft.Bot.Schema": "4.22.0", "Microsoft.Bot.Streaming": "4.22.0", "Microsoft.Extensions.Logging": "2.1.0", "Newtonsoft.Json": "13.0.1", "System.IO.Pipelines": "5.0.1", "System.Text.Encodings.Web": "5.0.1", "System.Text.Json": "4.7.2"}}, "Microsoft.Bot.Schema": {"type": "Transitive", "resolved": "4.22.0", "contentHash": "MmAoBfIUU9LfgbwvQ3liE+r7XIjReULDGK8hbGCd8ac1tNS6VMRihNN/MGePzS89r+0HJ1FWBXY7w0BeJi8rEg==", "dependencies": {"AdaptiveCards": "1.2.3", "Newtonsoft.Json": "13.0.1"}}, "Microsoft.Bot.Streaming": {"type": "Transitive", "resolved": "4.22.0", "contentHash": "WM3b9nPm2l5lJWx/kjkRutEYKiYC+M1mmwuTOoXFpQrPthwd/cjV1773KlxYGHYHHmDMuZzPS3xBb/ZnYWIQoA==", "dependencies": {"Microsoft.Extensions.Logging": "2.1.0", "Microsoft.Net.Http.Headers": "2.1.0", "Newtonsoft.Json": "13.0.1"}}, "Microsoft.CSharp": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA=="}, "Microsoft.EntityFrameworkCore": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "hPagYIuWPpZF6AwOR7mlKv+GLEk8wrbsIVr8qYHqSWN2zDghOYTu2Qxi6CtrJP3V9UgzZ6sjQVM/jnrodpz10Q==", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.1", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.1", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}}, "Microsoft.EntityFrameworkCore.Abstractions": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "KBj2meUDWmMDRYpxyebyYQMf7+aGyTWvKD9UTuFKPP/NQGVsJUqbCCM+p/LCxSppcm2dQt+z73e/yBFlq/2jmA=="}, "Microsoft.EntityFrameworkCore.Analyzers": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "8HgodfPiUEMu5rlkcGa9CJdEpF5VeaeWhHAdKuKstgr6GBFc91xCJo/haOVzM8jKPS167PrlC8ChYdtzFVpp4A=="}, "Microsoft.EntityFrameworkCore.Cosmos": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "5P5/ShJ2CS6a19wk0hsSUMSlQxgFr5bUSsn14DWHPfrq4DT7p2Tfiu+DFM0RjF4Hw1tZfxyThwD/esUe3Czs2Q==", "dependencies": {"Microsoft.Azure.Cosmos": "3.35.4", "Microsoft.EntityFrameworkCore": "8.0.1"}}, "Microsoft.Extensions.ApiDescription.Server": {"type": "Transitive", "resolved": "3.0.0", "contentHash": "LH4OE/76F6sOCslif7+Xh3fS/wUUrE5ryeXAMcoCnuwOQGT5Smw0p57IgDh/pHgHaGz/e+AmEQb7pRgb++wt0w=="}, "Microsoft.Extensions.Azure": {"type": "Transitive", "resolved": "1.7.1", "contentHash": "mS4uBCrUz6eWLZUgkSQKNM97rRMOTFMhi3rdehNJmmnaCQd0mbsrWYkOG9ZVF93zFuvpHlqRUuTGCsHNvMCVPQ==", "dependencies": {"Azure.Core": "1.35.0", "Azure.Identity": "1.10.3", "Microsoft.Extensions.Configuration.Abstractions": "2.1.0", "Microsoft.Extensions.Configuration.Binder": "2.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0"}}, "Microsoft.Extensions.Caching.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"type": "Transitive", "resolved": "8.0.2", "contentHash": "hVBF/rfSJG3N9cenB6mANvsz+7MQ79PihorQrk5itDWnvkjb0ThdsznwMfDDkJxHdh4Iw0u80pessVXGPdCIdA==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "StackExchange.Redis": "2.6.122"}}, "Microsoft.Extensions.Configuration": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.AzureAppConfiguration": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "ef49XuB+STPg0Yw/UcDKEyom5qm9dR1klR/PLVpAX6+hRdBuQOffZfilbfTNAYT6jsn3dgRjsOtq90hYlcI9vQ==", "dependencies": {"Azure.Data.AppConfiguration": "1.3.0", "Azure.Messaging.EventGrid": "4.7.0", "Azure.Security.KeyVault.Secrets": "4.3.0", "Microsoft.Extensions.Configuration": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0"}}, "Microsoft.Extensions.Configuration.Binder": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Json": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}}, "Microsoft.Extensions.Configuration.UserSecrets": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg=="}, "Microsoft.Extensions.DiagnosticAdapter": {"type": "Transitive", "resolved": "3.1.32", "contentHash": "oDv3wt+Q5cmaSfOQ3Cdu6dF6sn/x5gzWdNpOq4ajBwCMWYBr6CchncDvB9pF83ORlbDuX32MsVLOPGPxW4Lx4g==", "dependencies": {"System.Diagnostics.DiagnosticSource": "4.7.1"}}, "Microsoft.Extensions.Diagnostics": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "P9SoBuVZhJPpALZmSq72aQEb9ryP67EdquaCZGXGrrcASTNHYdrUhnpgSwIipgM5oVC+dKpRXg5zxobmF9xr5g==", "dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "AT2qqos3IgI09ok36Qag9T8bb6kHJ3uT9Q5ki6CySybFsK6/9JbvQAgAHf1pVEjST0/N4JaFaCbm40R5edffwg=="}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ=="}, "Microsoft.Extensions.Hosting": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ItYHpdqVp5/oFLT5QqbopnkKlyFG9EW/9nhM6/yfObeKt6Su0wkBio6AizgRHGNwhJuAtlE5VIjow5JOTrip6w==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.Extensions.Logging.EventSource": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Http": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Identity.Core": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "/8GGtoIIoaeqk3PLV++ClqmVvHvzimhiSiWEdM9kdaNVkfR0JSDjx2AQHZcuekqiSPyNprFcSAg81uGWtdqP3w==", "dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "8.0.1", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.1"}}, "Microsoft.Extensions.Logging": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging.ApplicationInsights": {"type": "Transitive", "resolved": "2.22.0", "contentHash": "5OmXub+9MyX8FbqgO+hBJRHk1iJ+UZUU20oIU3wo+RbmH6Jtsja79rriHLlzlrkMzWbpCkCzF6f4Yb6iGbsDag==", "dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.Extensions.Logging": "2.1.1"}}, "Microsoft.Extensions.Logging.AzureAppServices": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "ADjmGWZcb8+0j4XSPqkpFxObHvBDNJWp9ETPw388/aZJ1MHh9XvAf/5VotqN4jXlLWEq3OQwEJXSaN/ooiIICw==", "dependencies": {"Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "System.ValueTuple": "4.5.0"}}, "Microsoft.Extensions.Logging.Configuration": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ixXXV0G/12g6MXK65TLngYN9V5hQQRuV+fZi882WIoVJT7h5JvoYoxTEwCgdqwLjSneqh1O+66gM8sMr9z/rsQ==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Logging.Console": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "e+48o7DztoYog+PY430lPxrM4mm3PbA6qucvQtUDDwVo4MO+ejMw7YGc/o2rnxbxj4isPxdfKFzTxvXMwAz83A==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Text.Json": "8.0.0"}}, "Microsoft.Extensions.Logging.Debug": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging.EventLog": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3X9D3sl7EmOu7vQp5MJrmIJBl5XSdOhZPYXUeFfYa6Nnm9+tok8x3t3IVPLhm7UJtPOU61ohFchw8rNm9tIYOQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.EventLog": "8.0.0"}}, "Microsoft.Extensions.Logging.EventSource": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "oKcPMrw+luz2DUAKhwFXrmFikZWnyc8l2RKoQwqU3KIZZjcfoJE0zRHAnqATfhRZhtcbjl/QkiY2Xjxp0xu+6w==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0", "System.Text.Json": "8.0.0"}}, "Microsoft.Extensions.ObjectPool": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "SErON45qh4ogDp6lr6UvVmFYW0FERihW+IQ+2JyFv1PUyWktcJytFaWH5zarufJvZwhci7Rf1IyGXr9pVEadTw=="}, "Microsoft.Extensions.Options": {"type": "Transitive", "resolved": "8.0.2", "contentHash": "dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g=="}, "Microsoft.FeatureManagement": {"type": "Transitive", "resolved": "3.1.1", "contentHash": "H9KZxiFSJKGnQtiuHwkvKgw6ij1E9M5lBUtqn1VZPAR1Wy8fkrZOmWwoC+3DJrqOVeZq+riUKuwWst8zEpmtOw==", "dependencies": {"Microsoft.Extensions.Caching.Memory": "2.1.23", "Microsoft.Extensions.Configuration.Binder": "2.1.10", "Microsoft.Extensions.Logging": "2.1.1"}}, "Microsoft.Identity.Client": {"type": "Transitive", "resolved": "4.60.1", "contentHash": "rC+qiskr8RKq2a43hH55vuDRz4Wto+bxwxMrKzCIOann1NL0OFFTjEk4ZVnTTBdijVWC6mhOaSmdV1H6J6bXmA==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}}, "Microsoft.Identity.Client.Extensions.Msal": {"type": "Transitive", "resolved": "4.60.1", "contentHash": "EdPcGqvruFzNBcW+/3DSP4vNmLNYXSSnngj+QecAxmy6VRnvA7kt5KE2bU8qQmt4KkOitNHBVYVwze2XkqOLxw==", "dependencies": {"Microsoft.Identity.Client": "4.60.1", "System.Security.Cryptography.ProtectedData": "4.5.0"}}, "Microsoft.IdentityModel.Abstractions": {"type": "Transitive", "resolved": "7.1.2", "contentHash": "33eTIA2uO/L9utJjZWbKsMSVsQf7F8vtd6q5mQX7ZJzNvCpci5fleD6AeANGlbbb7WX7XKxq9+Dkb5e3GNDrmQ=="}, "Microsoft.IdentityModel.JsonWebTokens": {"type": "Transitive", "resolved": "7.1.2", "contentHash": "cloLGeZolXbCJhJBc5OC05uhrdhdPL6MWHuVUnkkUvPDeK7HkwThBaLZ1XjBQVk9YhxXE2OvHXnKi0PLleXxDg==", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.1.2"}}, "Microsoft.IdentityModel.Logging": {"type": "Transitive", "resolved": "7.1.2", "contentHash": "YCxBt2EeJP8fcXk9desChkWI+0vFqFLvBwrz5hBMsoh0KJE6BC66DnzkdzkJNqMltLromc52dkdT206jJ38cTw==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2"}}, "Microsoft.IdentityModel.Protocols": {"type": "Transitive", "resolved": "7.1.2", "contentHash": "SydLwMRFx6EHPWJ+N6+MVaoArN1Htt92b935O3RUWPY1yUF63zEjvd3lBu79eWdZUwedP8TN2I5V9T3nackvIQ==", "dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"type": "Transitive", "resolved": "7.1.2", "contentHash": "6lHQoLXhnMQ42mGrfDkzbIOR3rzKM1W1tgTeMPLgLCqwwGw0d96xFi/UiX/fYsu7d6cD5MJiL3+4HuI8VU+sVQ==", "dependencies": {"Microsoft.IdentityModel.Protocols": "7.1.2", "System.IdentityModel.Tokens.Jwt": "7.1.2"}}, "Microsoft.IdentityModel.Tokens": {"type": "Transitive", "resolved": "7.1.2", "contentHash": "oICJMqr3aNEDZOwnH5SK49bR6Z4aX0zEAnOLuhloumOSuqnNq+GWBdQyrgILnlcT5xj09xKCP/7Y7gJYB+ls/g==", "dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2"}}, "Microsoft.Net.Http.Headers": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "lPNIphl8b2EuhOE9dMH6EZDmu7pS882O+HMi5BJNsigxHaWlBrYxZHFZgE18cyaPp6SSZcTkKkuzfjV/RRQKlA==", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.1", "System.Buffers": "4.5.0"}}, "Microsoft.NETCore.Platforms": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ=="}, "Microsoft.NETCore.Targets": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg=="}, "Microsoft.OpenApi": {"type": "Transitive", "resolved": "1.2.3", "contentHash": "Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw=="}, "Microsoft.Recognizers.Text": {"type": "Transitive", "resolved": "1.3.2", "contentHash": "URNFAH3Q6rJILL2PixaOcUfoLOFRaiEw7K6AsVsbMzThBZeNU8GMpJb2mFABCyx5I43DrmpB0vl/7EmRP/16RQ==", "dependencies": {"Microsoft.Extensions.Caching.Memory": "2.0.0", "System.Collections.Immutable": "1.4.0", "System.ValueTuple": "4.4.0"}}, "Microsoft.Recognizers.Text.Choice": {"type": "Transitive", "resolved": "1.3.2", "contentHash": "4cPOSKNCN0BIeaAfSV7DnR/XxsTscm9lWgEzhYIbrXd94UuHzZuUR+0U5MaYTB9W6t7yoHJLSChAaGq4pAAMYw==", "dependencies": {"Microsoft.Recognizers.Text": "1.3.2", "System.Collections.Immutable": "1.4.0"}}, "Microsoft.Recognizers.Text.DateTime": {"type": "Transitive", "resolved": "1.3.2", "contentHash": "KGDTLJflS2qJVFHDaWivRHH8lp/Udpjd3v0We7MDYFNcnNsJKjlW3zXwx3DYmStRtGgFD+o9/oNLDFKhBUFdFg==", "dependencies": {"Microsoft.Recognizers.Text": "1.3.2", "Microsoft.Recognizers.Text.Number": "1.3.2", "Microsoft.Recognizers.Text.NumberWithUnit": "1.3.2", "System.Collections.Immutable": "1.4.0"}}, "Microsoft.Recognizers.Text.Number": {"type": "Transitive", "resolved": "1.3.2", "contentHash": "eYFPcfeQeF3gbb9ReEFT9OHznSI8WmU7dwVuTXbRreySZEfdDM967Vg0sGlcnploe9XDcqPPd66851htVR2dqg==", "dependencies": {"Microsoft.Recognizers.Text": "1.3.2", "System.Collections.Immutable": "1.4.0"}}, "Microsoft.Recognizers.Text.NumberWithUnit": {"type": "Transitive", "resolved": "1.3.2", "contentHash": "s7f+sqnJFmNV1BD32ESN02Exs2WJgA79aCDFIVg4plw3PBTxIFO+79BDf0J2WeH0JeSXpQekWuedIXFXQc5x+A==", "dependencies": {"Microsoft.Recognizers.Text": "1.3.2", "Microsoft.Recognizers.Text.Number": "1.3.2", "System.Collections.Immutable": "1.4.0"}}, "Microsoft.TestPlatform.ObjectModel": {"type": "Transitive", "resolved": "17.8.0", "contentHash": "AYy6vlpGMfz5kOFq99L93RGbqftW/8eQTqjT9iGXW6s9MRP3UdtY8idJ8rJcjeSja8A18IhIro5YnH3uv1nz4g==", "dependencies": {"NuGet.Frameworks": "6.5.0", "System.Reflection.Metadata": "1.6.0"}}, "Microsoft.Win32.Registry": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw=="}, "Newtonsoft.Json": {"type": "Transitive", "resolved": "13.0.3", "contentHash": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ=="}, "Newtonsoft.Json.Bson": {"type": "Transitive", "resolved": "1.0.2", "contentHash": "QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "dependencies": {"Newtonsoft.Json": "12.0.1"}}, "NuGet.Frameworks": {"type": "Transitive", "resolved": "6.5.0", "contentHash": "QWINE2x3MbTODsWT1Gh71GaGb5icBz4chS8VYvTgsBnsi8esgN6wtHhydd7fvToWECYGq7T4cgBBDiKD/363fg=="}, "Pipelines.Sockets.Unofficial": {"type": "Transitive", "resolved": "2.2.8", "contentHash": "zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "dependencies": {"System.IO.Pipelines": "5.0.1"}}, "PnP.Core": {"type": "Transitive", "resolved": "1.11.0", "contentHash": "bhUaK+Ivby3YB/6flVSrGUo5sENU7PnZ2e4usSBpWcHFG8cb30t2LeIzCNerwOauxCJqauYw1i4LNiFxVRvesA==", "dependencies": {"AngleSharp": "0.17.0", "Microsoft.ApplicationInsights": "2.21.0", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.IdentityModel.Tokens.Jwt": "6.27.0", "System.Text.Json": "8.0.0", "TimeZoneConverter": "6.1.0"}}, "PnP.Core.Auth": {"type": "Transitive", "resolved": "1.11.0", "contentHash": "EVMgBN50CbaQvepoOGMFhHGKE+2E0kTTLFbJMIDn//h5p74OhDH7HwAR2dUiGeT3a4KYQC1OT2yaBHKJ99QbSg==", "dependencies": {"Microsoft.Identity.Client": "4.50.0", "PnP.Core": "1.11.0"}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g=="}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw=="}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg=="}, "runtime.native.System": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ=="}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA=="}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ=="}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w=="}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg=="}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw=="}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w=="}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg=="}, "StackExchange.Redis": {"type": "Transitive", "resolved": "2.7.27", "contentHash": "Uqc2OQHglqj9/FfGQ6RkKFkZfHySfZlfmbCl+hc+u2I/IqunfelQ7QJi7ZhvAJxUtu80pildVX6NPLdDaUffOw==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Pipelines.Sockets.Unofficial": "2.2.8"}}, "Swashbuckle.AspNetCore": {"type": "Transitive", "resolved": "6.2.3", "contentHash": "cnzQDn0Le+hInsw2SYwlOhOCPXpYi/szcvnyqZJ12v+QyrLBwAmWXBg6RIyHB18s/mLeywC+Rg2O9ndz0IUNYQ==", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "3.0.0", "Swashbuckle.AspNetCore.Swagger": "6.2.3", "Swashbuckle.AspNetCore.SwaggerGen": "6.2.3", "Swashbuckle.AspNetCore.SwaggerUI": "6.2.3"}}, "Swashbuckle.AspNetCore.Newtonsoft": {"type": "Transitive", "resolved": "6.2.3", "contentHash": "O1i7j5obeREGs2XInJ33zqh+lmsgaR2SCuKCjwkNiAEm7GcUqgSXdSVLt2GW07h4kSKSCyca/gJFEAdKYYn1UQ==", "dependencies": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson": "6.0.0-rc.1.21452.15", "Swashbuckle.AspNetCore.SwaggerGen": "6.2.3"}}, "Swashbuckle.AspNetCore.Swagger": {"type": "Transitive", "resolved": "6.2.3", "contentHash": "qOF7j1sL0bWm8g/qqHVPCvkO3JlVvUIB8WfC98kSh6BT5y5DAnBNctfac7XR5EZf+eD7/WasvANncTqwZYfmWQ==", "dependencies": {"Microsoft.OpenApi": "1.2.3"}}, "Swashbuckle.AspNetCore.SwaggerGen": {"type": "Transitive", "resolved": "6.2.3", "contentHash": "+Xq7WdMCCfcXlnbLJVFNgY8ITdP2TRYIlpbt6IKzDw5FwFxdi9lBfNDtcT+/wkKwX70iBBFmXldnnd02/VO72A==", "dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.2.3"}}, "Swashbuckle.AspNetCore.SwaggerUI": {"type": "Transitive", "resolved": "6.2.3", "contentHash": "bCRI87uKJVb4G+KURWm8LQrL64St04dEFZcF6gIM67Zc0Sr/N47EO83ybLMYOvfNdO1DCv8xwPcrz9J/VEhQ5g=="}, "System.Buffers": {"type": "Transitive", "resolved": "4.5.1", "contentHash": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg=="}, "System.ClientModel": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "4.7.2"}}, "System.CodeDom": {"type": "Transitive", "resolved": "4.4.0", "contentHash": "2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA=="}, "System.Collections": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable": {"type": "Transitive", "resolved": "1.7.0", "contentHash": "RVSM6wZUo6L2y6P3vN6gjUtyJ2IF2RVtrepF3J7nrDKfFQd5u/SnSUFclchYQis8/k5scHy9E+fVeKVQLnnkzw=="}, "System.Configuration.ConfigurationManager": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "dependencies": {"System.Diagnostics.EventLog": "8.0.0", "System.Security.Cryptography.ProtectedData": "8.0.0"}}, "System.Diagnostics.Debug": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ=="}, "System.Diagnostics.EventLog": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A=="}, "System.Diagnostics.PerformanceCounter": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "gbeE5tNp/oB7O8kTTLh3wPPJCxpNOphXPTWVs1BsYuFOYapFijWuh0LYw1qnDo4gwDUYPXOmpTIhvtxisGsYOQ==", "dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}}, "System.Diagnostics.Tracing": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Drawing.Common": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "x0rAZECxIGx/YVjN28YRdpqka0+H7YMN9741FUDzipXPDzesd60gef/LI0ZCOcYSDsacTLTHvMAvxHG+TjbNNQ==", "dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}}, "System.Globalization": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt": {"type": "Transitive", "resolved": "7.1.2", "contentHash": "Thhbe1peAmtSBFaV/ohtykXiZSOkx59Da44hvtWfIMFofDA3M3LaVyjstACf2rKGn4dEDR2cUpRAZ0Xs/zB+7Q==", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}}, "System.IO": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.AccessControl": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "vMToiarpU81LR1/KZtnT7VDPvqAZfw9oOS5nY6pPP78nGYz3COLsQH3OfzbR+SjTgltd31R6KmKklz/zDpTmzw==", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.IO.FileSystem.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Packaging": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "8g1V4YRpdGAxFcK8v9OjuMdIOJSpF30Zb1JGicwVZhly3I994WFyBdV6mQEo8d3T+URQe55/M0U0eIH0Hts1bg=="}, "System.IO.Pipelines": {"type": "Transitive", "resolved": "5.0.1", "contentHash": "qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg=="}, "System.Linq": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Memory": {"type": "Transitive", "resolved": "4.5.5", "contentHash": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw=="}, "System.Memory.Data": {"type": "Transitive", "resolved": "1.0.2", "contentHash": "JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}}, "System.Net.Http": {"type": "Transitive", "resolved": "4.3.4", "contentHash": "aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.NameResolution": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "AFYl08R7MrsrEjqpQWTZWBadqXyTzNDaWpMqyxhb0d6sGhV6xMDKueuBXlLL30gz+DIRY6MpdgnHWlCh5wmq9w==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Net.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Numerics.Vectors": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ=="}, "System.Reflection": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata": {"type": "Transitive", "resolved": "1.6.0", "contentHash": "COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ=="}, "System.Reflection.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe": {"type": "Transitive", "resolved": "4.6.0", "contentHash": "HxozeSlipUK7dAroTYwIcGwKDeOVpQnJlpVaOkBz7CM4TsE5b/tKlQBZecTjh6FzcSbxndYaxxpsBMz+wMJeyw=="}, "System.Runtime.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.Numerics": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Algorithms": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg=="}, "System.Security.Cryptography.X509Certificates": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Principal.Windows": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA=="}, "System.Text.Encoding": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}}, "System.Text.Encodings.Web": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ=="}, "System.Text.Json": {"type": "Transitive", "resolved": "8.0.2", "contentHash": "29KA2StjWDYp32FvREifawRtNpTziLE1xyuDV9pVQ+MsaE9bIcIieP0io/eZZeLMxR+Nx9zI55RtUtpVpEIdeg==", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions": {"type": "Transitive", "resolved": "4.5.4", "contentHash": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg=="}, "System.ValueTuple": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ=="}, "TimeZoneConverter": {"type": "Transitive", "resolved": "6.1.0", "contentHash": "UGdtyKWJqXXinyvGB9X6NVoIYbTAidoZYmn3aXzxeEYC9+OL8vF36eDt1qjb6RqBkWDl4v7iE84ecI+dFhA80A=="}, "ZiggyCreatures.FusionCache": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "+FdV9nzhR9++gwdIJZDFRYGS5NrLXM8f/Zf33VO6Ewr3LYvoKraE+xXEsfvIvZYcvhCTytZJJb4jUtp6ZvBLaw==", "dependencies": {"Microsoft.Extensions.Caching.Memory": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "ZiggyCreatures.FusionCache.Backplane.StackExchangeRedis": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "VtXG7ayOIgjEmAQz6WrWeE8wtsSQBA3QN6vjJD20m/lQ+C8l1QTRuIeqLNV54yWVM7N3LacLp3wSXh2Wx4Gw0w==", "dependencies": {"StackExchange.Redis": "2.7.27", "System.Memory": "4.5.5", "ZiggyCreatures.FusionCache": "1.0.0"}}, "ZiggyCreatures.FusionCache.Serialization.SystemTextJson": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "fixQTZZbTQta4x2qhgM3nu0DYpzhmrT+jGTWYAlysgX5W8NxdBRg1Vsa5DC24HbPVLv0DvIRHi/4F8EEHbqemQ==", "dependencies": {"System.Text.Json": "8.0.2", "ZiggyCreatures.FusionCache": "1.0.0"}}, "dc.bot.repositories": {"type": "Project", "dependencies": {"DC.Customers.Client": "[3.0.********.4, )", "DC.Domain.Exceptions": "[1.2.1229175, )", "DC.Financials.Client": "[3.0.********.1, )", "DC.Products.Client": "[3.0.********.4, )", "DC.Repositories.Base": "[1.2.1229175, )", "DC.Storage.Client": "[3.0.********.2, )", "DC.Telemetry": "[1.2.1229175, )", "DC.Usages.Client": "[3.0.********.3, )", "DC.UserAccounts.Client": "[3.0.********.1, )", "Microsoft.Extensions.Hosting.Abstractions": "[8.0.0, )", "Microsoft.Rest.ClientRuntime": "[2.3.24, )"}}, "DC.Api.Base": {"type": "CentralTransitive", "requested": "[1.2.1229175, )", "resolved": "1.2.1229175", "contentHash": "L0Gqyj0rEAt03b4aDYu39uWBapD5FsXoMHS3rLsi7rMreG4t++70sRlES/9j6npPJAUU5ZP9I27BM59skol+5g==", "dependencies": {"Azure.Core": "1.38.0", "Azure.Identity": "1.11.0", "Azure.Security.KeyVault.Secrets": "4.5.0", "DC.BusinessLogic.Base": "1.2.1229175", "DC.Domain.Exceptions": "1.2.1229175", "DC.Repositories.Base": "1.2.1229175", "DC.Security.MutualSsl": "1.2.1229175", "DC.Telemetry": "1.2.1229175", "DC.Utilities": "1.2.1229175", "DnsClient": "1.7.0", "DocumentFormat.OpenXml": "3.0.1", "IdentityModel": "6.2.0", "Microsoft.ApplicationInsights": "2.22.0", "Microsoft.ApplicationInsights.AspNetCore": "2.22.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.16", "Microsoft.Azure.AppConfiguration.AspNetCore": "7.0.0", "Microsoft.EntityFrameworkCore": "8.0.1", "Microsoft.EntityFrameworkCore.Cosmos": "8.0.1", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Caching.StackExchangeRedis": "8.0.2", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.DiagnosticAdapter": "3.1.32", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Identity.Core": "8.0.1", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.ApplicationInsights": "2.22.0", "Microsoft.Extensions.Logging.AzureAppServices": "8.0.1", "Microsoft.FeatureManagement": "3.1.1", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.3", "Swashbuckle.AspNetCore": "6.2.3", "Swashbuckle.AspNetCore.Newtonsoft": "6.2.3", "System.Drawing.Common": "8.0.1", "ZiggyCreatures.FusionCache": "1.0.0", "ZiggyCreatures.FusionCache.Backplane.StackExchangeRedis": "1.0.0", "ZiggyCreatures.FusionCache.Serialization.SystemTextJson": "1.0.0"}}, "DC.Customers.Client": {"type": "CentralTransitive", "requested": "[3.0.********.4, )", "resolved": "3.0.********.4", "contentHash": "SMLcXp0Jesms9MTXGE5FiSQ3HzrbTO+GukxZE9EjO66hlt/+ml4w/6J2pN0+K8xAZ2PDDyy6zxk0sgQ0aqceMQ==", "dependencies": {"DC.Domain.Models": "1.2.1226754", "DC.Repositories.Base": "1.2.1226754", "Microsoft.Rest.ClientRuntime": "2.3.24"}}, "DC.Domain.Exceptions": {"type": "CentralTransitive", "requested": "[1.2.1229175, )", "resolved": "1.2.1229175", "contentHash": "KrUdFh9ir28aKVFnu6DBcV9EP9+lhZvnC3J0XYKcZfEPSm0mtR+/qa8ppRXmMC0JAEy/+v+YhMgP7ASJ2jyMsg==", "dependencies": {"Azure.Core": "1.38.0", "Azure.Identity": "1.11.0", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.3"}}, "DC.Financials.Client": {"type": "CentralTransitive", "requested": "[3.0.********.1, )", "resolved": "3.0.********.1", "contentHash": "Dl/Iwv2paNXh+bBK0dxMiUkwigNvMuvNsA4NckVD6HrAVOthGt84YT6NEkM5lwPTvunyD11FkteSl20CAnkbfQ==", "dependencies": {"DC.Api.Base": "1.2.1226754", "DC.Domain.Models": "1.2.1226754", "DC.Repositories.Base": "1.2.1226754", "Microsoft.Rest.ClientRuntime": "2.3.24"}}, "DC.Products.Client": {"type": "CentralTransitive", "requested": "[3.0.********.4, )", "resolved": "3.0.********.4", "contentHash": "LneL6VyWaawtgmI5HRxlOmHs4GEs8hV3zRct+/V6qoAVq0PLhljJsRoLS6DT6UrwUfqRnQL6plojcl9VuTyAJA==", "dependencies": {"DC.Domain.Models": "1.2.1226754", "DC.Repositories.Base": "1.2.1226754", "Microsoft.Rest.ClientRuntime": "2.3.24"}}, "DC.Repositories.Base": {"type": "CentralTransitive", "requested": "[1.2.1229175, )", "resolved": "1.2.1229175", "contentHash": "3cgPROGKno+4Qf5vPkvafkaPf9sUlrdBt3XmTt0CJUfu/dPXesjuUplcnSRmduao7DVSHA7f9X5zfWLBkdf0rg==", "dependencies": {"Azure.Core": "1.38.0", "Azure.Identity": "1.11.0", "DC.Domain.Models": "1.2.1229175", "DC.Security.ActiveDirectory": "1.2.1229175", "DC.Security.MutualSsl": "1.2.1229175", "DnsClient": "1.7.0", "DocumentFormat.OpenXml": "3.0.1", "Microsoft.ApplicationInsights": "2.22.0", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Caching.StackExchangeRedis": "8.0.2", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.3", "System.Drawing.Common": "8.0.1"}}, "DC.Storage.Client": {"type": "CentralTransitive", "requested": "[3.0.********.2, )", "resolved": "3.0.********.2", "contentHash": "mgxH17XiQGo4YZq1NGvby0UB/0SjVne0isnHG06X+Bsbi0VAtQR3EFEuZLJZ0wBSST/zw09nY3pSVzgA8bLStw==", "dependencies": {"DC.BusinessLogic.Base": "1.2.1226754", "DC.Domain.Models": "1.2.1226754", "DC.Repositories.Base": "1.2.1226754", "Microsoft.Rest.ClientRuntime": "2.3.24"}}, "DC.Usages.Client": {"type": "CentralTransitive", "requested": "[3.0.********.3, )", "resolved": "3.0.********.3", "contentHash": "Z6jpIApgUxmixr0jmAqfLMVjbt+USBbbVqu7HD6YQN/wtg5aF5LF+268y/R/DEnegNg5GFHFQYwtmlgPEhXg4w==", "dependencies": {"DC.Domain.Models": "1.2.1226754", "DC.Repositories.Base": "1.2.1226754", "Microsoft.Rest.ClientRuntime": "2.3.24"}}, "DC.UserAccounts.Client": {"type": "CentralTransitive", "requested": "[3.0.********.1, )", "resolved": "3.0.********.1", "contentHash": "L1rQK5c4YHYYMc/2hR50hGWE+Dx+k9rjJYCSvAwhNQXTCteIjtDkdAjveBQuIn5y57zMDISn79AnoYcsm5x4UA==", "dependencies": {"DC.Domain.Models": "1.2.1226754", "DC.Repositories.Base": "1.2.1226754", "Microsoft.Rest.ClientRuntime": "2.3.24"}}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "CentralTransitive", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Rest.ClientRuntime": {"type": "CentralTransitive", "requested": "[2.3.24, )", "resolved": "2.3.24", "contentHash": "hZH7XgM3eV2jFrnq7Yf0nBD4WVXQzDrer2gEY7HMNiwio2hwDsTHO6LWuueNQAfRpNp4W7mKxcXpwXUiuVIlYw==", "dependencies": {"Newtonsoft.Json": "10.0.3"}}}}}