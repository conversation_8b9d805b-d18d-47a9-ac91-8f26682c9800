﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Extensions.Logging;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.Auth;
using DC.Bot.BusinessLogic.Models.NextBestAction;
using DC.Customers.Client.Models;
using DC.Domain.Exceptions;
using DC.Repositories.Base.Enumerations;
using DC.Utilities.Formatters;
using Microsoft.ApplicationInsights;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Schema;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ActivityExtensions = DC.Bot.BusinessLogic.Extensions.ActivityExtensions;

namespace DC.Bot.BusinessLogic.Dialogs;

/// <summary>
/// Manager that manages the session and logging of telemetry and traces
/// </summary>
public class SessionManager : ISessionManager
{
    /// <summary>
    /// Session data accessor
    /// </summary>
    private readonly IStatePropertyAccessor<ConversationData> _conversationDataAccessor;

    /// <summary>
    /// Custom event AI client for all derived dialogs
    /// </summary>
    protected readonly TelemetryClient _telemetryClient;

    /// <summary>
    /// Configuration
    /// </summary>
    protected readonly IConfiguration _configuration;

    /// <summary>
    /// Logger
    /// </summary>
    protected readonly ILogger _logger;

    /// <summary>
    /// Open ID configuration manager (for OKTA)
    /// </summary>
    protected IConfigurationManager<OpenIdConnectConfiguration> _openIdConfigurationManager;

    /// <summary>
    /// The customers service
    /// </summary>
    private readonly ICustomersService _customersService;

    private readonly INextBestActionService _nbaService;

    /// <summary>
    /// Constructor
    /// </summary>
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Major Code Smell", "S107:Methods should not have too many parameters", Justification = "<Pending>")]
    public SessionManager(
        IStatePropertyAccessor<ConversationData> conversationDataAccessor,
        TelemetryClient telemetryClient,
        IConfiguration configuration,
        ILoggerFactory loggerFactory,
        IConfigurationManager<OpenIdConnectConfiguration> openIdConfigurationManager,
        ICustomersService customersService,
        INextBestActionService nbaService)
    {
        _logger = loggerFactory.CreateLogger(GetType().Name);
        _conversationDataAccessor = conversationDataAccessor;
        _telemetryClient = telemetryClient;
        _configuration = configuration;
        _openIdConfigurationManager = openIdConfigurationManager;
        _customersService = customersService;
        _nbaService = nbaService;
    }

    /// <summary>
    /// Returns the conversation data object
    /// </summary>
    private async Task<ConversationData> GetConversationData(ITurnContext context)
    {
        return await _conversationDataAccessor.GetAsync(context, () => new ConversationData
        {
            DialogData = new DialogData(),
            CurrentDialogAction = DialogAction.None
        }).ConfigureAwait(false);
    }

    /// <summary>
    /// Returns the user data inside the conversation data
    /// </summary>
    public async Task<DialogData> GetDialogData(ITurnContext context)
    {
        var conversationData = await GetConversationData(context).ConfigureAwait(false);
        return conversationData.DialogData;
    }

    /// <summary>
    /// SetCurrentDialogAction
    /// </summary>
    public async Task SetCurrentDialogAction(ITurnContext context, DialogAction action)
    {
        var conversationData = await GetConversationData(context).ConfigureAwait(false);
        conversationData.CurrentDialogAction = action;
    }

    /// <summary>
    /// Resets the conversation data
    /// </summary>
    public async Task ResetConversationData(ITurnContext context)
    {
        await _conversationDataAccessor.DeleteAsync(context).ConfigureAwait(false);
    }

    /// <summary>
    /// Sends an end of transaction activity to the client.
    /// </summary>
    public async Task SendEndOfTransactionActivity(ITurnContext context, string dialog, string dialogStep, TransactionStatus status, CancellationToken cancellationToken = default)
    {
        var dialogData = await GetDialogData(context).ConfigureAwait(false);

        // reset CurrentTransactionIsNba status
        if (dialogData.NextBestAction.CurrentTransactionIsNba)
        {
            dialogData.NextBestAction.CurrentTransactionIsNba = false;

            // In case the transaction has been closed with success or dismissed, delete the current data.
            if (status == TransactionStatus.Success || status == TransactionStatus.NextBestActionDismissed)
                dialogData.NextBestAction.Data = null;

            var customer = await _customersService.GetCustomer(dialogData).ConfigureAwait(false);

            // Always send an CustomerInfoEvent to CX containing the personalisationInfo and NBA available false. Otherwise after ending the dialog the available property remains true at CX side causing a unnecessary second $NBA request. 
            await SendCustomerInfoEvent(context, await _customersService.GetPersonalisationInfo(dialogData, customer).ConfigureAwait(false), new NextBestActionInfo { Available = false, Type = null }).ConfigureAwait(false);
        }

        dialogData.TransactionStatus = status;

        var conversationData = await GetConversationData(context).ConfigureAwait(false);

        var endOfTransaction = Activity.CreateEventActivity();
        endOfTransaction.Name = "endOfTransaction";
        endOfTransaction.Value = new EndOfTransactionValue
        {
            Status = status,
            Result = conversationData.CurrentDialogAction
        };
        _ = await context.SendActivityAsync(endOfTransaction, cancellationToken).ConfigureAwait(false);
        await LogEvent(context, dialog, dialogStep, status, completeDialogData: true).ConfigureAwait(false);
    }

    /// <summary>
    /// Logs a custom event in AI
    /// </summary>
    public async Task LogEvent(ITurnContext context, string dialog, string dialogStep, TransactionStatus status, string message = null, bool completeDialogData = false)
    {
        var conversationData = await GetConversationData(context).ConfigureAwait(false);
        _telemetryClient.TrackEvent($"Action_{conversationData.CurrentDialogAction}", conversationData.ToCustomEventProperties(dialog, dialogStep, status, context, message, completeDialogData));
    }


    /// <summary>
    /// Sends an encrypted customer info event
    /// </summary>
    public async Task SendCustomerInfoEvent(ITurnContext context, PersonalisationInfo personalisationInfo, NextBestActionInfo nextBestActionInfo)
    {
        var dialogData = await GetDialogData(context).ConfigureAwait(false);
        var customerInfo =
            new CustomerInfo
            {
                Name = dialogData.Customer?.Name,
                TokenIsValid = dialogData.Verification?.IsVerifiedWithToken,
                Personalisation = personalisationInfo,
                NextBestAction = nextBestActionInfo
            };
        _ = await context.SendActivityAsync(customerInfo.CreateEncryptedEvent(_configuration, true)).ConfigureAwait(false);
    }

    /// <summary>
    /// Sends an encrypted solar products information message with attachment
    /// </summary>
    public async Task SendCustomMessageWithAttachment<T>(ITurnContext context, T objectToSend, string type = null)
    {
        _ = await context.SendActivityAsync(objectToSend.CreateCustomMessageAttachment(type)).ConfigureAwait(false);
    }

    /// <summary>
    /// Send a message expecting an attachment
    /// </summary>
    public async Task SendMessageWithExpectedAttachmentSend(ITurnContext context, string message)
    {
        _ = await context.SendActivityAsync(ActivityExtensions.CreateMessageWithExpectedAttachment(message)).ConfigureAwait(false);
    }

    /// <summary>
    /// Handles the Authorization property of a TransactionStartValue object
    /// </summary>
    public async Task HandleAuthorization(ITurnContext context, TransactionStartValue transactionStartValue, CancellationToken cancellationToken = default)
    {
        var dialogData = await GetDialogData(context).ConfigureAwait(false);
        bool validated = false;

        // Just checks the Customer Id
        long customerId = transactionStartValue.CustomerId.GetValueOrDefault(0);

        // only proceed if the Customer Id is validate and the customerId claim was found in the TransactionStartValue
        if (customerId == 0)
            return;

        // set all verification flags in the userdata object
        dialogData.Verification.CustomerId = customerId;
        dialogData.Verification.CustomerIdVerified = customerId > 0;
        dialogData.Verification.IsFound = customerId > 0;
        dialogData.Verification.IsVerified = customerId > 0;
        dialogData.Verification.VerificationCommunicated ??= false;    // only set this if not set before
        dialogData.Verification.IsVerifiedWithToken = validated;
        dialogData.Verification.MfaSuccessful = true; // we don't have to do MFA for customers validated with a token

        if (customerId <= 0)
        {
            return;
        }
        var customer = await GetCustomerContext(dialogData).ConfigureAwait(false);
        if (customer != null)
        {
            var account = GetCustomerAccountContext(customer);
            if (account != null)
            {
                dialogData.Verification.PostalCode = account.Address?.PostalCode;
                dialogData.Verification.HouseNumber = account.Address?.HouseNumber;
                dialogData.Verification.HouseNumberSuffix = account.Address?.HouseNumberSuffix;


                dialogData.SetAllActiveAccountsFromCustomerModel(customer);
                dialogData.SetSelectedAccountFromDialogData(account.Id);
            }
        }
        else
        {
            dialogData.Verification.CustomerId = null;
            dialogData.Verification.CustomerIdVerified = false;
            dialogData.Verification.IsFound = false;
            dialogData.Verification.IsVerified = false;
        }
    }

    /// <summary>
    /// Validates an okta token and returns the customerId claim if exists
    /// </summary>
    private async Task<OktaValidationToken> ValidateToken(Label label, string token, CancellationToken cancellationToken = default)
    {
        // Make sure the Bearer part if the token is excluded.
        if (token.StartsWith("Bearer"))
        {
            token = token.Replace("Bearer", "").RemoveWhitespaces();
        }

        var validIssuer = _configuration[$"Authorization:{label}:OktaIssuer"];
        //always initiliaze new openid configuartion manager because of different label settings. 
        _openIdConfigurationManager = new ConfigurationManager<OpenIdConnectConfiguration>(
            validIssuer + "/.well-known/oauth-authorization-server",
            new OpenIdConnectConfigurationRetriever(),
            new HttpDocumentRetriever());

        // retrieve actual okta signing config
        var discoveryDocument = await _openIdConfigurationManager.GetConfigurationAsync(cancellationToken).ConfigureAwait(false);
        var signingKeys = discoveryDocument.SigningKeys;

        try
        {
            // try to validate token
            var validationParameters = new TokenValidationParameters
            {
                RequireExpirationTime = true,
                RequireSignedTokens = true,
                ValidateIssuer = true,
                ValidIssuer = validIssuer,
                ValidateIssuerSigningKey = true,
                IssuerSigningKeys = signingKeys,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.FromMinutes(2),
                ValidateAudience = false
            };
            var principal = new JwtSecurityTokenHandler()
                .ValidateToken(token, validationParameters, out SecurityToken _);

            // get claim "customerId" from the token
            if (principal.HasClaim(c => c.Type == "customerId") &&
                long.TryParse(principal.FindFirst(c => c.Type == "customerId")?.Value, out long customerId))
            {
                return new OktaValidationToken { Validated = true, CustomerId = customerId };
            }
            // validated but not containing a (parsable) customerId claim

            _logger.LogInformation("{ValidateTokenName} customer not found for the label {Label}",
                nameof(ValidateToken), label);
            return new OktaValidationToken { Validated = true, CustomerId = null };
        }
        // token not valid
        catch (SecurityTokenValidationException ex)
        {
            _logger.LogError(ex,
                "{ValidateTokenName} exception for the label {Label} with the following error message: {Message}",
                nameof(ValidateToken), label, ex.Message);
            return new OktaValidationToken { Validated = false, CustomerId = null };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "{ValidateTokenName} exception for the label {Label} with the following error message: {Message}",
                nameof(ValidateToken), label, ex.Message);
            return new OktaValidationToken { Validated = false, CustomerId = null };
        }
    }

    public async Task<OktaValidationToken> ValidateOktaToken(Label label, string token)
    {
        var result = await ValidateToken(label, token).ConfigureAwait(false);
        return result;
    }

    public async Task<(PersonalisationInfo personalisation, NextBestActionInfo nba, CustomerModel customer)> GetSeamlyCustomerInfo(DialogData dialogData)
    {
        var customer = await this.GetCustomerContext(dialogData).ConfigureAwait(false);
        if (customer == null)
            return (null, null, null);

        var nbaTask = _nbaService.GetNextBestActions(dialogData);
        var personalisationInfoTask = _customersService.GetPersonalisationInfo(dialogData, customer);
        await Task.WhenAll(nbaTask, personalisationInfoTask).ConfigureAwait(false);
        var nba = await nbaTask.ConfigureAwait(false);
        var personalisation = await personalisationInfoTask.ConfigureAwait(false);
        return (personalisation, nba.ToNextBestActionInfo(), customer);
    }

    private async Task<CustomerModel> GetCustomerContext(DialogData dialogData)
    {
        try
        {
            return await _customersService.GetCustomer(dialogData).ConfigureAwait(false);
        }
        catch (DigitalCoreException)
        {
            return null;
        }
    }

    private static CustomerAccountModel GetCustomerAccountContext(CustomerModel customer)
    {
        return customer.Accounts?.FirstOrDefault(a => a.Active.GetValueOrDefault(false)
                                                                 && !string.IsNullOrEmpty(a.Address?.PostalCode)
                                                                 && a.Address?.HouseNumber.HasValue == true);
    }
}
