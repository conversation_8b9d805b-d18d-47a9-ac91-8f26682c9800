﻿using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Interfaces;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Integration.AspNet.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net.Http;

namespace DC.Bot.BusinessLogic.Adapters
{
    /// <summary>
    /// 
    /// </summary>
#pragma warning disable CS0618 // Type or member is obsolete
    public class AdapterWithErrorHandler : CloudAdapter
#pragma warning restore CS0618 // Type or member is obsolete
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="configuration"></param>
        /// <param name="logger"></param>
        public AdapterWithErrorHandler(
            IConfiguration configuration,
            IHttpClientFactory httpClient,
            IMiddleware middleware,
            ILoggerFactory loggerFactory,
            ISessionManager sessionManager)
#pragma warning disable CS0618 // Type or member is obsolete
            : base(configuration, httpClient, loggerFactory.CreateLogger<CloudAdapter>())
#pragma warning restore CS0618 // Type or member is obsolete
        {
            Use(middleware);
            OnTurnError = async (turnContext, exception) =>
            {
                // Log any leaked exception from the application.
                Logger.LogError(exception, $"[OnTurnError] unhandled error : {exception.Message}");

                await sessionManager.SendEndOfTransactionActivity(turnContext, null, null, TransactionStatus.Error).ConfigureAwait(false);
            };
        }
    }
}
