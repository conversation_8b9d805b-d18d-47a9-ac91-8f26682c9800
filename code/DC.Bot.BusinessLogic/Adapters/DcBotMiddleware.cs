﻿using DC.Bot.BusinessLogic.Extensions.Logging;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Schema;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.ApplicationInsights.DataContracts;
using IMiddleware = Microsoft.Bot.Builder.IMiddleware;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Enumerations;
using DC.Bot.BusinessLogic.Constants;

namespace DC.Bot.BusinessLogic.Adapters
{
    public class DcBotMiddleware : IMiddleware
    {
        private readonly ILogger<DcBotMiddleware> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IStatePropertyAccessor<ConversationData> _conversationDataAccessor;

        public DcBotMiddleware(ILoggerFactory loggerfactory, IHttpContextAccessor httpContextAccessor, IStatePropertyAccessor<ConversationData> conversationDataAccessor)
        {
            _logger = loggerfactory.CreateLogger<DcBotMiddleware>();
            _httpContextAccessor = httpContextAccessor;
            _conversationDataAccessor = conversationDataAccessor;
        }

        /// <summary>
        /// Returns the conversation data object
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        private async Task<ConversationData> GetConversationData(ITurnContext context)
        {
            return await _conversationDataAccessor.GetAsync(context, () =>
            {
                return new ConversationData
                {
                    DialogData = new DialogData(),
                    CurrentDialogAction = DialogAction.None
                };
            }).ConfigureAwait(false);
        }

        public async Task OnTurnAsync(ITurnContext turnContext, NextDelegate next, CancellationToken cancellationToken = default)
        {
            try
            {
                // Log incoming activity
                await LogIncomingActivity(turnContext).ConfigureAwait(false);

                // Add handler to log the outgoing activities too for current request
                turnContext.OnSendActivities(LogOutgoingActivities);

                // call next step in the middleware pipeline
                await next(cancellationToken).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                // This should never happen because it means the bot has completely crashed and is unable to continue the conversation.
                // However if it does happen, at least we will the exception Logged
                _logger.LogError(ex, ex.Message);
                throw;
            }
        }


        /// <summary>
        /// Logs an incoming activity, with logging scope to add customDimensions
        /// </summary>
        /// <param name="activity"></param>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Minor Code Smell", "S2221:\"Exception\" should not be caught when not required by called methods", Justification = "Exception needs to catch everything.")]
        private async Task LogIncomingActivity(ITurnContext turnContext)
        {
            try
            {
                var conversationData = await GetConversationData(turnContext).ConfigureAwait(false);
                using (_logger.BeginScope(turnContext.Activity.CreateLoggingScope(conversationData)))
                {
                    AddExtraTelemetryToRequests(turnContext, conversationData);
                    _logger.LogInformation("Incoming {ActivityType} activity for conversation {ActivityConversationId}.", turnContext.Activity.Type, turnContext.Activity.Conversation.Id);
                }
            }
            // logging may never break the application
            catch (Exception ex)
            {
                _logger.LogError(ex, $"An error occured during {nameof(LogIncomingActivity)}. Activity type {turnContext?.Activity?.Type}.");
            }
        }


        /// <summary>
        /// Logs all outgoing activities in current request, with logging scope to add customDimensions
        /// </summary>
        /// <param name="turnContext"></param>
        /// <param name="activities"></param>
        /// <param name="next"></param>
        /// <returns></returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Minor Code Smell", "S2221:\"Exception\" should not be caught when not required by called methods", Justification = "Exception needs to catch everything.")]
        private async Task<ResourceResponse[]> LogOutgoingActivities(ITurnContext turnContext, List<Activity> activities, Func<Task<ResourceResponse[]>> next)
        {
            var conversationData = await GetConversationData(turnContext).ConfigureAwait(false);
            foreach (var activity in activities)
            {
                try
                {
                    using (_logger.BeginScope(activity.CreateLoggingScope(conversationData)))
                    {
                        AddExtraTelemetryToRequests(turnContext, conversationData);
                        _logger.LogInformation($"Outgoing {activity.Type} activity for conversation {activity.Conversation?.Id}.");
                    }
                }
                // logging may never break the application
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"An error occured during {nameof(LogOutgoingActivities)}. Activity type {activity.Type}.");
                }
            }

            return await next().ConfigureAwait(false);
        }

        private void AddExtraTelemetryToRequests(ITurnContext turnContext, ConversationData conversationData)
        {
            var telemetry = _httpContextAccessor.HttpContext.Features.Get<RequestTelemetry>();

            if (!telemetry.Properties.ContainsKey(LoggingTags.ConversationId) && !string.IsNullOrWhiteSpace(turnContext?.Activity?.Conversation?.Id))
                telemetry.Properties.Add(LoggingTags.ConversationId, turnContext.Activity?.Conversation?.Id);

            if (!telemetry.Properties.ContainsKey(LoggingTags.CustomerId) 
                && conversationData?.DialogData?.Verification != null 
                && conversationData.DialogData.Verification.CustomerIdVerified == true
                && conversationData.DialogData.Verification.CustomerId != null)
                telemetry.Properties.Add(LoggingTags.CustomerId, conversationData.DialogData.Verification.CustomerId.ToString());

            if (conversationData?.CurrentDialogAction != null)
            {
                if (!telemetry.Properties.ContainsKey(LoggingTags.Action))
                    telemetry.Properties.Add(LoggingTags.Action, string.Empty);

                telemetry.Properties[LoggingTags.Action] = conversationData.CurrentDialogAction.ToString();
            }
        }
    }
}
