﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ProduceReferenceAssembly>false</ProduceReferenceAssembly>
  </PropertyGroup>
  <ItemGroup>
    <AdditionalFiles Include="..\.sonarlint\digital.dc.xapi.bot\CSharp\SonarLint.xml" Link="SonarLint.xml" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="DC.BusinessLogic.Base" />
    <PackageReference Include="DC.SAPI" />
    <PackageReference Include="DC.Security.Encryption" />
    <PackageReference Include="DC.Telemetry" />
    <PackageReference Include="DC.Utilities" />
    <PackageReference Include="JWT" />
    <PackageReference Include="Microsoft.Bot.Builder" />
    <PackageReference Include="Microsoft.Bot.Builder.Dialogs" />
    <PackageReference Include="Microsoft.Bot.Builder.Integration.AspNet.Core" />
    <PackageReference Include="Microsoft.TestPlatform.TestHost" />
    <PackageReference Include="MimeTypeMapOfficial" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DC.Bot.Repositories\DC.Bot.Repositories.csproj" />
  </ItemGroup>
</Project>