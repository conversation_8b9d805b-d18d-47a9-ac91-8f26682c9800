

Adaptive Cards how to:
======================

De adaptive cards in het bot framework bieden extra visualisatie mogelijkheden voor in de bot die out of the box supported zijn door de bot emulator.
Dit zorgt ervoor dat er visueel een zogeheten card gerenderd wordt die qua styling en validatie in de code aangemaakt kan worden. 

Een voorbeeld hiervan zou de meterstanden dialoog zijn waarvoor we per meter de telwerken in een card met de daarbij behorende telwerk limits kunnen weergeven. 
De max tekens per telwerk wordt dan direct toegepast en ook zijn er links te plaatsen naar hulp pagina's.
Vervolgens word na het posten direct ook een json object teruggegeven met daarin een lijst van alle velden met ID en de daarbij behorende values.

Benodigdheden
-------------

Om dit mogelijk te maken mode de AdaptiveCards package vanuit Nuget geinstalleerd worden.
Vervolgens kan er een card gedefineerd worden in de code. Zie onderstaand voorbeeld:

            AdaptiveCard card;
            card = new AdaptiveCard(new AdaptiveSchemaVersion(1, 0))
            {
                Body = new List<AdaptiveElement>(),
                // Use LINQ to turn the choices into submit actions
                Actions = new List<AdaptiveAction>
                {
                    new AdaptiveSubmitAction
                    {
                        Title = "Meterstanden bevestigen"
                    },
                    new AdaptiveSubmitAction
                    {
                        Title = "Hulp nodig",
                        Data = "Hulp nodig",
                        AssociatedInputs = AdaptiveAssociatedInputs.None
                    }
                }
            };
Hierin zijn de actions de knoppen. Zodra er geen "Data" meegegeven wordt zal de card alle invulbare content van de card per ID doorsturen naar de volgende stap. Bij het doorgeven van een "Data" waarde kan dit als bericht worden ontvangen om zo te beslissen dat een andere stap genomen moet worden.

Vervolgens is de content van de card te vullen door bij de Body nieuwe elementen toe te voegen. Als voorbeeld is te nemen: 

	        card.Body.Add(
                new AdaptiveContainer
                {
                    Style = AdaptiveContainerStyle.Emphasis,
                    Bleed = true,
                    Items = new List<AdaptiveElement>{new AdaptiveTextBlock
                    {
                        Text = meterType.FirstLetterToUpper(),
                        Type = "TextBlock",
                        Size = AdaptiveTextSize.ExtraLarge,
                        Weight = AdaptiveTextWeight.Bolder,
                        HorizontalAlignment = AdaptiveHorizontalAlignment.Center
                    }}
                });

            // Bekijk hier hoe je de <metertype>meter uitleest
            string seeHowToReadOutMeterLink = TextLabels.GetValue("SeeHowToReadOutMeterLink").Replace("{metertype}", meterType);
            // Je vorige stand voor <metertype>:
            string yourPreviousReadingForMeter = TextLabels.GetValue("YourPreviousReadingForMeter").Replace("{metertype}", meterType);

            card.Body.Add(new AdaptiveTextBlock
            {
                Text = $"[{seeHowToReadOutMeterLink}](https://eneco.com/)",
                Type = "TextBlock",
                Size = AdaptiveTextSize.Small,
                IsSubtle = true,
                HorizontalAlignment = AdaptiveHorizontalAlignment.Right
            });

            card.Body.Add(new AdaptiveTextBlock
            {
                Text = yourPreviousReadingForMeter,
                Type = "TextBlock",
                Size = AdaptiveTextSize.Default
            });

Hierin is terug te zien dat ook markdown gebruikt kan worden om links toe te voegen in de tekstblokken. Voor meer informatie over de velden die toegevoegd kunnen worden is https://adaptivecards.io/ een goede infobron en zijn de cards die je maakt goed te testen in de editor https://adaptivecards.io/visualizer/index.html?hostApp=Bot%20Framework%20WebChat .

Let wel op dat bij de editor je eerst de json moet exporteren uit de bot emulator aangezien je de card voor de flexibiliteit in de code wilt schrijven.

Uiteindelijk zijn de tekstvelden toe te voegen op de volgende manier:

	string counterString = null;
            int counterId = 1;
            List<AdaptiveTextBlock> counterNames = new List<AdaptiveTextBlock>();
            List<AdaptiveTextInput> counterInputFields = new List<AdaptiveTextInput>();
            foreach (var counter in meterModel.Counters)
            {
                string counterNumber = meterModel.Counters.Count > 1 ? $" {counterId}" : null;
                counterString += $"**{meterType}{counterNumber}**: {counter.Value} {counter.Denotation.ToEnumMemberValue()}{(counterNumber != null ? "\r\n" : null)}";

                counterInputFields.Add(new AdaptiveTextInput
                {
                    Id = $"{meterType}Counter{counterId}-{Guid.NewGuid()}",
                    Placeholder = $"{counter.Value}",
                    MaxLength = counter.Positions,
                    Type = "Input.Text"
                });
                counterNames.Add(new AdaptiveTextBlock($"{meterType}{counterNumber}: "));

                counterId++;
            }

            // Add text showing previous counter values
            card.Body.Add(new AdaptiveTextBlock
            {
                Text = counterString,
                Type = "TextBlock",
                Separator = true
            });

            // Add the names and textboxes for all available counters
            var col1 = new AdaptiveColumn { Width = "auto" };
            col1.Items.AddRange(counterNames);
            var col2 = new AdaptiveColumn { Width = "stretch" };
            col2.Items.AddRange(counterInputFields);

            card.Body.Add(new AdaptiveTextBlock
            {
                Text = "Vul hieronder je meterstanden in:",
                Separator = true,
                Spacing = AdaptiveSpacing.Large,
                Type = "TextBlock"
            });
            card.Body.Add(new AdaptiveColumnSet { Columns = new List<AdaptiveColumn> { col1, col2 } });

Hierbij is belangrijk dat elk veld wat je terug wilt ontvangen en wilt verwerken een uniek ID heeft zodat bij het opnieuw genereren van een card met dezelfde structuur er geen verdubbeling plaats vind. Dit geeft anders fouten in de bot emulator. 

Uiteindelijk is de AdaptiveCard te tonen door zoals hieronder door te geven als attachment met het juiste contentType:

                // Show card for meter and request reading
                return await stepContext.PromptAsync(nameof(CheckChoiceReadingInput),
                    new PromptOptions
                    {
                        Prompt = (Activity)MessageFactory.Attachment(new Attachment
                        {
                            ContentType = AdaptiveCard.ContentType,
                            // Convert the AdaptiveCard to a JObject
                            Content = card
                        })
                    },
                    cancellationToken);

Nadelen
-----------
Op dit moment is het voor converationals nog niet mogelijk om het juiste format terug te sturen via seamly om dit te supporten. Hiervoor moet een aanpassing worden gemaakt in de code. 
De aanpassing via de DirectLine API die nodig is aan hun kant is hier te vinden: https://www.tutorialguruji.com/javascript/ms-bot-framework-adaptive-cards-how-to-send-value-stepcontext-value-from-directline/