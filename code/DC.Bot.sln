﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32014.148
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DC.Bot.Api", "DC.Bot.Api\DC.Bot.Api.csproj", "{7B9D917B-6311-4EB0-ACD5-0AB4CE86C0ED}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DC.Bot.BusinessLogic", "DC.Bot.BusinessLogic\DC.Bot.BusinessLogic.csproj", "{E134610D-DF93-4718-AAB3-AE2A5DE67104}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DC.Bot.Repositories", "DC.Bot.Repositories\DC.Bot.Repositories.csproj", "{F32B8529-E8AB-4AAB-A7B3-CBA7D3D5BE35}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DC.Bot.Tests", "..\tests\DC.Bot.Tests\DC.Bot.Tests.csproj", "{1C47A1EC-4F36-4C14-908E-84D2DF93F97F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{80DB3F59-392F-43D6-87BA-B7CE95B227A8}"
	ProjectSection(SolutionItems) = preProject
		..\.editorconfig = ..\.editorconfig
		Directory.Build.props = Directory.Build.props
		..\Directory.Packages.props = ..\Directory.Packages.props
		Nuget.config = Nuget.config
		..\readme.md = ..\readme.md
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DC.Bot.Tests.Integration", "..\tests\DC.Bot.Tests.Integration\DC.Bot.Tests.Integration.csproj", "{AC69EDDE-E7FC-49AC-8CEA-7A2F58653EDA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DC.Bot.Agents", "DC.Bot.Agents\DC.Bot.Agents.csproj", "{73EAB713-B1D1-4BB9-A5FD-C6FBA9FDFFF5}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7B9D917B-6311-4EB0-ACD5-0AB4CE86C0ED}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7B9D917B-6311-4EB0-ACD5-0AB4CE86C0ED}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7B9D917B-6311-4EB0-ACD5-0AB4CE86C0ED}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7B9D917B-6311-4EB0-ACD5-0AB4CE86C0ED}.Release|Any CPU.Build.0 = Release|Any CPU
		{E134610D-DF93-4718-AAB3-AE2A5DE67104}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E134610D-DF93-4718-AAB3-AE2A5DE67104}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E134610D-DF93-4718-AAB3-AE2A5DE67104}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E134610D-DF93-4718-AAB3-AE2A5DE67104}.Release|Any CPU.Build.0 = Release|Any CPU
		{F32B8529-E8AB-4AAB-A7B3-CBA7D3D5BE35}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F32B8529-E8AB-4AAB-A7B3-CBA7D3D5BE35}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F32B8529-E8AB-4AAB-A7B3-CBA7D3D5BE35}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F32B8529-E8AB-4AAB-A7B3-CBA7D3D5BE35}.Release|Any CPU.Build.0 = Release|Any CPU
		{1C47A1EC-4F36-4C14-908E-84D2DF93F97F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1C47A1EC-4F36-4C14-908E-84D2DF93F97F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1C47A1EC-4F36-4C14-908E-84D2DF93F97F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1C47A1EC-4F36-4C14-908E-84D2DF93F97F}.Release|Any CPU.Build.0 = Release|Any CPU
		{AC69EDDE-E7FC-49AC-8CEA-7A2F58653EDA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AC69EDDE-E7FC-49AC-8CEA-7A2F58653EDA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AC69EDDE-E7FC-49AC-8CEA-7A2F58653EDA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AC69EDDE-E7FC-49AC-8CEA-7A2F58653EDA}.Release|Any CPU.Build.0 = Release|Any CPU
		{73EAB713-B1D1-4BB9-A5FD-C6FBA9FDFFF5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{73EAB713-B1D1-4BB9-A5FD-C6FBA9FDFFF5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{73EAB713-B1D1-4BB9-A5FD-C6FBA9FDFFF5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{73EAB713-B1D1-4BB9-A5FD-C6FBA9FDFFF5}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2F94BA70-C18F-42A5-B25E-8FFDBAFD2228}
	EndGlobalSection
EndGlobal
