{"Logging": {"LogLevel": {"Default": "Debug", "System": "Information", "Microsoft": "Information", "Microsoft.AspNetCore.Localization": "Error"}}, "AuthenticationSettings": {"DigitalCore": {"ServiceAccount": {"Audience": "https://test.api-digital.enecogroup.com", "Apikey": "2f7573b07a3b4e058ba3728405093b0f"}}, "AppRegistration": {"Scope": "api://appreg-digitalcore-services-t/.default", "Audience": "api://appreg-digitalcore-services-t"}}, "FeatureToggle": {}, "Azure": {"KeyVault": "https://digitalcore-vault-t.vault.azure.net/"}, "AllowTextLabelsExport": false, "Authorization": {"Eneco": {"OktaIssuer": "https://inloggen.acc.eneco.nl/oauth2/default"}, "Oxxio": {"OktaIssuer": "https://inloggen.acc.oxxio.nl/oauth2/default"}}, "DcCustomersRepository": {"BaseUrl": "https://digitalcore-api-ene-customers-v1-t.azurewebsites.net"}, "DcFinancialsRepository": {"BaseUrl": "https://digitalcore-api-ene-financials-v1-t.azurewebsites.net"}, "DcProductsRepository": {"BaseUrl": "https://digitalcore-api-ene-products-v1-t.azurewebsites.net"}, "DcUserAccountsRepository": {"BaseUrl": "https://digitalcore-api-ene-useraccounts-v1-t.azurewebsites.net"}, "DcUsagesRepository": {"BaseUrl": "https://digitalcore-api-ene-usages-v1-t.azurewebsites.net"}, "DcStorageRepository": {"BaseUrl": "https://digitalcore-api-ene-storage-v1-t.azurewebsites.net"}, "DynatraceOpenTelemetry": {"Url": "https://wcf41793.live.dynatrace.com/api/v2/otlp/"}, "KafkaOptions": {"Instance": "dta", "Environment": "test", "Host": "bootstrap.dtaaz.esp.eneco.com:9094", "SchemaRegistryServer": "https://schemas.dtaaz.esp.eneco.com"}}