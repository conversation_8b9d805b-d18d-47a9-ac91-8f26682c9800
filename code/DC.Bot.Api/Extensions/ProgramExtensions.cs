using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using DC.Api.Base.ExceptionHandling;
using DC.Api.Base.IntegrationTesting;
using DC.Api.Base.Startup;
using DC.Api.Base.Telemetry;
using DC.Api.Base.Validation;
using DC.Repositories.Base.Options;
using DC.Security.MutualSsl;
using DC.Telemetry;
using DC.Utilities.Validators;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Localization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.ApplicationInsights;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json.Converters;

namespace DC.Bot.Api.Extensions;

internal static class ProgramExtensions
{
    private const int minWorkerThreads = 300;
    private const int minIOCPThreads = 100;

    public static void CustomConfigureAndRun(this string[] args, Action<WebApplicationBuilder> configureServices)
    {
        WebApplicationBuilder builder = ConfigureWebApplicationBuilder(args, configureServices, true);

        // build the application
        var app = builder.Build();

        app.UseCors("AllowFrontend");

        // configure the middleware, routing, cultureinfo, AI and swagger (what was previously in BaseStartup.cs -> Configure(app))
        app.ConfigureApplication(builder.Environment, builder.Configuration, false, false, false);

        //xapi bot use web sockets
        app.UseWebSockets();
        app.MapMcp("api/eneco/mcp");

        ThreadPool.SetMinThreads(minWorkerThreads, minIOCPThreads);

        // run the application
        app.Run();
    }

    private static WebApplicationBuilder ConfigureWebApplicationBuilder(string[] args, Action<WebApplicationBuilder> configureServices, bool isXapiBot)
    {
        // create the builder (previously in BaseProgram.cs)
        var builder = WebApplication.CreateBuilder(args);

        // configure the config files + logging (previously in BaseProgram.cs)
        // add environment variables (for keyvault entries)
        builder.Configuration.AddEnvironmentVariables("DIGITALCORE_");

        // if ran locally, a third level of environment is used
        if (args.Contains("--DevelopmentLocal=true"))
        {
            builder.Configuration.Sources.Clear();

            var environment = builder.Environment.EnvironmentName;
            builder.Configuration.AddJsonFile("appsettings.json", false, true)
                .AddJsonFile($"appsettings.{environment}.json", true, true)
                .AddJsonFile($"appsettings.{environment}.Local.json", true, true);

            builder.Configuration.AddEnvironmentVariables();
            builder.Configuration.AddCommandLine(args);
        }

        builder.Logging.AddAzureWebAppDiagnostics();
        var appInsightConnectionString = builder.Configuration["APPLICATIONINSIGHTS_CONNECTION_STRING"];
        if (appInsightConnectionString.IsNotEmpty())
        {
            builder.Logging.AddApplicationInsights(configureTelemetryConfiguration:
                (config) =>
                    config.ConnectionString = appInsightConnectionString,
                configureApplicationInsightsLoggerOptions: (options) =>
                {
                    options.TrackExceptionsAsExceptionTelemetry = true;
                    options.FlushOnDispose = true;
                    options.IncludeScopes = true;
                });
        }

        // ApplicationInsights logs traces from Warning and up by default.
        // Add information level for DcBotMiddleware only.
        if (isXapiBot)
        {
            builder.Logging.AddFilter<ApplicationInsightsLoggerProvider>("DC.Bot.BusinessLogic.Adapters.DcBotMiddleware", LogLevel.Information);
        }

        // configure the dependency injection that goes for all solutions (previously in BaseStartup.cs)
        builder.Services.BaseConfigureServices(builder.Environment, builder.Configuration);

        builder.Services.AddOptionInstances(builder.Configuration);

        // execute the solution specific ConfigureServices method
        configureServices(builder);
        return builder;
    }

    private static void BaseConfigureServices(this IServiceCollection services, IWebHostEnvironment environment, IConfiguration configuration)
    {
        services.AddControllers(options =>
        {
            options.Filters.Add(typeof(EnrichTelemetryFilter));
        }).AddNewtonsoftJson(options =>
        {
            options.SerializerSettings.Converters.Add(new StringEnumConverter());
        });

        services.AddHttpContextAccessor();
        services.AddMemoryCache();
        services.AddHttpClient();

        services.AddApplicationInsightsTelemetry();
        services.AddSingleton<ITelemetryInitializer, AppendTagsTelemetryInitializer>();
        services.AddSingleton<ITelemetryInitializer, CustomTelemetryInitializer>();

        // Overwrite the way validation errors are being given back by .Net Core
        services.AddCustomValidationErrorsResponse(environment);
        services.AddCaching(configuration);

        services.AddApiAuthentication(configuration);
        services.AddAuthorization();
    }

    private static void ConfigureApplication(this IApplicationBuilder app, IWebHostEnvironment env, IConfiguration configuration, bool serializeAsV2, bool enforceClientCertificate, bool isXapiAgentPortal)
    {
        // Warning: Order does matter!
        // https://docs.microsoft.com/en-us/aspnet/core/fundamentals/middleware/?view=aspnetcore-2.2#order
        IList<CultureInfo> supportedCultures = new List<CultureInfo>
        {
            new CultureInfo("nl-NL"),
            new CultureInfo("en-US"),
        };

        app.UseRequestLocalization(new RequestLocalizationOptions
        {
            DefaultRequestCulture = new RequestCulture("nl-NL"),
            SupportedCultures = supportedCultures,
            SupportedUICultures = supportedCultures
        });

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();

            // 2020-06-30 Removed HTTPS redirection from base startup because:
            // 1. its implemented two times (https only setting in azure and here in the startup)
            // 2. the implementation below does not take into account that the keep alive call is done on http
            // Keep setting just for LocalDev
            app.UseHttpsRedirection();
        }
        else
        {
            app.UseHsts();
        }

        // Additional/customized exception handling.
        app.UseExceptionHandlingMiddleware();

        // Additional check on integration test.
        app.UseIntegrationTestingMiddleware();

        // Check whether Mutual SSL is being used, as this should be enforced with priority.
        // EnforceClientCertificate is true by default, unless set to false by derived class.
        if (enforceClientCertificate)
        {
            app.UseClientCertMiddleware();
        }

        // Runs matching. An endpoint is selected and set on the HttpContext if a match is found.
        app.UseRouting();

        app.UseAuthentication();
        app.UseAuthorization();

        // If SerializeAsV2 is set to to false we will serialize as OpenApi3
        app.UseSwagger(c =>
        {
            c.SerializeAsV2 = serializeAsV2;
        });
        app.UseSwaggerUI(c =>
        {
            c.DisplayOperationId();
        });

        // Respond with static response to Azure requests from the AlwaysOn agent to keep the application alive and kicking.
        app.MapWhen(IsAlwaysOnAgentOnRoot, HandleRequestToRootByAzureAlwaysOnAgent);

        if (isXapiAgentPortal)
        {
            app.UseAuthorization();
        }

        // Executes the endpoint that was selected by routing.
        app.UseEndpoints(endpoints =>
        {
            // Mapping of endpoints goes here:
            endpoints.MapControllers();
        });

        // This line is necessary to instantiate the HttpRequestActivityEnrichment and subscribe to the DiagnosticListener. Be careful when removing it.
        _ = app.ApplicationServices.GetRequiredService<HttpRequestActivityEnrichment>();
    }

    private static bool IsAlwaysOnAgentOnRoot(HttpContext context)
    {
        if (context.Request.Path != "/") return false;

        var userAgent = context.Request.Headers[HeaderNames.UserAgent].FirstOrDefault();
        return userAgent != null && userAgent.Contains("AlwaysOn", StringComparison.InvariantCultureIgnoreCase);
    }

    private static void HandleRequestToRootByAzureAlwaysOnAgent(IApplicationBuilder app)
    {
        app.Run(async context =>
        {
            await context.Response.WriteAsync("API up and running!").ConfigureAwait(false);
        });
    }

    private static void AddOptionInstances(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<AppRegistrationOptions>(configuration.GetSection("AuthenticationSettings:AppRegistration"));
    }
}
