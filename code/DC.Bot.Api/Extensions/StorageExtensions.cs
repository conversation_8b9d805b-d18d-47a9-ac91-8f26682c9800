﻿using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Azure.Blobs;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using System;

namespace DC.Bot.Api.Extensions;

/// <summary>
/// Storage Extensions
/// </summary>
public static class StorageExtensions
{
    /// <summary>
    /// On production and acceptance, multiple instances are running and therefore 1 shared session storage is desired
    /// On test and local dev, only one instance is running so memory storage is sufficient to store the sessions
    /// You can use the acceptance blobstorage on your local machine if you declare it in your developmentlocal appsettings.
    /// </summary>
    /// <param name="configuration"></param>
    /// <param name="containerName"></param>
    /// <returns></returns>
    public static IStorage GetStorage(this IConfiguration configuration, string containerName)
    {
        // get environment and determine if to use blob or memory
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        var blobConnectionString = configuration["AzureBlobStorage:ConnectionString"];

        var useBlob = environment == Environments.Production ||
            (environment == "Acceptance" && !blobConnectionString.Equals("azurekeyvault", StringComparison.OrdinalIgnoreCase));

        // return the right concrete class.
        if (useBlob)
        {
            return new BlobsStorage(configuration["AzureBlobStorage:ConnectionString"], containerName);
        }
        else
        {
            return new MemoryStorage();
        }
    }
}
