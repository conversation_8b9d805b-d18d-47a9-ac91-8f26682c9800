﻿using DC.Repositories.Base;
using DC.Security.MutualSsl;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Rest;
using System;
using System.Net.Http;
using Azure.Identity;
using DC.Repositories.Base.Handlers;
using DC.Repositories.Base.Options;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;

namespace DC.Bot.Api.Extensions
{
    /// <summary>
    /// 
    /// </summary>
    public static class SingletonExtensions
    {
        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TService"></typeparam>
        /// <typeparam name="TImplementation"></typeparam>
        /// <typeparam name="TRepository"></typeparam>
        /// <param name="services"></param>
        /// <param name="implementationFactory"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static IServiceCollection AddSingletonDcClientWithHttpClientFactory<TService, TImplementation, TRepository>(
            this IServiceCollection services, Func<Uri, HttpClient, TImplementation> implementationFactory, IConfiguration configuration,
        IWebHostEnvironment environment)
            where TService : class, IDisposable
            where TImplementation : ServiceClient<TImplementation>, TService
            where TRepository : BaseRepository
        {
            IHttpClientBuilder builder = services
                .AddHttpClient(typeof(TRepository).Name)
                .ConfigureHttpClient((serviceProvider, client) =>
                {
                    const string dependencyTypeHeaderName = "DC-DependencyType";
                    string dependencyTypeHeaderValue = serviceProvider.GetDependencyType<TRepository>();

                    if (!string.IsNullOrEmpty(dependencyTypeHeaderValue) && !client.DefaultRequestHeaders.Contains(dependencyTypeHeaderName))
                    {
                        client.DefaultRequestHeaders.Add(dependencyTypeHeaderName, dependencyTypeHeaderValue);
                    }
                })
                .AddClientCertificateToRestClient(configuration);

            AddBearerTokenHandler<TService, TImplementation>(environment, builder);

            return services.AddSingleton<TService>(provider =>
            {
                var uri = provider.GetServiceUriForRepository<TRepository>();
                var httpClient = provider.GetRequiredService<IHttpClientFactory>().CreateClient(typeof(TRepository).Name);
                return implementationFactory(uri, httpClient);
            });
        }

        private static void AddBearerTokenHandler<TService, TImplementation>(
            IWebHostEnvironment environment,
            IHttpClientBuilder builder)
            where TService : class, IDisposable
            where TImplementation : ServiceClient<TImplementation>, TService
        {
            // Acquire Bearer token via Managed Identity or localhost alternatives
            builder.AddHttpMessageHandler(serviceProvider =>
            {
                var options = serviceProvider.GetRequiredService<IOptions<AppRegistrationOptions>>().Value;
                var scope = options.Scope;
                var credential = new DefaultAzureCredential(new DefaultAzureCredentialOptions
                {
                    ExcludeVisualStudioCredential = true,
                    ExcludeInteractiveBrowserCredential = !environment?.IsDevelopment() ?? true,
                    ExcludeManagedIdentityCredential = environment?.IsDevelopment() ?? false
                });
                return new BearerTokenHandler(credential, scope);
            });
        }
    }
}
