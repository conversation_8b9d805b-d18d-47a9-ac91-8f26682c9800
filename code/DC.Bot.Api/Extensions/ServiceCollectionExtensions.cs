﻿#nullable enable
using DC.ESP.Clients;
using DC.ESP.Models.Json.JourneyTracker.Customer;
using DC.ESP.Models.Json.JourneyTracker.ServiceLocation;
using DC.ESP.Services;
using DC.MassTransit.Extensions;
using DC.MassTransit.Kafka.Clients;
using DC.MassTransit.Kafka.Extensions;
using DC.MassTransit.Kafka.Producers.Interfaces;
using DC.MassTransit.Options;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Security.Claims;

namespace DC.Bot.Api.Extensions;

/// <summary>
/// Simplify and streamline the process of adding services
/// </summary>
public static class ServiceCollectionExtensions
{
    private const string TenantIdConfigName = "AuthenticationSettings:AppRegistration:TenantId";
    private const string AudienceConfigName = "AuthenticationSettings:AppRegistration:Audience";

    /// <summary>
    /// MassTransit configuration
    /// </summary>
    /// <param name="services"></param>
    /// <param name="configuration"></param>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException"></exception>
    public static IServiceCollection ConfigureMassTransit(this IServiceCollection services, IConfiguration configuration)
    {
        KafkaOptions? options = configuration.GetRequiredSection(nameof(KafkaOptions)).Get<KafkaOptions>();

        if (options is null)
        {
            throw new InvalidOperationException("KafkaOptions configuration section is empty or null");
        }

        if (options.UseMockImplementation)
        {
            services.AddSingleton(typeof(IEspClient<,>), typeof(EspClientMock<,>));
        }
        else
        {
            services.AddSingleton(typeof(IEspClient<,>), typeof(EspClient<,>));
        }

        services.AddScoped<IJourneyTrackerService, JourneyTrackerService>();

        services.AddMassTransit(configuration,
                configureBus: (x => { }, default),
                configureRider: (x =>
                {
                    x.AddJsonProducer<CustomerJourneyEventSubject, CustomerJourneyEvent>(configuration);
                    x.AddJsonProducer<LocationJourneyEventSubject, LocationJourneyEvent>(configuration);
                }, (context, cfg) => { }
        ));

        return services;
    }

    internal static void AddApiAuthentication(this IServiceCollection services, IConfiguration configuration)
    {
        string appregTenantId = configuration[TenantIdConfigName] ?? throw new InvalidOperationException($"Missing configuration: '{TenantIdConfigName}'");
        string appregAudience = configuration[AudienceConfigName] ?? throw new InvalidOperationException($"Missing configuration: '{AudienceConfigName}'");

        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(option =>
            {
                option.Authority = $"https://login.microsoftonline.com/{appregTenantId}/v2.0";
                option.Audience = appregAudience;
                option.TokenValidationParameters = new TokenValidationParameters
                {
                    RoleClaimType = ClaimTypes.Role,
                    ValidateIssuer = true,
                    ValidIssuers =
                    [
                        $"https://sts.windows.net/{appregTenantId}/"
                    ]
                };
            });
    }
}
