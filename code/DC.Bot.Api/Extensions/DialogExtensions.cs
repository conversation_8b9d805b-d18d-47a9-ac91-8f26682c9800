﻿using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Dialogs.Components;
using DC.Bot.BusinessLogic.Dialogs.Customers;
using DC.Bot.BusinessLogic.Dialogs.Customers.PhoneNumberDialogs;
using DC.Bot.BusinessLogic.Dialogs.Customers.Validators;
using DC.Bot.BusinessLogic.Dialogs.Financials;
using DC.Bot.BusinessLogic.Dialogs.Financials.Validators;
using DC.Bot.BusinessLogic.Dialogs.Products;
using DC.Bot.BusinessLogic.Dialogs.Products.Validators;
using DC.Bot.BusinessLogic.Dialogs.Usages;
using DC.Bot.BusinessLogic.Dialogs.UserAccounts;
using DC.Bot.BusinessLogic.Interfaces.DialogContainers;
using Microsoft.Extensions.DependencyInjection;

namespace DC.Bot.Api.Extensions
{
    /// <summary>
    /// Dialog Extensions
    /// </summary>
    public static class DialogExtensions
    {
        /// <summary>
        /// Adding Containers
        /// </summary>
        /// <param name="services"></param>
        public static void AddDialogContainers(this IServiceCollection services)
        {
            services.AddTransient<IDialogContainer, DialogContainer>();
            services.AddTransient<ICustomersDialogContainer, CustomersDialogContainer>();
            services.AddTransient<ICustomerVerificationDialogContainer, CustomerVerificationDialogContainer>();
            services.AddTransient<IFinancialsDialogContainer, FinancialsDialogContainer>();
            services.AddTransient<IProductsDialogContainer, ProductsDialogContainer>();
            services.AddTransient<IUserAccountsDialogContainer, UserAccountsDialogContainer>();
            services.AddTransient<IUsagesDialogContainer, UsagesDialogContainer>();
        }

        /// <summary>
        /// Adding Validators
        /// </summary>
        /// <param name="services"></param>
        public static void AddValidators(this IServiceCollection services)
        {
            services.AddSingleton<IDialogValidators, DialogValidators>();
            services.AddSingleton<ICustomerValidator, CustomerDialogValidator>();
            services.AddSingleton<IFinancialsDialogValidator, FinancialsDialogValidator>();
            services.AddSingleton<IProductsDialogValidator, ProductsDialogValidator>();
        }

        /// <summary>
        /// Adding Dialogs
        /// </summary>
        /// <param name="services"></param>
        public static void AddDialogs(this IServiceCollection services)
        {
            services.AddTransient<MainDialog>();
            services.AddTransient<CustomerVerificationDialog>();
            services.AddTransient<CustomerAccountsDialog>();
            services.AddTransient<AdvancePaymentAmountDialog>();
            services.AddTransient<GiroCardStepDialog>();
            services.AddTransient<AdvancePaymentDayDialog>();
            services.AddTransient<YearnoteDateDialog>();
            services.AddTransient<ProductRatesDialog>();
            services.AddTransient<UsernameDialog>();
            services.AddTransient<ProductEndDatesDialog>();
            services.AddTransient<PaymentArrangementDialog>();
            services.AddTransient<RelocateDateDialog>();
            services.AddTransient<ProductUsagesDialog>();
            services.AddTransient<KetelComfortAppointmentDialog>();
            services.AddTransient<ChangeEmailDialog>();
            services.AddTransient<ChangeIbanDialog>();
            services.AddTransient<ChangePhoneNumberDialog>();
            services.AddTransient<CustomerIdVerification>();
            services.AddTransient<DiscontinueToonDialog>();
            services.AddTransient<DiscontinueServiceContractDialog>();
            services.AddTransient<ReadingsReportRequestDialog>();
            services.AddTransient<SaveReadingPersonalDialog>();
            services.AddTransient<CreateServiceOrderDialog>();
            services.AddTransient<ProductEndDatesAdviceDialog>();
            services.AddTransient<NextBestActionDialog>();
            services.AddTransient<AdvancePaymentAdviceDialog>();
            services.AddTransient<IsmaMandateDialog>();
            services.AddTransient<ProductFineCalculationDialog>();
            services.AddTransient<ZonOpDakDialog>();
            services.AddTransient<ChangeContactPreferencesDialog>();
        }
    }
}
