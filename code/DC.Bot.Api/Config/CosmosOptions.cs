﻿using System.ComponentModel.DataAnnotations;

namespace DC.Bot.Api.Config
{
    /// <summary>
    /// 
    /// </summary>
    public class CosmosOptions
    {
        /// <summary>
        /// 
        /// </summary>
        [Required]
        public string AccountEndpoint { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [Required]
        public string AccountKey { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [Required]
        public string ChatReferencesCollectionId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [Required]
        public string ConversationStatesCollectionId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [Required]
        public string DatabaseId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [Required]
        public int DefaultThroughput { get; set; }
    }
}
