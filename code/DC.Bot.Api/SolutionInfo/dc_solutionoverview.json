{"version": 1.0, "nugetInfo": [{"project": "DC.Bot.Api", "reference": "DC.Api.Base", "version": "1.2.1229175"}, {"project": "DC.Bot.Api", "reference": "DC.Usages.Client", "version": "3.0.********.3"}, {"project": "DC.Bot.Api", "reference": "Microsoft.Azure.KeyVault.Core", "version": "3.0.5"}, {"project": "DC.Bot.Api", "reference": "Microsoft.Bcl.TimeProvider", "version": "9.0.4"}, {"project": "DC.Bot.Api", "reference": "Microsoft.Bot.Builder", "version": "4.22.0"}, {"project": "DC.Bot.Api", "reference": "Microsoft.Bot.Builder.Azure", "version": "4.22.0"}, {"project": "DC.Bot.Api", "reference": "Microsoft.Bot.Builder.Azure.Blobs", "version": "4.22.0"}, {"project": "DC.Bot.Api", "reference": "Microsoft.Bot.Builder.Integration.AspNet.Core", "version": "4.22.0"}, {"project": "DC.Bot.BusinessLogic", "reference": "AutoMapper", "version": "12.0.1"}, {"project": "DC.Bot.BusinessLogic", "reference": "DC.BusinessLogic.Base", "version": "1.2.1229175"}, {"project": "DC.Bot.BusinessLogic", "reference": "DC.SAPI", "version": "1.2.1229175"}, {"project": "DC.Bot.BusinessLogic", "reference": "DC.Security.Encryption", "version": "1.2.1229175"}, {"project": "DC.Bot.BusinessLogic", "reference": "DC.Telemetry", "version": "1.2.1229175"}, {"project": "DC.Bot.BusinessLogic", "reference": "DC.Utilities", "version": "1.2.1229175"}, {"project": "DC.Bot.BusinessLogic", "reference": "JWT", "version": "10.1.1"}, {"project": "DC.Bot.BusinessLogic", "reference": "Microsoft.Bot.Builder", "version": "4.22.0"}, {"project": "DC.Bot.BusinessLogic", "reference": "Microsoft.Bot.Builder.Dialogs", "version": "4.22.0"}, {"project": "DC.Bot.BusinessLogic", "reference": "Microsoft.Bot.Builder.Integration.AspNet.Core", "version": "4.22.0"}, {"project": "DC.Bot.BusinessLogic", "reference": "Microsoft.TestPlatform.TestHost", "version": "17.8.0"}, {"project": "DC.Bot.BusinessLogic", "reference": "MimeTypeMapOfficial", "version": "1.0.17"}, {"project": "DC.Bot.Repositories", "reference": "DC.Customers.Client", "version": "3.0.20250523.4"}, {"project": "DC.Bot.Repositories", "reference": "DC.Domain.Exceptions", "version": "1.2.1229175"}, {"project": "DC.Bot.Repositories", "reference": "DC.Financials.Client", "version": "3.0.********.1"}, {"project": "DC.Bot.Repositories", "reference": "DC.Products.Client", "version": "3.0.********.4"}, {"project": "DC.Bot.Repositories", "reference": "DC.Repositories.Base", "version": "1.2.1229175"}, {"project": "DC.Bot.Repositories", "reference": "DC.Storage.Client", "version": "3.0.********.2"}, {"project": "DC.Bot.Repositories", "reference": "DC.Telemetry", "version": "1.2.1229175"}, {"project": "DC.Bot.Repositories", "reference": "DC.Usages.Client", "version": "3.0.********.3"}, {"project": "DC.Bot.Repositories", "reference": "DC.UserAccounts.Client", "version": "3.0.********.1"}, {"project": "DC.Bot.Repositories", "reference": "Microsoft.Extensions.Hosting.Abstractions", "version": "8.0.0"}, {"project": "DC.Bot.Repositories", "reference": "Microsoft.Rest.ClientRuntime", "version": "2.3.24"}, {"project": "DC.Bot.Tests", "reference": "Microsoft.AspNetCore.Mvc.Testing", "version": "8.0.1"}, {"project": "DC.Bot.Tests", "reference": "Microsoft.Bot.Builder.Testing", "version": "4.22.0"}, {"project": "DC.Bot.Tests", "reference": "Microsoft.Extensions.TimeProvider.Testing", "version": "9.4.0"}, {"project": "DC.Bot.Tests", "reference": "Microsoft.NET.Test.Sdk", "version": "17.8.0"}, {"project": "DC.Bot.Tests", "reference": "Moq", "version": "4.20.70"}, {"project": "DC.Bot.Tests", "reference": "xunit", "version": "2.6.6"}, {"project": "DC.Bot.Tests", "reference": "xunit.runner.visualstudio", "version": "2.5.6"}, {"project": "DC.Bot.Tests.Integration", "reference": "coverlet.collector", "version": "6.0.0"}, {"project": "DC.Bot.Tests.Integration", "reference": "DC.Test.Integration", "version": "1.2.1229175"}, {"project": "DC.Bot.Tests.Integration", "reference": "Microsoft.AspNetCore.Mvc.Testing", "version": "8.0.1"}, {"project": "DC.Bot.Tests.Integration", "reference": "Microsoft.Bot.Builder.Testing", "version": "4.22.0"}, {"project": "DC.Bot.Tests.Integration", "reference": "Microsoft.NET.Test.Sdk", "version": "17.8.0"}, {"project": "DC.Bot.Tests.Integration", "reference": "xunit.extensibility.core", "version": "2.6.6"}], "externalReferences": ["DcCustomersRepository", "DcFinancialsRepository", "DcProductsRepository", "DcStorageRepository", "DcUsagesRepository", "DcUserAccountsRepository"]}