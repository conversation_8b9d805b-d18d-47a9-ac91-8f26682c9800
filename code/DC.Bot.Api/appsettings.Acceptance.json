{"Logging": {"LogLevel": {"Default": "Debug", "System": "Information", "Microsoft": "Information", "Microsoft.AspNetCore.Localization": "Error"}}, "AuthenticationSettings": {"DigitalCore": {"ServiceAccount": {"Audience": "https://acc.api-digital.enecogroup.com", "Apikey": "d067148ea4354b3288b0447e59551aa7"}}, "AppRegistration": {"Scope": "api://appreg-digitalcore-services-a/.default", "Audience": "api://appreg-digitalcore-services-a"}}, "FeatureToggle": {"NewFinePolicyDate": "2021-06-01"}, "AllowTextLabelsExport": false, "Authorization": {"Eneco": {"OktaIssuer": "https://inloggen.acc.eneco.nl/oauth2/default"}, "Oxxio": {"OktaIssuer": "https://inloggen.acc.oxxio.nl/oauth2/aus18tkgmqwPGZuMk417"}}, "Azure": {"KeyVault": "https://digitalcore-vault-a.vault.azure.net/"}, "DcCustomersRepository": {"BaseUrl": "https://digitalcore-api-ene-customers-v1-a.azurewebsites.net"}, "DcFinancialsRepository": {"BaseUrl": "https://digitalcore-api-ene-financials-v1-a.azurewebsites.net"}, "DcProductsRepository": {"BaseUrl": "https://digitalcore-api-ene-products-v1-a.azurewebsites.net"}, "DcUserAccountsRepository": {"BaseUrl": "https://digitalcore-api-ene-useraccounts-v1-a.azurewebsites.net"}, "DcUsagesRepository": {"BaseUrl": "https://digitalcore-api-ene-usages-v1-a.azurewebsites.net"}, "DcStorageRepository": {"BaseUrl": "https://digitalcore-api-ene-storage-v1-a.azurewebsites.net"}, "DynatraceOpenTelemetry": {"Url": "https://oql75059.live.dynatrace.com/api/v2/otlp/"}, "KafkaOptions": {"Instance": "dta", "Environment": "acc", "Host": "bootstrap.dtaaz.esp.eneco.com:9094", "SchemaRegistryServer": "https://schemas.dtaaz.esp.eneco.com"}}