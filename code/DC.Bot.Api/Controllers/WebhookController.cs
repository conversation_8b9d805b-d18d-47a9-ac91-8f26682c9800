﻿using DC.Api.Base.Controllers;
using DC.Bot.BusinessLogic;
using DC.Bot.BusinessLogic.Configuration;
using DC.Bot.BusinessLogic.Extensions;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.Auth;
using DC.Bot.BusinessLogic.Models.Request;
using DC.Domain.Exceptions.ResponseModels;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using DC.Utilities.Formatters;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Reflection;
using System.Threading.Tasks;

namespace DC.Bot.Api.Controllers
{
    /// <summary>
    /// Webhook controller
    /// </summary>
    [Route("api/{label}")]
    public class WebhookController : BaseController
    {
        private readonly IStorageService _storageService;
        private readonly ISessionManager _sessionManager;
        private readonly AesEncryptionSettings _aesEncryptionSettings;

        /// <summary>
        /// Webhook Constructor
        /// </summary>
        public WebhookController(
            IConfiguration configuration,
            ILoggerFactory loggerFactory,
            IStorageService storageService,
            ISessionManager sessionManager,
            IOptions<AesEncryptionSettings> aesEncryptionSettings) : base(configuration, loggerFactory)
        {
            _storageService = storageService;
            _sessionManager = sessionManager;
            _aesEncryptionSettings = aesEncryptionSettings.Value;
        }

        /// <summary>
        /// Add Conversation
        /// </summary>
        [HttpPost]
        [Route("conversation")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> AddConversation(Label label, [FromBody] ConversationWrapper request)
        {
            await _storageService.AddConversation(label, request).ConfigureAwait(false);
            return Ok(new { });
        }

        /// <summary>
        /// Callback function that validates if the token has a valid Okta identity. 
        /// Returns context of the customer to Seamly 
        /// </summary>
        [HttpGet]
        [Route("token")]
        [Produces("application/json")]
        [ProducesResponseType(typeof(SeamlyTokenCallbackResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> GetTokenCallback(Label label, [FromHeader(Name = "Authorization")] string token)
        {
            if (string.IsNullOrWhiteSpace(token))
            {
                return BadRequest(new { Message = "Token is not correctly provided in the Authorization header." });
            }

            var tokenInfo = await _sessionManager.ValidateOktaToken(label, token).ConfigureAwait(false);
            if (!tokenInfo.Validated)
            {
                return Unauthorized(new { Message = "Okta validation failed." });
            }

            SeamlyContextVariables seamlyContextVariables = new();
            if (tokenInfo.CustomerId.GetValueOrDefault(0) > 0)
            {
                var customerContext = await _sessionManager.GetSeamlyCustomerInfo(new BusinessLogic.Models.DialogData
                {
                    Customer = new BusinessLogic.Models.CustomerData
                    {
                        Label = label
                    },
                    Verification = new BusinessLogic.Models.VerificationData
                    {
                        CustomerId = tokenInfo.CustomerId
                    },
                    Channel = BotChannel.Web //use the default bot channel web (it is only used in the headers in the customer api call)
                }).ConfigureAwait(false);

                if (customerContext.personalisation != null && customerContext.customer != null)
                {
                    var seamlyValues = customerContext.personalisation.ToDerived<PersonalisationInfo, SeamlyVariablePersonalisation>(BindingFlags.Public | BindingFlags.Instance);
                    seamlyValues.Name = customerContext.customer.GetName();
                    seamlyValues.Nba = customerContext.nba;

                    seamlyContextVariables.Personalisation = new SeamlyContextPersonalisation
                    {
                        Context = customerContext.personalisation,
                        Values = seamlyValues
                    };
                }
            }

            var callbackResponse = new SeamlyTokenCallbackResponse
            {
                ExternalId = AesEncryptionHelper.Encrypt($"{_aesEncryptionSettings.Padding}{tokenInfo.CustomerId}", _aesEncryptionSettings.Key, _aesEncryptionSettings.Iv),
                Variables = seamlyContextVariables
            };

            return Ok(callbackResponse);
        }

        /// <summary>
        /// Exports the text labels to the test folder.
        /// </summary>
        [HttpPost]
        [Route("textlabels/export")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ErrorResponse), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> TextLabelExport(Label label)
        {
            await _storageService.ExportTextLabels(label).ConfigureAwait(false);
            return Ok(new { });
        }
    }
}
