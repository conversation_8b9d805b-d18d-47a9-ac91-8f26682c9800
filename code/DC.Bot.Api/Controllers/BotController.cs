﻿using DC.Api.Base.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Integration.AspNet.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace DC.Bot.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/messages")]
    public class BotController : BaseController
    {
        private readonly IBotFrameworkHttpAdapter _adapter;
        private readonly IBot _bot;

        /// <summary>
        /// Constructor
        /// </summary>
        public BotController(IConfiguration configuration, ILoggerFactory loggerFactory, IBotFrameworkHttpAdapter adapter, IBot bot) :
            base(configuration, loggerFactory)
        {
            _adapter = adapter;
            _bot = bot;
        }

        /// <summary>
        /// Messaging endpoint
        /// </summary>
        [HttpPost, HttpGet]
        public async Task PostAsync()
        {
            await _adapter.ProcessAsync(Request, Response, _bot).ConfigureAwait(false);
        }
    }
}