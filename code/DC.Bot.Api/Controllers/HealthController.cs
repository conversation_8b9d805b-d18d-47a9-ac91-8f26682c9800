using DC.Api.Base.Controllers;
using DC.Bot.Api.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Bot.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DC.Bot.Api.Controllers
{
    /// <summary>
    /// Implementation of the Health controller for XAPI Bot
    /// </summary>
    public class HealthController : BaseHealthController
    {
        private readonly IStorage _storage;

        /// <summary>
        /// Constructor
        /// </summary>
        public HealthController(IConfiguration configuration, ILoggerFactory loggerFactory) : base(configuration, loggerFactory)
        {
            _storage = configuration.GetStorage(configuration["AzureBlobStorage:HealthContainer"]);
        }

        /// <summary>
        /// Healthcheck with extra session storage check.
        /// </summary>
        public async override Task<IActionResult> HealthCheck()
        {
            // write and delete something to session storage
            await _storage.WriteAsync(new Dictionary<string, object>
            {
                { "healthkey", new JObject(new JProperty("health", new JValue(true))) }
            }).ConfigureAwait(false);
            await _storage.DeleteAsync(new string[] { "healthkey" }).ConfigureAwait(false);
            // trace and return OK
            return await base.HealthCheck();
        }
    }
}
