using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DC.Api.Base.Controllers;
using DC.Bot.Agents.Agents;
using DC.Bot.Agents.Models;
using DC.Bot.Agents.Repositories;
using DC.Repositories.Base.Enumerations;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.ChatCompletion;
#pragma warning disable SKEXP0110

namespace DC.Bot.Api.Controllers;

[Route("api/{label}")]
public class ChatController : BaseController
{
    private readonly AgentService _agentService;
    private readonly ChatHistoryRepository _chatHistoryRepository;

    public ChatController(
        IConfiguration configuration,
        ILoggerFactory loggerFactory,
        AgentService agentService,
        ChatHistoryRepository chatHistoryRepository)
        : base(
            configuration,
            loggerFactory)
    {
        _agentService = agentService;
        _chatHistoryRepository = chatHistoryRepository;
    }

    [Route("chat")]
    [HttpPost]
    [Consumes("application/json")]
    [Produces("application/json", "text/event-stream")]
    public async Task<IActionResult> Chat(
        Label label,
        [FromBody] ChatRequest chatRequest)
    {
        List<ToolCall> toolCallInformation = [];
        IActionResult returnValue;
        StringBuilder responseBuilder = new();
        List<string> images = [];

        var aiAgentThread = await _agentService.GetOrCreateThread(chatRequest.ThreadId);
        var chatHistory = GetChatHistoryThread(chatRequest.CustomerId, chatRequest.AccountId, chatRequest.ThreadId);
        var orchestratorAgent = await _agentService.CreateOrchestratorAgent(aiAgentThread, images);

        var chatMessages = new List<ChatMessageContent>();

        if (string.IsNullOrEmpty(chatRequest.ThreadId))
        {
            chatMessages.Add(new ChatMessageContent(AuthorRole.Assistant,
                $"Customer number is {chatRequest.CustomerId}"));
        }

        chatMessages.Add(new ChatMessageContent(AuthorRole.User, chatRequest.Message));

        if (!chatRequest.Stream)
        {
            AgentResponseItem<ChatMessageContent> chatResponse =
                await orchestratorAgent.InvokeAsync(chatMessages, chatHistory).FirstAsync();

            chatMessages.Add(new ChatMessageContent(AuthorRole.Assistant, chatResponse.Message.Content));

            var chatMessageHistoryContent = chatHistory.ChatHistory.Select(m => new ChatMessageContent(m.Role, m.Content)).ToList();

            _chatHistoryRepository.SetChatMessagesAsync(chatRequest.CustomerId, chatRequest.AccountId, aiAgentThread.Id!, chatMessageHistoryContent);

            dynamic response = new ExpandoObject();
            response.message = chatResponse.Message.Content;
            response.threadId = aiAgentThread.Id!;

            if (chatRequest.Debug == true)
            {
                response.toolCalls = toolCallInformation;
            }
            if (images.Count > 0)
            {
                response.images = images;
            }

            returnValue = Ok(response);
        }
        else
        {
            bool responseStarted = false;
            orchestratorAgent.Kernel.FunctionInvocationFilters.Add(new StreamingFilter(Response));

            SetupEventStreamHeaders();
            await Response.WriteAsync($"[STARTED THREAD]: {aiAgentThread.Id}");
            await Response.Body.FlushAsync();

            await foreach (StreamingChatMessageContent chunk in orchestratorAgent.InvokeStreamingAsync(chatMessages, chatHistory))
            {

                string chunkString = chunk.ToString();

                if (responseStarted == false)
                {
                    if (chunkString.Trim() != "")
                    {
                        responseStarted = true;
                        responseBuilder.Append(chunk);
                        await Response.WriteAsync($"[STARTED RESPONSE]: {chunkString}");
                        await Response.Body.FlushAsync();
                    }
                }
                else
                {
                    await Response.WriteAsync(chunkString);
                    await Response.Body.FlushAsync();
                }
            }

            // Add images if generated.
            if (images.Count > 0)
            {
                foreach (var img in images)
                {
                    string test = $"[IMAGE]: {img} [IMAGE_END]";
                    await Response.WriteAsync(test);
                    await Response.Body.FlushAsync();
                }
            }

            chatMessages.Add(new ChatMessageContent(AuthorRole.Assistant, responseBuilder.ToString()));

            var chatMessageHistoryContent = chatHistory.ChatHistory.Select(m => new ChatMessageContent(m.Role, m.Content)).ToList();

            _chatHistoryRepository.SetChatMessagesAsync(chatRequest.CustomerId, chatRequest.AccountId, aiAgentThread.Id!, chatMessageHistoryContent);

            returnValue = new EmptyResult();
        }

        return returnValue;
    }

    private ChatHistoryAgentThread GetChatHistoryThread(
        long customerId,
        int accountId,
        string threadId)
    {
        if (string.IsNullOrWhiteSpace(threadId))
        {
            return new ChatHistoryAgentThread();
        }

        var chatHistory = _chatHistoryRepository.GetHistory(customerId, accountId, threadId);

        if (chatHistory.Count > 0)
        {
            ChatHistory history = [];

            foreach (var message in chatHistory)
            {
                if (message.Role == AuthorRole.User)
                {
                    history.AddUserMessage(message.Content);
                }
                else if (message.Role == AuthorRole.Assistant)
                {
                    history.AddAssistantMessage(message.Content);
                }
                else if (message.Role == AuthorRole.System)
                {
                    history.AddSystemMessage(message.Content);
                }
            }

            return new ChatHistoryAgentThread(history);
        }

        return new ChatHistoryAgentThread();
    }

    private void SetupEventStreamHeaders()
    {
        Response.Headers.Append("Content-Type", "text/event-stream");
        Response.Headers.Append("Cache-Control", "no-cache");
        Response.Headers.Append("Connection", "keep-alive");
    }
}
