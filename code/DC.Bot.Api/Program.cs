﻿using DC.Api.Base.Startup;
using DC.Bot.Api.Extensions;
using DC.Bot.BusinessLogic;
using DC.Bot.BusinessLogic.Adapters;
using DC.Bot.BusinessLogic.Bots;
using DC.Bot.BusinessLogic.Configuration;
using DC.Bot.BusinessLogic.Dialogs;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.Repositories;
using DC.Bot.Repositories.Interfaces;
using DC.Customers.Client;
using DC.Financials.Client;
using DC.Products.Client;
using DC.Storage.Client;
using DC.Usages.Client;
using DC.UserAccounts.Client;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Integration.AspNet.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using System;
using DC.Bot.Agents.Configuration;
using DC.Bot.Agents.Extensions;
using DC.OpenTelemetry.Dynatrace.Extensions;
using ServiceCollectionExtensions = DC.Customers.Client.Repository.ServiceCollectionExtensions;
using DC.Repositories.Base.Options;

args.CustomConfigureAndRun(ConfigureServices);

static void ConfigureServices(WebApplicationBuilder builder)
{
    // Only configure the repositories and (Digital Core) outbound API clients if we are NOT trying to generate swaggerfiles
    // (AddScopedDigitalCoreClient is the main problem, as it verifies local existence of the Digital Core client certificate
    ConfigureRemoteIntegrationServices(builder.Services, builder.Configuration, builder.Environment);
    builder.ConfigureFeatureManagement("bot");
}

// <summary>
// Configures Swagger and Dependency injection (previously in Startup.cs)
// </summary>
static void ConfigureRemoteIntegrationServices(IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
{
    services.AddCors(options =>
    {
        options.AddPolicy("AllowFrontend", policy =>
        {
            policy
                .AllowAnyOrigin()
                .AllowAnyMethod()
                .AllowAnyHeader();
        });
    });

    services.AddHttpContextAccessor();
    services.AddVirtualAgent(configuration);

    services.Configure<AppRegistrationOptions>(configuration.GetSection("AuthenticationSettings:AppRegistration"));

    services.AddDynatraceOpenTelemetryService(configuration);

    // Tools
    services.AddSingleton(DcAutoMapper.GetMapperConfiguration().CreateMapper());

    services.ConfigureMassTransit(configuration);

    ServiceCollectionExtensions.AddDcCustomersRepository(services, configuration, environment);

    // services
    services.AddSingleton<ICustomersService, CustomersService>();
    services.AddSingleton<IFinancialsService, FinancialsService>();
    services.AddSingleton<IProductsService, ProductsService>();
    services.AddSingleton<IStorageService, StorageService>();
    services.AddSingleton<IUserAccountsService, UserAccountsService>();
    services.AddSingleton<IUsagesService, UsagesService>();
    services.AddSingleton<ILoggingService, LoggingService>();
    services.AddSingleton<INextBestActionService, NextBestActionService>();
    services.AddSingleton<IProductFineCalculationService, ProductFineCalculationService>();
    services.AddSingleton<IContactPreferencesService, ContactPreferencesService>();
    services.AddSingleton(TimeProvider.System);

    // session manager
    services.AddSingleton<ISessionManager, SessionManager>();

    services.Configure<AesEncryptionSettings>(configuration.GetSection("AesEncryption"));
    services.Configure<FeatureToggle>(configuration.GetSection("FeatureToggle"));
    services.Configure<AgentSettings>(configuration.GetSection("AgentSettings"));
    services.Configure<AiFoundrySettings>(configuration.GetSection("AiFoundrySettings"));

    // open id okta manager
    services.AddSingleton<IConfigurationManager<OpenIdConnectConfiguration>, ConfigurationManager<OpenIdConnectConfiguration>>((provider) =>
        new ConfigurationManager<OpenIdConnectConfiguration>(
            configuration["Authorization:Eneco:OktaIssuer"] + "/.well-known/oauth-authorization-server",
            new OpenIdConnectConfigurationRetriever(),
            new HttpDocumentRetriever()));

    // repositories
    services.AddSingleton<IDcStorageRepository, DcStorageRepository>();
    services.AddSingletonDcClientWithHttpClientFactory<IDCApiStorage, DCApiStorage, DcStorageRepository>((uri, httpClient) => new DCApiStorage(uri, httpClient), configuration, environment);

    services.AddSingleton<IDcCustomersRepository, DcCustomersRepository>();
    services.AddSingletonDcClientWithHttpClientFactory<IDCApiCustomers, DCApiCustomers, DcCustomersRepository>((uri, httpClient) => new DCApiCustomers(uri, httpClient), configuration, environment);

    services.AddSingleton<IDcFinancialsRepository, DcFinancialsRepository>();
    services.AddSingletonDcClientWithHttpClientFactory<IDCApiFinancials, DCApiFinancials, DcFinancialsRepository>((uri, httpClient) => new DCApiFinancials(uri, httpClient), configuration, environment);

    services.AddSingleton<IDcProductsRepository, DcProductsRepository>();
    services.AddSingletonDcClientWithHttpClientFactory<IDCApiProducts, DCApiProducts, DcProductsRepository>((uri, httpClient) => new DCApiProducts(uri, httpClient), configuration, environment);

    services.AddSingleton<IDcUserAccountsRepository, DcUserAccountsRepository>();
    services.AddSingletonDcClientWithHttpClientFactory<IDCApiUserAccounts, DCApiUserAccounts, DcUserAccountsRepository>((uri, httpClient) => new DCApiUserAccounts(uri, httpClient), configuration, environment);

    services.AddSingleton<IDcUsagesRepository, DcUsagesRepository>();
    services.AddSingletonDcClientWithHttpClientFactory<IDCApiUsages, DCApiUsages, DcUsagesRepository>((uri, httpClient) => new DCApiUsages(uri, httpClient), configuration, environment);

    IStorage storage = configuration.GetStorage(configuration["AzureBlobStorage:ContainerName"]);

    var conversationState = new ConversationState(storage);
    var userState = new UserState(storage);

    // Add the states as singletons
    services.AddSingleton(conversationState);
    services.AddSingleton(userState);

    // Create property's
    services.AddSingleton(conversationState.CreateProperty<ConversationData>(nameof(ConversationData)));
    services.AddSingleton(userState.CreateProperty<DialogData>(nameof(DialogData)));

    // Add services and repositories here with services.AddScoped
    // Create the Bot Framework Adapter with error handling enabled.
    services.AddSingleton<IMiddleware, DcBotMiddleware>();
    services.AddSingleton<IBotFrameworkHttpAdapter, AdapterWithErrorHandler>();

    // Dialog containers, dialogs and validators
    services.AddDialogContainers();
    services.AddDialogs();
    services.AddValidators();

    // Create the bot as a transient. In this case the ASP Controller is expecting an IBot.
    services.AddTransient<IBot, EnecoBot<MainDialog>>();

    services.ConfigureSwagger(configuration, true, true);

#pragma warning disable ASP0000 // Do not call 'IServiceCollection.BuildServiceProvider' in 'ConfigureServices'
    IServiceProvider serviceProvider = services.BuildServiceProvider();
#pragma warning restore ASP0000 // Do not call 'IServiceCollection.BuildServiceProvider' in 'ConfigureServices'
    IWebHostEnvironment env = serviceProvider.GetService<IWebHostEnvironment>();
    var kestrelSection = configuration.GetSection("Kestrel");
    //adding kestrel configuration only for local development
    if (kestrelSection != null && env.IsDevelopment())
        services.Configure<KestrelServerOptions>(kestrelSection);
}
