{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:63970", "sslPort": 0}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "commandLineArgs": "--DevelopmentLocal=true", "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "http://localhost:5000;http://localhost:63970", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Acceptance"}, "ancmHostingModel": "OutOfProcess"}, "DC.Bot.Api": {"commandName": "Project", "commandLineArgs": "--DevelopmentLocal=true", "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "http://localhost:5000;http://localhost:63970", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Acceptance"}, "ancmHostingModel": "OutOfProcess"}, "PRODUCTION_TESTING": {"commandName": "Project", "commandLineArgs": "--ProductionLocal=true", "launchBrowser": true, "launchUrl": "production-testing", "applicationUrl": "http://localhost:5000;http://localhost:63970", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "ProductionLocal"}, "ancmHostingModel": "OutOfProcess"}}}