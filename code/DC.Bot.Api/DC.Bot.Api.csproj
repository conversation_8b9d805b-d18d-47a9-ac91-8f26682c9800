﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ProduceReferenceAssembly>false</ProduceReferenceAssembly>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="DC.Api.Base" />
    <PackageReference Include="DC.OpenTelemetry.Dynatrace" />
    <PackageReference Include="DC.Usages.Client" />
    <PackageReference Include="Microsoft.Azure.KeyVault.Core" />
    <PackageReference Include="Microsoft.Bot.Builder" />
    <PackageReference Include="Microsoft.Bot.Builder.Azure" />
    <PackageReference Include="Microsoft.Bot.Builder.Azure.Blobs" />
    <PackageReference Include="Microsoft.Bot.Builder.Integration.AspNet.Core" />
    <PackageReference Include="ModelContextProtocol.AspNetCore" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DC.Bot.Agents\DC.Bot.Agents.csproj" />
    <ProjectReference Include="..\DC.Bot.BusinessLogic\DC.Bot.BusinessLogic.csproj" />
  </ItemGroup>
  <Target Name="_ResolveCopyLocalNuGetPkgXmls" AfterTargets="ResolveReferences">
    <ItemGroup>
      <!-- Copy XML files from DC PackageReferences to output dir -->
      <ReferenceCopyLocalPaths Include="@(ReferenceCopyLocalPaths->'%(RootDir)%(Directory)%(Filename).xml')" Condition="$([System.String]::new('%(ReferenceCopyLocalPaths.NuGetPackageId)').StartsWith('DC.')) and Exists('%(RootDir)%(Directory)%(Filename).xml')" />
    </ItemGroup>
  </Target>
</Project>
