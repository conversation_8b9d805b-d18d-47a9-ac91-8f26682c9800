﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<packageSources>
		<clear />
		<add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
		<!-- Additional package source (Eneco specific). Note; login during restore is needed with @eneco.com credentials -->
		<add key="DigitalCore" value="https://enecomanagedcloud.pkgs.visualstudio.com/_packaging/DigitalCore/nuget/v3/index.json" />
	</packageSources>
	<packageSourceMapping>
		<packageSource key="nuget.org">
			<package pattern="*" />
		</packageSource>

		<packageSource key="DigitalCore">
			<package pattern="DC.*" />
		</packageSource>
	</packageSourceMapping>
</configuration>