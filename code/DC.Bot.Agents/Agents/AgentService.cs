using Azure.AI.Agents.Persistent;
using DC.Bot.Agents.Configuration;
using DC.Bot.Agents.Mcp.Tools;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.Agents.AzureAI;
using Agent = Microsoft.SemanticKernel.Agents.Agent;

#pragma warning disable SKEXP0001
#pragma warning disable SKEXP0110

namespace DC.Bot.Agents.Agents;

public class AgentService
{
    private readonly PersistentAgentsClient _aiFoundryClient;
    private readonly Kernel _kernel;
    private readonly McpToolProvider _mcpToolProvider;
    private readonly AgentSettings _agentSettings;
    private readonly ILogger<AgentService> _logger;

    private readonly Dictionary<string, PersistentAgent> _persistentAgents = new();

    public AgentService(
        PersistentAgentsClient aiFoundryClient,
        McpToolProvider mcpToolProvider,
        IOptions<AgentSettings> agentSettings,
        ILogger<AgentService> logger,
        Kernel kernel)
    {
        _aiFoundryClient = aiFoundryClient;
        _mcpToolProvider = mcpToolProvider;
        _logger = logger;
        _kernel = kernel;
        _agentSettings = agentSettings.Value;

        foreach (var agentConfiguration in _agentSettings.Agents)
        {
            var agent = _aiFoundryClient.Administration.GetAgent(agentConfiguration.AgentId);

            if (agent.HasValue)
            {
                _persistentAgents.Add(agentConfiguration.AgentName, agent.Value);
            }
        }
    }

    private AzureAIAgent? InstallmentAmountAgent { get; set; }

    private AzureAIAgent? Edwin { get; set; }

    private AzureAIAgent? VisualisationAgent { get; set; }

    private AzureAIAgent? InstallmentAmountRuleEvaluationAgent { get; set; }

    private AzureAIAgent? ScenarioAgent { get; set; }

    public async Task<AzureAIAgentThread> GetOrCreateThread(string? threadIdentifier)
    {
        if (InstallmentAmountAgent is null)
        {
            await InitializeAgents();
        }

        if (string.IsNullOrWhiteSpace(threadIdentifier))
        {
            // Create the thread manually to ensure it is created with threadId.
            var aiFoundryThread = await _aiFoundryClient.Threads.CreateThreadAsync();
            var thread = new AzureAIAgentThread(_aiFoundryClient, aiFoundryThread.Value.Id);
            return thread;
        }

        return new AzureAIAgentThread(_aiFoundryClient, threadIdentifier);
    }

    public async Task<ChatCompletionAgent> CreateOrchestratorAgent(
        AzureAIAgentThread aiAgentThread,
        List<string> images)
    {
        if (InstallmentAmountAgent is null)
        {
            await InitializeAgents();
        }

        var settings = _agentSettings.Agents.Single(a => a.AgentName.Equals("orchestrator-agent", StringComparison.OrdinalIgnoreCase));

        Kernel agentKernel = _kernel.Clone();

        var subAgents = CreateSubAgents(aiAgentThread, [ InstallmentAmountAgent, Edwin, VisualisationAgent, InstallmentAmountRuleEvaluationAgent, ScenarioAgent ]);

        agentKernel.Plugins.Add(subAgents);
        agentKernel.FunctionInvocationFilters.Add(new ImageFilter(_aiFoundryClient, images));

        return new()
        {
            Name = "orchestratoragent",
            Description = "This agent orchestrates the conversation between the user and the AI agents.",
            Instructions = settings.Prompt,
            Kernel = agentKernel,
            Arguments = new KernelArguments(new PromptExecutionSettings() { FunctionChoiceBehavior = FunctionChoiceBehavior.Auto() })
        };
    }

    private async Task InitializeAgents()
    {
        await ProvisionAgents();

        InstallmentAmountAgent = await CreateAgent(_aiFoundryClient, _persistentAgents["installment-amount-agent"]);
        VisualisationAgent = await CreateAgent(_aiFoundryClient, _persistentAgents["visualisation-agent"]);
        Edwin = await CreateAgent(_aiFoundryClient, _persistentAgents["Edwin"]);
        InstallmentAmountRuleEvaluationAgent = await CreateAgent(_aiFoundryClient, _persistentAgents["installment-amount-rule-evaluation-agent"]);
        ScenarioAgent = await CreateAgent(_aiFoundryClient, _persistentAgents["scenario-agent"]);
    }

    private async Task ProvisionAgents()
    {
        var existingAgents = _aiFoundryClient.Administration.GetAgents();
        var existingAgentsDict = existingAgents
            .GroupBy(a => a.Name)
            .ToDictionary(g => g.Key, g => g.First());

        foreach (var agentConfiguration in _agentSettings.Agents)
        {
            string? configuredPrompt = agentConfiguration.Prompt;

            bool hasCodeInterpreterTool = agentConfiguration.HasCodeInterpreterTool;

            if (string.IsNullOrWhiteSpace(agentConfiguration.Prompt))
            {
                _logger.LogInformation("No prompt configured for agent '{AgentName}', skipping.", agentConfiguration.AgentName);
                continue;
            }

            var tools = new List<ToolDefinition>();
            if (hasCodeInterpreterTool)
            {
                // Add code interpreter tool if configured
                tools.Add(new CodeInterpreterToolDefinition());
            }

            if (existingAgentsDict.TryGetValue(agentConfiguration.AgentName, out var existingAgent))
            {
                // Agent exists, check if prompt matches
                if (ShouldUpdateAgent(agentConfiguration, existingAgent))
                {
                    // Update agent with new prompt
                    await _aiFoundryClient.Administration.UpdateAgentAsync(
                        existingAgent.Id,
                        instructions: agentConfiguration.Prompt,
                        model: agentConfiguration.ModelName,
                        tools: tools,
                        temperature: agentConfiguration.Temperature,
                        topP: agentConfiguration.TopP);

                    _logger.LogInformation("Updated agent '{AgentName}' with new prompt.", agentConfiguration.AgentName);
                }
                else
                {
                    _logger.LogInformation("Agent '{AgentName}' is up to date, utilize {Id}.", agentConfiguration.AgentName, existingAgent.Id);
                }
            }
            else
            {
                // Agent does not exist, create it
                var createdAgent = await _aiFoundryClient.Administration.CreateAgentAsync(
                    name: agentConfiguration.AgentName,
                    model: agentConfiguration.ModelName,
                    instructions: configuredPrompt,
                    tools: tools,
                    temperature: agentConfiguration.Temperature,
                    topP: agentConfiguration.TopP
                );
                _logger.LogInformation("Created agent '{AgentName}' with {Id}.", agentConfiguration.AgentName, createdAgent.Value.Id);
            }
        }
    }

    private static bool ShouldUpdateAgent(
        Configuration.Agent agentConfiguration,
        PersistentAgent existingAgent) =>
        existingAgent.Instructions != agentConfiguration.Prompt
        || existingAgent.Model != agentConfiguration.ModelName
        || existingAgent.Temperature != agentConfiguration.Temperature
        || existingAgent.TopP != agentConfiguration.TopP;

    private async Task<AzureAIAgent> CreateAgent(
        PersistentAgentsClient aiFoundryClient,
        PersistentAgent persistentAgent)
    {
        var agent = new AzureAIAgent(persistentAgent, aiFoundryClient);

        await AddMcpTools(agent);

        return agent;
    }

    private async Task AddMcpTools(AzureAIAgent agent)
    {
        var agentSettings = _agentSettings.Agents.Single(a => a.AgentName == agent.Name);

        if (agentSettings.AvailableMcpTools.Count > 0)
        {
            var tools = await _mcpToolProvider.GetMcpTools();

            var toolSelection = tools
                .Where(tool => agentSettings.AvailableMcpTools.Contains(tool.Name, StringComparer.OrdinalIgnoreCase))
                .Select(tool => tool.AsKernelFunction())
                .ToList();

            if (toolSelection.Count > 0)
            {
                agent.Kernel.Plugins.AddFromFunctions("MCP", toolSelection);
            }
        }
    }

    private static KernelPlugin? CreateSubAgents(
        AzureAIAgentThread aiAgentThread,
        List<Agent>? subAgents)
    {
        if (subAgents is not null && subAgents.Count > 0)
        {
            List<KernelFunction> subAgentsAsFunctions = new();

            foreach (var subAgent in subAgents)
            {
                subAgentsAsFunctions.Add(AgentAsToolFactory.CreateFromAgent(subAgent, aiAgentThread));
            }

            return KernelPluginFactory.CreateFromFunctions("AgentsPlugin", subAgentsAsFunctions);
        }

        return null;
    }
}
