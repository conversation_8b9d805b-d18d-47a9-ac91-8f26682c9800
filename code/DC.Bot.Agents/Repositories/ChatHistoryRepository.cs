using Microsoft.Extensions.Caching.Memory;
using Microsoft.SemanticKernel;

namespace DC.Bot.Agents.Repositories;

public class ChatHistoryRepository
{
    private readonly IMemoryCache _cache;
    private readonly TimeSpan _defaultExpiration = TimeSpan.FromMinutes(60);

    public ChatHistoryRepository(IMemoryCache cache)
    {
        _cache = cache;
    }

    public List<ChatMessageContent> GetHistory(long customerId, int accountId, string threadId)
    {
        var key = GenerateKey(customerId, accountId, threadId);

        _cache.TryGetValue(key, out List<ChatMessageContent> history);

        return history ?? new List<ChatMessageContent>();
    }

    public void SetChatMessagesAsync(long customerId, int accountId, string threadId, List<ChatMessageContent> history)
    {
        var key = GenerateKey(customerId, accountId, threadId);

        var options = new MemoryCacheEntryOptions
        {
            SlidingExpiration = _defaultExpiration,
            Priority = CacheItemPriority.Normal
        };

        _cache.Set(key, history, options);
    }

    private static string GenerateKey(long customerId, int accountId, string threadId) => $"chat_messages_{customerId}_{accountId}_{threadId}";
}
