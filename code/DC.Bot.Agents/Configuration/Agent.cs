namespace DC.Bot.Agents.Configuration;

public class Agent
{
    public string AgentName { get; init; }

    public string Prompt { get; init; }

    public bool HasCodeInterpreterTool { get; init; }

    public List<string> AvailableMcpTools { get; init; } = [];

    public string ModelName { get; init; }

    public string AgentId { get; init; }

    public float Temperature { get; init; }

    public float TopP { get; init; }
}
