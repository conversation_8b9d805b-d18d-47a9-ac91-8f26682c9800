using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using ModelContextProtocol.Client;

namespace DC.Bot.Agents.Mcp.Tools;

public class McpToolProvider
{
    private List<McpClientTool> _clientTools = [];

    private readonly ILogger<McpToolProvider> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public McpToolProvider(IHttpContextAccessor httpContextAccessor, ILogger<McpToolProvider> logger)
    {
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    public async Task<List<McpClientTool>> GetMcpTools()
    {
        if (_clientTools.Count != 0)
        {
            return _clientTools.ToList();
        }

        var request = _httpContextAccessor.HttpContext?.Request;

        var isLocalEnvironment = request.Host.Value.Contains("localhost");

        return isLocalEnvironment
            ? await GetMcpToolsFromLocalEnvironment()
            : await GetMcpToolsFromAcceptanceEnvironment();
    }

    private async Task<List<McpClientTool>> GetMcpToolsFromLocalEnvironment()
    {
        var endpoint = new Uri("http://localhost:5000/api/eneco/mcp");

        var mcpClient = await McpClientFactory.CreateAsync(
            new SseClientTransport(
                new SseClientTransportOptions
                {
                    Endpoint = endpoint
                }));

        var mcpClientTools = await mcpClient.ListToolsAsync();

        _clientTools = mcpClientTools.ToList();

        return _clientTools.ToList();
    }

    private async Task<List<McpClientTool>> GetMcpToolsFromAcceptanceEnvironment()
    {
        try
        {
            var endpoint =
                new Uri("https://ene-reverse-proxy-v1.acc.api-digital.enecogroup.com/v1/bot/public/eneco/mcp");

            _logger.LogInformation("Attempting to get mcp tools from {Endpoint}", endpoint);

            var mcpClient = await McpClientFactory.CreateAsync(
                new SseClientTransport(
                    new SseClientTransportOptions { Endpoint = endpoint }));

            var mcpClientTools = await mcpClient.ListToolsAsync();

            if (mcpClientTools is null || mcpClientTools.Count == 0)
            {
                endpoint = new Uri("https://acc.api-digital.enecogroup.com/v1/bot/public/eneco/mcp");

                _logger.LogInformation("Attempting to get mcp tools from {Endpoint}", endpoint);

                mcpClient = await McpClientFactory.CreateAsync(
                    new SseClientTransport(
                        new SseClientTransportOptions
                        {
                            Endpoint = endpoint
                        }));

                mcpClientTools = await mcpClient.ListToolsAsync();
            }

            _clientTools = mcpClientTools.ToList();

            return _clientTools.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Could not connect to primary mcp endpoint, attempting second");

            var endpoint = new Uri("https://acc.api-digital.enecogroup.com/v1/bot/public/eneco/mcp");

            _logger.LogInformation("Attempting to get mcp tools from {Endpoint}", endpoint);

            var mcpClient = await McpClientFactory.CreateAsync(
                new SseClientTransport(
                    new SseClientTransportOptions
                    {
                        Endpoint = endpoint
                    }));

            var mcpClientTools = await mcpClient.ListToolsAsync();

            _clientTools = mcpClientTools.ToList();

            return _clientTools.ToList();
        }
    }
}
