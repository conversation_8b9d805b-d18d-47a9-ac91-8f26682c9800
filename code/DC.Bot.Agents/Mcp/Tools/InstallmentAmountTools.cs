using System.ComponentModel;
using DC.Bot.Agents.Services;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Bot.BusinessLogic.Models.Financial;
using DC.Financials.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using ModelContextProtocol.Server;

namespace DC.Bot.Agents.Mcp.Tools;

[McpServerToolType]
[Description("These tools are used whenever something installment amount related is asked by the customer")]
public class InstallmentAmountTools
{
    [McpServerTool]
    [Description("This MCP tool gets the customers current installment amount, advice. " +
                 "It also informs whether the customer can change their installment amount.")]
    public static async Task<AdvancePaymentAdviceV2Response> GetInstallmentAmountAdvice(
        IFinancialsService financialsService,
        [Description("The customer identifier")] long customerId,
        [Description("The customer account identifier")] int accountId)
    {
        var dialogData = new DialogData
        {
            Customer = new CustomerData { Label = Label.Eneco },
            Channel = BotChannel.Web,
            Verification = new VerificationData { CustomerId = customerId },
            SelectedAccount = new AccountData { AccountId = accountId }
        };

        var result = await financialsService.GetAdvancePaymentAdviceV2(dialogData);

        return result;
    }

    [McpServerTool]
    [Description("This MCP tool can adjust the customer's installment amount")]
    public static async Task<AdvancedPaymentAdviceExtraStatus> AdjustInstallmentAmount(
        IFinancialsService financialsService,
        [Description("The customer identifier")] long customerId,
        [Description("The customer account identifier")] int accountId,
        [Description("The installment amount the customer desires")] int newInstallmentAmount)
    {
        var dialogData = new DialogData
        {
            Customer = new CustomerData { Label = Label.Eneco },
            Channel = BotChannel.Web,
            Verification = new VerificationData { CustomerId = customerId },
            SelectedAccount = new AccountData { AccountId = accountId }
        };

        var result = await financialsService.UpdateAdvancePaymentAmount(dialogData, newInstallmentAmount);

        return result;
    }

    [McpServerTool]
    [Description("This MCP tool can search through a knowledge base regarding installment amount")]
    public static async Task<string> SearchKnowledgeBase(
        ISearchService searchService,
        string query)
    {
        return await searchService.Search(query);
    }
}
