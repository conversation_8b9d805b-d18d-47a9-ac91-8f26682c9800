using System.ComponentModel;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Domain.Models.Agreements;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using ModelContextProtocol.Server;

namespace DC.Bot.Agents.Mcp.Tools;

[McpServerToolType]
public class AgreementTools
{
    [McpServerTool]
    public static async Task<IList<Agreement>> GetActiveAgreements(
        ICustomersService customersService,
        [Description("The customer identifier")] long customerId,
        [Description("The customer account identifier")] int accountId)
    {
        var dialogData = new DialogData
        {
            Customer = new CustomerData { Label = Label.Eneco },
            Channel = BotChannel.Web,
            Verification = new VerificationData { CustomerId = customerId },
            SelectedAccount = new AccountData { AccountId = accountId }
        };

        var result = await customersService.GetAgreements(dialogData, onlyActive: true);

        return result;
    }
}
