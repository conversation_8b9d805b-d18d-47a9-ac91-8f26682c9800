using System.ComponentModel;
using DC.Bot.BusinessLogic.Interfaces;
using DC.Bot.BusinessLogic.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using ModelContextProtocol.Server;

namespace DC.Bot.Agents.Mcp.Tools;

[McpServerToolType]
[Description("Whenever a customer asks about usage, this is the set of tools to use")]
public class UsageTools
{
    [McpServerTool]
    [Description("This MCP tool is capable of getting the customers usages from the past half year.")]
    public static async Task<UsagesSummaryModel> GetPastHalfYearUsages(
        IUsagesService usagesService,
        [Description("The customer identifier")] long customerId,
        [Description("The customer account identifier")] int accountId)
    {
        var dialogData = new DialogData
        {
            Customer = new CustomerData { Label = Label.Eneco },
            Channel = BotChannel.Web,
            Verification = new VerificationData { CustomerId = customerId },
            SelectedAccount = new AccountData { AccountId = accountId }
        };

        var result = await usagesService.GetPastHalfYearUsages(dialogData, accountId);

        return result;
    }
}
