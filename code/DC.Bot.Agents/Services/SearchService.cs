using Azure.Search.Documents.Indexes;
using Azure.Search.Documents.Models;
using DC.Bot.Agents.Configuration;
using DC.Bot.Agents.Models;
using Microsoft.Extensions.Options;
using OpenAI.Embeddings;

namespace DC.Bot.Agents.Services;

public class SearchService : ISearchService
{
    private readonly SearchIndexClient _searchIndexClient;
    private readonly EmbeddingClient _embeddingClient;
    private readonly AzureAiSearchSettings _azureAiSearchSettings;

    public SearchService(
        SearchIndexClient searchIndexClient,
        EmbeddingClient embeddingClient,
        IOptions<AzureAiSearchSettings> azureAiSearchSettings)
    {
        _searchIndexClient = searchIndexClient;
        _embeddingClient = embeddingClient;
        _azureAiSearchSettings = azureAiSearchSettings.Value;
    }

    public async Task<string> Search(string query)
    {
        var embeddings = await _embeddingClient.GenerateEmbeddingAsync(query);

        var result = await SearchVector(embeddings.Value.ToFloats().ToArray());

        return string.Join(Environment.NewLine, result.Select(r => r.Content));
    }

    private async Task<List<SearchDocumentModel>> SearchVector(float[] queryVector)
    {
        var searchClient = _searchIndexClient.GetSearchClient(_azureAiSearchSettings.IndexName);

        var options = new Azure.Search.Documents.SearchOptions
        {
            Size = 5,
            Select = { "id", "content", "title", "summary" },
            VectorSearch = new VectorSearchOptions
            {
                Queries =
                {
                    new VectorizedQuery(queryVector)
                    {
                        KNearestNeighborsCount = 5,
                        Fields = { "content_vector" }
                    }
                }
            }
        };

        var response = await searchClient.SearchAsync<SearchDocument>("*", options);

        var output = new List<SearchDocumentModel>();
        await foreach (SearchResult<SearchDocument> result in response.Value.GetResultsAsync())
        {
            output.Add(new SearchDocumentModel
            {
                Id = result.Document["id"] as string,
                Content = result.Document["content"] as string,
                Title = result.Document["title"] as string,
                Summary = result.Document["summary"] as string,
            });
        }

        return output;
    }
}
