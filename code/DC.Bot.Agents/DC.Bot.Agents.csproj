﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Azure.AI.Agents.Persistent" />
      <PackageReference Include="Azure.Search.Documents" />
      <PackageReference Include="Microsoft.SemanticKernel" />
      <PackageReference Include="Microsoft.SemanticKernel.Agents.AzureAI" />
      <PackageReference Include="Microsoft.SemanticKernel.Agents.Core" />
      <PackageReference Include="ModelContextProtocol" />
      <PackageReference Include="ModelContextProtocol.AspNetCore" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\DC.Bot.BusinessLogic\DC.Bot.BusinessLogic.csproj" />
    </ItemGroup>

</Project>
