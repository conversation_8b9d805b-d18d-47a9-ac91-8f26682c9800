using Azure;
using Azure.AI.OpenAI;
using Azure.Identity;
using Azure.Search.Documents.Indexes;
using DC.Bot.Agents.Agents;
using DC.Bot.Agents.Configuration;
using DC.Bot.Agents.Mcp.Tools;
using DC.Bot.Agents.Repositories;
using DC.Bot.Agents.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents.AzureAI;
using OpenAI.Embeddings;

#pragma warning disable SKEXP0110

namespace DC.Bot.Agents.Extensions;

public static class IServiceCollectionExtensions
{
    public static IServiceCollection AddVirtualAgent(
        this IServiceCollection serviceCollection,
        IConfiguration configuration)
    {
        serviceCollection
            .BootstrapMcpServer()
            .AddAiFoundry(configuration)
            .AddOpenAiChatCompletion(configuration)
            .AddAzureAiSearch(configuration);

        serviceCollection.AddSingleton<AgentService>();
        serviceCollection.AddSingleton<McpToolProvider>();
        serviceCollection.AddMemoryCache();
        serviceCollection.AddSingleton<ChatHistoryRepository>();

        return serviceCollection;
    }

    private static IServiceCollection BootstrapMcpServer(
        this IServiceCollection serviceCollection)
    {
        serviceCollection
            .AddMcpServer()
            .WithHttpTransport()
            .WithTools<InstallmentAmountTools>()
            .WithTools<AgreementTools>()
            .WithTools<UsageTools>();

        return serviceCollection;
    }

    private static IServiceCollection AddAiFoundry(
        this IServiceCollection serviceCollection,
        IConfiguration configuration)
    {
        var endpoint = configuration.GetValue<string>("AiFoundrySettings:AiFoundryProjectEndpoint");

        var aiFoundryClient = AzureAIAgent.CreateAgentsClient(endpoint, new DefaultAzureCredential());

        serviceCollection.AddSingleton(aiFoundryClient);

        return serviceCollection;
    }

    private static IServiceCollection AddOpenAiChatCompletion(
        this IServiceCollection serviceCollection,
        IConfiguration configuration)
    {
        serviceCollection
            .AddKernel()
            .AddAzureOpenAIChatCompletion(
                configuration.GetValue<string>("AiFoundrySettings:ModelName"),
                configuration.GetValue<string>("AiFoundrySettings:OpenAiBaseUrl"),
                new DefaultAzureCredential());

        return serviceCollection;
    }

    private static IServiceCollection AddAzureAiSearch(
        this IServiceCollection serviceCollection,
        IConfiguration configuration)
    {
        serviceCollection.Configure<AzureAiSearchSettings>(configuration.GetSection("AzureAiSearchSettings"));

        serviceCollection.AddSingleton<EmbeddingClient>(provider =>
        {
            var options = provider.GetRequiredService<IOptions<AzureAiSearchSettings>>().Value;

            var endpoint = new Uri(options.OpenAiBaseUrl);
            var credential = new DefaultAzureCredential();
            var azureOpenAiClient = new AzureOpenAIClient(endpoint, credential);

            return azureOpenAiClient.GetEmbeddingClient(options.EmbeddingDeploymentName);
        });

        serviceCollection.AddSingleton<SearchIndexClient>(provider =>
        {
            var options = provider.GetRequiredService<IOptions<AzureAiSearchSettings>>().Value;

            var endpoint = new Uri(options.Endpoint);
            var credential = new AzureKeyCredential(options.ApiKey);
            return new SearchIndexClient(endpoint, credential);
        });

        serviceCollection.AddSingleton<ISearchService, SearchService>();

        return serviceCollection;
    }
}
