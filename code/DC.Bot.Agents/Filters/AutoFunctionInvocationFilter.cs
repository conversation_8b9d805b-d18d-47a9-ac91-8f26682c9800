using DC.Bot.Agents.Models;
using Microsoft.SemanticKernel;

namespace DC.Bot.Agents.Filters;

public class AutoFunctionInvocationFilter : IAutoFunctionInvocationFilter
{
    private readonly List<ToolCall> _toolCallList;

    public AutoFunctionInvocationFilter(List<ToolCall> toolCallList)
    {
        this._toolCallList = toolCallList;
    }

    public async Task OnAutoFunctionInvocationAsync(AutoFunctionInvocationContext context, Func<AutoFunctionInvocationContext, Task> next)
    {
        await next(context);
        var parametersList = new List<ToolParameter>();

        if (context.Arguments != null)
        {
            parametersList = context.Arguments.Select(p => new ToolParameter { Key = p.Key, Value = p.Value?.ToString() }).ToList();
        }
        var response = context.Result.GetValue<ChatMessageContent[]>()?.First();
        ToolCall toolCallInfo = new ToolCall
        {
            FunctionName = context.Function.Name,
            PluginName = context.Function.PluginName!,
            Parameters = parametersList,
            Response = response?.Content
        };

        _toolCallList.Add(toolCallInfo);
    }
}
