﻿using DC.Bot.Repositories.Interfaces;
using DC.Domain.Models.NextBestAction;
using DC.Domain.Models.Products;
using DC.Domain.Models.Products.ServiceOrders;
using DC.Products.Client;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using System.Threading.Tasks;

namespace DC.Bot.Repositories;

public class DcProductsRepository : DcBaseBotToPapiRepository, IDcProductsRepository
{
    private readonly IDCApiProducts _client;

    public DcProductsRepository(
        IConfiguration configuration,
        IHttpContextAccessor contextAccessor,
        ILoggerFactory loggerFactory,
        IMemoryCache memoryCache,
        IDCApiProducts client) : base(configuration, contextAccessor, loggerFactory, memoryCache)
    {
        _client = client;
    }

    public async Task<HttpOperationResponse<object>> GetProductRates(Label label, BotChannel botChannel, long customerId, int accountId) =>
        await _client.DCApiProductsProductRatesGetProductRatesGETWithHttpMessagesAsync(label, customerId, accountId, false,
            await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);

    // discontinueProductType.ToEnumMemberValue() return toonservice, because this is the product that should be cancelled, however the intake needs 'toon' therefore toString() is used
    public async Task<HttpOperationResponse<object>> GetProductDiscontinueIntake(Label label, BotChannel botChannel, long customerId, DiscontinueProductType discontinueProductType) =>
        await _client.DCApiProductsProductsGetProductDiscontinueIntakeGETWithHttpMessagesAsync(
            label, customerId, discontinueProductType, await GetBotHeaders(botChannel).ConfigureAwait(false))
        .ConfigureAwait(false);

    public async Task<HttpOperationResponse<object>> PutProductOrderV2(Label label, RequestDataProductOrderV2Model productOrder) =>
        await _client.DCApiProductsOrdersV2CreateOrderPUTWithHttpMessagesAsync(
            productOrder, label, GetHeaders(label, authHeaderRequired: false))
        .ConfigureAwait(false);

    public async Task<HttpOperationResponse<object>> GetKetelComfortProductDetails(Label label, BotChannel botChannel, long customerId, int accountId) =>
        await _client.DCApiProductsProductsGetKetelComfortProductDetailsGETWithHttpMessagesAsync(label, customerId, accountId,
            await GetBotHeaders(botChannel, false).ConfigureAwait(false)).ConfigureAwait(false);

    public async Task<HttpOperationResponse<object>> GetHardwareProductSpecifications(Label label, BotChannel botChannel, string productId = null) =>
        await _client.DCApiProductsProductSpecificationsGetHardwareProductSpecificationsGETWithHttpMessagesAsync(label, null, productId,
            await GetBotHeaders(botChannel, false).ConfigureAwait(false)).ConfigureAwait(false);

    public async Task<HttpOperationResponse<object>> CreateServiceOrder(Label label, BotChannel botChannel, long customerId, int accountId, RequestDataServiceOrderRequestModel requestDataServiceOrderRequestModel) =>
        await _client.DCApiProductsServiceOrderCreateServiceOrderPUTWithHttpMessagesAsync(label, customerId, accountId, requestDataServiceOrderRequestModel,
            await GetBotHeaders(botChannel, false).ConfigureAwait(false)).ConfigureAwait(false);

    public async Task<HttpOperationResponse<object>> GetServiceOrderPlanLink(Label label, BotChannel botChannel, string orderId, PlanLinkType? type = null) =>
        await _client.DCApiProductsServiceOrderGetServiceOrderPlanLinkGETWithHttpMessagesAsync(label, orderId, type,
            await GetBotHeaders(botChannel, false).ConfigureAwait(false)).ConfigureAwait(false);

    public async Task<HttpOperationResponse<object>> GetServiceAgreementsForAgreementId(Label label, BotChannel botChannel, int agreementId, bool? activeSelfService = true) =>
        await _client.DCApiProductsServiceOrderGetServiceOrderByAgreementIdGETWithHttpMessagesAsync(label, agreementId, activeSelfService,
            await GetBotHeaders(botChannel, false).ConfigureAwait(false)).ConfigureAwait(false);

    public async Task<HttpOperationResponse<object>> GetServiceOrdersFromServiceAgreement(Label label, BotChannel botChannel, string serviceAgreementId, ServiceOrderStatus? orderStatus = null) =>
        await _client.DCApiProductsServiceOrderGetServiceOrdersByServiceAgreementGETWithHttpMessagesAsync(label, serviceAgreementId, orderStatus,
            await GetBotHeaders(botChannel, false).ConfigureAwait(false)).ConfigureAwait(false);

    /// <summary>
    /// Get Products of the Customer
    /// </summary>
    /// <param name="label"></param>
    /// <param name="botChannel"></param>
    /// <param name="customerId"></param>
    /// <param name="includeConditionsLabel"></param>
    /// <param name="active"></param>
    /// <param name="includeProductRates"></param>
    /// <returns></returns>
    public async Task<HttpOperationResponse<object>> GetCustomerProducts(Label label, BotChannel botChannel,
        long customerId, bool? active = null, bool includeProductRates = false, bool includeConditionsLabel = false) =>
        await _client.DCApiProductsProductsGetCustomerProductsGETWithHttpMessagesAsync(label, customerId, active, includeProductRates, false, includeConditionsLabel, await GetBotHeaders(botChannel, false)).ConfigureAwait(false);

    /// <summary>
    /// Get Products of the Customer Account
    /// </summary>
    /// <param name="label"></param>
    /// <param name="customerId"></param>
    /// <param name="accountId"></param>
    /// <returns></returns>
    public async Task<HttpOperationResponse<object>> GetCustomerAccountProducts(Label label, BotChannel botChannel, long customerId, int accountId, bool? active = null, bool includeProductRates = false, bool includeConditionsLabel = false) =>
        await _client.DCApiProductsProductsGetCustomerAccountProductsGETWithHttpMessagesAsync(label, customerId, accountId, active, includeProductRates, false, includeConditionsLabel
            , customHeaders: await GetBotHeaders(botChannel, false)).ConfigureAwait(false);

    public async Task<HttpOperationResponse<object>> GetNextBestActions(Label label, BotChannel botChannel, long customerId, int? servingPointId = null) =>
        await _client.DCApiProductsNextBestActionV2GetNextBestActionGETWithHttpMessagesAsync(label, customerId, Channel.Chatbot, servingPointId, await GetBotHeaders(botChannel, false).ConfigureAwait(false)).ConfigureAwait(false);

    //Fine calculation
    public async Task<HttpOperationResponse<object>> CalculateFineCancelledProduct(Label label, RequestDataProductCancelRequestModel request) =>
        await _client.DCApiProductsFineCalculationCalculateFineCancelledProductPOSTWithHttpMessagesAsync(label, request, GetHeaders(label, false)).ConfigureAwait(false);

    public async Task<HttpOperationResponse<object>> CalculateFineSwitchedProduct(Label label, RequestDataProductSwitchRequestModel request) =>
        await _client.DCApiProductsFineCalculationCalculateFineSwitchedProductPOSTWithHttpMessagesAsync(label, request, GetHeaders(label, false)).ConfigureAwait(false);
    public async Task<HttpOperationResponse<object>> GetCustomerRelocations(Label label, BotChannel botChannel, long customerId) =>
        await _client.DCApiProductsRelocationsGetRelocationsGETWithHttpMessagesAsync(label, customerId, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);

}
