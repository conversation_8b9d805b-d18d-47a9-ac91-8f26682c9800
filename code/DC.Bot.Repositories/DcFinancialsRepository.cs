﻿using DC.Bot.Repositories.Interfaces;
using DC.Domain.Exceptions.ResponseModels;
using DC.Financials.Client;
using DC.Financials.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using System.Threading.Tasks;

namespace DC.Bot.Repositories
{
    public class DcFinancialsRepository : DcBaseBotToPapiRepository, IDcFinancialsRepository
    {
        private readonly IDCApiFinancials _client;

        public DcFinancialsRepository(
            IConfiguration configuration,
            IHttpContextAccessor contextAccessor,
            ILoggerFactory loggerFactory,
            IMemoryCache memoryCache,
            IDCApiFinancials client) : base(configuration, contextAccessor, loggerFactory, memoryCache)
        {
            _client = client;
        }

        public async Task<HttpOperationResponse<object>> GetAdvancePayment(Label label, BotChannel botChannel, long customerId, int accountId) =>
            await _client.DCApiFinancialsAdvancePaymentGetAdvancePaymentGETWithHttpMessagesAsync(label, customerId, accountId, false, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);
        public async Task<HttpOperationResponse<object>> GetFinancialsPreferences(Label label, BotChannel botChannel, long customerId, int accountId) =>
            await _client.DCApiFinancialsPreferencesGetPreferencesGETWithHttpMessagesAsync(label, customerId, accountId, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);
        public async Task<HttpOperationResponse<object>> GetPaymentArrangement(Label label, BotChannel botChannel, long customerId, int accountId) =>
            await _client.DCApiFinancialsPaymentV2GetPaymentArrangementGETWithHttpMessagesAsync(label, customerId, accountId, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);
        public async Task<HttpOperationResponse<object>> PutAdvancePayment(Label label, BotChannel botChannel, long customerId, int accountId, RequestDataAdvancePaymentChangeRequest request) =>
            await _client.DCApiFinancialsAdvancePaymentPutAdvancePaymentPUTWithHttpMessagesAsync(request, label, customerId, accountId, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);
        public async Task<HttpOperationResponse<object>> GetPaymentPreferences(Label label, BotChannel botChannel, long customerId, int accountId) =>
            await _client.DCApiFinancialsPreferencesGetPreferencesGETWithHttpMessagesAsync(label, customerId, accountId, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);
        public async Task<HttpOperationResponse<ErrorResponse>> UpdatePaymentPreferences(Label label, BotChannel botChannel, long customerId, int accountId, RequestDataFinancialPreferences request) =>
            await _client.DCApiFinancialsPreferencesPatchPreferencesPATCHWithHttpMessagesAsync(label, customerId, accountId, request, true, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);
        public async Task<HttpOperationResponse<object>> GetAdvancePaymentAdviceV2(Label label, BotChannel botChannel, long customerId, int accountId) =>
            await _client.DCApiFinancialsAdvancePaymentV2GetAdvancePaymentAdviceV2GETWithHttpMessagesAsync(label, customerId, accountId, OneMeterSource.App, false, true, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);
    }
}
