﻿using DC.Bot.Repositories.Interfaces;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using DC.UserAccounts.Client;
using DC.UserAccounts.Client.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using System.Threading.Tasks;

namespace DC.Bot.Repositories
{
    public class DcUserAccountsRepository : DcBaseBotToPapiRepository, IDcUserAccountsRepository
    {
        private readonly IDCApiUserAccounts _client;

        public DcUserAccountsRepository(
            IConfiguration configuration,
            IHttpContextAccessor contextAccessor,
            ILoggerFactory loggerFactory,
            IMemoryCache memoryCache,
            IDCApiUserAccounts client) : base(configuration, contextAccessor, loggerFactory, memoryCache)
        {
            _client = client;
        }

        public async Task<HttpOperationResponse<object>> GetUsername(Label label, BotChannel botChannel, long customerId, RequestDataValidationRequest request) =>
            await _client.DCApiUserAccountsUserAccountsEmailForgottenUsernamePOSTWithHttpMessagesAsync(request, label, customerId, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);
    }
}
