﻿using DC.Bot.Repositories.Interfaces;
using DC.Domain.Exceptions.ResponseModels;
using DC.Domain.Models.Usages;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using DC.Usages.Client;
using DC.Usages.Client.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using System;
using System.Threading.Tasks;

namespace DC.Bot.Repositories
{
    public class DcUsagesRepository : DcBaseBotToPapiRepository, IDcUsagesRepository
    {
        private readonly IDCApiUsages _client;

        public DcUsagesRepository(
            IConfiguration configuration,
            IHttpContextAccessor contextAccessor,
            ILoggerFactory loggerFactory,
            IMemoryCache memoryCache,
            IDCApiUsages client) : base(configuration, contextAccessor, loggerFactory, memoryCache)
        {
            _client = client;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Major Code Smell", "S107:Methods should not have too many parameters", Justification = "All params are needed in the call to the PAPI")]
        //// previous year set on false because is not used in dialogs
        public async Task<HttpOperationResponse<object>> GetUsages(Label label, BotChannel botChannel, long customerId, int accountId, DateTime startDate, DateTime endDate, UsageAggregation aggregation = UsageAggregation.Month, UsageInterval interval = UsageInterval.Month) =>
            await _client.DCApiUsagesUsageV2GetUsagesGETWithHttpMessagesAsync(label, customerId, accountId, aggregation, interval, startDate, endDate, false, false, false, false, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);

        public async Task<HttpOperationResponse<ErrorResponse>> RegisterReportRequest(Label label, BotChannel botChannel, long customerId, int accountId, RequestDataReportRequestModel requestModel) =>
            await _client.DCApiUsagesReadingsV2RegisterReportRequestPUTWithHttpMessagesAsync(label, customerId, accountId, requestModel, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);

        public async Task<HttpOperationResponse<object>> GetReadingsHistory(Label label, BotChannel botChannel, long customerId, int accountId) =>
            await _client.DCApiUsagesReadingsGetReadingsGETWithHttpMessagesAsync(label, customerId, accountId, DateTime.Now, 7, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);

        public async Task<HttpOperationResponse<object>> GetOutstandingReadings(Label label, BotChannel botChannel, long customerId, int accountId) =>
            await _client.DCApiUsagesReadingsGetOutstandingReadingsByAccountIdGETWithHttpMessagesAsync(label, customerId, accountId, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);

        public async Task<HttpOperationResponse<object>> GetReading(Label label, BotChannel botChannel, long customerId, int accountId) =>
            await _client.DCApiUsagesReadingsV2GetReadingGETWithHttpMessagesAsync(label, customerId, accountId, false, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);

        public async Task<HttpOperationResponse<object>> GetSmartMeterInterruption(Label label, BotChannel botChannel, long customerId, int accountId) =>
            await _client.DCApiUsagesMeterSmartMeterInterruptionGETWithHttpMessagesAsync(label, customerId, accountId, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);

        public async Task<HttpOperationResponse<object>> GetMandate(Label label, BotChannel botChannel, long customerId, int accountId, ServiceProductType serviceProductType) =>
            await _client.DCApiUsagesMandateGetMandatesGETWithHttpMessagesAsync(label, customerId, accountId, serviceProductType, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);

        public async Task<HttpOperationResponse<object>> EnableMandate(Label label, BotChannel botChannel, long customerId, int accountId, ServiceProductType serviceProductType) =>
            await _client.DCApiUsagesMandateEnableMandatePUTWithHttpMessagesAsync(label, customerId, accountId, serviceProductType, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);

        public async Task<HttpOperationResponse<ErrorResponse>> DisableMandate(Label label, BotChannel botChannel, long customerId, int accountId, ServiceProductType serviceProductType) =>
            await _client.DCApiUsagesMandateDisableMandateDELETEWithHttpMessagesAsync(label, customerId, accountId, serviceProductType, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);
    }
}
