﻿using DC.Domain.Exceptions;
using DC.Repositories.Base;
using DC.Security.ActiveDirectory;
using DC.Telemetry.Models;
using DC.Utilities.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DC.Bot.Repositories;

public class DcBaseBotToPapiRepository(IConfiguration configuration,
        IHttpContextAccessor contextAccessor,
        ILoggerFactory loggerFactory,
        IMemoryCache memoryCache)
    : DcBaseRepository(configuration, contextAccessor, loggerFactory)
{
    public async Task<Dictionary<string, List<string>>> GetBotHeaders(BotChannel botChannel, bool passthroughLanguage = false)
    {
        Dictionary<string, List<string>> headers = await GetBotServiceAccountHeaders(passthroughLanguage);
        headers.Add(RequestHeaderKeys.BotChannel, new List<string>
        {
            botChannel.ToString()
        });
        return headers;
    }

    /// <summary>
    /// Should NOT be used anymore in a bot-to-papi context.
    /// </summary>
    /// <param name="passthroughLanguage"></param>
    /// <returns></returns>
    protected async Task<Dictionary<string, List<string>>> GetServiceAccountHeaders(bool passthroughLanguage)
    {
        var error = "GetBotHeaders should be used instead of GetServiceAccountHeaders because in this case the BotChannel is not logged.";
        // will not break production for this violation, but log an error instead.
        if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == Environments.Production)
        {
            _logger.LogError(error);
            return await GetBotServiceAccountHeaders(passthroughLanguage).ConfigureAwait(false);
        }
        throw new NotImplementedException(error);
    }

    /// <summary>
    /// Get Bot Service Account Headers
    /// </summary>
    /// <param name="passthroughLanguage"></param>
    /// <returns></returns>
    private async Task<Dictionary<string, List<string>>> GetBotServiceAccountHeaders(bool passthroughLanguage)
    {
        var headers = GetRuntimeHintAndLanguageHeaders(passthroughLanguage);

        headers.Add("apikey",
            new List<string> { _configuration["AuthenticationSettings:DigitalCore:ServiceAccount:Apikey"] });
        headers.Add("Authorization", new List<string> { await GetServiceAccountBearerToken().ConfigureAwait(false) });
        return headers;
    }

    /// <summary>
    /// Gets the serviceaccount bearer token based on the configuration.
    /// </summary>
    /// <returns></returns>
    private async Task<string> GetServiceAccountBearerToken()
    {
        var tokenProvider = new BearerToken(memoryCache);

        var clientId = _configuration["AuthenticationSettings:DigitalCore:ServiceAccount:ClientId"];
        var secret = _configuration["AuthenticationSettings:DigitalCore:ServiceAccount:ClientSecret"];
        var tenant = _configuration["AuthenticationSettings:DigitalCore:ServiceAccount:Tenant"];
        var audience = _configuration[$"AuthenticationSettings:DigitalCore:ServiceAccount:Audience"];

        if (string.IsNullOrWhiteSpace(clientId))
            throw new TechnicalException(new Guid("5f5c841f-8800-43b2-8d2d-2fc6f4b9dcd6"), "AuthenticationSettings-DigitalCore-ServiceAccount-ClientId is missing in the appsettings.");
        if (string.IsNullOrWhiteSpace(secret) || secret.Length < 6)
            throw new TechnicalException(new Guid("acc2509b-7775-44e9-85ac-75d1f95e2337"), "AuthenticationSettings-DigitalCore-ServiceAccount-ClientSecret is missing in the appsettings.");
        if (string.IsNullOrWhiteSpace(tenant))
            throw new TechnicalException(new Guid("44d5f53f-4cfa-49ed-b897-35d8cfe4d857"), "AuthenticationSettings-DigitalCore-ServiceAccount-Tenant is missing in the appsettings.");
        if (string.IsNullOrWhiteSpace(audience))
            throw new TechnicalException(new Guid("3647d9ff-ed25-4294-9e67-fecc8f9938fe"), "AuthenticationSettings-DigitalCore-ServiceAccount-Audience is missing in the appsettings.");

        _logger.LogDebug($"Bearer token request ${nameof(clientId)}: {clientId}");
        _logger.LogDebug($"Bearer token request ${nameof(secret)}:  {secret[..6]}");
        _logger.LogDebug($"Bearer token request ${nameof(tenant)}: {tenant}");
        _logger.LogDebug($"Bearer token request ${nameof(audience)}: {audience}");

        // get a valid access token with these settings and store it in the cache for 1 hour
        return await tokenProvider.GetAccessTokenAsync(audience, clientId, secret, tenant).ConfigureAwait(false);
    }
}
