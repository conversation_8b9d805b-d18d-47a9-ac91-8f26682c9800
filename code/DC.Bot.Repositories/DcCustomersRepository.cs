﻿using DC.Bot.Repositories.Interfaces;
using DC.Customers.Client;
using DC.Customers.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using System;
using System.Threading.Tasks;

namespace DC.Bot.Repositories
{
    public class DcCustomersRepository : DcBaseBotToPapiRepository, IDcCustomersRepository
    {
        private readonly IDCApiCustomers _client;

        public DcCustomersRepository(
            IConfiguration configuration,
            IHttpContextAccessor contextAccessor,
            ILoggerFactory loggerFactory,
            IMemoryCache memoryCache,
            IDCApiCustomers client) : base(configuration, contextAccessor, loggerFactory, memoryCache)
        {
            _client = client;
        }

        public async Task<HttpOperationResponse<object>> VerifyCustomer(Label label, BotChannel botChannel, RequestDataCustomerVerification request) =>
            await _client.DCApiCustomersCustomersCustomerVerificationPOSTWithHttpMessagesAsync(label, request, Guid.NewGuid(), await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);
        public async Task<HttpOperationResponse<object>> GetCustomer(Label label, BotChannel botChannel, long customerId) =>
            await _client.DCApiCustomersCustomersGetCustomerGETWithHttpMessagesAsync(label, customerId, true, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);
        public async Task<HttpOperationResponse<object>> GetCustomerV2(Label label, BotChannel botChannel, long customerId) =>
            await _client.DCApiCustomersCustomersV2GetCustomerV2GETWithHttpMessagesAsync(label, customerId, await GetBotHeaders(botChannel).ConfigureAwait(false)).ConfigureAwait(false);
        public async Task<HttpOperationResponse<object>> UpdateProfile(Label label, BotChannel botChannel, long customerId, RequestDataCustomerMutation request) =>
                 await _client.DCApiCustomersCustomersPatchCustomerProfilePATCHWithHttpMessagesAsync(request, label, customerId, customHeaders: await GetBotHeaders(botChannel, false).ConfigureAwait(false)).ConfigureAwait(false);
        public async Task<HttpOperationResponse<object>> GetAgreements(Label label, BotChannel botChannel, long customerId, int accountId, bool? onlyActive = null, bool includeLastYearProductUsage = false) =>
            await _client.DCApiCustomersAccountV3GetAgreementsV3GET1WithHttpMessagesAsync(label, customerId, accountId, onlyActive: onlyActive, includeLastYearUsages: includeLastYearProductUsage, customHeaders: await GetBotHeaders(botChannel, false).ConfigureAwait(false)).ConfigureAwait(false);
        public async Task<HttpOperationResponse<object>> GetCustomerAgreements(Label label, BotChannel botChannel, long customerId) =>
            await _client.DCApiCustomersAccountV3GetAgreementsV3GETWithHttpMessagesAsync(label, customerId, customHeaders: await GetBotHeaders(botChannel, false).ConfigureAwait(false)).ConfigureAwait(false);
        public async Task<HttpOperationResponse<object>> GetPaymentPlan(Label label, BotChannel botChannel, long customerId, int accountId) =>
            await _client.DCApiCustomersAccountV2GetAccountPaymentPlanV2GETWithHttpMessagesAsync(label, customerId, accountId, await GetBotHeaders(botChannel, false).ConfigureAwait(false)).ConfigureAwait(false);
        public async Task<HttpOperationResponse<object>> GetCustomerOrders(Label label, BotChannel botChannel, long customerId) =>
            await _client.DCApiCustomersOrdersGetCustomerOrdersGETWithHttpMessagesAsync(label, customerId, await GetBotHeaders(botChannel, false).ConfigureAwait(false)).ConfigureAwait(false);
    }
}