﻿using DC.Customers.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using Microsoft.Rest;
using System.Threading.Tasks;

namespace DC.Bot.Repositories.Interfaces
{
    public interface IDcCustomersRepository
    {
        Task<HttpOperationResponse<object>> VerifyCustomer(Label label, BotChannel botChannel, RequestDataCustomerVerification request);
        Task<HttpOperationResponse<object>> GetCustomer(Label label, BotChannel botChannel, long customerId);
        Task<HttpOperationResponse<object>> GetCustomerV2(Label label, BotChannel botChannel, long customerId);
        Task<HttpOperationResponse<object>> UpdateProfile(Label label, BotChannel botChannel, long customerId, RequestDataCustomerMutation request);
        Task<HttpOperationResponse<object>> GetAgreements(Label label, BotChannel botChannel, long customerId, int accountId, bool? onlyActive = null, bool includeLastYearProductUsage = false);
        Task<HttpOperationResponse<object>> GetCustomerAgreements(Label label, BotChannel botChannel, long customerId);
        Task<HttpOperationResponse<object>> GetPaymentPlan(Label label, BotChannel botChannel, long customerId, int accountId);
        Task<HttpOperationResponse<object>> GetCustomerOrders(Label label, BotChannel botChannel, long customerId);
    }
}