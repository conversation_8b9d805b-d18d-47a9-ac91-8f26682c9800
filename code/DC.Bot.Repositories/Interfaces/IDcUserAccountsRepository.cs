﻿using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using DC.UserAccounts.Client.Models;
using Microsoft.Rest;
using System.Threading.Tasks;

namespace DC.Bot.Repositories.Interfaces
{
    public interface IDcUserAccountsRepository
    {
        Task<HttpOperationResponse<object>> GetUsername(Label label, BotChannel botChannel, long customerId, RequestDataValidationRequest request);
    }
}
