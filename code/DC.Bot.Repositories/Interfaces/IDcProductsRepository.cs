﻿using DC.Domain.Models.Products;
using DC.Domain.Models.Products.ServiceOrders;
using DC.Products.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using Microsoft.Rest;
using System.Threading.Tasks;

namespace DC.Bot.Repositories.Interfaces;

public interface IDcProductsRepository
{
    Task<HttpOperationResponse<object>> GetProductRates(Label label, BotChannel botChannel, long customerId, int accountId);
    Task<HttpOperationResponse<object>> GetProductDiscontinueIntake(Label label, BotChannel botChannel, long customerId, DiscontinueProductType discontinueProductType);
    Task<HttpOperationResponse<object>> PutProductOrderV2(Label label, RequestDataProductOrderV2Model productOrder);
    Task<HttpOperationResponse<object>> GetKetelComfortProductDetails(Label label, BotChannel botChannel, long customerId, int accountId);
    Task<HttpOperationResponse<object>> GetHardwareProductSpecifications(Label label, BotChannel botChannel, string productId = null);
    Task<HttpOperationResponse<object>> CreateServiceOrder(Label label, BotChannel botChannel, long customerId, int accountId, RequestDataServiceOrderRequestModel requestDataServiceOrderRequestModel);
    Task<HttpOperationResponse<object>> GetServiceOrderPlanLink(Label label, BotChannel botChannel, string orderId, PlanLinkType? type = null);
    Task<HttpOperationResponse<object>> GetServiceAgreementsForAgreementId(Label label, BotChannel botChannel, int agreementId, bool? activeSelfService = true);
    Task<HttpOperationResponse<object>> GetServiceOrdersFromServiceAgreement(Label label, BotChannel botChannel, string serviceAgreementId, ServiceOrderStatus? orderStatus = null);
    Task<HttpOperationResponse<object>> GetNextBestActions(Label label, BotChannel botChannel, long customerId, int? servingPointId = null);
    Task<HttpOperationResponse<object>> GetCustomerProducts(Label label, BotChannel botChannel, long customerId,
         bool? active = null, bool includeProductRates = false, bool includeConditionsLabel = false);
    Task<HttpOperationResponse<object>> GetCustomerAccountProducts(Label label, BotChannel botChannel, long customerId, int accountId, bool? active = null, bool includeProductRates = false, bool includeConditionsLabel = false);
    Task<HttpOperationResponse<object>> CalculateFineCancelledProduct(Label label, RequestDataProductCancelRequestModel request);
    Task<HttpOperationResponse<object>> CalculateFineSwitchedProduct(Label label, RequestDataProductSwitchRequestModel request);
    Task<HttpOperationResponse<object>> GetCustomerRelocations(Label label, BotChannel botChannel, long customerId);
}
