﻿using DC.Domain.Exceptions.ResponseModels;
using DC.Financials.Client.Models;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using Microsoft.Rest;
using System.Threading.Tasks;

namespace DC.Bot.Repositories.Interfaces
{
    public interface IDcFinancialsRepository
    {
        Task<HttpOperationResponse<object>> GetAdvancePayment(Label label, BotChannel botChannel, long customerId, int accountId);
        Task<HttpOperationResponse<object>> GetFinancialsPreferences(Label label, BotChannel botChannel, long customerId, int accountId);
        Task<HttpOperationResponse<object>> GetPaymentArrangement(Label label, BotChannel botChannel, long customerId, int accountId);
        Task<HttpOperationResponse<object>> PutAdvancePayment(Label label, BotChannel botChannel, long customerId, int accountId, RequestDataAdvancePaymentChangeRequest request);
        Task<HttpOperationResponse<ErrorResponse>> UpdatePaymentPreferences(Label label, BotChannel botChannel, long customerId, int accountId, RequestDataFinancialPreferences request);
        Task<HttpOperationResponse<object>> GetPaymentPreferences(Label label, BotChannel botChannel, long customerId, int accountId);
        Task<HttpOperationResponse<object>> GetAdvancePaymentAdviceV2(Label label, BotChannel botChannel, long customerId, int accountId);
    }
}
