﻿using DC.Domain.Exceptions.ResponseModels;
using DC.Repositories.Base.Enumerations;
using DC.Storage.Client.Models;
using Microsoft.Rest;
using System.Threading.Tasks;

namespace DC.Bot.Repositories.Interfaces;

public interface IDcStorageRepository
{
    Task<HttpOperationResponse<object>> GetTextlabels(Label label, RequestDataTextLabelRequest request, bool passthroughLanguage = true);

    Task<HttpOperationResponse<ErrorResponse>> AddConversation(Label label, RequestDataBotConversationRequest request);
}
