﻿using DC.Domain.Exceptions.ResponseModels;
using DC.Domain.Models.Usages;
using DC.Repositories.Base.Enumerations;
using DC.Telemetry.Models;
using DC.Usages.Client.Models;
using Microsoft.Rest;
using System;
using System.Threading.Tasks;

namespace DC.Bot.Repositories.Interfaces
{
    public interface IDcUsagesRepository
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Major Code Smell", "S107:Methods should not have too many parameters", Justification = "All params are needed in the call to the PAPI")]
        Task<HttpOperationResponse<object>> GetUsages(Label label, BotChannel botChannel, long customerId, int accountId, DateTime startDate, DateTime endDate, UsageAggregation aggregation = UsageAggregation.Month, UsageInterval interval = UsageInterval.Month);

        Task<HttpOperationResponse<Domain.Exceptions.ResponseModels.ErrorResponse>> RegisterReportRequest(Label label, BotChannel botChannel, long customerId, int accountId, RequestDataReportRequestModel requestModel);

        Task<HttpOperationResponse<object>> GetReadingsHistory(Label label, BotChannel botChannel, long customerId, int accountId);

        Task<HttpOperationResponse<object>> GetOutstandingReadings(Label label, BotChannel botChannel, long customerId, int accountId);

        Task<HttpOperationResponse<object>> GetReading(Label label, BotChannel botChannel, long customerId, int accountId);

        Task<HttpOperationResponse<object>> GetSmartMeterInterruption(Label label, BotChannel botChannel, long customerId, int accountId);

        Task<HttpOperationResponse<object>> GetMandate(Label label, BotChannel botChannel, long customerId, int accountId, ServiceProductType serviceProductType);

        Task<HttpOperationResponse<object>> EnableMandate(Label label, BotChannel botChannel, long customerId, int accountId, ServiceProductType serviceProductType);

        Task<HttpOperationResponse<ErrorResponse>> DisableMandate(Label label, BotChannel botChannel, long customerId, int accountId, ServiceProductType serviceProductType);
    }
}
