﻿using DC.Bot.Repositories.Interfaces;
using DC.Domain.Exceptions.ResponseModels;
using DC.Repositories.Base.Enumerations;
using DC.Storage.Client;
using DC.Storage.Client.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Rest;
using System.Threading.Tasks;

namespace DC.Bot.Repositories;

public class DcStorageRepository : DcBaseBotToPapiRepository, IDcStorageRepository
{
    private readonly IDCApiStorage _client;

    public DcStorageRepository(
        IConfiguration configuration,
        IHttpContextAccessor contextAccessor,
        ILoggerFactory loggerFactory,
        IMemoryCache memoryCache,
        IDCApiStorage client) : base(configuration, contextAccessor, loggerFactory, memoryCache)
    {
        _client = client;
    }

    public async Task<HttpOperationResponse<object>> GetTextlabels(Label label, RequestDataTextLabelRequest request, bool passthroughLanguage = true) =>
        await _client.DCApiStorageTextLabelGetTextLabelsPOSTWithHttpMessagesAsync(label, request, GetHeaders(label, passthroughLanguage: passthroughLanguage));

    public async Task<HttpOperationResponse<ErrorResponse>> AddConversation(Label label, RequestDataBotConversationRequest request) =>
        await _client.DCApiStorageBotAddConversationPUTWithHttpMessagesAsync(request, label, GetHeaders(label));
}
