﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ProduceReferenceAssembly>false</ProduceReferenceAssembly>
  </PropertyGroup>
  <ItemGroup>
    <AdditionalFiles Include="..\.sonarlint\digital.dc.xapi.bot\CSharp\SonarLint.xml" Link="SonarLint.xml" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="DC.Customers.Client" />
    <PackageReference Include="DC.Domain.Exceptions" />
    <PackageReference Include="DC.Financials.Client" />
    <PackageReference Include="DC.Products.Client" />
    <PackageReference Include="DC.Repositories.Base" />
    <PackageReference Include="DC.Storage.Client" />
    <PackageReference Include="DC.Telemetry" />
    <PackageReference Include="DC.Usages.Client" />
    <PackageReference Include="DC.UserAccounts.Client" />
    <PackageReference Include="Microsoft.Rest.ClientRuntime" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" />
  </ItemGroup>
</Project>