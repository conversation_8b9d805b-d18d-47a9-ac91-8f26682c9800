## Project specific entries for a root level .gitignore file

## Only add items to this root level ignore file which can't be added in a
##	first level subfolder .gitignore file (such as /src/.gitignore) for all
##  source code related ignores

*.pdb
.vs/
*.user
.vscode/
**/bin/*
**/obj/*
**/debug/
*.cache
*.ProductionLocal.json
*.Acceptance.Local.json
*.DevelopmentLocal.runsettings
**/.idea/*

## Deprecated, leave for now to prevent accidental checkins
*.DevelopmentLocal.json
/code/DC.Bot.Api/appsettings.DevelopmentProd2.json
