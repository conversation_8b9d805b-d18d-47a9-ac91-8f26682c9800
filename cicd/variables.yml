# Solution specific variables
variables:
  sonarcloudProjectKey: 'digital.dc.xapi.bot'
  apiName: 'Bot'
  devopsBuildPipelineId: '986'
  devopsReleasePipelineId: '4130'
  devopsRepositoryName: 'dc_xapi_bot'
  dotNetVersion: 'net8.0'
  dotNetCliVersion: '8.x'
  NUGET_PACKAGES: '$(Build.ArtifactStagingDirectory)/.nuget/packages'
  CACHE_RESTORED: ''
  cacheVersion: 'version2'
  apimCertBlobPersonal: ''
  apimCertBlobPersonalPrevious: ''
  apimCertBlobPublic: 'LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tDQpNSUlEVFRDQ0FqV2dBd0lCQWdJUWJpRGNHTTlKUzdwRW1BdHhhZjNqeGpBTkJna3Foa2lHOXcwQkFRc0ZBREFuTVNVd0l3WURWUVFEDQpEQnhCVUVsTkxrTnNhV1Z1ZEVObGNuUnBabWxqWVhSbGN5NVNiMjkwTUI0WERUSXpNRGd4TlRJek1EY3hOVm9YRFRJME1EZ3hOVEl6DQpNVGN4TlZvd05URXpNREVHQTFVRUF3d3FRVkJKVFM1RGJHbGxiblJEWlhKMGFXWnBZMkYwWlhNdVFtOTBMbEIxWW14cFl5NHlNREl6DQpMVEE0TUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF0bms2MnJWZmJMSUdmQmQvZ2NDa00xeTcySkRsDQo0VkxBdDNhTVRpTEVPSWx3Z3hCYnlmSmlLSWRQODlzTkY4WHJKby9CZkcxSFVoZ1hwTHlpRmt6Vm1zblpHS3VqMDNORUZRR2lObStRDQp6eVVhMkh5REF4VkxNQU5BZnc5WFB1SGkzYXRCT2Qrd2RpMWp2c28wVGF0YWtGZ3U0SHg1Wm5CTWxtenhtdStvam9lSXkvdU5zN2pLDQpmOCtib1drQm91WmJlQ0NOQ1ZUL3JuVTArZ2VoYmlBMnc1NTBWTE12ODRZeHVHVk5RdlA3OFNYc0xTQWl5ajVKME1nNG50L0hxU3pJDQp1ajE2SUt2Sk1ZT2gwRHQ3THhnRmtrQnlNMEdkMEJMKzg4UXJTWjQvOXI2ZG11K0RyT0ZjeU1RV0tVUGNqNkoydmI5YXNza1pTUGFVDQpSNXY1ZTk2MThRSURBUUFCbzJjd1pUQU9CZ05WSFE4QkFmOEVCQU1DQmFBd0V3WURWUjBsQkF3d0NnWUlLd1lCQlFVSEF3SXdId1lEDQpWUjBqQkJnd0ZvQVVqY1dUdEZ4TlVzWHk1QzROZ0NUa3dVMXV0OXN3SFFZRFZSME9CQllFRklLSHFzQ09hMXdydFNsTWpReW1LdVVKDQoyZldoTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCQVFBb1hHVXBaeVJsTHBGeERRb2c5R1N1UU85WGlZUUcxNnpkRzBzSkhBVS9xZVJ3DQpMTDN4aVppeHFqTHh5MUFKSzI4THA4WmxqcWJJZDBsUkRSTmRadWRLVG1IQVVIMnU1cUlpUC8vVjgwT0tlNk5PQXRLaEFIYUZwbnJYDQpzZzFtMC9heGpwYW50SmNJYWg4YnloaHFrMmNvZ2dDbHlTUGJEMlRROTYxWUcxVitCMGRZVHRGNFNHZGZTeXoyTFp5Q0NCQmcrOVJYDQptcDNJaUltSmRRRzNOdFBXRlUvZXFEd3ErWHhER3NXZ25rMURKVHFud2d5RGdhK2hiWUF3aHVoY29DOXB3cS9ZSUxsMUJYN3dxU3FoDQorN1VyNlZGalZOdWtseXF0amlkUFlycGhuc1VqbG9qcCt0MlV3WElSeXo2NjJKeCsyRzlEbEVETlVrVElSOExXUEwvaA0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQ0K'
  apimCertBlobPublicPrevious: 'LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tDQpNSUlEVFRDQ0FqV2dBd0lCQWdJUUdCdWMyQm1nOHF4SWxhWWJMR0hnRHpBTkJna3Foa2lHOXcwQkFRc0ZBREFuTVNVd0l3WURWUVFEDQpEQnhCVUVsTkxrTnNhV1Z1ZEVObGNuUnBabWxqWVhSbGN5NVNiMjkwTUI0WERUSXlNRGt3TWpBNU16YzFOMW9YRFRJek1Ea3dNakE1DQpORGMxTjFvd05URXpNREVHQTFVRUF3d3FRVkJKVFM1RGJHbGxiblJEWlhKMGFXWnBZMkYwWlhNdVFtOTBMbEIxWW14cFl5NHlNREl5DQpMVEE1TUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUEya3F1SmtSaXA1K3NNT3hONHNjR0JNYSttdStnDQprMWZPcHJnRFl3RWpNaWExdElUUElhWUtkSUZqRFpmaUw5VS9LTTVhZ1NkQjFyTEpLaFU2ZlFnakNmemIxM1FkY3N4dTdyVEFNS3AwDQo1NnNkZG16amZtOE9wVFZsR1Uzd3RBaURteVFwbWlmeFBzR1J6NkZRRU9pUjRCck03K0ZKZituMjVJd21UNmR0THE2ZW9uYnJad1IvDQpwemlZZTFVdzg2WFVSbzJ4R0pYb3R6NDJNRmZsZXVoYXo4aDNhY0FWaDc1b1ZvTXRYVHdUU2VBWDZHdG9QdHZOR2QyZ2R1TkRyWlQrDQptM1ZDSjZSQVArb0dzaXYvVTJpV2N2ZWlTWnNMUk1nZEE1c29ldHVvSG94aEhNU0tWYTVUVko2U1czSjZ2VG5tZEdvVTgyNjlnOWFjDQpmcjNJUkZZY0FRSURBUUFCbzJjd1pUQU9CZ05WSFE4QkFmOEVCQU1DQmFBd0V3WURWUjBsQkF3d0NnWUlLd1lCQlFVSEF3SXdId1lEDQpWUjBqQkJnd0ZvQVVqY1dUdEZ4TlVzWHk1QzROZ0NUa3dVMXV0OXN3SFFZRFZSME9CQllFRk9waVZ5Q2R5UVVNSll1dnU1TmdHc0FJDQpXMzE2TUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCQVFDaUF5SVQwVkJLRXIxcnRsTDBmN1dDTkcvTWhIYzFySjBrdDlkdXF5bGY1dnR0DQpBeEIwcFNRUjRSWksxTmkxeWhPVlpiNndBY3gzUS90bmpDaWlCci83KytZMFA0WkFYQXpTQjhnbUJ2T25EU2hwaU4xRmc5UythbjRQDQpCemdlTTRYODFrbFJYVmliODc1bUlxN2dGWlVLNjdjRTFpYStrSWdQOWNpZGhrUkJBZ0lkTHc1QzByRlVSZ0xsdTZwL3dVUVBVNEtaDQp4QkpwOUhvMGRQNVBvNkFobE1uZThXZitvV2UvS0dtY2crZEZXVkNFN1ZEd1lVTGFhRzVwb2plaUdnTlgxZUJyV2JuZTlPeW1jUjB1DQp6K2tWUUdrYndkNGdHK3VGYVFnclVxT1ZOampyYk9ia3Y5Sk9tdUc3U1d0WDROVkVIRVBKTm9RV3FXZGNGVlA5eHNvMA0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQ0K'
  armTemplateDisplayName: 'Deploy Bot Service as Azure resource'
  armTemplatePath: 'bot_cicd'
  armTemplateDeploymentName: 'BotService'
  apimPersonalApiCerBlobsSecretName: ''
  apimPublicApiCerBlobsSecretName: 'ApimBotPublicApiCerBlobs'
  appSettingsTemplate: >-
    -DIGITALCORE_AesEncryption:Padding "@Microsoft.KeyVault(__REPLACE_VAULT__;SecretName=AesEncryption-Padding-XapiBot)"
    -DIGITALCORE_AesEncryption:Key "@Microsoft.KeyVault(__REPLACE_VAULT__;SecretName=AesEncryption-Key-XapiBot)"
    -DIGITALCORE_AesEncryption:IV "@Microsoft.KeyVault(__REPLACE_VAULT__;SecretName=AesEncryption-IV-XapiBot)"
    -DIGITALCORE_AzureBlobStorage:ConnectionString "@Microsoft.KeyVault(__REPLACE_VAULT__;SecretName=BotFrameworkAzureBlobConnectionString)"
    -DIGITALCORE_AuthenticationSettings:DigitalCore:ServiceAccount:ClientId "@Microsoft.KeyVault(__REPLACE_VAULT__;SecretName=DigitalCore-ServiceAccount-ClientId-XapiBot)"
    -DIGITALCORE_AuthenticationSettings:DigitalCore:ServiceAccount:ClientSecret "@Microsoft.KeyVault(__REPLACE_VAULT__;SecretName=DigitalCore-ServiceAccount-ClientSecret-XapiBot)"
    -DIGITALCORE_HMACSHA256Encryption:Key "@Microsoft.KeyVault(__REPLACE_VAULT__;SecretName=HMACSHA256Encryption-Key-Token-XapiBot)"
    -DIGITALCORE_AzureAiSearchSettings:ApiKey "@Microsoft.KeyVault(__REPLACE_VAULT__;SecretName=AzureAiSearchSettings-ApiKey)"
    -MicrosoftAppId "@Microsoft.KeyVault(__REPLACE_VAULT__;SecretName=BotFrameworkXapiBotClientId)"
    -MicrosoftAppPassword "@Microsoft.KeyVault(__REPLACE_VAULT__;SecretName=BotFrameworkXapiBotClientSecret)"
