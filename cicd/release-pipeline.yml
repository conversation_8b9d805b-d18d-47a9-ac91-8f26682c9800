# Load dc_devops_pipelines repo for access to shared deployment steps
resources:
  repositories:
    - repository: templates
      type: git
      name: "Digital Core/dc_devops_pipelines"
      branch: master
    - repository: dora-metrics-collector
      type: git
      name: 668ed616-964e-4b44-a788-0c5b20b23f63/_git/722febd9-c1a7-457c-9c13-7a2abbcc6282
      # https://dev.azure.com/enecomanagedcloud/DTS%20Enabling%20Technology%20-%20System%20Team/_git/dora-metrics-collector
      ref: refs/heads/main
  pipelines:
  - pipeline: _Infra_CI
    source: Infra_Build
    branch: master
  - pipeline: _Build_CI
    source: Bot_XAPI_Build # <=== Solution Specific
    branch: master
    trigger: 
     branches:
       include: 
       - master

parameters:
  - name: ignoreTestResults
    type: boolean
    displayName: 'Ignore test results'
    default: false

# Release does not get triggered by Pushes, only by build pipeline 
trigger: none

variables:
- template: variables.yml # Load local variables First
- template: vars/variables.yml@templates
- template: override-variables.yml

pool:
  vmImage: $(vmImageName)

stages:

# Test Deployment
- stage: Test_Stage
  jobs:
  - template: release/001-release-xapi-template.yml@templates
    parameters:
      environment:
        name: ${{ variables.testName }}
        keyvault: ${{ variables.testKeyvault }}
        secretsFilter: ${{ variables.secretsFilter }}
        azureSubscription: ${{ variables.testAzureSubscription }}
        apimSubscription: ${{ variables.testApimSubscription }}
        apimCertBlobPersonal: ${{ variables.apimCertBlobPersonal }}
        apimCertBlobPersonalPrevious : ${{ variables.apimCertBlobPersonalPrevious }}
        apimCertBlobPublic: ${{ variables.apimCertBlobPublic }}
        apimCertBlobPublicPrevious : ${{ variables.apimCertBlobPublicPrevious }}
        apimPersonalApiCerBlobsSecretName: ${{ variables.apimPersonalApiCerBlobsSecretName }}
        apimPublicApiCerBlobsSecretName: ${{ variables.apimPublicApiCerBlobsSecretName }}
        appSettings: $(testAppSettings)
        skipIntegrationTest: true
        hasCustomArmTemplate: true
        armTemplateDisplayName: ${{ variables.armTemplateDisplayName }}
        armTemplatePath: ${{ variables.armTemplatePath  }}
        armTemplateDeploymentName: ${{ variables.armTemplateDeploymentName }}
        # === Solution Specific ==
        webAppName: 'digitalcore-xapi-bot-v1-t'
      ignoreTestResults: ${{ parameters.ignoreTestResults }}

# Acceptance Deployment
- stage: Acceptance_Stage
  jobs:
  - template: release/001-release-xapi-template.yml@templates
    parameters:
      environment:
        name: ${{ variables.accName }}
        keyvault: ${{ variables.accKeyvault }}
        secretsFilter: '${{ variables.secretsFilter }}'
        azureSubscription: ${{ variables.accAzureSubscription }}
        apimSubscription: ${{ variables.accApimSubscription }}
        apimCertBlobPersonal: ${{ variables.apimCertBlobPersonal }}
        apimCertBlobPersonalPrevious : ${{ variables.apimCertBlobPersonalPrevious }}
        apimCertBlobPublic: ${{ variables.apimCertBlobPublic }}
        apimCertBlobPublicPrevious : ${{ variables.apimCertBlobPublicPrevious }}
        apimPersonalApiCerBlobsSecretName: ${{ variables.apimPersonalApiCerBlobsSecretName }}
        apimPublicApiCerBlobsSecretName: ${{ variables.apimPublicApiCerBlobsSecretName }}
        appSettings: $(accAppSettings)
        skipIntegrationTest: true
        hasCustomArmTemplate: true
        armTemplateDisplayName: ${{ variables.armTemplateDisplayName }}
        armTemplatePath: ${{ variables.armTemplatePath  }}
        armTemplateDeploymentName: ${{ variables.armTemplateDeploymentName }}
        # === Solution Specific ===
        webAppName: 'digitalcore-xapi-bot-v1-a'
      ignoreTestResults: ${{ parameters.ignoreTestResults }}

# Production Deployment
- stage: Production_Stage
  dependsOn: []
  jobs:
  - template: release/001-release-xapi-template.yml@templates
    parameters:
      environment:
        name: ${{ variables.prodName }}
        keyvault: ${{ variables.prodKeyvault }}
        secretsFilter: '${{ variables.secretsFilterProd }}'
        azureSubscription: ${{ variables.prodAzureSubscription }}
        apimSubscription: ${{ variables.prodApimSubscription }}
        apimCertBlobPersonal: ${{ variables.apimCertBlobPersonal }}
        apimCertBlobPersonalPrevious : ${{ variables.apimCertBlobPersonalPrevious }}
        apimCertBlobPublic: ${{ variables.apimCertBlobPublic }}
        apimCertBlobPublicPrevious : ${{ variables.apimCertBlobPublicPrevious }}
        apimPersonalApiCerBlobsSecretName: ${{ variables.apimPersonalApiCerBlobsSecretName }}
        apimPublicApiCerBlobsSecretName: ${{ variables.apimPublicApiCerBlobsSecretName }}
        appSettings: $(prodAppSettings)
        releasePipelineId: ${{ variables.devopsReleasePipelineId }}
        repositoryName: ${{ variables.devopsRepositoryName }}
        skipIntegrationTest: true
        hasCustomArmTemplate: true
        armTemplateDisplayName: ${{ variables.armTemplateDisplayName }}
        armTemplatePath: ${{ variables.armTemplatePath  }}
        armTemplateDeploymentName: ${{ variables.armTemplateDeploymentName }}
        skipIntegrationTestCoverage: true
        # === Solution Specific ===
        webAppName: 'digitalcore-xapi-bot-v1-p'
      ignoreTestResults: ${{ parameters.ignoreTestResults }}


- stage: ObtainDoraMetrics
  displayName: 'Obtain DORA Metrics'
  dependsOn: Production_Stage
  jobs:
  - template: dora-metrics-template.yml@dora-metrics-collector
    parameters:
      app_name: 'DC-XapiBot'
      #TestRun: 'Yes'
