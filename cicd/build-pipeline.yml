# Load dc_devops_pipelines repo for access to shared deployment steps
resources:
  repositories:
    - repository: templates
      type: git
      name: "Digital Core/dc_devops_pipelines"

# Start pipeline on push on Master
trigger: 
- master

variables:
- template: variables.yml # Load local variables First
- template: vars/variables.yml@templates

pool:
  vmImage: 'windows-2022'


steps:

# Prepare Agent
- template: build/prepare-agent.yml@templates
  parameters: 
    dotNetCliVersion: ${{ variables.dotNetCliVersion }}
# Cache Nugets
- template: build/cache-packages.yml@templates
# Restore Nugets
- template: build/restore-packages-v2.yml@templates
# Prepare SonarCloud
- template: build/prepare-sonarcloud.yml@templates
  parameters: 
    projectKey: '$(sonarcloudProjectKey)'
# Check on prerelease nuget packages
- template: build/check-pre-release-packages.yml@templates
# Build Solution
- ${{ if eq(variables.CACHE_RESTORED, 'false') }}:
    - template: build/build-solution.yml@templates
- ${{ if ne(variables.CACHE_RESTORED, 'false') }}:
    - template: build/build-solution-v2.yml@templates
# Run Unit Tests
- template: build/run-unit-tests.yml@templates
# Run SonarCloud Analysis
- template: build/run-sonarcloud.yml@templates

# Only trigger the following steps if the build was not triggered by a Pull Request
- ${{ if ne(variables['Build.Reason'], 'PullRequest') }}:
  # Publish API Code
  - template: build/publish-api-code.yml@templates
  # Publish IntegrationTest Project
  - template: build/publish-integration-client.yml@templates
  # Publish API Code Artifact
  - template: build/publish-artifact-api-code.yml@templates
  # Publish IntegrationTest Artifact
  - template: build/publish-artifact-integration-client.yml@templates
  # Publish Bot Service ARM template
  - template: publish-artifact-bot-service-arm-template.yml

# Publish Quality Gate Result
- template: build/publish-sonarcloud-result.yml@templates
