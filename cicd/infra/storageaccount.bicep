@description('The name of the target DTAP environment')
@allowed([
  'Test'
  'Acceptance'
  'Production'
])
param environmentName string

param resourceName string

resource storageAccount 'Microsoft.Storage/storageAccounts@2019-04-01' = if (contains(
  [
    'Acceptance'
    'Production'
  ],
  environmentName
)) {
  name: resourceName
  location: resourceGroup().location
  sku: {
    name: 'Standard_LRS'
  }
  kind: 'StorageV2'
  properties: {
    accessTier: 'Hot'
    supportsHttpsTrafficOnly: true
    minimumTlsVersion: 'TLS1_2'
  }
}

resource blobService 'Microsoft.Storage/storageAccounts/blobServices@2019-06-01' = if (contains(
  [
    'Acceptance'
    'Production'
  ],
  environmentName
)) {
  parent: storageAccount
  name: 'default'
}

resource sessionStateContainer 'Microsoft.Storage/storageAccounts/blobServices/containers@2019-06-01' = if (contains(
  [
    'Acceptance'
    'Production'
  ],
  environmentName
)) {
  parent: blobService
  name: 'sessionstate'
  properties: {
    publicAccess: 'None'
  }
}

resource managementPolicy 'Microsoft.Storage/storageAccounts/managementPolicies@2019-06-01' = if (contains(
  [
    'Acceptance'
    'Production'
  ],
  environmentName
)) {
  parent: storageAccount
  name: 'default'
  properties: {
    policy: {
      rules: [
        {
          name: 'sessionstate-cleanup'
          type: 'Lifecycle'
          definition: {
            actions: {
              baseBlob: {
                delete: {
                  daysAfterModificationGreaterThan: 2
                }
              }
            }
            filters: {
              prefixMatch: [
                'sessionstate'
              ]
              blobTypes: [
                'blockBlob'
              ]
            }
          }
        }
      ]
    }
  }
}
