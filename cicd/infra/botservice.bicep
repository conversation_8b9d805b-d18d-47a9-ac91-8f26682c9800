param appServiceResourceName string

param botSku string

param appRegistrationBotServiceClientId string

param appInsightsInstrumentationKey string
param appInsightsApplicationId string

resource botService 'Microsoft.BotService/botServices@2018-07-12' = {
  name: appServiceResourceName
  location: 'global'
  sku: {
    name: botSku
  }
  kind: 'sdk'
  properties: {
    displayName: 'Digital Core Bot'
    endpoint: 'https://${reference('Microsoft.Web/sites/${appServiceResourceName}','2018-02-01').defaultHostName}/api/messages'
    msaAppId: appRegistrationBotServiceClientId
    developerAppInsightKey: appInsightsInstrumentationKey
    developerAppInsightsApplicationId: appInsightsApplicationId
  }
}

resource directLineChannel 'Microsoft.BotService/botServices/channels@2018-07-12' = {
  parent: botService
  name: 'DirectLineChannel'
  location: 'global'
  properties: {
    channelName: 'DirectLineChannel'
    properties: {
      sites: [
        {
          siteName: 'Conversationals-Seamly'
          isEnabled: true
          isV1Enabled: true
          isV3Enabled: true
          isSecureSiteEnabled: false
          trustedOrigins: null
        }
      ]
    }
  }
}
