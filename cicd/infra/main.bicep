@description('The name of the API of the applicable app service resource, including version. Example: preferencescontact-v0')
param apiName string

@description('The layer within the API platform the API is part of (default = XAPI for bot).')
@allowed([
  'papi'
  'xapi'
])
param apiLayer string = 'xapi'

@description('The ClientId of the AAD App Registration used to create a trust between the Azure Bot Framework and our Bot Service resource.')
param appRegistrationBotServiceClientId string

@description('The name of the target DTAP environment')
@allowed([
  'Test'
  'Acceptance'
  'Production'
])
param environmentName string

@allowed(['S1', 'F0'])
param botServiceSku string

@allowed(['001', '101', '201'])
param environmentNumber string

@description('SKU name for the AI Foundry service')
@allowed(['S0', 'S1', 'S2'])
param aiFoundrySku string = 'S0'

@description('AI Foundry projects configuration')
param aiFoundryProjects array = [
  {
    name: 'default-project'
  }
]

@description('Model deployments configuration')
param modelDeployments array = [
  {
    name: 'gpt-4o'
    sku: {
      capacity: 150
      name: 'GlobalStandard'
    }
    model: {
      name: 'gpt-4o'
      format: 'OpenAI'
    }
  }
]

var appInsightsResourceName = '${appServiceResourceNameWithoutEnvSuffix}-${environmentNameSuffix}-ai'
var appServiceResourceName = '${((toLower(apiLayer)=='papi')?appServiceResourceNameWithoutEnvSuffix:'digitalcore-xapi-${apiName}')}-${environmentNameSuffix}'
var appServiceResourceNameWithoutEnvSuffix = '${resourceGroupNameStripped}-${apiName}'
var environmentNameSuffix = toLower(substring(environmentName, 0, 1))
var resourceGroupNameStripped = substring(
  resourceGroupNameWithoutRgPrefixAndWithoutEnvNumberSuffix,
  0,
  lastIndexOf(resourceGroupNameWithoutRgPrefixAndWithoutEnvNumberSuffix, '-')
)
var resourceGroupNameWithoutRgPrefix = replace(resourceGroup().name, 'rg-', '')
var resourceGroupNameWithoutRgPrefixAndWithoutEnvNumberSuffix = substring(
  resourceGroupNameWithoutRgPrefix,
  0,
  lastIndexOf(resourceGroupNameWithoutRgPrefix, '-')
)

var aiFoundryResourceName = '${appServiceResourceNameWithoutEnvSuffix}-${environmentNameSuffix}-aifoundry-test'

resource applicationInsights 'Microsoft.Insights/components@2020-02-02' existing = {
  name: appInsightsResourceName
  scope: resourceGroup('rg-digitalcore-mon-ene-${environmentNameSuffix}-${environmentNumber}')
}

module botService 'botservice.bicep' = {
  name: '${deployment().name}_bot'
  params: {
    appInsightsApplicationId: applicationInsights.properties.ApplicationId
    appInsightsInstrumentationKey: applicationInsights.properties.InstrumentationKey
    appRegistrationBotServiceClientId: appRegistrationBotServiceClientId
    botSku: botServiceSku
    appServiceResourceName: appServiceResourceName
  }
}

module storageAccount 'storageaccount.bicep' = {
  name: '${deployment().name}_storage'
  params: {
    environmentName: environmentName
    resourceName: 'egdigitaldcxapibot${environmentNameSuffix}'
  }
}

module aiFoundry 'aiFoundry.bicep' = {
  name: '${deployment().name}_aifoundry_test'
  params: {
    aiFoundryName: aiFoundryResourceName
    location: 'swedencentral'
    skuName: aiFoundrySku
    aiFoundryProjects: [for project in aiFoundryProjects: {
      name: '${project.name}-${environmentNameSuffix}'
      location: 'swedencentral'
    }]
    modelDeployments: modelDeployments
  }
}
