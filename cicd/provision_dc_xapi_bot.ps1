[CmdletBinding()]
Param(
    [Parameter(Mandatory=$true)]
    [string]$ApiName = $( Read-Host "Name of target API name? (example: enecoweb-v1)"),

    [Parameter(Mandatory=$true)]
    [string]$TargetEnvironmentName = $( Read-Host "Name of target environment? (example: test)"),

    [Parameter(Mandatory=$false)]
    [string]$BrandLabel = "eneco"
)
$ApiName                = $ApiName.ToLower();
$BrandLabel             = $BrandLabel.ToLower();
$TargetEnvironmentName  = $TargetEnvironmentName.ToLower();

# ARM TEMPLATE BASED ON
# https://github.com/Azure/azure-quickstart-templates/tree/master/101-webapp-custom-deployment-slots

Write-Host "Start provisioning Digital Core Azure resources" -ForegroundColor Yellow
Write-Host "==============================================================" -ForegroundColor Yellow
Write-Host " Environment:`t`t$TargetEnvironmentName" -ForegroundColor Yellow
Write-Host " Brand:`t`t`t$BrandLabel" -ForegroundColor Yellow
Write-Host " Api name:`t`t$ApiName" -ForegroundColor Yellow

$ResourceGroupNameSuffix = $null;
$SubscriptionId = $null;
switch ($targetEnvironmentName) 
{ 
    "test"          { $ResourceGroupNameSuffix = "t-201" ; $SubscriptionId = "7b1ba02e-bac6-4c45-83a0-7f0d3104922e"; }
    "acceptance"    { $ResourceGroupNameSuffix = "a-101" ; $SubscriptionId = "da29ede9-8464-467d-981b-258ef454a8a2";  }
    "production"    { $ResourceGroupNameSuffix = "p-001" ; $SubscriptionId = "323c3ace-1913-42da-a831-3a9f5e3cd2ac";  }

    default { Throw "Environment unknown: $targetEnvironmentName" }
}

#Determine Azure ARM files exists
#-------------------------------
$ArmTemplatePath = "$($PSScriptRoot)\dc_xapi_bot_ARM.json";
$ArmParametersPath = "$($PSScriptRoot)\dc_xapi_bot_ARM.parameters.{0}.json" -f $targetEnvironmentName;
if((Test-Path $ArmTemplatePath) -eq $false) {
    throw [System.IO.FileNotFoundException] "Azure ARM file '$ArmTemplatePath' required, but not found."
}
Write-Host " ARM template file`t`t$ArmTemplatePath"


if((Test-Path $ArmParametersPath) -eq $false) {
throw [System.IO.FileNotFoundException] "Azure ARM parameters file '$ArmParametersPath' required, but not found."
}

# Determine target Resource Group
$BrandLabelInsertion = $BrandLabel.Substring(0,3);
$ResourceGroupName = "rg-digitalcore-api-{0}-{1}" -f $BrandLabelInsertion, $ResourceGroupNameSuffix;
$DeploymentName = "{0}-from-devbox" -f $($ResourceGroupName.Substring(3));
$location = "West Europe";

Write-Host
Write-Host " Target resourcegroup:`t$ResourceGroupName";
Write-Host "  Location (if new):`t$location" -ForegroundColor Gray;

# Connect to Azure subscription / login
if ([string]::IsNullOrEmpty($(Get-AzureRmContext).Account)) {
    Write-Host "Explicit login to Azure (no ARM context yet)";
    Login-AzureRmAccount;
}

#Select subscription for the target environment
$TargetSubscription = Get-AzureRmSubscription -SubscriptionId $SubscriptionId;
Set-AzureRmContext -Subscription $TargetSubscription;

#region Create Params Object
# Adding the params as a hashtable is the only way to do it
$additionalParams = New-Object -TypeName Hashtable;
$params = Get-Content $ArmParametersPath -Raw | ConvertFrom-Json;
$params = $params.parameters;
Write-Host " Parameter values (overrides)" -ForegroundColor Gray
foreach($p in $params | Get-Member -MemberType *Property)
{
    if($params.$($p.Name).value.GetType() -ne [System.String]) {
        # object and/or secureobject
        # https://blogs.msdn.microsoft.com/golive/2016/08/16/passing-complex-parameters-to-new-azurermresourcegroupdeployment-inside-an-azure-automation-runbook/
        $valueValue = $params.$($p.Name).value;
        $complexObject = @{}
        foreach($attrib in (($params.$($p.Name).value) | Get-Member -MemberType *Property)) {
            $complexObject.Add($attrib.Name, $valueValue.$($attrib.Name));
        }
        $additionalParams.Add($p.Name, $complexObject);
        Write-Host ('  {0} - {1}' -f $p.Name, ($complexObject | ConvertTo-Json)) -ForegroundColor Gray
    }
    # or a normal plain text parameter
    else
    {
        $additionalParams.Add($p.Name, $params.$($p.Name).value);
        if($params.$($p.Name).type -ne "securestring") {
            Write-Host ('  {0} - {1}' -f $p.Name, $params.$($p.Name).value) -ForegroundColor Gray
        }
    }
}

# Construct Parameters Object that are needed besides the values from parameter files
#  on Azure DevOps these values will come from the variables set on the Release pipeline
$additionalParams.add("apiName",    $ApiName);
Write-Host "  Additional params:" -ForegroundColor Gray -NoNewline;
$(foreach ($ht in $additionalParams)
 {new-object PSObject -Property $ht}) | Format-List

try {

    Write-Host "Starting ARM deployment...";

    $output = New-AzureRmResourceGroupDeployment -Name $DeploymentName -ResourceGroupName $ResourceGroupName -TemplateFile $ArmTemplatePath -TemplateParameterObject $additionalParams;
        # -DeploymentDebugLogLevel All -Debug;
    
    Write-Host "Deployment Complete.";
    Write-Host
    Write-Host "note: explicitly check deployment status in logging above."
    Write-Host "---------------" -ForegroundColor Green
    Write-Host "SCRIPT FINISHED" -ForegroundColor Green
    Write-Host "----------------" -ForegroundColor Green
    Write-Host
    Write-Host $output.OutputsString -ForegroundColor Gray;

    if($output.ProvisioningState -eq "Succeeded") {
        [console]::beep(2000,500)
        [console]::beep(2000,500)
    } else {
        [console]::beep(1700,300)
        [console]::beep(2000,300)
        [console]::beep(1700,300)
        [console]::beep(2000,300)
        [console]::beep(1700,300)
        [console]::beep(2000,300)
    }

}
catch 
{
    Write-Error $_.Exception.Message

    [console]::beep(1700,300)
    [console]::beep(2000,300)
    [console]::beep(1700,300)
    [console]::beep(2000,300)
    [console]::beep(1700,300)
    [console]::beep(2000,300)

    Break;
}